<?xml version="1.0"?>
<mapper namespace="default">
    <select id="list_role_page">
        with role_page as (
            select page_id, role_id, operation_type
            from sys.role_page_permission
            where role_id =  #{role_id}
        ),role_page_detail as (
        select
		    case when r_p.operation_type is null then 0
			    else r_p.operation_type
		    end as operation_type,
		    page.page_id,  page.page_name, page.weight, page.parent_id
        from sys.page_permission page
        left join role_page r_p on r_p.page_id = page.page_id
        where page.is_active is True
        )
        select
        page_id::varchar,  page_name, weight::varchar, parent_id::varchar, operation_type
        from role_page_detail
        order by weight desc
    </select>

    <insert id="upsert_role_page_permission">
        with upsert as (
            update sys.role_page_permission
            set operation_type = #{operation_type}
            where role_id = #{role_id}
            and page_id = #{page_id}
            returning *
        )
        insert into sys.role_page_permission(role_id,page_id,operation_type)
        select #{role_id},#{page_id},#{operation_type}
        where not exists( select 1 from upsert where role_id = #{role_id} and page_id = #{page_id})
    </insert>

    <select id ="get_role_page_permission">
        select operation_type from sys.role_page_permission
        where role_id = #{role_id} and page_id = #{page_id}
    </select>

    <delete id="delete_all_role_page_permission">
        delete from sys.role_page_permission where role_id = #{role_id}
    </delete>

    <insert id="save_role_page_permission">
        insert into sys.role_page_permission(role_id,page_id,operation_type)
        values(#{role_id},#{page_id},#{operation_type})
    </insert>
    <select id="list_admin_page">
        select page_id::varchar,
               page_name as title,
               page_route as path,
               extra1 as name,
               weight,
               parent_id::varchar,
               icon
            from sys.page_permission
            where is_active is TRUE
            order by weight desc
    </select>
    <!-- 普通用户登录页面树 -->
    <select id="list_normal_user_role_page">
        with role_page as (
            select page_id, role_id, operation_type
            from sys.role_page_permission
            where role_id =  #{role_id}
        ),role_page_detail as (
        select
		    case when r_p.operation_type is null then 0
			    else r_p.operation_type
		    end as operation_type,
		    page.page_id,
            page_route,
            extra1,
            icon,
            page.page_name,
            page.weight,
            page.parent_id
        from sys.page_permission page
        left join role_page r_p on r_p.page_id = page.page_id
        where page.is_active is True
        )
        select
            page_id::varchar,
            page_name as title,
            page_route as path,
            extra1 as name,
            weight,
            parent_id::varchar,
            icon,
            operation_type
        from role_page_detail
        order by weight desc
    </select>
</mapper>
