class OurBatisError(RuntimeError):
    """
    A generic, OurBatis-specific error.
    """


class NoInitSessionException(OurBatisError):
    pass


class CtxException(OurBatisError):
    MESSAGE = """\
Working outside context.
"""

    def __init__(self):
        super().__init__(self.MESSAGE)


class ForeachCollectionMiss(OurBatisError):
    def __init__(self, collect_name):
        super().__init__(f"miss {collect_name} which use by foreach")


class ForeachItemNameMiss(OurBatisError):
    def __init__(self):
        super().__init__(f"foreach miss 'item' attribute")


class SQLParamMiss(OurBatisError):
    def __init__(self, param_name):
        super().__init__(f"miss param '{param_name}'")


class SQLParamTypeError(OurBatisError):
    def __init__(self, param_name, target_type, param_type):
        super().__init__(
            f"param '{param_name}' target type {target_type} get type {param_type}"
        )


class SqlIDMissError(OurBatisError):
    def __init__(self, file_path: str, content: str):
        super().__init__(f"not have sql id. in file[{file_path}] content[{content}]")


class SqlIDNamingConflictError(OurBatisError):
    def __init__(self, sql_id: str, first_place: str, now_place: str):
        super().__init__(
            f"'{now_place}' have a sql_id('{sql_id}') which is already defined in '{first_place}'. "
        )


class NotFoundSqlIDError(OurBatisError):
    def __init__(self, sql_id: str):
        super().__init__(f"not found sql_id '{sql_id}'")


class ParamValidFailed(OurBatisError):
    def __init__(self, message: str):
        super().__init__(message)


class ResultValidFailed(OurBatisError):
    pass


class NotFoundNamespaceError(OurBatisError):
    pass


class EvalFailed(OurBatisError):
    def __init__(self, message: str):
        super().__init__(message)
