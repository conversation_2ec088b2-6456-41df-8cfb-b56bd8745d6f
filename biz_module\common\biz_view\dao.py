from typing import Any, List
from ourbatis import sql_func
from .typedef import (
    BizViewEntity,
    BizViewDimensionEntity,
    BizViewDimensionDropListResponse,
    BizViewFilter,
)


@sql_func(namespace="biz_view")
class BizViewDao:
    """
    业务视图DAO
    """

    def find_by_alias_name(self, alias_name: str, exclude_biz_view_id: int = None) -> BizViewEntity:
        ...

    def list_biz_view(self) -> List[BizViewEntity]:
        ...

    def find_biz_view_by_id(self, id: int) -> BizViewEntity:
        ...

    def find_biz_view_by_code(self, view_code: str) -> BizViewEntity:
        ...

    def save_biz_view(self, biz_view_entity: BizViewEntity) -> int:
        ...

    def update_biz_view_by_id(self, biz_view_entity: BizViewEntity) -> int:
        ...


@sql_func(namespace="biz_view_dimension")
class BizViewDimensionDao:
    """
    业务视图维度DAO
    """

    def list_by_biz_view_id(self, biz_view_id) -> List[BizViewDimensionDropListResponse]:
        ...

    def find_biz_view_dimension_by_code(self, biz_view_id: int, dimension_code: str) -> BizViewDimensionEntity:
        ...

    def save_dimension(self, dimension: BizViewDimensionEntity) -> int:
        ...

    def update_dimension(self, dimension: BizViewDimensionEntity):
        ...

    def find_view_dimension_by_id(self, dimension_id: int) -> BizViewDimensionEntity:
        ...


@sql_func(namespace="biz_view_filter")
class BizViewFilterDao:
    def save_view_filter(self, view_filter: BizViewFilter) -> int:
        ...

    def delete_by_biz_view_id(self, biz_view_id: int, modifier: int):
        ...
