import logging
from datetime import datetime
from enum import Enum
from typing import List, Tu<PERSON>, Optional

from fastboot.utils import sync_request
from fastboot.utils.config import ConfigMeta, get_tenant_code, get_virtual_code
from fastboot.utils.nest import egg
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class DagStateEnum(str, Enum):
    STATE_RUNNING = "running"
    STATE_FAILED = "failed"
    STATE_SUCCESS = "success"


class GetDagStateRequest(BaseModel):
    dag_id: str


class GetDagStateResponse(BaseModel):
    base_date: datetime
    dr_choices: List[List[str]]
    dr_state: Optional[DagStateEnum]
    dttm: datetime
    execution_date: str
    num_runs: int


class ClearTaskRequest(BaseModel):
    dag_id: str
    task_id: str
    execution_date: str
    downstream: bool = True
    recursive: bool = True


class ClearTaskResponse(BaseModel):
    count: int


@egg
class AirFlowUtils:
    AIRFLOW_ADDRESS: str
    INSTANCE_VERSION: str = ConfigMeta(name="tenant_instance.version")
    AIRFLOW_RERUN_TASK: str

    @property
    def dag_state_url(self) -> str:
        return f"http://{self.AIRFLOW_ADDRESS}/customer_api/get_dag_state"  # 获取调度图状态

    @property
    def clear_task_instance_url(self) -> str:
        return f"http://{self.AIRFLOW_ADDRESS}/customer_api/clear_task_instance"  # clear task状态

    @classmethod
    def gen_dag_id(cls) -> str:
        result = f"p{get_tenant_code()}_{get_virtual_code()}_ordinary_v{cls.INSTANCE_VERSION.replace('.', '')}"
        return result

    @classmethod
    def get_execution_date(cls, dag_state_response: GetDagStateResponse) -> Optional[str]:
        if not dag_state_response.dr_choices:
            return None

        for dr_choice in dag_state_response.dr_choices:
            execution_date, choice_info = dr_choice
            return execution_date  # 现在算法取最新的调度情况

        return None

    def get_dag_state(self) -> Tuple[Optional[GetDagStateResponse], bool]:
        request = GetDagStateRequest(dag_id=self.gen_dag_id())
        response = None
        result, ok = sync_request.post(self.dag_state_url, json=request.dict())
        if ok:
            response = GetDagStateResponse(**result)
        return response, ok

    def clear_task_instance(self, rerun_execution_date: str) -> Tuple[Optional[ClearTaskResponse], bool]:
        request = ClearTaskRequest(
            dag_id=self.gen_dag_id(),
            task_id=self.AIRFLOW_RERUN_TASK,
            execution_date=rerun_execution_date
        )
        response, ok = sync_request.post(self.clear_task_instance_url, json=request.dict())
        return response, ok
