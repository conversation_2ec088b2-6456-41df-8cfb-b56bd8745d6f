REPORT_TABLE_MAP = {}


def get_all_subclasses(cls, subclass_set: set):
    """
    获取所有子类
    :param cls:
    :param subclass_set:
    :return:
    """
    for subclass in cls.__subclasses__():
        subclass_set.add(subclass)
        if len(subclass.__subclasses__()) > 0:
            get_all_subclasses(subclass, subclass_set)

    return subclass_set


def register_report_component():
    from biz_module.common.report_table.component_parse.component_parse import ComponentParseBase
    subclasses = get_all_subclasses(ComponentParseBase, set())
    for subclass in subclasses:
        REPORT_TABLE_MAP[subclass.component_name] = subclass
