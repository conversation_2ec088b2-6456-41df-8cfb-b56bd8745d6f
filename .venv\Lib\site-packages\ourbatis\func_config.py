from typing import Optional, Callable, Dict

from pydantic import BaseModel

from ourbatis.const import DEFAULT_NS
from ourbatis.session import SqlSession


class CompileConfig(BaseModel):
    """
    sql函数定义时的配置信息
    """

    raw: bool = False  # 是否获取原始输出(CursorResult.fetchall(), CursorResult.keys())
    sql_id: Optional[str] = None  # 该sql_id代替默认的函数名
    decorator_func: Optional[Callable] = None
    namespace: str = DEFAULT_NS  # 命名空间
    no_annotate: Optional[bool] = None  # 是否需要注释
    # GP数据库基于GPORCA优化器并发更新同一记录时会报错, 该配置项生效时,执行sql片段时采用 Postgres Planner 优化器
    concurrent_protect = False


class RuntimeConfig(BaseModel):
    """
    sql函数运算时配置信息
    """

    run_session: Optional[SqlSession] = None  # 运行时session
    only_sql_snippet = False
    append_sql: Optional[str] = None
    direct_func_params: Dict = {}  # todo 基于sql_id直接运行时参数入口
    no_annotate: Optional[bool] = None

    class Config:
        arbitrary_types_allowed = True
