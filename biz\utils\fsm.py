#!usr/bin/env python
# -*-coding:utf-8-*-
"""
author:fly_

"""
import threading

from biz.common.lz_db import DBUtil
from biz import config

CONSTANT = config['schema_constant']

# transition_list = [
#     {
#         "source_state": "extra",
#         "next_state": "replenish",
#         "event": ["extra_view", "extra_modify", "extra_commit"],
#         "start_state": True
#     },
#     {
#         "source_state": "replenish",
#         "next_state": "intra",
#         "event": ["replenish_view", "replenish_modify", "replenish_commit"]
#     },
#     {
#         "source_state": "intra",
#         "next_state": "end",
#         "event": ["intra_view", "intra_modify", "intra_commit"]
#     },
# ]


def get_transition_list(params=None):
    params = params if params else {}
    sql = """
        select source_state, next_state, event, start_state
        from {schema_name}.rst_ra_finite_state
        order by start_state
    """.format(**CONSTANT)
    result = DBUtil.fetch_data_sql(sql=sql, params=params)
    transition_list = [dict(row) for row in result]
    return transition_list


def synchronized(func):

    func.__lock__ = threading.Lock()

    def lock_func(*args, **kwargs):
        with func.__lock__:
            return func(*args, **kwargs)
    return lock_func


class FiniteStateMachine(object):
    _instance_lock = threading.Lock()
    state_map = {}
    action_map = {}

    def __init__(self, table=None):
        self.table = table or get_transition_list()
        self._traverse_table()

        self.all_state = self._get_all_state
        self._generate_transition()

    def get_state(self, state=None, **tran):
        if state in self.state_map:
            return self.state_map[state]
        elif state is None:     # 是None就初始化转换表的第一个状态（初始状态）
            return self.state_map[self._start_state]
        else:
            return State(state, **tran)

    @property
    def _get_all_state(self):
        """
        without end state
        :return:
        """
        all_state = [tran["source_state"] for tran in self.table]
        return all_state

    def _generate_transition(self):
        for tran in self.table:
            source_state = tran["source_state"]
            # tran["source_state"] = State(source_state, **tran)
            state = State(source_state, **tran)

            self._add_tran(source_state, state)
            # source_state.do_action = tran["action"]

    @property
    def _start_state(self):
        start_state = [tran["source_state"] for tran in self.table if tran["start_state"] is True]
        return start_state[0]

    def _traverse_table(self):
        _previous = {tran["next_state"]: tran["source_state"] for tran in self.table}
        for tran in self.table:
            tran["previous_state"] = _previous.get(tran["source_state"], tran["source_state"])

    @classmethod
    def _add_tran(cls, source, tran):
        cls.state_map[source] = tran

    @synchronized
    def __new__(cls, *args, **kwargs):
        if not hasattr(cls, "_instance"):
            cls._instance = super(FiniteStateMachine, cls).__new__(cls, *args, **kwargs)
        return cls._instance


class State(object):

    def __init__(self, status, **tran):
        self._status = status
        self._tran = tran or {}

        self._do_action = None

    def do_action(self, **action):
        result = self._do_action(**action)
        if result is True:
            pass

    def set_action(self, action_method):
        self._do_action = action_method

    @property
    def status(self):
        return self._status

    @property
    def next_status(self):
        return self._tran.get("next_state") or None

    @property
    def previous_status(self):
        return self._tran.get("previous_state") or None

    @property
    def events(self):
        return self._tran.get("event") or []


# fsm = FiniteStateMachine(table=get_transition_list())

if __name__ == '__main__':
    from flask import request
    from biz import ra_detail_blueprint

    commit_table = {}   # table save skc commit status


    def _get_status(**kwargs):
        """
        从数据库获取skc状态，并使用状态机实例化状态
        :param args:
        :param kwargs:
        :return:
        """
        skc = kwargs.get("skc")
        skc_status = commit_table.get(skc) or None   # 获取数据库中状态
        state = fsm.get_state(state=skc_status)
        #   TODO 过期自动提交应该在这里处理比较方便

        return state


    def commit_view():
        params = {
            "skc": request.get_json()["skc"],
            "subject": request.get_json()["subject"]
        }
        state = _get_status(skc=params["skc"])
        event = params["subject"] + "_commit"
        #   验证当前动作是否合法
        if event not in state.events:
            return "cont commit now"

        #   合法则触发状态转移
        commit_table[params["skc"]] = state.next_status  # 记录状态
        return "commit success"


    def modify_view():
        params = {
            "skc": request.get_json()["skc"],
            "subject": request.get_json()["subject"]
        }
        state = _get_status(**params)
        event = params["subject"] + "_modify"

        if event not in state.events:
            return "failed"
        return "success", state.status


    ra_detail_blueprint.add_url_rule('/api/test/commit', view_func=commit_view, methods=["POST"])
    ra_detail_blueprint.add_url_rule('/api/test/modify', view_func=modify_view, methods=["POST"])
