# *_*coding:utf-8 *_*
# @Time    : 2018/12/3 18:58
# <AUTHOR> hl_python
# @Email   : <EMAIL>
# @File    : tools.py
# @Software: PyCharm
import datetime
import functools
import logging
import traceback
from collections import Counter
import hashlib
import math
from math import ceil
from typing import Union

import flask
import os
import re

from flask import current_app
from flask_login import current_user
from openpyxl.comments import Comment
from openpyxl.styles import Side, Border, Alignment, PatternFill
from openpyxl.utils import get_column_letter
from pydantic import BaseModel
from typing import Callable

from biz.api_exception import ParameterException
from biz.common.biz_context.context import ctx
from biz.common.lz_db import DBUtil
from biz.extensions import cache
from biz import config as conf
from collections import OrderedDict

CONSTANT = conf['schema_constant']
env = os.getenv('biz_ENV', 'dev')
logger = logging.getLogger(__name__)


def context_wrapper(func):
    # 对于需要在线程中运行的任务，提供线程中的 app_context
    app = current_app._get_current_object()
    # 获取request_id
    request_id = ctx.request_id

    @functools.wraps(func)
    def wrapped(*args, **kwargs):
        with app.app_context():
            try:
                ctx.request_id = request_id
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                logger.error(traceback.format_exc())
                raise e
            finally:
                ctx.request_id = None

    return wrapped


class LabelValue:
    """键值类
    用于创建:
        {
            "label": "xxx",
            "value": "xxx"
        }
    格式数据
    """

    def __init__(self, k, v):
        self.label = k
        self.value = v

    def as_dict(self):
        return {"label": self.label, "value": self.value}


class KeyValue:
    """键值类
    用于创建:
        {
            "name": "xxx",
            "value": "xxx"
        }
    格式数据
    """

    def __init__(self, k, v):
        self.name = k
        self.value = v

    def as_dict(self):
        return {"name": self.name, "value": self.value}


class PageResult:
    """
    分页返回的数据类
        {
        "body": content,
        "total_pages": pages,
        "total_rows": total_rows
        }
    """

    def __init__(self, c, p, r):
        """
        初始化 分页数据对象
        :param c: content 内容数据
        :param p: total_pages 总页数
        :param r: total_rows 总行数
        """
        self.content = c
        self.pages = p
        self.rows = r

    def as_dict(self):
        return {
            "body": self.content,
            "total_pages": self.pages,
            "total_rows": self.rows
        }


def page_validate(page, page_size, row_count):
    """分页校验"""
    page_count = math.ceil(row_count / page_size)
    return page_count, row_count


def get_tree(df, index_key, parent_key, callback=None, sort_callback=None):
    """

    :param df: pandas DataFrame
    :param index_key:
    :param parent_key:
    :return:
    """
    # drop掉 某行 index_key 和 parent_key 同时为null的数据
    result = df.dropna(how='all', subset=[index_key, parent_key])

    result["index"] = result[index_key]
    result.set_index("index", inplace=True)

    # 移除value为空的数据
    result = result[~(result[index_key].isnull())]
    # 记录转为字典，格式 {“index1”： row1_dict， “index2”：row2_dict ...}
    result_dict = result.to_dict(orient="index", into=OrderedDict)
    # 获取根节点列表
    # root_key = [i for i in result[result[parent_key].isna()].index]
    root_key = []
    for index, row in result_dict.items():
        if not row[parent_key] in result_dict:
            root_key.append(index)

        if callback:
            result_dict[index] = callback(row)

    # 获取parent分组， 格式 {“parent1”： childrenes_list,  “parent2”： childrens_list}
    parent_groups = result.groupby(parent_key).groups
    for group, childrens in parent_groups.items():
        # 在result_dict上维护父子关系
        if sort_callback:
            childrens = sort_callback(group, childrens, result_dict)

        for children in childrens:
            if result_dict.get(group):
                if callback:
                    result_dict[group].setdefault("children", []).append(callback(result_dict[children]))
                else:
                    result_dict[group].setdefault("children", []).append(result_dict[children])
                # result_dict[group].setdefault("children", []).append(result_dict[children])
            else:
                break
    content = []
    # 获取维护好父子关系result_dict中的根节点
    for i in root_key:
        # 被注释掉的是redmain #19499引起bug后修复时，加的条件
        # if result_dict[i].get("children", None):
        #     content.append(result_dict[i])
        content.append(result_dict[i])
    return content, result_dict


def get_tree_by_longcode(df):
    """

    :param df:
    :return:
    """

    pass


def style_range(ws, cell_range=None, border=None, fill=None, alignment=None, merge_header=False,
                auto_width=False, merge_list=None, fill_row_list=None, comment_msg=None):
    """
    # excel设置范围样式函数
    :param ws: 表格对象
    :param cell_range:  图表类型
    :param border: 边框样式
    :param fill: 颜色填充样式
    :param alignment:  对齐样式
    :param merge_header： 表头是否合并并居中
    :param auto_width：是否根据内容自动设置列宽
    :param merge_list 要合并的行列数据
    :param fill_row_list 要填充颜色的行数据
    :param comment_msg 添加标注的字典数据
    :return:
    """
    if border is None:
        # 定义边框填充
        bd = Side(border_style='thin', color='000000')
        border = Border(left=bd, right=bd, top=bd, bottom=bd)
    if alignment is None:
        alignment = Alignment(horizontal='center', vertical='center')

    if cell_range == 'product_target':
        # 设置前四行合并数据
        ws.merge_cells(start_row=1, end_row=1, start_column=1, end_column=ws.max_column)
        if current_user.role_name == "管理员":
            ws.merge_cells(start_row=2, end_row=2, start_column=6, end_column=ws.max_column)
            ws.merge_cells(start_row=2, end_row=3, start_column=1, end_column=1)
            ws.merge_cells(start_row=2, end_row=3, start_column=2, end_column=2)
            ws.merge_cells(start_row=2, end_row=3, start_column=3, end_column=3)
            ws.merge_cells(start_row=2, end_row=3, start_column=4, end_column=4)
            ws.merge_cells(start_row=2, end_row=3, start_column=5, end_column=5)
            ws.merge_cells(start_row=4, end_row=4, start_column=1, end_column=3)
            if merge_list:
                for i in merge_list:
                    ws.merge_cells(start_row=i.get("start_row"), end_row=i.get("end_row"),
                                   start_column=i.get("start_column"), end_column=i.get("end_column"))
        else:
            if merge_list:
                for i in merge_list:
                    ws.merge_cells(start_row=i.get("start_row"), end_row=i.get("end_row"),
                                   start_column=i.get("start_column"), end_column=i.get("end_column"))
        # import pdb;pdb.set_trace()
        # 第一行行高
        ws.row_dimensions[1].height = 40
        ws['A1'].alignment = alignment
        # 设置每一行的高
        for row in range(2, ws.max_row + 1):
            # 其他行行高
            ws.row_dimensions[row].height = 25
        # A2：E20 第一列字母+起始行数 :  最后一列字母  + 最后一行
        #                A   2    :   E           20
        cell_range = get_column_letter(1) + "2:" + get_column_letter(ws.max_column) + str(
            ws.max_row)

        rows = ws[cell_range]

        column_widths = dict()
        # 填充颜色
        blue = PatternFill(start_color='87CEFA', end_color='87CEFA', fill_type='solid')  # 蓝色
        orange = PatternFill(start_color='FF7F50', end_color='FF7F50', fill_type='solid')  # 橙色
        # # 设置每一列的格式（居中）无效
        # for column in range(1, ws.max_column + 1):
        #     # print(get_column_letter(column))
        #     ws.column_dimensions[get_column_letter(column)].alignment = alignment
        # # 设置每一行的格式（颜色填充）无效
        # for row in range(1, ws.max_row + 1):
        #     if row == 4:
        #         ws.row_dimensions[row].fill = orange
        #     elif row in fill_row_list:
        #         ws.row_dimensions[row].fill = blue
        if current_user.role_name == "管理员":
            for row in rows:
                for c in row:
                    # 第四行填充
                    if c.row == 4:
                        c.fill = orange
                    if c.row in fill_row_list:
                        c.fill = blue
                    if alignment:
                        c.alignment = alignment
                    if border:
                        c.border = border
        else:
            for row in rows:
                for c in row:
                    # 第四行填充
                    if c.row == 2 or c.row == 3:
                        c.fill = orange
                    if alignment:
                        c.alignment = alignment
                    if border:
                        c.border = border
        # ws.cell(row=4, column=1, value='合计').fill = orange
        # for i in fill_row_list:
        #     ws.cell(row=i.get("minor_total")).fill = blue
        # 列宽自适应
        if auto_width:
            for row in rows:
                for c in row:
                    if current_user.role_name == "管理员":
                        if c.row == 4:
                            c.fill = orange
                        if c.row in fill_row_list:
                            c.fill = blue
                        if alignment:
                            c.alignment = alignment
                        if border:
                            c.border = border
                    else:
                        # 第四行填充
                        if c.row == 2 or c.row == 3:
                            c.fill = orange
                        if alignment:
                            c.alignment = alignment
                        if border:
                            c.border = border
                    if c.value is not None:
                        if isinstance(c.value, datetime.datetime) or isinstance(c.value, datetime.date):
                            cell_width = len(str(c.value))
                        elif isinstance(c.value, str):
                            cell_width = len(c.value.encode('utf-8'))
                        else:
                            cell_width = len(str(c.value))
                        if c.column not in column_widths:
                            column_widths.update({c.column: cell_width})
                        else:
                            if cell_width > column_widths.get(c.column, 20):
                                # c.column列（A,B,C,D），cell_width宽度
                                column_widths.update({c.column: cell_width})

            # 设置列宽
            for k, v in column_widths.items():
                ws.column_dimensions[k].width = v
    elif cell_range == 'product_sale':
        # 设置前四行合并数据
        ws.merge_cells(start_row=1, end_row=1, start_column=1, end_column=ws.max_column)
        if current_user.role_name == "管理员":
            if merge_list:
                for i in merge_list:
                    ws.merge_cells(start_row=i.get("start_row"), end_row=i.get("end_row"),
                                   start_column=i.get("start_column"), end_column=i.get("end_column"))
            # 保证最后一个类别合并
            if merge_list:
                if merge_list[-1] == {'start_row': 2, 'end_row': 2, 'start_column': 17, 'end_column': 18}:
                    ws.merge_cells(start_row=4, end_row=ws.max_row,
                                   start_column=1, end_column=1)
                else:
                    ws.merge_cells(start_row=merge_list[-1].get("end_row") + 1, end_row=ws.max_row,
                                   start_column=1, end_column=1)
            else:
                ws.merge_cells(start_row=3, end_row=ws.max_row,
                               start_column=1, end_column=1)
        else:
            if merge_list:
                for i in merge_list:
                    ws.merge_cells(start_row=i.get("start_row"), end_row=i.get("end_row"),
                                   start_column=i.get("start_column"), end_column=i.get("end_column"))
        # 第一行行高
        ws.row_dimensions[1].height = 40
        ws['A1'].alignment = alignment
        # 设置每一行的高
        for row in range(2, ws.max_row + 1):
            # 其他行行高
            ws.row_dimensions[row].height = 25
        # A2：E20 第一列字母+起始行数 :  最后一列字母  + 最后一行
        #                A   2    :   E           20
        cell_range = get_column_letter(1) + "2:" + get_column_letter(ws.max_column) + str(
            ws.max_row)
        rows = ws[cell_range]

        column_widths = dict()

        # 列宽自适应
        if auto_width:
            for row in rows:
                for c in row:
                    # 设置每一个单元格的填充颜色，对齐样式，边框
                    # 第四行填充
                    if fill:
                        c.fill = fill
                    if alignment:
                        c.alignment = alignment
                    if border:
                        c.border = border
                    if c.value is not None:
                        if isinstance(c.value, datetime.datetime) or isinstance(c.value, datetime.date):
                            cell_width = len(str(c.value))
                        elif isinstance(c.value, str):
                            cell_width = len(c.value.encode('utf-8'))
                        else:
                            cell_width = len(str(c.value))

                        if c.column not in column_widths:
                            column_widths.update({c.column: cell_width})
                        # 当前单元格数据大于上一次属于同一列数据， 取大的一列数据宽度
                        else:
                            if cell_width > column_widths.get(c.column, 20):
                                # c.column列（A,B,C,D），cell_width宽度
                                column_widths.update({c.column: cell_width})
                                # print(column_widths)

            # 设置列宽
            for k, v in column_widths.items():
                ws.column_dimensions[k].width = v
    else:
        # 合并第一行单元格
        if merge_header:
            ws.merge_cells(start_row=1, end_row=1, start_column=1, end_column=ws.max_column)
            # 设置第一行行高
            ws.row_dimensions[1].height = 40
            # 设置水平垂直居中
            ws['A1'].alignment = alignment
            # 设置每一行的高
            for row in range(2, ws.max_row + 1):
                ws.row_dimensions[row].height = 25
            # 表格起始单元格和结束单元格 A2:K12
            cell_range = get_column_letter(1) + "2:" + get_column_letter(ws.max_column) + str(
                ws.max_row)
        else:
            cell_range = ws.dimensions
            if comment_msg:
                for i in range(1, len(comment_msg) + 1):
                    comment = Comment(comment_msg.get(i), 'owner')
                    ws['%s1' % get_column_letter(i)].comment = comment

        if merge_list:
            for i in merge_list:
                ws.merge_cells(start_row=i.get("start_row"), end_row=i.get("end_row"),
                               start_column=i.get("start_column"), end_column=i.get("end_column"))

        rows = ws[cell_range]
        # 列宽字典
        column_widths = dict()

        # 列宽自适应
        if auto_width:
            for row in rows:
                for c in row:
                    # 设置每一个单元格的填充颜色，对齐样式，边框
                    # 第四行填充
                    if fill:
                        c.fill = fill
                    if alignment:
                        c.alignment = alignment
                    if border:
                        c.border = border
                    if c.value is not None:
                        # 获取每个单元格的数据大小从而设置宽度
                        if isinstance(c.value, datetime.datetime) or isinstance(c.value, datetime.date):
                            cell_width = len(str(c.value))
                        elif isinstance(c.value, str):
                            cell_width = len(c.value.encode('utf-8'))
                        else:
                            cell_width = len(str(c.value))
                        # 设置单元格的宽度，同一列的单元格大的宽度覆盖小的宽度
                        if c.column not in column_widths:
                            column_widths.update({c.column: cell_width})
                        else:
                            if cell_width > column_widths.get(c.column, 20):
                                # c.column列（A,B,C,D），cell_width宽度
                                column_widths.update({c.column: cell_width})

            # 设置列宽
            for k, v in column_widths.items():
                ws.column_dimensions[k].width = v + 1


def page_split(sp_list=None, page=None, page_size=None):
    """
    :param sp_list: 要分页的数组
    :param page: 当前页
    :param page_size: 每页条数
    :return: total_rows : 总条数
            total_pages: 总页数
            sp_list_   : 取出该页的数据
    """
    page = page if page else 1
    page_size = page_size if page_size else conf.PAGE_SIZE

    total_rows = len(sp_list)
    total_pages = ceil(total_rows / page_size)

    start_row = (page - 1) * page_size
    end_row = page * page_size
    return total_rows, total_pages, sp_list[start_row: end_row]


def page_init(params):
    if 'page_size' not in params.keys():
        params['page_size'] = conf.PAGE_SIZE
    params['page_limit'] = params['page_size']
    if 'page' in params.keys():
        params['page_offset'] = (params['page'] - 1) * params['page_size']

    return params


def get_sys_date(dtype="str"):
    """
    获取系统时间

    :param dtype: str :返回类型 候选值为"str", "date"
    :return: str with %Y-%m-%d format
    """
    sys_date = datetime.date.today()
    # sys_date = datetime.date(2019, 5, 20)

    if dtype == "str":
        return str(sys_date)
    if dtype == "date":
        return sys_date


def make_name(f_name=None):
    return "{0}{1}".format(f_name, flask.request.path)


# @cache.cached(timeout=1000, key_prefix='table_map/%s')
@cache.memoize(timeout=1000, make_name=make_name)
def table_mapping(subject=None):
    """
    由于接口复用，改进了装饰器（同一个url根据subject的不同，在缓存的key里加上subject参数）

    :param subject: 接口复用标志
    :return:
    """

    sql = """
        select title_code AS key, title_name AS title, data_type, title_type, --前四个字段要固定
              default_sort,read_only as "readOnly", visible, stamp from {schema_name}.rst_table_mapping
        WHERE url_code=:url_code and subject {is_null}
        ORDER BY default_sort
    """.format(is_null=" =:subject " if subject else " ='null' ", **CONSTANT)

    path = flask.request.path
    params = {"url_code": path, "subject": subject}
    table_result = DBUtil.fetch_data_sql(sql=sql, params=params)
    _r = {
        "header": [],
        "first_column": [],
        "first_row": [],
        "second_row": []
    }

    for _row in table_result:
        _r[_row[3]].append(dict(_row)) if _row[3] in _r else None
    return _r


def get_field_map(row, titles):
    """
    将 titles 中需要的字段 进行 k/v 格式转化
    {
        field_key: filed_value
    }
    ->
    {
        filed_name：{
            name:filed_name,
            value=field_value
            }
    }
    """
    field_map = {
        _t["key"]: KeyValue(_t['title'], row[_t['key']] or 0).as_dict()
        for _t in titles
    }
    return field_map


#   TODO 开发时注释掉缓存
# @cache.cached(timeout=1000, key_prefix='charts_map/%s')
def chart_mapping():
    sql = """
        select chart_body
        from {schema_name}.rst_chart_mapping
        WHERE url_code=:url_code
    """.format(**CONSTANT)
    path = flask.request.path
    result = DBUtil.fetch_data_sql(sql=sql, params={"url_code": path})
    result, = result[0]
    return result


def insert_chart_json():
    sql = """
     insert into {schema_name}.rst_chart_mapping(module_code, url_code, chart_body)
     VALUES ('dec_feedback','/api/dec_feedback/distribution_chart',:content)
     """
    adoption_chart = {
        "legend": {
            "data": [
                "<60%",
                "60%-70%",
                "70%-80%",
                "80-90%",
                "90%-100%"
            ]
        },
        "xAxis": [
            {
                "name": None,
                "data": None
            }
        ],
        "yAxis": [
            {
                "name": "区域数"
            }
        ],
        "series": [
            {
                "name": "<60%",
                "type": "bar",
                "stack": "adoption_rates",
                "data": None
            },
            {
                "name": "60%-70%",
                "type": "bar",
                "stack": "adoption_rates",
                "data": None
            },
            {
                "name": "70%-80%",
                "type": "bar",
                "stack": "adoption_rates",
                "data": None
            },
            {
                "name": "80-90%",
                "type": "bar",
                "stack": "adoption_rates",
                "data": None
            },
            {
                "name": "90%-100%",
                "type": "bar",
                "stack": "adoption_rates",
                "data": None
            }
        ]
    }

    compare_chart = {
        "legend": {
            "data": [
                "机不如人",
                "机高一等",
                "旗鼓相当"
            ]
        },
        "xAxis": [{
            "name": None
        }],
        "yAxis": [
            {
                "name": None,
                "data": None
            },
            {
                "position": "left",
                "name": None,
                "data": None
            }
        ],
        "series": [
            {
                "name": "机不如人",
                "type": "bar",
                "stack": "contrast",
                # // 堆叠图堆叠的标识
                "data": None
            },
            {
                "name": "机高一等",
                "type": "bar",
                "stack": "contrast",
                "data": None
            },
            {
                "name": "旗鼓相当",
                "type": "bar",
                "stack": "contrast",
                "data": None
            }
        ]
    }

    broken_rate_chart = {
        "legend": {
            "data": ["期初断码率", "模型补调后断码率", "人工补调后断码率"]
        },
        "xAxis": [
            {
                "name": None,
                "data": None  # ["A", "B", "C", "A", "B", "C", "A", "B", "C"]
            },
            {
                "name": None,
                "position": "bottom",
                "data": None  # ["畅", "平", "滞"]
            }
        ],
        "yAxis": [{
            "name": "断码率"
        }],
        "series": [
            {
                "name": "期初断码率",
                "type": "bar",
                "data": None  # [1, 0.8, 0.6, 1, 0.8, 0.6, 1, 0.8, 0.6]
            },
            {
                "name": "模型补调后断码率",
                "type": "bar",
                "data": None  # [0.8, 0.6, 0.4, 0.8, 0.6, 0.4, 0.8, 0.6, 0.4]
            },
            {
                "name": "人工补调后断码率",
                "type": "bar",
                "data": None  # [0.6, 0.4, 0.2, 0.6, 0.4, 0.2, 0.6, 0.4, 0.2]
            }
        ]
    }

    distribution_chart = {
        "legend": {
            "data": ["期初铺货门店数", "模型补调后铺货门店数", "人工补调后铺货门店数"]
        },
        "xAxis": [
            {
                "name": None,
                "data": None  # ["A", "B", "C", "A", "B", "C", "A", "B", "C"]
            },
            {
                "name": None,
                "position": "bottom",
                "data": None  # ["畅", "平", "滞"]
            }
        ],
        "yAxis": [{
            "name": "铺货门店数"
        }],
        "series": [
            {
                "name": "期初铺货门店数",
                "type": "bar",
                "data": None  # [1, 0.8, 0.6, 1, 0.8, 0.6, 1, 0.8, 0.6]
            },
            {
                "name": "模型补调后铺货门店数",
                "type": "bar",
                "data": None  # [0.8, 0.6, 0.4, 0.8, 0.6, 0.4, 0.8, 0.6, 0.4]
            },
            {
                "name": "人工补调后铺货门店数",
                "type": "bar",
                "data": None  # [0.6, 0.4, 0.2, 0.6, 0.4, 0.2, 0.6, 0.4, 0.2]
            }
        ]
    }
    from psycopg2.extras import Json
    params = {"content": Json(distribution_chart)}
    result = DBUtil.update_data_sql(sql=sql, params=params)


def str_to_md5(param_str):
    m = hashlib.md5()
    m.update(param_str.encode("utf-8"))
    return m.hexdigest()


class PropertyBaseModel(BaseModel):
    """
    继承BaseModel
    允许property属性被作为参数输入
    """
    @classmethod
    def get_properties(cls):
        return [prop for prop in dir(cls) if
                isinstance(getattr(cls, prop), property) and prop not in ("__values__", "fields")]
    def dict(
            self,
            *,
            include: Union['AbstractSetIntStr', 'MappingIntStrAny'] = None,
            exclude: Union['AbstractSetIntStr', 'MappingIntStrAny'] = None,
            by_alias: bool = False,
            skip_defaults: bool = None,
            exclude_unset: bool = False,
            exclude_defaults: bool = False,
            exclude_none: bool = False,
    ) -> 'DictStrAny':
        attribs = super().dict(
            include=include,
            exclude=exclude,
            by_alias=by_alias,
            skip_defaults=skip_defaults,
            exclude_unset=exclude_unset,
            exclude_defaults=exclude_defaults,
            exclude_none=exclude_none
        )
        props = self.get_properties()
        # Include and exclude properties
        if include:
            props = [prop for prop in props if prop in include]
        if exclude:
            props = [prop for prop in props if prop not in exclude]

        # Update the attribute dict with the properties
        if props:
            attribs.update({prop: getattr(self, prop) for prop in props})

        return attribs


def is_int(number_str):
    """验证字符串是否是整数"""
    if not isinstance(number_str, str):
        number_str = str(number_str)
    if number_str[0] in ('-', '+'):
        return number_str[1:].isdigit()
    return number_str.isdigit()


def is_greater_than_0_int(number_str):
    """验证字符串是否是正整数"""
    if not isinstance(number_str, str):
        number_str = str(number_str)
    if not is_int(number_str):
        return False
    return int(number_str) > 0


def is_float(number_str):
    """验证字符串是否是浮点数"""
    if not isinstance(number_str, str):
        number_str = str(number_str)
    return number_str.replace('.', '', 1).isdigit()


def class_first_root(node: dict):
    return node.get("parent_value") is None


def class_parent_child(root: dict, node: dict):
    return root.get('value') == node.get('parent_value')


def list_to_tree(data,
                 first_root_method: Callable[[dict], bool]=None,
                 parent_child_method: Callable[[dict, dict], bool]=None
                 ):
    """
    param: data
    param: first_root_method
    param: parent_child_method
    """
    if not first_root_method:
        first_root_method = class_first_root

    if not parent_child_method:
        parent_child_method = class_parent_child

    root = []
    node = []

    # 初始化数据，获取根节点和其他子节点list
    for d in data:
        d["choice"] = 0
        if first_root_method(d):
            root.append(d)
        else:
            node.append(d)
    # print("root----",root)
    # print("node----",node)
    # 查找子节点
    for p in root:
        add_node(p, node, parent_child_method)

    # 无子节点
    if len(root) == 0:
        return node

    return root


def add_node(p, node, parent_child_method: Callable[[dict, dict], bool]=None):
    # 子节点list
    p["children"] = []
    for n in node:
        if parent_child_method(p, n):
            p["children"].append(n)

    # 递归子节点，查找子节点的节点
    for t in p["children"]:
        if not t.get("children"):
            t["children"] = []
        t["children"].append(add_node(t, node, parent_child_method))

    # 退出递归的条件
    if len(p["children"]) == 0:
        p["choice"] = 1
        return


def check_non_negative_integer(field_value):
    try:
        new_field_value = int(field_value)
        if new_field_value == field_value and 0 <= new_field_value <= 2147483647:
            return True
        elif not (str(field_value).isdigit() and 0 <= new_field_value <= 2147483647):
            return False
    except (ValueError, TypeError) as e:
        return False
    return True


def check_integer(field_value):
    try:
        new_field_value = int(field_value)
        if new_field_value == field_value and new_field_value <= 2147483647:
            return True
        elif not (str(field_value).isdigit() and new_field_value <= 2147483647):
            return False
    except (ValueError, TypeError) as e:
        return False
    return True