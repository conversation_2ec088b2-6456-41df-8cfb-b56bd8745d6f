import re
import logging
from pathlib import Path

from ourbatis.const import DEFAULT_NS
from ourbatis.exceptions import NotFoundSqlIDError
from ourbatis.sql_source_parser.base_parser import BaseParser
from ourbatis.sql_source_parser.objects import (
    SessionSQLMapper,
    SQLMapperMeta,
    BatisElement,
    SQLMapper,
)
from ourbatis.sql_source_parser.params import basic_convert_parameters
from ourbatis.sql_source_parser.sql_context import SqlContext

logger = logging.getLogger("sql_parse")

SQL_META_PATTERN = re.compile(r"--\s*!{\s*id\s*=(?P<sql_id>\S+)\s*}")


class SQLParser(BaseParser):
    SOURCE_SUFFIX = (".sql",)

    @classmethod
    def create_mapper(cls, source_file: Path) -> SessionSQLMapper:
        """
        Parse SQL files
        Get mybatis mapper
        :return:
        """
        meta = SQLMapperMeta(file_name=source_file)
        with open(source_file, "r", encoding="utf-8") as f:
            sql_raw_text = f.read()
        logger.info(f"handler sql file '{meta.file_name}' now")
        session_sql_mapper: SessionSQLMapper = {DEFAULT_NS: {}}

        # 全文件为一个sql片段
        if len(sql_info := SQL_META_PATTERN.split(sql_raw_text)) <= 1:
            session_sql_mapper[DEFAULT_NS][source_file.stem] = BatisElement(
                meta=meta.copy(), parser=cls, element=sql_raw_text
            )
            return session_sql_mapper
        else:
            info_iter = iter(sql_info[1:])
            for sql_id, sql_element in zip(info_iter, info_iter):
                session_sql_mapper[DEFAULT_NS][sql_id] = BatisElement(
                    meta=meta.copy(), parser=cls, element=sql_element
                )

        return session_sql_mapper

    @staticmethod
    def get_sql_statement(
        sql_mapper: SQLMapper, child_id: str, context: SqlContext
    ) -> str:
        child = sql_mapper.get(child_id, None)

        if child is None:
            logger.error(f"have no sql_id '{child_id}'!check your code")
            raise NotFoundSqlIDError(child_id)

        sql_statement, _ = basic_convert_parameters(child.element, context)

        return sql_statement
