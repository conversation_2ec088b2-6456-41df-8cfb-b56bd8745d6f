import inspect
from typing import Optional, Callable, Any, List

from pydantic.fields import <PERSON><PERSON>ield, FieldInfo, Required
from pydantic.schema import get_annotation_from_field_info

from ourbatis.utils import get_name
from ourbatis.utils import get_typed_signature, create_response_field, logger


class Dependant:
    """
    函数的字段和返回信息
    """

    def __init__(
        self,
        *,
        params: Optional[List[ModelField]] = None,
        result: Optional[ModelField] = None,
        call: Optional[Callable[..., Any]] = None,
        name: Optional[str] = None,
    ) -> None:
        self.params = params or []
        self.result = result or None
        self.name = name
        self.call = call


def get_param_field(
    *,
    param: inspect.Parameter,
    param_name: str,
) -> ModelField:
    default_value = Required if param.default == param.empty else param.default
    required = default_value == Required

    field_info = FieldInfo(default_value)
    annotation: Any = Any
    if not param.annotation == param.empty:
        annotation = param.annotation
    annotation = get_annotation_from_field_info(annotation, field_info, param_name)
    field = create_response_field(
        name=param.name,
        type_=annotation,
        default=None if required else default_value,
        alias=param.name,  # todo 暂不支持 alias 功能
        required=required,
        field_info=field_info,
    )
    field.required = required
    return field


def gen_dependent(call: Callable) -> Dependant:
    name = get_name(call)
    dependant: Dependant = Dependant(call=call, name=name)

    params_signature, return_signature = get_typed_signature(call)
    signature_params = params_signature.parameters
    for param_name, param in signature_params.items():
        param_field: ModelField = get_param_field(param=param, param_name=param_name)
        logger.debug(f"{name} param => {param_field}")
        dependant.params.append(param_field)

    dependant.result = (
        create_response_field(name="result", type_=return_signature)
        if return_signature != inspect._empty
        else None
    )

    return dependant
