from datetime import datetime
from typing import Optional

from blinker import Namespace
from pydantic import BaseModel

_signals = Namespace()

FILTER_MODIFY_SIGNAL = _signals.signal("filter_modify")                 # 筛选器已修改信号
TEMPLATE_MODIFY_SIGNAL = _signals.signal("template_modify")             # 业务动作模板已修改信号
GTO_PLAN_BEFORE_RUN_SIGNAL = _signals.signal("gto_plan_before_run")     # 模型运行前信号
GTO_PLAN_AFTER_RUN_SIGNAL = _signals.signal("gto_plan_after_run")       # 模型运行后信号


class GtoPlanBeforeSignalContext(BaseModel):
    biz_action_template_id: int    # 业务动作模板ID
    biz_action_type_code: str      # 业务动作模板类型
    operating_unit_sk: int
    org_sk: int
    day_date: datetime
    solution_id: int            # 流通方案ID
    step_id: int                # 流通步骤ID
    action_id: int              # 流通动作ID
    batch_id: int               # 批次ID


class GtoPlanAfterSignalContext(BaseModel):
    biz_action_template_id: int    # 业务动作模板ID
    biz_action_type_code: str      # 业务动作模板类型
    operating_unit_sk: int
    org_sk: int
    day_date: datetime
    solution_id: int            # 流通方案ID
    step_id: int                # 流通步骤ID
    action_id: int              # 流通动作ID
    batch_id: Optional[int]     # 批次ID, 一些情况下流通方案失败时,不存在
    is_success: bool
