import redis

from biz import config


redis_url = config.APP_REDIS_URL
APP_ID = config.APP_ID


class RedisClientTool:
    """
    redis工具类
    """

    def __init__(self):
        self.prefix = str(APP_ID) + "_"
        self.redis_client = redis.Redis.from_url(redis_url, decode_responses=True)

    def add(self, key, value, expire=None):
        """
        当缓存中键不存在时向缓存中写入键值，可以设置有效期;
        :param key: 键
        :param value: 值
        :param expire: 有效期时间
        :return:
        """
        redis_key = self.prefix + key
        return self.redis_client.add(redis_key, value, expire)

    def set(self, key, value, expire=None):
        """
        存储 redis 数据
        :param key: key
        :param value: values
        :param expire:设置过期时间 单位秒
        :return:
        """
        redis_key = self.prefix + key
        self.redis_client.set(redis_key, value, ex=expire)

    def incr(self, key, count=1, expire=0):
        """
        获取自增序列
        :param key: key
        :param count: count 默认从几开始
        :param expire:设置过期时间 单位秒
        :return:
        """
        redis_key = self.prefix + key
        self.redis_client.incr(redis_key, count)
        if expire:
            self.redis_client.expire(redis_key, expire)

    def delete_key(self, key):
        """
        删除redis里的key
        :param key: 键
        :return:
        """
        redis_key = self.prefix + key
        return self.redis_client.delete(redis_key)

    def get(self, key):
        """
        获取redis里面key对应的值
        :param
        self:
        :param
        key:
        :return:
        """
        redis_key = self.prefix + key
        return self.redis_client.get(redis_key)

    def exists(self,key):
        redis_key=  self.prefix + key 
        return self.redis_client.exists(redis_key)

    
    def get_keys(self,pattern):
        return self.redis_client.keys(pattern)

    def del_keys(self,pattern):
        pattern = self.prefix + pattern
        for key in self.redis_client.keys(pattern):
            self.redis_client.delete(key)
