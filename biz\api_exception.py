#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import inspect
import sys
import os
import six
import traceback
from enum import Enum

import flask
import wsme
from flask_babel import lazy_gettext as _
import pydantic
from sqlalchemy.exc import SQLAlchemyError

from biz.common.lz_wsme.lz_signature import get_dataformat
from biz.constants import EnvEnum

env = os.getenv("biz_ENV", "dev")
debug_mode = os.getenv("")

log = logging.getLogger(__name__)

UNDEFINED_EXCEPTION_CODE = 500
UNDEFINED_EXCEPTION_MSG = _("服务器内部异常（失败）")

PRODUCT_MSG = _("System busy, please contact system admin or try again later")
PRODUCT_ERROR_CODE = 0x1F4


class ErrorCons:
    def __init__(self, **kwargs):
        if "code" in kwargs.keys():
            self.code = kwargs["code"]
        if "msg" in kwargs.keys():
            self.msg = kwargs["msg"]
        self.debuginfo = None

    def as_dict(self):
        return self.__dict__


class EnvErrorCode(Enum):
    prod = ErrorCons(code=PRODUCT_ERROR_CODE, msg=six.text_type(PRODUCT_MSG))
    qa_test = ErrorCons(code=UNDEFINED_EXCEPTION_CODE, msg=six.text_type(UNDEFINED_EXCEPTION_MSG))
    dev = ErrorCons(code=UNDEFINED_EXCEPTION_CODE, msg=six.text_type(UNDEFINED_EXCEPTION_MSG))
    test = ErrorCons(code=UNDEFINED_EXCEPTION_CODE, msg=six.text_type(UNDEFINED_EXCEPTION_MSG))
    local = ErrorCons(code=UNDEFINED_EXCEPTION_CODE, msg=six.text_type(UNDEFINED_EXCEPTION_MSG))


exception_config = {
    "prod": EnvErrorCode.prod,
    "qa_test": EnvErrorCode.qa_test,
    "dev": EnvErrorCode.dev,
    "test": EnvErrorCode.test,
    "local": EnvErrorCode.prod,
}


def lz_format_error(exception_info):
    """
    log exception information and show debuginfo according debug_mode.
    :param exception_info:
        tuple of sys.exc_info() result
    :return:
        dict of ErrorCons instance attributes.
    """
    error = exception_info[1]
    error_code = getattr(error, "error_code", None)
    r = ErrorCons()
    r.msg = six.text_type(error)
    # 区分是否定义的异常
    if error_code:
        if hasattr(error, "msg"):
            r.msg = six.text_type(error.msg)
        r.code = error_code
        log.debug("[biz DEFINED ERROR]: {0}".format(r.msg))
    else:
        debug_info = "\n".join(traceback.format_exception(*exception_info))
        log.error('[biz UNDEFINED ERROR]: "{0}". Detail: \n{1}'.format(r.msg, debug_info))
        if debug_mode:
            r.debuginfo = debug_info

    return r.as_dict()


def lz_exception_data(e):
    # 非指定环境显示 debug 信息
    is_product = True if env != EnvEnum.product else False
    if is_product:
        return EnvErrorCode.prod

    if isinstance(e, SQLAlchemyError):
        data = dict(code=DBAccessFail.error_code, msg=DBAccessFail.msg)
    else:
        data = lz_format_error(sys.exc_info())

    return data


def lz_exception_handler(e):
    data = lz_exception_data(e)
    formatter = get_dataformat()
    res = flask.make_response(formatter.encode_error(None, data))
    res.mimetype = formatter.content_type
    return res


def lz_format_exception(excinfo, debug=False):
    """Extract informations that can be sent to the client."""
    error = excinfo[1]
    error_code = getattr(error, "error_code", None)
    if error_code:
        msg = six.text_type(error.msg) if hasattr(error, "msg") else six.text_type(error)
        r = dict(success=False, code=error_code, msg=msg)
        log.debug("Defined error: %s" % r["msg"])
        r["debuginfo"] = None
    else:
        msg = six.text_type(error)
        debuginfo = "\n".join(traceback.format_exception(*excinfo))

        log.error('Undefined error: "%s". Detail: \n%s' % (msg, debuginfo))

        r = dict(success=False, code=UNDEFINED_EXCEPTION_CODE, msg=six.text_type(UNDEFINED_EXCEPTION_MSG))
        if debug:
            r["debuginfo"] = msg
        else:
            r["debuginfo"] = None
    if hasattr(error, "data") and error.data:
        r["data"] = error.data
    return r


def lz_exception_process(e):
    if isinstance(e, SQLAlchemyError):
        data = dict(success=False, code=DBAccessFail.error_code, msg=DBAccessFail.msg)
        log.error("orm error: {}".format(e))
    elif isinstance(e, (wsme.exc.UnknownAttribute, wsme.exc.UnknownArgument)):
        data = dict(success=False, code=ParameterException.error_code, msg=ParameterException.msg)
        log.error("wsme UnknownAttribute: {}".format(e))
    elif isinstance(e, pydantic.ValidationError):
        data = dict(success=False, code=PydanticValidationError.error_code, msg=PydanticValidationError.msg)

    else:
        exception_info = sys.exc_info()
        data = lz_format_exception(exception_info)
        # data = {
        #    "success": False,
        #    "code": e.error_code,
        #    "msg": six.text_type(e.msg),
        # }
    dataformat = get_dataformat()
    res = flask.make_response(dataformat.encode_error(None, data))
    res.mimetype = dataformat.content_type
    return res


class APIException(Exception):
    data = ""

    def __init__(self, msg=None, error_code=None, data=None):
        if msg:
            self.msg = msg
        if error_code:
            self.error_code = error_code
        if not hasattr(self, "error_code"):
            self.error_code = getattr(self, "code", None)
        if data:
            self.data = data
        caller = inspect.getframeinfo(inspect.stack()[1][0])
        print("%s :::: %d" % (caller.filename, caller.lineno))

        super(APIException, self).__init__(msg, None, None)


class DBAccessFail(APIException):
    msg = _("数据库异常")
    error_code = 0x00000001


class ExcelException(APIException):
    msg = _("excel文件解析错误")
    code = 30000003


class Fail(APIException):
    msg = _("Query failed")
    error_code = 0x00000002


class ParameterException(APIException):
    msg = _("Invalid parameter")
    error_code = 0x00000003


class ServerError(APIException):
    msg = _("The server is busy, please try again later")
    error_code = 0x00000004


class NotFound(APIException):
    msg = _("Request content does not exist")
    error_code = 0x00000005


class DBError(APIException):
    msg = _("The database is busy, please try again later")
    error_code = 0x00000006


class QueryError(APIException):
    msg = _("Query error")
    error_code = 0x00000007


class InvalidUser(APIException):
    msg = _("Invalid username or password")
    error_code = 0x00000008


class InvalidPassword(APIException):
    msg = _("Invalid username or password")
    error_code = 0x00000009


class UserExists(APIException):
    msg = _("This user already exists")
    error_code = 0x0000000A


class CaptchaError(APIException):
    msg = _("Captcha is error")
    error_code = 0x0000000B


class NoLegalPassword(APIException):
    msg = _("The password is not legal.")
    error_code = 0x0000000C


class NewNoLegalPassword(APIException):
    msg = _("The new password is not legal.")
    error_code = 0x0000000D


class DifferentPassword(APIException):
    msg = _("The password entered twice is different.")
    error_code = 0x0000000E


class InvalidUserMail(APIException):
    msg = _("Invalid user or mailbox")
    error_code = 0x0000000F


class InvalidCsrfToken(APIException):
    msg = _("Invalid csrf token")
    error_code = 0x00000010


class NeedLogin(APIException):
    msg = _("登录已超时，请重新登录")
    error_code = 0x00000011


class ResourceTypeExists(APIException):
    msg = _("The resource type already exists")
    error_code = 0x00000012


class ResourceNotEmpty(APIException):
    msg = _("The resource needs to be empty")
    error_code = 0x00000013


class ResourceTypeNotExists(APIException):
    msg = _("The resource type does not exist")
    error_code = 0x00000014


class ResourceNotExists(APIException):
    msg = _("The resource does not exist")
    error_code = 0xF00000015


class ResourceExists(APIException):
    msg = _("The resource already exists")
    error_code = 0x00000016


class RoleNotExists(APIException):
    msg = _("The role does not exist")
    error_code = 0x00000017


class UserForbidden(APIException):
    msg = _("The user already forbidden")
    error_code = 0x00000018


class IsExists(APIException):
    msg = _("This data already exists")
    error_code = 0x00000019


class FieldTypeError(APIException):
    msg = _("Field type error")
    error_code = 0x0000001A


class ResourceTypeMappingExists(APIException):
    msg = _("This resource_type_mapping already exists")
    error_code = 0x0000001B


class ResourceTypeMappingNotExists(APIException):
    msg = _("This resource_type_mapping not exists")
    error_code = 0x0000001C


class FieldEmptyError(APIException):
    msg = _("The field cannot be empty")
    error_code = 0x0000001D


class AttributeNotExist(APIException):
    msg = _("The resource attribute does not exist")
    error_code = 0x0000001F


class FieldLengthNotExist(APIException):
    msg = _("varchar type field length must exist")
    error_code = 0x00000020


class FileTypeError(APIException):
    msg = _("The file type error")
    error_code = 0x00000021


class InsufficientPermissionsError(APIException):
    msg = _("Insufficient permissions")
    error_code = 0x00000022


class RoleUsed(APIException):
    msg = _("This role is in use and cannot be deleted")
    error_code = 0x00000023


class UserUsed(APIException):
    msg = _("This user is in use and cannot be deleted")
    error_code = 0x00000024


class EmailExists(APIException):
    msg = _("This email already exists")
    error_code = 0x00000025


class PhoneExists(APIException):
    msg = _("This phone number already exists")
    error_code = 0x00000026


class ValidationException(APIException):
    msg = _("Validation error")
    error_code = 0x00000027


class ValidationExpireException(APIException):
    msg = _("您的系统于{}到期，请联系系统管理员！")
    error_code = 0x00000028
#
# T2 相关接口使用下面的异常类
#


class T2InternalError(APIException):
    msg = "服务器内部异常"
    # 平台通用
    errror_code = 500


class T2LimitExceed(APIException):
    msg = "超出限流"
    # 平台通用
    errror_code = 429


class T2NotFound(APIException):
    # 平台通用
    error_code = ********
    msg = "请求内容不存在"


class ModuleNotExists(APIException):
    msg = "未找到该版本的模块"
    error_code = ********


class ModuleExists(APIException):
    msg = "已存在该版本的模块"
    error_code = ********


class OperateOnBasicModule(APIException):
    msg = "禁止操作基础模块"
    error_code = ********


class T2AdminAccountEmpty(APIException):
    msg = "账户不能为空"
    error_code = ********


class T2AdminNameEmpty(APIException):
    msg = "名称不能为空"
    error_code = ********


class T2AdminPwdEmpty(APIException):
    msg = "密码不能为空"
    error_code = ********


class T2AdminEmailEmpty(APIException):
    msg = "邮箱不能为空"
    error_code = ********


class T2AdminPhoneEmpty(APIException):
    msg = "手机号不能为空"
    error_code = ********


class T2AccountExists(APIException):
    msg = "账户已存在"
    error_code = ********


class T2AccountNotExists(APIException):
    msg = "账户不存在"
    error_code = ********


class T2EmailEmpty(APIException):
    msg = "邮箱不能为空"
    error_code = ********


class T2UserNotFound(APIException):
    msg = "未找到使用该邮箱的用户"
    error_code = ********


class T2UserIsAdmin(APIException):
    msg = "超级管理员用户请到租户中心修改密码"
    error_code = ********


class T2UrlEmpty(APIException):
    msg = "url 不能为空"
    error_code = ********


class T2TokenEmpty(APIException):
    msg = "token 不能为空"
    error_code = ********


class T2TokenInvalid(APIException):
    msg = "无效的 token"
    error_code = ********


class T2TokenExpired(APIException):
    msg = "token 已过期"
    error_code = ********


class T2PwdEmpty(APIException):
    msg = "密码不能为空"
    error_code = ********


class T2RepeatPwdEmpty(APIException):
    msg = "重复密码不能为空"
    error_code = ********


class T2PwdRepeatPwdNotEqual(APIException):
    msg = "密码和重复密码不一致"
    error_code = 11003009


class T2PwdNotLegal(APIException):
    msg = "密码由字母和数字组成并不少于 8 字符"
    error_code = 11003010


class InsufficientBrandPermission(APIException):
    msg = "无品牌权限"
    error_code = 11003011


class BatchIDNoRunRecord(APIException):
    msg = "相关批次id无运行记录"
    error_code = 11003012


class BatchIDNoAction(APIException):
    msg = "相关批次id关联的流通动作"
    error_code = 11003013


class LoginForbidden(APIException):
    msg = "当前用户已登录,禁止登录"
    error_code = 11003014


class UserNotActive(APIException):
    msg = "当前用户处于禁用状态,请联系管理员"
    error_code = 11003014


class PageSizeOuterError(APIException):
    msg = "页记录数超出范围(1-100000)"
    error_code = 11004007


class OrderStatusError(APIException):
    msg = "单据状态不合法"
    error_code = 11004008


class PackIdUpdateStatusError(APIException):
    msg = "通过打包号更新任务状态失败"
    error_code = 11004009


class PackIdNotEmpty(APIException):
    msg = "打包号不能为空"
    error_code = 11004010


class TaskStatusNotEmpty(APIException):
    msg = "任务状态不能为空"
    error_code = 11004011


class GtoPlanPreviewFailed(APIException):
    msg = "生成流通计划预览数据失败"
    error_code = 11004012


class GtoPlanPreviewTimeout(APIException):
    msg = "生成流通计划预览数据超时"
    error_code = 11004013


class GtoPlanGetPreviewFailed(APIException):
    msg = "获取流通计划预览数据失败.其余用户已修改流通计划数据"
    error_code = 11004014


class PydanticValidationError(APIException):
    msg = "请求参数错误"
    error_code = 11004015


class FeatureTemplatePermissionError(APIException):
    msg = "无操作该模板的权限"
    error_code = 11005001


class FeatureTemplateContentError(APIException):
    msg = ""
    error_code = 11005002


class BuiltinCallNotRegisteredError(APIException):
    msg = ""
    error_code = 11005003


class ScheduleStatusException(APIException):
    msg = _("存在正在运行中的调度，请稍等后重新计算！")
    error_code = 11005004


class NoReRunOrgs(APIException):
    msg = _("未选定重跑组织")
    error_code = 11005005


class NoReRunInfo(APIException):
    msg = _("当前决策日无对应组织的流通方案信息")
    error_code = 11005006


class ClearTaskException(APIException):
    msg = ""
    error_code = 11005007


class GetScheduleInfoFailed(APIException):
    msg = _("获取调度运行信息失败！")
    error_code = 11005008


class CurrentNoScheduleInfo(APIException):
    msg = _("当前无调度运行信息")
    error_code = 11005009


class ReRunFailed(APIException):
    msg = _("重运行流通方案失败！")
    error_code = 11005010
