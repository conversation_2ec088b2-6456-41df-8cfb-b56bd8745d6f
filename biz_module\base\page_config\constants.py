#!/usr/bin/env python
# encoding: utf-8

"""
页面配置 -- 常量
@author: shaco
@file: models
@time: 2019/08/16 13:16
@desc:
@Reference:
"""
import numpy as np

from enum import Enum
from typing import Dict, Tuple


class ModuleEnum(Enum):
    """
    模块枚举
    """
    # 补调模块
    REPLENISH_ALLOT = 'replenish_allot'
    # 铺货模块
    DISTRIBUTE = 'distribute'
    # 备货模块
    STOCK_UP = 'stock_up'

    @classmethod
    def _names(cls) -> Tuple:
        names = (
            ('replenish_allot', '补货调拨模块'),
            ('distribute', '铺货模块'),
            ('stock_up', '备货模块'),
        )
        return names

    @classmethod
    def get_cn_name(cls, key):
        names = dict(cls._names())
        return names.get(key, None)

    @classmethod
    def get_names(cls) -> Tuple:
        return cls._names()


class PageEnum(Enum):
    """
    模块枚举
    """
    # 补调决策页面
    RA_DECISION = 'ra_decision'
    # 门店概览页面
    STORE_SUMMARY = 'store_summary'
    # 铺货商品明细页面
    PRODUCT_DETAIL = 'product_detail'
    # 决策池目标商品配置页面
    DECISION_POOL_ORG_PRODUCT_CONFIG = 'decision_pool'
    # 备货页面
    STOCK_UP = 'stock_up'

    @classmethod
    def _names(cls) -> Tuple:
        names = (
            ('ra_decision', '补调决策', 4),
            ('store_summary', '门店概览', 5),
            ('product_detail', '商品明细', 205),
            (cls.DECISION_POOL_ORG_PRODUCT_CONFIG.value, '决策池目标商品配置', 206),
            (cls.STOCK_UP.value, '备货粒度配置', 207)
        )
        return names

    @classmethod
    def get_cn_name(cls, key):
        names = np.array(cls._names())[:,:2].tolist()
        names = dict(names)
        return names.get(key, None)

    @classmethod
    def get_names(cls) -> Tuple:
        return cls._names()


class SetTypeEnum(Enum):
    """
    设置类型
    """
    # 系统默认
    SYS_DEFAULT = 'sys_default'
    # 自定义
    CUSTOM = 'custom'

    @classmethod
    def _names(cls) -> Tuple:
        names = (
            ('sys_default', '系统默认配置',),
            ('custom', '自定义配置'),
        )
        return names

    @classmethod
    def get_cn_name(cls, key):
        names = dict(cls._names())
        return names.get(key, None)

    @classmethod
    def get_names(cls) -> Tuple:
        return cls._names()


class ConfigType(Enum):
    """
    配置类型
    """
    # 普通级别
    COMMON = 'common'
    # 租户级别
    TENANT = 'tenant'
    # 产品级别
    PRODUCT = 'product'


ROLE_PAGE_ID = 20400000
