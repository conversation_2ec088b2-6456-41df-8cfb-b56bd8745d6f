"""
业务对象筛选器中的特征
应用于款/店/款店/规则参数
"""
import logging
from typing import List

from fastboot.utils.nest import egg
from linezone_commonserver.services.feature.service import FeatureService
from linezone_commonserver.services.feature.typedef import FeatureEntity
from linezone_commonserver.services.meta_data.service import MetaDataService
from linezone_commonserver.utils.tools import object_to_object_list

from biz import BaseService
from biz_module.common.feature_mapping.constants import ObjectTypeEnum, EMPTY_LIST, CascadedLevelEnum
from biz_module.common.feature_mapping.mapping_rule.condition_item import create_dimension_overlap, create_dimension_eq
from biz_module.common.feature_mapping.mapping_rule.loader import RuleLoader
from biz_module.common.feature_mapping.mapping_rule.typedef import (
    BizObjectMappingReq,
    DimensionMappingReq,
    DynamicObjectMappingReq,
    LoaderListFeatureReq,
)

logger = logging.getLogger(__name__)


@egg
class FeatureMappingLoaderErr(Exception):
    pass


class FeatureMappingService(BaseService):
    """
    特征映射服务
    """

    feature_service = FeatureService()
    meta_data_service = MetaDataService()

    @classmethod
    def from_biz_object(cls, req: BizObjectMappingReq) -> List[FeatureEntity]:
        """
        业务对象映射

        匹配规则:
            1. 优先规则类型匹配，存在则返回
            2. 不存在规则，会fallback到维度级联匹配
        Parameters
        ----------
        req
           可能存在的动态扩展条件：目前业务对象暂无特殊扩展条件

        Returns
        -------

        """
        if not req.biz_object_code:
            logger.warning("通过业务对象获取特征映射失败，业务对象代码为空")
            raise FeatureMappingLoaderErr("业务对象代码为空")

        biz_object = cls.meta_data_service.get_biz_object_by_code(req.biz_object_code)
        if not biz_object:
            return []

        list_feature_req = LoaderListFeatureReq(
            is_load_dimension=req.is_load_dimension,
            freestyle_rule=req.condition_items,
            is_need_filter=req.is_load_feature_filter,
        )
        rule_loader = RuleLoader.create_from_object_type(ObjectTypeEnum.BIZ_OBJECT, req.biz_object_code)
        if rule_loader.has_rule_configured():
            return rule_loader.load_features(list_feature_req)

        dimension_codes = cls.meta_data_service.list_biz_object_meta_dimension_with_parent_codes(req.biz_object_code)

        if not dimension_codes:
            return []

        loader = RuleLoader.create_from_rule_definition(create_dimension_overlap(dimension_codes))
        return loader.load_features(list_feature_req)

    @classmethod
    def from_dimension_codes(cls, req: DimensionMappingReq) -> List[FeatureEntity]:
        """
        维度映射
        Parameters
        ----------
        req
            可能存在的动态扩展条件：
                根据特征code排除特征
                计算类型的限制
        Returns
        -------

        """
        if not req.dimension_codes:
            return EMPTY_LIST

        condition_items = []
        if req.is_cascaded:
            dimension_codes = req.dimension_codes
            if req.cascaded_level is CascadedLevelEnum.CURRENT_AND_PARENT:
                cascaded_dimension_entities = cls.meta_data_service.list_cascaded_dimensions(req.dimension_codes)
                dimension_codes = [dimension.dimension_code for dimension in cascaded_dimension_entities]
            condition_items.append(create_dimension_overlap(dimension_codes))
        else:
            condition_items.append(create_dimension_eq(req.dimension_codes))

        if req.condition_items:
            condition_items.extend(object_to_object_list(req.condition_items))

        list_feature_req = LoaderListFeatureReq(
            is_load_dimension=req.is_load_dimension, freestyle_rule=condition_items, is_need_filter=req.is_load_feature_filter
        )
        return RuleLoader().load_features(list_feature_req)

    @classmethod
    def from_dynamic_object(cls, req: DynamicObjectMappingReq) -> List[FeatureEntity]:
        """
        动态对象映射

        依赖规则解析器
        Parameters
        ----------
        req
            支持动态条件(可选)
        Returns
        -------

        """
        if not req.object_code:
            return EMPTY_LIST

        is_single_object = isinstance(req.object_code, str)
        if is_single_object:
            rule_loader = RuleLoader.create_from_object_type(ObjectTypeEnum.DYNAMIC_OBJECT, req.object_code)
        else:
            rule_loader = RuleLoader.create_from_different_object(ObjectTypeEnum.DYNAMIC_OBJECT, req.object_code)

        if not rule_loader.has_rule_configured():
            return EMPTY_LIST

        list_feature_req = LoaderListFeatureReq(
            is_load_dimension=req.is_load_dimension,
            freestyle_rule=req.condition_items,
            is_need_filter=req.is_load_feature_filter,
        )
        return rule_loader.load_features(list_feature_req)
