<?xml version="1.0"?>
<mapper namespace="biz_view">
    <!-- 查询 -->
    <select id="find_by_alias_name">
        select id,
        view_code,
        alias_name,
        biz_view_combine,
        join_relation,
        relation_sql,
        is_builtin
        from biz.base_biz_view
        where alias_name = #{alias_name}
        <if test="exclude_biz_view_id">
            and id != #{exclude_biz_view_id}
        </if>
        and is_deleted = 0
    </select>
    <select id="list_biz_view">
        select id,
        view_code,
        alias_name,
        biz_view_combine,
        join_relation,
        relation_sql,
        is_builtin
        from biz.base_biz_view
        where is_deleted = 0
        order by id;
    </select>

    <select id="find_biz_view_by_code">
        select id,
        view_code,
        alias_name,
        biz_view_combine,
        join_relation,
        relation_sql,
        is_builtin
        from biz.base_biz_view
        where view_code = #{view_code}
        and is_deleted = 0
    </select>

    <select id="find_biz_view_by_id">
        select id, view_code, alias_name, biz_view_combine, join_relation, relation_sql from biz.base_biz_view
        where id = #{id}
        and is_deleted = 0
    </select>

    <insert id="save_biz_view">
        insert into biz.base_biz_view(
            view_code,
            alias_name,
            biz_view_combine,
            join_relation,
            relation_sql,
            main_object_code,
            creator,
            create_time
        )
        values(
            #{view_code},
            #{alias_name},
            #{biz_view_combine},
            #{join_relation},
            #{relation_sql},
            #{main_object_code},
            #{creator},
            now()
        )RETURNING id
    </insert>

    <update id="update_biz_view_by_id">
        UPDATE biz.base_biz_view
        SET
        alias_name = #{alias_name},
        join_relation = #{join_relation},
        relation_sql = #{relation_sql},
        main_object_code = #{main_object_code},
        modifier = #{modifier},
        modify_time = now()
        where id = #{id}
        RETURNING id
    </update>
</mapper>
