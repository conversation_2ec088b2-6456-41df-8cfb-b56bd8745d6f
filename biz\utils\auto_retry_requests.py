#!/usr/bin/env python
# _*_ coding: utf-8 _*_

"""
@author: yang<PERSON><PERSON><PERSON>
@license: Copyright 2017-2020, LineZoneData.
@contact: yang<PERSON><PERSON><PERSON>@linezonedata.com
@software: pycharm
@time: 2019/11/8 10:36
@desc: 
"""
from functools import wraps
from inspect import signature

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from biz.common.biz_context.context import ctx

import logging


logger = logging.getLogger(__name__)


def requests_retry_session(retries=2,
                           backoff_factor=2,
                           status_forcelist=(500, 502, 503, 504),
                           session: requests.Session = None):
    """
    :param retries: 重试次数
    :param backoff_factor: 在重试期间用于计算延时的 backoff 因子。每次请求间的sleep时间为:

        {backoff factor} * (2 ^ ({number of total retries} - 1))

    :param status_forcelist: 需要重试的http状态码
    :param session:
    :return:
    """
    session = session or requests.Session()
    retry = Retry(
        total=retries,
        read=retries,
        connect=retries,
        backoff_factor=backoff_factor,
        status_forcelist=status_forcelist,
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session


def logged_request(func):
    """装饰requests中各请求方法的装饰器，在请求前输出请求参数到日志"""
    sig = signature(func)
    method = func.__name__.upper()

    @wraps(func)
    def wrapper(*args, **kwargs):
        # 输出日志，包含请求参数和结果
        bound = sig.bind(*args, **kwargs)

        infos = []
        for name, value in bound.arguments.items():
            if name == 'self':
                continue
            elif name == 'url':
                infos.append("{method}: {url}".format(method=method, url=value))
            else:
                infos.append("{arg} = {val}".format(arg=name, val=repr(value)))
        logger.info('\n'.join(infos))

        result = func(*args, **kwargs)
        return result

    return wrapper


def add_variable_header(func):
    """装饰requests中各请求方法的装饰器，在请求前加入可变header到request"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        req_obj = args[0]
        req_obj._requests.headers['X-REQUEST-ID'] = ctx.request_id
        return func(*args, **kwargs)
    return wrapper


class AutoRetryRequests:
    def __init__(self, retries=2,
                 backoff_factor=2,
                 status_forcelist=(500, 502, 503, 504),
                 session: requests.Session = None):
        self._requests = requests_retry_session(retries=retries,
                                                backoff_factor=backoff_factor,
                                                status_forcelist=status_forcelist,
                                                session=session)
        # 设置请求默认的 headers
        self._requests.headers.update({'Accept': 'application/json, text/plain, */*'})

    @add_variable_header
    @logged_request
    def get(self, url, params=None, **kwargs):
        if 'headers' not in kwargs:
            kwargs['headers'] = {'Accept': 'application/json, text/plain, */*'}
        return self._requests.get(url, params=params, **kwargs)

    @add_variable_header
    @logged_request
    def post(self, url, data=None, json=None, **kwargs):
        return self._requests.post(url, data=data, json=json, **kwargs)

    @add_variable_header
    @logged_request
    def put(self, url, data=None, **kwargs):
        return self._requests.put(url, data=data, **kwargs)

    @add_variable_header
    @logged_request
    def delete(self, url, **kwargs):
        return self._requests.delete(url, **kwargs)


requests = AutoRetryRequests()


if __name__ == "__main__":
    import sys
    sh = logging.StreamHandler(stream=sys.stdout)  # 往屏幕上输出
    sh.setLevel(logging.DEBUG)
    sh.setFormatter(logging.Formatter('%(levelname)s: %(asctime)s: %(filename)s:%(lineno)d * %(thread)d %(message)s'))
    logger.addHandler(sh)

    r = requests.get("http://www.baidu.com", params={'x': 1})
    print(r.text)
