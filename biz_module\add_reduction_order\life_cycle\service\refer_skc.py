import copy
import json
import logging
from collections import defaultdict
from typing import List

from fastboot.utils.nest import egg
from flask_login import current_user
from ourbatis import transaction_manager

from biz import BaseService
from biz.api_exception import ParameterException
from biz.utils.image_utils import get_images_name
from biz_module.add_reduction_order.life_cycle.constants import ReferSkcListTitle, ReferSkcReferSkcListTitle
from biz_module.add_reduction_order.life_cycle.dao import ReferSkcDao
from biz_module.add_reduction_order.multiple_product_preview.service.page_config import PageComponentConfigService
from biz_module.add_reduction_order.life_cycle import typedef as td


logger = logging.getLogger(__name__)


@egg
class ReferSkcService(BaseService):
    dao: ReferSkcDao

    page_config_service: PageComponentConfigService

    org_filter = ["channel_type", "org_sk"]
    skc_filter = ["big_class","product_range","product_year_quarter","product_belong_name"
        ,"ps_mid_class","sale_mid_tiny_class","gender","product_name", "skc_sk"]

    @classmethod
    def column_order_sql_build(cls, column_orders: List[dict], filed_table_alis_dict):
        order_sqls = []
        error_msg = []
        for column_order in column_orders:
            field_column = filed_table_alis_dict.get(column_order["column_name"])
            if field_column.get("is_calculate"):
                error_msg.append(f'指标:{field_column["cn_name"]}正在计算中，不支持排序')
                continue
            table_alias = field_column.get("table_alias")
            value_type = field_column.get("value_type")
            column_order["value_type"] = value_type
            data_type = field_column.get("data_type")
            column_order["data_type"] = data_type

            if table_alias:
                alias_name = table_alias + "."

            # if column_order["order"].upper() == "DESC":
            #     order_type = " nulls last"
            # else:
            #     order_type = " nulls first"

            order_type = " nulls last"
            column_order["order_type"] = order_type

            column_order["alias_name"] = alias_name
            if data_type in ("numeric", "int"):
                order_sql = (
                    "{alias_name}{column_name}::{data_type} {order} {order_type}"
                )
            elif data_type == "text":
                order_sql = (
                    "convert_to({alias_name}{column_name}, 'GBK') {order} {order_type}"
                )
            else:
                order_sql = "{alias_name}{column_name} {order} {order_type}"
            order_sqls.append(order_sql.format(**column_order))
        if error_msg:
            raise ParameterException("; ".join(error_msg))
        return ",".join(order_sqls)

    @staticmethod
    def add_picture(item: dict, skc_code=None, _skc_code=None):
        """添加图片"""
        if skc_code or item.get("skc_code"):
            item["image_url"] = get_images_name(skc_code) if skc_code else get_images_name(item["skc_code"])
        else:
            item["image_url"] = get_images_name(_skc_code) if _skc_code else get_images_name(item["_skc_code"])


    def query_refer_skc_filter(self, query_info: td.ReferSkcFilterRequest):

        filter_key = query_info.filter_key
        data = []
        # 如果组织筛选 则需要过滤时间
        if filter_key in self.org_filter:
            index = self.org_filter.index(filter_key)
            for i in self.org_filter[index+1:]:
                setattr(query_info, i, None)
            from biz_module.base.authorization.auth_region.service import auth_region_service
            org_sk_list = auth_region_service.list_authorized_region_org_sk()
            query_info.org_sk_list = org_sk_list
            data = self.dao.query_refer_skc_org_filter(query_info)

        elif filter_key in self.skc_filter:
            index = self.skc_filter.index(filter_key)
            # 将index更大的值置为None
            for i in self.skc_filter[index+1:]:
                setattr(query_info, i, None)
            query_info.is_admin = current_user.is_admin
            query_info.user_id = current_user.user_id
            data = self.dao.query_refer_skc_filter(query_info)
        return data
    #
    #
    def query_refer_skc_list(self, query_info: td.ReferSkcListRequest):

        if not query_info.channel_type or not query_info.org_sk:
            raise ParameterException('渠道、大区必填')
        page_size = query_info.page_size
        page_no = query_info.page_no
        query_info.page_offset = (page_no - 1) * page_size
        query_info.is_admin = current_user.is_admin
        query_info.user_id = current_user.user_id
        data = self.dao.query_refer_skc_kpi(query_info)
        title = copy.deepcopy(ReferSkcListTitle)
        if not data:
            return {"data": data, "title": title, "page_size":  page_size, "page_no": page_no, "total_count": 0}
        total_count = data[0].get('total_count')
        query_info.refer_skc_code = list({i.get('refer_skc_code') for i in data})
        query_info.current_quarter_skc_sk = list({i.get('current_quarter_skc_sk') for i in data})
        week_data = self.dao.query_refer_skc_week_kpi(query_info)

        # 处理周数
        week_info = defaultdict(list)
        weeks = set()
        for i in week_data:
            skc_code = i.get('skc_code')
            refer_skc_code = i.get('refer_skc_code')
            listing_time =  i.get('listing_time')
            weeks.add(listing_time)
            week_info[f"{skc_code}@@{refer_skc_code}"].append(i)
        weeks = list(weeks)
        weeks.sort()
        for i in title:
            code = i.get("code")
            children = i.get("children")
            if children is not None:
                for index, week in enumerate(weeks):
                    if index == 0:
                        children.append({"name": f"W{week}", "display": True, "code": f"{code}_{week}",
                                         "value_type": "number",
                                         "formatter": "0,000" if code == 'week_sale_qty' else "0.0%",
                                         "fold": False, "hidden": False, "width": 100} )
                    else:
                        children.append({"name": f"W{week}", "display": True, "code": f"{code}_{week}",
                                         "value_type": "number", "formatter": "0,000" if code == 'week_sale_qty' else "0.0%",
                                         "fold": True, "hidden": True, "width": 100})
        for i in data:
            skc_code = i.get("skc_code")
            refer_skc_code  = i.get("refer_skc_code")
            week_data = week_info.get(f"{skc_code}@@{refer_skc_code}", [])
            for j in week_data:
                listing_time = j.get('listing_time')
                week_sale_qty = j.get('week_sale_qty')
                week_sale_out = j.get('week_sale_out')
                week_discount = j.get('week_discount')
                i[f"week_sale_qty_{listing_time}"] = week_sale_qty
                i[f"week_sale_out_{listing_time}"] = week_sale_out
                i[f"week_discount_{listing_time}"] = week_discount
        return {"data": data, "title": title, "page_size":  page_size, "page_no": page_no, "total_count": total_count}


    def query_refer_skc_refer_skc_filter(self, query_info: td.ReferSkcFilterRequest):

        filter_key = query_info.filter_key
        index = self.skc_filter.index(filter_key)
        # 将index更大的值置为None
        for i in self.skc_filter[index+1:]:
            setattr(query_info, i, None)
        data = self.dao.query_refer_skc_refer_skc_filter(query_info)
        return data

    def query_refer_skc_refer_skc_list(self, query_info: td.ReferSkcReferSkcListRequest):

        page_size = query_info.page_size
        page_no = query_info.page_no
        query_info.page_offset = (page_no - 1) * page_size
        data = self.dao.query_refer_skc_refer_skc_list(query_info)
        return {"data": data, "title": ReferSkcReferSkcListTitle}

    def add_refer_skc(self, req: td.AddOrRemoveReferSkcRequest):

        req.user_id = current_user.user_id
        # 校验是否存在
        if self.dao.is_exist_refer_skc(req):
            raise ParameterException("该关系已存在")
        self.dao.add_refer_skc(req)

        return {"message": "添加成功"}

    def remove_refer_skc(self, req: td.AddOrRemoveReferSkcRequest):

        req.user_id = current_user.user_id
        # 校验是否存在
        if not self.dao.is_exist_refer_skc(req):
            raise ParameterException("该关系已不存在")
        self.dao.remove_refer_skc(req)

        return {"message": "添加成功"}

    def save_refer_skc(self, req: td.SaveReferSkcRequest):

        req.user_id = current_user.user_id
        add_refer_skc_sk = req.add_refer_skc_sk
        add_refer_skc_sk = [{"channel_type": req.channel_type, "org_sk": req.org_sk, "current_quarter_skc_sk": req.current_quarter_skc_sk, "source": "人工添加", "refer_skc_sk": i, "user_id": current_user.user_id} for i in add_refer_skc_sk]
        add_refer_skc_sk = json.dumps(add_refer_skc_sk)
        delete_refer_skc_sk = req.delete_refer_skc_sk
        with transaction_manager():
            if add_refer_skc_sk:
                self.dao.add_refer_skc_list(add_refer_skc_sk)
            if delete_refer_skc_sk:
                self.dao.remove_refer_skc_list(req)

        return {"message": "保存成功"}

refer_skc_service = ReferSkcService()

