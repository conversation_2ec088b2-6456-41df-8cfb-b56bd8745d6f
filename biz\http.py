# -*- coding: utf-8 -*-
import collections
import functools
import inspect
import logging
import os

from flask import Blueprint

from biz.modules import load_information_from_description_file

_logger = logging.getLogger(__name__)


def route(route=None, **kw):
    """Decorator marking the decorated method as being a handler for
    requests. The method must be part of a subclass of ``Controller``.

    :param route: string or array. The route part that will determine which
                  http requests will match the decorated method. Can be a
                  single string or an array of strings. See werkzeug's routing
                  documentation for the format of route expression (
                  http://werkzeug.pocoo.org/docs/routing/ ).
    :param type: The type of request, can be ``'http'`` or ``'json'``.
    :param auth: The type of authentication method, can on of the following:

                 * ``user``: The user must be authenticated and the current request
                   will perform using the rights of the user.
                 * ``public``: The user may or may not be authenticated. If she isn't,
                   the current request will perform using the shared Public user.
                 * ``none``: The method is always active, even if there is no
                   database. Mainly used by the framework and authentication
                   modules. There request code will not have any facilities to access
                   the database nor have any configuration indicating the current
                   database nor the current user.
    :param methods: A sequence of http methods this route applies to. If not
                    specified, all methods are allowed.
    :param cors: The Access-Control-Allow-Origin cors directive value.
    :param bool csrf: Whether CSRF protection should be enabled for the route.

                      Defaults to ``True``. See :ref:`CSRF Protection
                      <csrf>` for more.

    .. _csrf:

    .. admonition:: CSRF Protection
        :class: alert-warning

        .. versionadded:: 9.0

        Odoo implements token-based `CSRF protection
        <https://en.wikipedia.org/wiki/CSRF>`_.

        CSRF protection is enabled by default and applies to *UNSAFE*
        HTTP methods as defined by :rfc:`7231` (all methods other than
        ``GET``, ``HEAD``, ``TRACE`` and ``OPTIONS``).

        CSRF protection is implemented by checking requests using
        unsafe methods for a value called ``csrf_token`` as part of
        the request's form data. That value is removed from the form
        as part of the validation and does not have to be taken in
        account by your own form processing.

        When adding a new controller for an unsafe method (mostly POST
        for e.g. forms):

        * if the form is generated in Python, a csrf token is
          available via :meth:`request.csrf_token()
          <odoo.http.WebRequest.csrf_token`, the
          :data:`~odoo.http.request` object is available by default
          in QWeb (python) templates, it may have to be added
          explicitly if you are not using QWeb.

        * if the form is generated in Javascript, the CSRF token is
          added by default to the QWeb (js) rendering context as
          ``csrf_token`` and is otherwise available as ``csrf_token``
          on the ``web.core`` module:

          .. code-block:: javascript

              require('web.core').csrf_token

        * if the endpoint can be called by external parties (not from
          Odoo) as e.g. it is a REST API or a `webhook
          <https://en.wikipedia.org/wiki/Webhook>`_, CSRF protection
          must be disabled on the endpoint. If possible, you may want
          to implement other methods of request validation (to ensure
          it is not called by an unrelated third-party).

    """
    routing = kw.copy()
    routing['type'] = "json"

    # assert 'type' not in routing or routing['type'] in ("http", "json")

    def decorator(f):
        if route:
            if isinstance(route, list):
                routes = route
            else:
                routes = [route]
            routing['routes'] = routes

        @functools.wraps(f)
        def response_wrap(*args, **kw):
            args = [f.original_cls] + list(args)
            response = f(*args, **kw)

            return response

        response_wrap.routing = routing
        response_wrap.original_func = f
        return response_wrap

    return decorator


# ----------------------------------------------------------
# Controller and route registration
# ----------------------------------------------------------
addons_manifest = {}
# controllers_per_module = collections.defaultdict(list)
controllers_per_blueprint = collections.defaultdict(list)


class ControllerType(type):
    def __init__(cls, name, bases, attrs):
        super(ControllerType, cls).__init__(name, bases, attrs)

        bp = Blueprint(cls.__module__.rsplit('.', 2)[1], cls.__module__)
        # flag old-style methods with req as first argument
        for k, v in attrs.items():
            if inspect.isfunction(v) and hasattr(v, 'original_func'):
                # Set routing type on original functions
                routing_type = v.routing.get('type')
                parent = [claz for claz in bases if isinstance(claz, ControllerType) and hasattr(claz, k)]
                parent_routing_type = getattr(parent[0],
                                              k).original_func.routing_type if parent else routing_type or 'http'
                if routing_type is not None and routing_type is not parent_routing_type:
                    routing_type = parent_routing_type
                    _logger.warn("Subclass re-defines <function %s.%s.%s> with different type than original."
                                 " Will use original type: %r" % (cls.__module__, cls.__name__, k, parent_routing_type))
                v.original_func.routing_type = routing_type or parent_routing_type

                spec = inspect.getfullargspec(v.original_func)
                first_arg = spec.args[1] if len(spec.args) >= 2 else None
                if first_arg in ["req", "request"]:
                    v._first_arg_is_req = True

                for _rule in v.routing["routes"]:
                    # bp.add_url_rule(_rule, view_func=v.original_func, methods=v.routing.get("methods"))
                    v.original_func.original_cls = cls
                    bp.add_url_rule(_rule, view_func=v, methods=v.routing.get("methods"))

        # store the controller in the controllers list
        name_class = ("%s.%s" % (cls.__module__, cls.__name__), cls)
        class_path = name_class[0].split(".")
        if not class_path[:2] == ["biz", "biz_module"]:
            module = ""
        else:
            # we want to know all modules that have controllers
            module = class_path[2]
        # but we only store controllers directly inheriting from Controller
        if not "Controller" in globals() or not Controller in bases:
            return
        # controllers_per_module[module].append(name_class)
        info = load_information_from_description_file(None, os.path.dirname(inspect.getfile(cls)))
        if info and info.get('is_display', False):
            controllers_per_blueprint[bp.name] = [bp]
            # blueprint_module_info[bp.name] = None
        if bp.name == 'repeat_order':

            before_decorators = getattr(cls,'before_decorators',())
            for func in before_decorators:
                bp.before_request(func)
            
            after_decorators = getattr(cls,'after_decorators',())
            for func in after_decorators:
                bp.after_request(func)


            # bp.before_request( getattr(cls,decorators))
     

Controller = ControllerType('Controller', (object,), {})
