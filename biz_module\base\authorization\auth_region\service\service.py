from fastboot.utils.nest import egg
from linezone_commonserver.services.auth.org_service import CommonAuthOrgService
from linezone_commonserver.services.auth.typedef import CommonUserReq
from pandas import DataFrame

from biz.api_exception import ParameterException
from biz.base import BaseService
from typing import List, Any, Optional, Union, Dict
from flask_login import current_user
from biz.utils import tools
from biz_module.base.authorization.auth_region.constants import RegionQueryWayEnum, OrgFlagEnum
from biz_module.base.authorization.auth_region.dao import AuthRegionDao
from biz_module.base.authorization.auth_region.typedef import (
    UpsertAuthRegionRequest,
    RemoveAuthRegionBody,
    RegionOperatingSK,
    AuthRegionTreeRequest, OrgEgg,
)
from biz_module.base.authorization.base.tools import UserAuthCache
from biz_module.base.authorization.base.typedef import CheckedEnum
from biz_module.base.authorization.auth_region.typedef import UpsertAuthRegionRequest, RemoveAuthRegionBody, RegionUnitLevel


@egg
class AuthRegionService(BaseService):
    common_auth_org_service: CommonAuthOrgService

    def __init__(self):
        self.dao = AuthRegionDao()
        self.user_auth_cache = UserAuthCache()

    def list_auth_region_by_id(self, auth_record_id: int):
        """
        @param auth_record_id:
        @return:
        """
        auth_regions = self.dao.get_auth_regions_by_auth_record_id(auth_record_id)
        # auth_regions = self.dao.list_record_auth_region(auth_record_ids=[auth_record_id])
        return auth_regions or []

    def list_auth_region_by_ids(self, auth_record_ids: List[int]):
        auth_region = self.dao.list_record_auth_region(auth_record_ids)
        return auth_region or []

    def get_auth_region_dict(self, auth_record_ids: List[int]):
        result = {}
        if not auth_record_ids:
            return result

        auth_regions = self.list_auth_region_by_ids(auth_record_ids)
        if not auth_regions:
            return result

        auth_region_df = DataFrame([region.__dict__ for region in auth_regions])
        auth_region_df["dict"] = auth_region_df[
            ["org_sk", "org_name", "operating_unit_sk", "operating_unit_name", "org_type", "org_type_name"]
        ].to_dict("records")
        regions = auth_region_df.groupby(["auth_record_id"])["dict"].apply(list).to_dict()

        for key, region_list in regions.items():
            record_dict = {"auth_regions_b": [], "auth_regions_f": []}
            one_record_region_df = DataFrame.from_records(region_list)
            one_record_region_df["dict"] = one_record_region_df[
                ["org_sk", "org_name", "operating_unit_sk", "operating_unit_name"]
            ].to_dict("records")
            org_type_groups = one_record_region_df.groupby(["org_type", "org_type_name"])["dict"].apply(list).to_dict()
            # 每个授权记录授权的组织层级
            for key_tuple, operating_unit in org_type_groups.items():
                org_type, org_type_name = key_tuple

                operating_unit_names = []
                operating_unit_df = DataFrame.from_records(operating_unit)
                operating_unit_groups = (
                    operating_unit_df.groupby(["operating_unit_sk", "operating_unit_name"])[["org_sk", "org_name"]]
                    .agg(lambda x: list(x))
                    .to_dict("index")
                )

                for unit_tuple, orgs in operating_unit_groups.items():
                    operating_unit_sk, operating_unit_name = unit_tuple
                    if operating_unit_sk == orgs.get("org_sk")[0]:
                        org_name_list = "全部"
                    else:
                        org_name_list = "<br/>".join(orgs.get("org_name"))
                    full_operating_unit_name = f"{operating_unit_name}:<br/>{org_name_list}"
                    operating_unit_names.append(full_operating_unit_name)
                    record_dict["auth_regions_b"].extend(
                        [
                            {"operating_unit_sk": operating_unit_sk, "org_sk": org_sk, "org_type": org_type}
                            for org_sk in orgs.get("org_sk")
                        ]
                    )

                org_type_name = f"{org_type_name}:<br/>"
                operating_unit_str = "<br/>".join(operating_unit_names)
                # 前端直接渲染的内容
                record_dict["auth_regions_f"].append(org_type_name + operating_unit_str)
            result[key] = record_dict
        return result

    def get_operating_unit_list(self,):
        pass

    def get_auth_record_region_tree(self, request_params: AuthRegionTreeRequest):
        """
        获取权限组合的组织树
        @param request_params:AuthRegionTreeRequest
        @return:
        """
        all_region_df = self.dao.list_auth_region_tree(request_params.auth_record_id, request_params.org_type)
        checked_list = all_region_df[all_region_df["operation_type"] == CheckedEnum.CHECKED]["id"].to_list()
        tree, _ = tools.get_tree(all_region_df, "tree_unique_sk", "parent_org_sk")
        return {"tree": tree, "checked_list": checked_list}

    def get_auth_record_region_tree_bak(self, org_type, auth_record_id=None):
        """
        可能删除
        根据权限组合org_type获取指定type的树
        选中某个节点代表
        """
        if not org_type:
            raise ParameterException("请选择组织类型")
        # 获取已授权region
        auth_regions = None
        if auth_record_id:
            auth_regions = self.dao.list_auth_record_region_by_org_type(auth_record_id, org_type)
        # 获取所有组织层级
        all_region_df = self.dao.list_region_by_org_type(org_type)
        if auth_regions:
            for auth_region in auth_regions:
                # 查找当前节点
                current_df = all_region_df.loc[
                    (all_region_df["org_sk"] == auth_region.org_sk)
                    & (all_region_df["operating_unit_sk"] == auth_region.operating_unit_sk)
                ]
                if not current_df.empty:
                    all_region_df.loc[
                        (all_region_df["org_sk"] == auth_region.org_sk)
                        & (all_region_df["operating_unit_sk"] == auth_region.operating_unit_sk),
                        "operating_type",
                    ] = 2
                    self.find_and_tag_children_recursive(all_region_df, current_df)
                    self.find_and_tag_parent_recursive(all_region_df, current_df)

        tree, _ = tools.get_tree(all_region_df, "org_sk", "parent_org_sk")
        return {"tree": tree}

    def find_and_tag_children_recursive(self, all_region_df, child_df):
        """
        递归标记子
        """
        if child_df.empty:
            return

        for index, row in child_df.iterrows():
            all_region_df.loc[
                (all_region_df["parent_org_sk"] == row["org_sk"])
                & (all_region_df["operating_unit_sk"] == row["operating_unit_sk"]),
                "operating_type",
            ] = 2
            # 到门店仓库不用找了
            if row["org_flag"] == 0:
                children_df = all_region_df.loc[
                    (all_region_df["parent_org_sk"] == row["org_sk"])
                    & (all_region_df["operating_unit_sk"] == row["operating_unit_sk"])
                ]
                self.find_and_tag_children_recursive(all_region_df, children_df)
        return

    def find_and_tag_parent_recursive(self, all_region_df, parent_df):
        """
        递归标记父
        """
        if parent_df.empty:
            return
        # 标记
        for _, row in parent_df.iterrows():
            all_region_df.loc[
                (all_region_df["org_sk"] == row["parent_org_sk"])
                & (all_region_df["operating_unit_sk"] == row["operating_unit_sk"]),
                "operating_type",
            ] = 1
            tem_parent_df = all_region_df.loc[
                (all_region_df["org_sk"] == row["parent_org_sk"])
                & (all_region_df["operating_unit_sk"] == row["operating_unit_sk"])
            ]
            self.find_and_tag_parent_recursive(all_region_df, tem_parent_df)
        return

    def list_region_tree_type(self):
        """
        产品标准组织权限树
        商品管理权限 和 零售管理权限
        """
        org_tree = self.dao.list_region_tree_type()
        return {"org_types": org_tree or []}

    def get_authorized_region_tree(self, org_type, way=None) -> dict:
        """
        获取登录用户组织权限树
        @param way: None:整个授权的树, up:授权节点向上的树, down:授权节点向下的树(排除店仓)
        @param org_type: 1：集团公司，2：商品管理组织，3：零售管理组织
        @return:字典树
        """
        if way == "all":
            way = None

        tree_df = self.dao.get_authorized_region_list(org_type, way)
        if not tree_df.empty and way == "up":
            if not current_user.is_admin:
                auth_record_id = self.user_auth_cache.get()
                if not auth_record_id:
                    raise ParameterException("找不到当前用户的权限组合，请重新登录~")
                org_sks = tree_df["org_sk"].to_list()
                operating_unit_sks = tree_df["operating_unit_sk"].to_list()
                down_regions = self.dao.get_auth_record_down_region_from_view(auth_record_id, org_sks, operating_unit_sks)
                if down_regions:
                    for region in down_regions:
                        tree_df.loc[
                            (tree_df["org_sk"] == region.org_sk) & (tree_df["operating_unit_sk"] == region.operating_unit_sk),
                            "disabled",
                        ] = "down"

        tree_df.replace(
            {"disabled": {"up": 1, "down|up": 0, "up|down": 0, "down": 0, "up|up": 1, "down|down": 0}}, inplace=True
        )
        tree, _ = tools.get_tree(tree_df, "org_sk", "parent_org_sk")
        return {"tree": tree}

    def get_authorized_region_tree_v2(self, org_type, way=None) -> dict:
        """
        获取登录用户组织权限树
        @param way: None:整个授权的树, up:授权节点向上的树, down:授权节点向下的树(排除店仓)
        @param org_type: 1：集团公司，2：商品管理组织，3：零售管理组织
        @return:字典树
        """
        if way == "all":
            way = None

        tree_df = self.dao.get_authorized_region_list_v2(org_type, way)
        if not tree_df.empty and way == "up":
            if not current_user.is_admin:
                auth_record_id = self.user_auth_cache.get()
                if not auth_record_id:
                    raise ParameterException("找不到当前用户的权限组合，请重新登录~")
                org_sks = tree_df["org_sk"].to_list()
                operating_unit_sks = tree_df["operating_unit_sk"].to_list()
                down_regions = self.dao.get_auth_record_down_region_from_view(auth_record_id, org_sks, operating_unit_sks)
                if down_regions:
                    for region in down_regions:
                        tree_df.loc[
                            (tree_df["org_sk"] == region.org_sk) & (tree_df["operating_unit_sk"] == region.operating_unit_sk),
                            "disabled",
                        ] = "down"

        tree_df.replace(
            {"disabled": {"up": 1, "down|up": 0, "up|down": 0, "down": 0, "up|up": 1, "down|down": 0}}, inplace=True
        )
        tree, _ = tools.get_tree(tree_df, "org_sk", "parent_org_sk")
        return {"tree": tree}

    def get_org_tree_by_org(self, org_type, way=None, org_sks: List = None) -> dict:
        """
        获取登录用户组织权限树
        @param way: None:整个授权的树, up:授权节点向上的树, down:授权节点向下的树(排除店仓)
        @param org_type: 1：集团公司，2：商品管理组织，3：零售管理组织
        @return:字典树
        """
        if way == "all":
            way = None

        tree_df = self.dao.get_authorized_region_list(org_type, way)
        if not tree_df.empty and way == "up":
            if not current_user.is_admin:
                auth_record_id = self.user_auth_cache.get()
                if not auth_record_id:
                    raise ParameterException("找不到当前用户的权限组合，请重新登录~")
                org_sks = tree_df["org_sk"].to_list()
                operating_unit_sks = tree_df["operating_unit_sk"].to_list()
                down_regions = self.dao.get_auth_record_down_region_from_view(auth_record_id, org_sks, operating_unit_sks)
                if down_regions:
                    for region in down_regions:
                        tree_df.loc[
                            (tree_df["org_sk"] == region.org_sk) & (tree_df["operating_unit_sk"] == region.operating_unit_sk),
                            "disabled",
                        ] = "down"

        tree_df.replace(
            {"disabled": {"up": 1, "down|up": 0, "up|down": 0, "down": 0, "up|up": 1, "down|down": 0}}, inplace=True
        )
        if org_sks:
            tree_df["disabled"] = 1
            tree_df.loc[tree_df["org_sk"].isin(org_sks), "disabled"] = 0
        tree, _ = tools.get_tree(tree_df, "org_sk", "parent_org_sk")
        return {"tree": tree}

    def get_org_tree(self):

        tree_df = self.dao.get_authorized_region_list(2, None)

        tree_df["disabled"] = 0
        tree, _ = tools.get_tree(tree_df, "org_sk", "parent_org_sk")
        return {"tree": tree}


    def list_authorized_region_org_sk(self, way="down") -> List[int]:
        """

        :return:
        """
        return self.dao.list_authorized_region_org_sk(way) or []

    def list_org_and_store_children(self, org_sks: List[int]):
        return self.dao.list_org_and_store_children(org_sks)

    def list_authorized_region_org_sk_include_store(self, way="down") -> List[int]:
        """

        :return:
        """
        return self.dao.list_authorized_region_org_sk_include_store(way) or []

    def list_authorized_region_org_sk_by_brand_code(self, brand_code, way="down") -> List[int]:
        """

        :return:
        """
        return self.dao.list_authorized_region_org_sk_by_brand_code(brand_code, way)

    def list_authorized_region_store_sk(self, brand_code=None, way="down") -> List[int]:
        """

        :return:
        """
        return self.dao.list_authorized_region_store_sk(brand_code, way)

    def list_authorized_region_warehouse_sk(self, brand_code=None, way="down") -> List[int]:
        """
        """
        return self.dao.list_authorized_region_warehouse_sk(brand_code, way)

    def list_authorized_region_org_code(self, way="down") -> List[str]:
        """

        :return:
        """
        return self.dao.list_authorized_region_org_code(way)

    def list_authorized_region_org(self, way: str = "down", brand_code: str = None) -> List[OrgEgg]:
        """
        Args:
            way: 查询组织上层/下层
                up: 上级
                down: 下级
            brand_code: 品牌code
        Returns:
            List[OrgEgg]: 组织信息体列表
                org_sk
                org_code
                org_flag
        """
        return self.dao.list_authorized_region_org(way=way, brand_code=brand_code)

    def upsert_auth_region(self, request_params: UpsertAuthRegionRequest):
        """
        更新auth region
        @param request_params: AuthRegionRequest
        @return:
        """
        auth_record_id = self.dao.upsert_auth_region(request_params)
        return auth_record_id

    def save_auth_region(self, request_params: UpsertAuthRegionRequest):
        auth_record_id = self.dao.save_auth_record_region(request_params)
        return auth_record_id

    def batch_save_auth_region(self, request_params: List[UpsertAuthRegionRequest]):
        """
        批量新增
        @param request_params:
        @return:
        """
        ids: List[int] = []
        for request in request_params:
            auth_region_id = self.save_auth_region(request)
            ids.append(auth_region_id)
        return ids

    def batch_upsert_auth_region(self, request_params: List[UpsertAuthRegionRequest]):
        """
        批量upsert 废弃 gp不支持only support CTEs with one writable clause, called in a non-writable context.
        @param request_params:
        @return:
        """
        ids: List[int] = []
        for request in request_params:
            auth_region_id = self.upsert_auth_region(request)
            ids.append(auth_region_id)
        return ids

    def remove_auth_region_by_record_id(self, body: RemoveAuthRegionBody):
        """
        删除组织层级授权记录
        @param body:RemoveAuthRegionBody
        @return:
        """
        return self.dao.remove_record_auth_region(body)

    def update_region_auth_mapping_up(self, auth_record_id: int):
        self.dao.update_region_auth_mapping_up(auth_record_id)

    def update_region_auth_mapping_down(self, auth_record_id: int):
        self.dao.update_region_auth_mapping_down(auth_record_id)

    def get_inherit_org_by_sk(
        self,
        org_sk_list: Union[List[int], int],
        way_enum: Union[RegionQueryWayEnum, None],
        org_flags: Union[List[OrgFlagEnum], OrgFlagEnum, None],
        is_only_parent: bool = False,
        operating_unit_sk=None,
    ) -> List[int]:
        """

        Args:
            org_sk_list: 兼容单个 org_sk 不推荐
            way_enum: 查询 方向
            is_only_parent: 是否只查询 父级; 如果 is_only_parent = True 只有参数 org_sk_list 起作用
            org_flags: List[OrgFlagEnum] 要查询的组织单元类型
            operating_unit_sk: 区域树结构根节点类型
        Return:
            List[int] : list[org_sk]
        """
        # 兼容单个 org_sk
        if isinstance(org_sk_list, int):
            org_sk_list = [org_sk_list]
        if is_only_parent:
            res = self.dao.list_parent_sk(org_sk_list)
            return res

        if not isinstance(way_enum, RegionQueryWayEnum):
            raise ParameterException(msg="层级查询方向参数： way_enum must be RegionQueryWayEnum")
        if isinstance(org_flags, OrgFlagEnum):
            org_flags = [org_flags]

        org_flags = [i.value if isinstance(i, OrgFlagEnum) else OrgFlagEnum.get(i) for i in org_flags]

        if way_enum == RegionQueryWayEnum.all:
            res_up = self.dao.list_inherit_region_sk(org_sk_list, org_flags, operating_unit_sk, way=RegionQueryWayEnum.up.value)
            res_down = self.dao.list_inherit_region_sk(
                org_sk_list, org_flags, operating_unit_sk, way=RegionQueryWayEnum.down.value
            )
            if res_up is None:
                res_up = []
            if res_down is None:
                res_down = []
            res = list(set(res_up + res_down))
            return res
        res = self.dao.list_inherit_region_sk(org_sk_list, org_flags, operating_unit_sk, way=way_enum.value)
        return res

    def get_region_unit_level_group(self, org_sk_list: Union[List, int]) -> Dict:
        """
        根据区域单元查询对应层级对其分组。
        Args:
            org_sk_list
        Return:
            Dict
        """
        regions = self.dao.list_region_info_by_sks(org_sk_list)
        rr = regions[["org_hierarchy_order", "org_sk"]].set_index("org_sk")

        res = {
            "level_group": rr.groupby("org_hierarchy_order").groups,
            "level_order": sorted(rr.groupby("org_hierarchy_order").groups.keys()),
        }
        return res

    def get_region_unit_level_list(self, org_sk_list: Union[List, int]) -> List[RegionUnitLevel]:
        """
        根据区域单元查询对应层级对其分组。
        Args:
            org_sk_list
        Return:
            List[RegionUnitLevel]
        """
        regions = self.dao.list_region_info_by_sks(org_sk_list)
        rr = regions[["org_hierarchy_order", "org_sk"]].sort_values(by="org_hierarchy_order").groupby("org_hierarchy_order")
        res = [RegionUnitLevel(org_hierarchy_order=name, org_sks=grouped["org_sk"].to_list()) for name, grouped in rr]

        return res

    def get_region_unit_level_list_by_org_order(self, org_sk_list: Union[List, int]) -> List[RegionUnitLevel]:
        """
        根据区域单元查询对应层级对其分组。
        Args:
            org_sk_list
        Return:
            List[RegionUnitLevel]
        """
        regions = self.dao.list_region_by_sks_org_order(org_sk_list)
        group_regions = regions[["org_order", "org_sk"]].sort_values(by="org_order").groupby("org_order")
        res = [
            RegionUnitLevel(org_hierarchy_order=order, org_sks=grouped["org_sk"].to_list()) for order, grouped in group_regions
        ]

        return res

    def get_region_unit_level_dict(self, org_sk_list: Union[List, int]) -> List[RegionUnitLevel]:
        """
        根据区域单元查询对应层级对其分组。
        Args:
            org_sk_list
        Return:
            List[RegionUnitLevel]
        """
        if isinstance(org_sk_list, int):
            org_sk_list = [org_sk_list]
        regions = self.dao.list_region_info_by_sks(org_sk_list)
        res = regions[["org_sk", "org_hierarchy_order"]].set_index("org_sk").to_dict()["org_hierarchy_order"]
        return res

    def get_region_operating_sk_list(self, org_sk_list: Union[List, int]) -> List[RegionOperatingSK]:
        """
        根据 区域单元查询对应 operating_unit_sk
        Args:
            org_sk_list
        Return:
            List[RegionOperatingSK]
        """
        regions = self.dao.list_region_info_by_sks(org_sk_list)
        rr = regions[["operating_unit_sk", "org_sk"]].sort_values(by="operating_unit_sk").groupby("operating_unit_sk")
        res = [RegionOperatingSK(operating_unit_sk=name, org_sks=grouped["org_sk"].to_list()) for name, grouped in rr]

        return res

    def get_region_operating_sk_dict(self, org_sk_list: Union[List, int]) -> List[RegionOperatingSK]:
        """
        根据 区域单元查询对应 operating_unit_sk
        Args:
            org_sk_list
        Return:
            List[RegionOperatingSK]
        """
        regions = self.dao.list_region_info_by_sks(org_sk_list)
        res = regions[["operating_unit_sk", "org_sk"]].set_index("org_sk").to_dict()["operating_unit_sk"]
        return res

    def list_user_authorized_org_operating_units(self):
        """
        获取用户授权的业务实体
        @return:
        """
        req = CommonUserReq(user_id=current_user.user_id)
        return self.common_auth_org_service.list_user_authorized_org_operating_units(req)


auth_region_service = AuthRegionService()
