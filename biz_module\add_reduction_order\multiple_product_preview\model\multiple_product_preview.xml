<?xml version="1.0"?>
<mapper namespace="add_reduction_order_multiple_product_preview">

    <select id="query_qrs_page_config">
        with conf_tenant as (
            select * from sys.page_config
            where config_type='tenant'
                and module_name=#{module_name}
                and page_name=#{page_name}
                and is_deleted=0
        ), conf_product as (
            select * from sys.page_config
            where config_type='product'
                and module_name=#{module_name}
                and page_name=#{page_name}
                and is_deleted=0
        ), conf_common as (
            -- 获取普通用户配置时如果有租户配置则能看到租户级别的配置，否则只能看到产品配置
            select * from sys.page_config
            where creator = ${user_id}
                and module_name=#{module_name}
                and page_name=#{page_name}
                and config_type='common'
                and is_deleted=0
            union all
            select * from conf_tenant
            union all
            select * from conf_product where not exists (select 1 from conf_tenant)
        ), conf as(
            select * from sys.page_config where 1 = 2 and is_deleted=0

            <if test="is_common">
                union all
                -- 普通用户配置
                select * from conf_common
            </if>

            <if test="is_tenant">
                -- 租户级别配置
                union all
                select * from conf_tenant
            </if>

            <if test="is_product">
                -- 产品级别配置
                union all
                select * from conf_product
            </if>
        )
        select content from conf
        order by set_type asc, modify_time desc limit 1
    </select>

    <select id="get_page_config_by_id">
        select content from sys.page_config
        where page_id= #{page_id}
        and is_deleted = 0
    </select>

    <select id="get_page_config_basic_by_id">
        select page_name, module_name, page_id from sys.page_config
        where page_id= #{page_id}
        and is_deleted = 0
    </select>

<!--    <select id="user_page_config_mapping">-->
<!--        select content from sys.user_page_config-->
<!--        where page_id= #{page_id}-->
<!--        and is_deleted = 0-->
<!--    </select>-->

    <insert id="add_user_page_config_mapping">
        delete from sys.user_page_config_mapping
        where page_name=#{page_name} and module_name=#{module_name} and user_id=#{user_id};
        insert into sys.user_page_config_mapping(
            page_id,
            page_name,
            module_name,
            user_id,
            creator_id,
            modifier_id,
            create_time,
            modify_time,
            is_deleted)
        values (
            #{page_id},
            #{page_name},
            #{module_name},
            #{user_id},
            #{user_id},
            #{user_id},
            now(),
            now(),
            0)
        returning id;
    </insert>

    <select id="get_user_page_config_mapping">
        select m.page_id from sys.user_page_config_mapping m
        inner join sys.page_config p on m.page_id = p.page_id
        where m.user_id= #{user_id}
        and m.page_name = #{page_name}
        and m.module_name = #{module_name}
        and m.is_deleted = 0
        and p.is_deleted = 0
        order by m.modify_time desc limit 1
    </select>

    <update id="update_page_config_format">
        UPDATE sys.page_config
        SET
           content = #{content}
        WHERE page_id = #{page_id}
    </update>

    <select id="get_page_config_by_module_name_and_page_name">
        SELECT page_id, content
        FROM sys.page_config WHERE module_name = #{module_name} and page_name = #{page_name}
    </select>

    <select id="query_multiple_product_preview_org_filter">
        select
            <if test="filter_key == 'channel_type'">
                channel_type as label, channel_type as value
            </if>
            <if  test="filter_key == 'org_sk'">
                org_name as label, org_sk as value
            </if>
        from biz.qrs_multiple_product_overview
        where ${filter_key} is not null
        <if test="day_date">
            and day_date = #{day_date}
        </if>
        <if test="org_sk_list">
            and org_sk in ${org_sk_list}
        </if>
        <if test="not org_sk_list">
            and 1 != 1
        </if>
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="filter_key == 'org_sk'">
            group by org_sk, org_name
        </if>
        <if test="filter_key != 'org_sk'">
            group by ${filter_key}
        </if>
        order by ${filter_key} desc
    </select>

    <select id="query_multiple_product_preview_filter">
        select
            <if test="filter_key == 'skc_sk'">
                skc_code as label, skc_sk as value
            </if>
            <if test="filter_key == 'day_date'">
                to_char(day_date, 'yyyy-mm-dd') as label, to_char(day_date, 'yyyy-mm-dd') as value
            </if>
            <if test="filter_key not in ('skc_sk', 'day_date')">
                ${filter_key} as label, ${filter_key} as value
            </if>
        from biz.qrs_multiple_product_overview
        where ${filter_key} is not null
        <if test="not is_admin">
            and product_range in (select product_range from sys.user_product where user_id = #{user_id})
            and big_class in (select big_class from sys.user_product where user_id = #{user_id})
        </if>
        <if test="day_date">
            and day_date = #{day_date}
        </if>
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="org_sk">
            and org_sk = #{org_sk}
        </if>
        <if test="not org_sk and filter_key !='day_date'">
            and 1 != 1
        </if>
        <if test="big_class">
            and big_class in ${big_class}
        </if>
        <if test="product_range">
            and product_range in ${product_range}
        </if>
        <if test="product_year_quarter">
            and product_year_quarter in ${product_year_quarter}
        </if>
        <if test="product_belong_name">
            and product_belong_name in ${product_belong_name}
        </if>
        <if test="ps_mid_class">
            and ps_mid_class in ${ps_mid_class}
        </if>
        <if test="sale_mid_tiny_class">
            and sale_mid_tiny_class in ${sale_mid_tiny_class}
        </if>
        <if test="gender">
            and gender in ${gender}
        </if>
        <if test="product_name">
            and product_name in ${product_name}
        </if>
        <if test="filter_key == 'skc_sk'">
            group by skc_code, skc_sk
        </if>
        <if test="filter_key != 'skc_sk'">
            group by ${filter_key}
        </if>
        order by ${filter_key} desc
    </select>

    <select id="query_multiple_product_preview_list">
        with refer_skc as (
            select channel_type, org_sk, skc_sk, count(1) as refer_skc_qty
            from biz.qrs_refer_skc_item
            where is_deleted = 0
            <if test="channel_type">
                and channel_type = #{channel_type}
            </if>
            <if test="org_sk">
                and org_sk = #{org_sk}
            </if>
            <if test="not org_sk">
                and 1 != 1
            </if>
        group by channel_type, org_sk, skc_sk
        )
        , multiple_data as (
            select *, salable_weeks_num::numeric / salable_weeks_den as salable_weeks
            from biz.qrs_multiple_product_overview as qrs
            where 1=1
            <if test="not is_admin">
                and qrs.product_range in (select product_range from sys.user_product where user_id = #{user_id})
                and qrs.big_class in (select big_class from sys.user_product where user_id = #{user_id})
            </if>
            <if test="day_date">
                and qrs.day_date = #{day_date}
            </if>
            <if test="channel_type">
                and qrs.channel_type = #{channel_type}
            </if>
            <if test="org_sk">
                and qrs.org_sk = #{org_sk}
            </if>
            <if test="not org_sk">
                and 1 != 1
            </if>
            <if test="big_class">
                and qrs.big_class in ${big_class}
            </if>
            <if test="product_range">
                and qrs.product_range in ${product_range}
            </if>
            <if test="product_year_quarter">
                and qrs.product_year_quarter in ${product_year_quarter}
            </if>
            <if test="product_belong_name">
                and qrs.product_belong_name in ${product_belong_name}
            </if>
            <if test="ps_mid_class">
                and qrs.ps_mid_class in ${ps_mid_class}
            </if>
            <if test="sale_mid_tiny_class">
                and qrs.sale_mid_tiny_class in ${sale_mid_tiny_class}
            </if>
            <if test="gender">
                and qrs.gender in ${gender}
            </if>
            <if test="product_name">
                and qrs.product_name in ${product_name}
            </if>
            <if test="skc_sk">
                and qrs.skc_sk in ${skc_sk}
            </if>
            <if test="decision_suggest">
                and qrs.decision_suggest in ${decision_suggest}
            </if>
            <if test="status">
                and qrs.status in ${status}
            </if>
        )
        select
            qrs.org_sk || '@@' ||  qrs.skc_sk ||  '@@' || qrs.day_date as uuid
            , qrs.day_date
            , qrs.skc_sk
            , qrs.skc_code
            , qrs.product_name
            , refer_skc_qty
            , decision_suggest
            , human_decision_qty
            , model_decision_qty
            , change_reason
            , sale_out_target
            , sale_out_date
            , lt
            , st
            , salable_weeks_num::numeric / salable_weeks_den as salable_weeks
            , status
            , count(1) over() as total_count
            , c.picture_url as image_url
        from multiple_data as qrs
        left join refer_skc as b on qrs.channel_type = b.channel_type and qrs.org_sk = b.org_sk and qrs.skc_sk = b.skc_sk
        left join dm.dim_skc as c on qrs.skc_sk = c.skc_sk
        <if test="column_orders_str">
            order by ${column_orders_str}
        </if>
        <if test="page_size">
            LIMIT ${page_size}
            OFFSET ${page_offset}
        </if>
    </select>

    <select id="query_multiple_product_preview_detail_kpi">
        WITH base_org AS (
            SELECT a.org_sk, a.org_name, b.org_name as select_org_name
            FROM dm.dim_org_integration as a
            cross join dm.dim_org_integration as b
            WHERE array_length(string_to_array(a.org_long_code, ':'), 1) &lt;=3 AND a.org_flag =3
            AND CAST(#{org_sk} as text) = any(string_to_array(a.org_long_sk, ':')) and b.org_sk = #{org_sk}
        )
        SELECT
            max(product_year_quarter) as product_year_quarter -- 发布季
            ,max(gender) as gender -- 性别
            ,max(color_name) as color_name -- 颜色
            ,max(target_price) as target_price -- 吊牌价
            ,max(big_class) as big_class -- 大类
            ,max(ps_mid_class) as ps_mid_class -- PS中类
            ,max(sale_mid_tiny_class) as sale_mid_tiny_class -- 销售中类小类
            ,max(life_cycle) as life_cycle -- 生命周期
            ,sum(last_two_weeks_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_two_weeks_sale_rate-- 上上周售罄
            ,sum(last_week_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_week_sale_rate-- 上周售罄
            ,sum(last_two_weeks_sale_qty_without_group_buy)::numeric / nullif(sum(total_shipment_qty), 0) as last_two_weeks_without_group_buy_sale_rate-- 剔除团购上上周售罄
            ,sum(last_week_sale_qty_without_group_buy)::numeric / nullif(sum(total_shipment_qty), 0) as last_week_without_group_buy_sale_rate-- 剔除团购上周售罄
            ,sum(total_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as total_sale_rate-- 累计售罄
            ,sum(same_listing_month_sale_qty)::numeric / nullif(sum(same_listing_month_arrival_qty), 0) as same_listing_month_sale_rate-- 同上市月份平均售罄
            ,case
                when max(select_org_name) = '正价店' and  COALESCE(sum(lt_sale_qty), 0) -  COALESCE(sum(stock_qty), 0) >= 0 then '有断货风险'
                when max(select_org_name) = '正价店' and  COALESCE(sum(lt_sale_qty), 0) -  COALESCE(sum(stock_qty), 0) &lt; 0 then '无断货风险'
                else null
            end as lt_sale_risk -- lt断货风险
            , to_char(min(real_listing_date), 'yyyy-mm-dd') as real_listing_date -- 实际上市日期
            , to_char(min(first_sale_date), 'yyyy-mm-dd') as first_sale_date -- 首销日期
            , sum(order_qty) as order_qty -- 订货量
            , sum(total_shipment_qty) as total_shipment_qty -- 累计发货量
            , sum(total_sale_qty) as total_sale_qty -- 累计销量
            , sum(total_sale_qty_without_group_buy) as total_sale_qty_without_group_buy -- 无团购累销
            , sum(last_two_weeks_sale_qty) as last_two_weeks_sale_qty -- 上上周销量
            , sum(last_week_sale_qty) as last_week_sale_qty -- 上周销量
            , sum(total_sale_amt)::numeric / nullif(sum(total_sale_tag_amt), 0) total_discount -- 累计则扣
            , sum(last_two_weeks_sale_amt)::numeric / nullif(sum(last_two_weeks_sale_tag_amt), 0) last_two_weeks_sale_discount -- 上上周折扣
            , sum(last_week_sale_amt)::numeric / nullif(sum(last_week_sale_tag_amt), 0) last_week_sale_discount -- 上周折扣
            , sum(stock_qty) stock_qty -- 店仓剩余库存
            , sum(not_in_stock_qty) not_in_stock_qty -- 未到货量
            , sum(salable_weeks_num)::numeric / nullif(sum(salable_weeks_den), 0) salable_weeks -- 可销周数
            , sum(has_sale_store_qty) has_sale_store_qty -- 有销门店数
            , sum(last_week_sale_qty)::numeric / nullif(sum(has_sale_store_qty),0) avg_store_week_sale -- 店均周销
            , sum(st_forecast_sale_qty) st_forecast_sale_qty -- ST预计销量
            , sum(life_cycle_stock_demand) life_cycle_stock_demand -- 生命周期内库存需求
            , case when select_org_name in ('直营', '正价店')then max(refer_skc_sale_rate) else null end refer_skc_sale_rate -- 参照款上市至决策日销量占比
            , case when select_org_name in ('直营', '正价店') then max(refer_skc_lt_sale_rate) else null end refer_skc_lt_sale_rate -- 参照款LT销量占比
            , case when select_org_name in ('直营', '正价店') then max(refer_skc_st_sale_rate) else null end refer_skc_st_sale_rate -- 参照款ST销量占比
        FROM biz.qrs_multiple_product_overview_kpi as a
        inner join base_org as b on a.org_sk = b.org_sk
        WHERE a.day_date = #{day_date}
        AND a.skc_sk = #{skc_sk}
        GROUP BY select_org_name
    </select>

    <select id="query_multiple_product_preview_by_uuid_list">
        DROP TABLE IF EXISTS temp_update;
        CREATE TEMP TABLE temp_update AS
        with t as (
            select *
            from json_to_recordset(#{params})
            as temp_table("org_sk" int, "skc_sk" int, "day_date" timestamp)
        )
        SELECT * FROM t;

        select a.*, a.org_sk || '@@' ||  a.skc_sk ||  '@@' || a.day_date as uuid
        from biz.qrs_multiple_product_overview as a
        inner join temp_update as b
        on a.org_sk = b.org_sk
        and a.skc_sk = b.skc_sk
        and a.day_date = b.day_date
    </select>

    <select id="save_multiple_product_preview">
        DROP TABLE IF EXISTS temp_update;
        CREATE TEMP TABLE temp_update AS
        with t as (
            select *
            from json_to_recordset(#{params})
            as temp_table("org_sk" int, "skc_sk" int, "day_date" timestamp, "human_decision_qty" int,
            "decision_suggest" varchar, "change_reason" varchar)
        )
        SELECT * FROM t;
        update  biz.qrs_multiple_product_overview as a
            set
                human_decision_qty = b.human_decision_qty
                , decision_suggest = b.decision_suggest
                , change_reason = b.change_reason
                , modify_time = now()
                , modifier_id = #{user_id}
                , status = 1
        from temp_update as b
        where a.org_sk = b.org_sk and a.skc_sk = b.skc_sk and a.day_date = b.day_date;
    </select>

    <update id="update_multiple_product_preview">
        update biz.qrs_multiple_product_overview
        set
            status = 1,
            change_reason = #{change_reason},
            decision_suggest = #{decision_suggest},
            human_decision_qty = #{human_decision_qty},
            modify_time = now(),
            modifier_id = #{user_id}
            where day_date = #{day_date}
        and skc_sk = #{skc_sk}
        and org_sk = #{org_sk}
    </update>

    <update id="confirm_multiple_product_preview">
        DROP TABLE IF EXISTS temp_update;
        CREATE TEMP TABLE temp_update AS
        with t as (
            select *
            from json_to_recordset(#{params})
            as temp_table("skc_sk" int, "org_sk" int)
        )
        SELECT * FROM t;
        UPDATE biz.qrs_multiple_product_overview as a
        SET
            status = #{status},
            modify_time = now(),
            modifier_id = #{user_id}
        FROM temp_update
        WHERE
            a.skc_sk = temp_update.skc_sk AND
            a.org_sk = temp_update.org_sk AND
            a.day_date = #{day_date}
    </update>

    <select id="query_export_data">
        with t1 as (
            select *, salable_weeks_num::numeric / salable_weeks_den as salable_weeks
            from biz.qrs_multiple_product_overview as qrs
            where 1=1
            <if test="not is_admin">
                and qrs.product_range in (select product_range from sys.user_product where user_id = #{user_id})
                and qrs.big_class in (select big_class from sys.user_product where user_id = #{user_id})
            </if>
            <if test="day_date">
                and qrs.day_date = #{day_date}
            </if>
            <if test="channel_type">
                and qrs.channel_type = #{channel_type}
            </if>
            <if test="org_sk">
                and qrs.org_sk = #{org_sk}
            </if>
            <if test="not org_sk">
                and 1 != 1
            </if>
            <if test="big_class">
                and qrs.big_class in ${big_class}
            </if>
            <if test="product_range">
                and qrs.product_range in ${product_range}
            </if>
            <if test="product_year_quarter">
                and qrs.product_year_quarter in ${product_year_quarter}
            </if>
            <if test="product_belong_name">
                and qrs.product_belong_name in ${product_belong_name}
            </if>
            <if test="ps_mid_class">
                and qrs.ps_mid_class in ${ps_mid_class}
            </if>
            <if test="sale_mid_tiny_class">
                and qrs.sale_mid_tiny_class in ${sale_mid_tiny_class}
            </if>
            <if test="gender">
                and qrs.gender in ${gender}
            </if>
            <if test="product_name">
                and qrs.product_name in ${product_name}
            </if>
            <if test="skc_sk">
                and qrs.skc_sk in ${skc_sk}
            </if>
            <if test="decision_suggest">
                and qrs.decision_suggest in ${decision_suggest}
            </if>
            <if test="status">
                and qrs.status in ${status}
            </if>
        )
        ,t2 as (
            SELECT a.org_sk, a.org_name, b.org_name as select_org_name
            FROM dm.dim_org_integration as a
            cross join dm.dim_org_integration as b
            WHERE array_length(string_to_array(a.org_long_code, ':'), 1) &lt;=3 AND a.org_flag =3
            AND CAST(#{org_sk} as text) = any(string_to_array(a.org_long_sk, ':')) and b.org_sk = #{org_sk}
        )
        ,t3 as (
            SELECT
            skc_sk
            ,max(product_year_quarter) as product_year_quarter -- 发布季
            ,max(gender) as gender -- 性别
            ,max(color_name) as color_name -- 颜色
            ,max(target_price) as target_price -- 吊牌价
            ,max(big_class) as big_class -- 大类
            ,max(ps_mid_class) as ps_mid_class -- PS中类
            ,max(sale_mid_tiny_class) as sale_mid_tiny_class -- 销售中类小类
            ,max(life_cycle) as life_cycle -- 生命周期
            ,sum(last_two_weeks_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_two_weeks_sale_rate-- 上上周售罄
            ,sum(last_week_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_week_sale_rate-- 上周售罄
            ,sum(last_two_weeks_sale_qty_without_group_buy)::numeric / nullif(sum(total_shipment_qty), 0) as last_two_weeks_without_group_buy_sale_rate-- 剔除团购上上周售罄
            ,sum(last_week_sale_qty_without_group_buy)::numeric / nullif(sum(total_shipment_qty), 0) as last_week_without_group_buy_sale_rate-- 剔除团购上周售罄
            ,sum(total_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as total_sale_rate-- 累计售罄
            ,sum(same_listing_month_sale_qty)::numeric / nullif(sum(same_listing_month_arrival_qty), 0) as same_listing_month_sale_rate-- 同上市月份平均售罄
            ,case
                when max(select_org_name) = '正价店' and  COALESCE(sum(lt_sale_qty), 0) -  COALESCE(sum(stock_qty), 0) >= 0 then '有断货风险'
                when max(select_org_name) = '正价店' and  COALESCE(sum(lt_sale_qty), 0) -  COALESCE(sum(stock_qty), 0) &lt; 0 then '无断货风险'
                else null
            end as lt_sale_risk -- lt断货风险
            , to_char(min(real_listing_date), 'yyyy-mm-dd') as real_listing_date -- 实际上市日期
            , to_char(min(first_sale_date), 'yyyy-mm-dd') as first_sale_date -- 首销日期
            , sum(order_qty) as order_qty -- 订货量
            , sum(total_shipment_qty) as total_shipment_qty -- 累计发货量
            , sum(total_sale_qty) as total_sale_qty -- 累计销量
            , sum(total_sale_qty_without_group_buy) as total_sale_qty_without_group_buy -- 无团购累销
            , sum(last_two_weeks_sale_qty) as last_two_weeks_sale_qty -- 上上周销量
            , sum(last_week_sale_qty) as last_week_sale_qty -- 上周销量
            , sum(total_sale_amt)::numeric / nullif(sum(total_sale_tag_amt), 0) total_discount -- 累计则扣
            , sum(last_two_weeks_sale_amt)::numeric / nullif(sum(last_two_weeks_sale_tag_amt), 0) last_two_weeks_sale_discount -- 上上周折扣
            , sum(last_week_sale_amt)::numeric / nullif(sum(last_week_sale_tag_amt), 0) last_week_sale_discount -- 上周折扣
            , sum(stock_qty) stock_qty -- 店仓剩余库存
            , sum(not_in_stock_qty) not_in_stock_qty -- 未到货量
            , sum(salable_weeks_num)::numeric / nullif(sum(salable_weeks_den), 0) salable_weeks -- 可销周数
            , sum(has_sale_store_qty) has_sale_store_qty -- 有销门店数
            , sum(last_week_sale_qty)::numeric / nullif(sum(has_sale_store_qty),0) avg_store_week_sale -- 店均周销
            , sum(st_forecast_sale_qty) st_forecast_sale_qty -- ST预计销量
            , sum(life_cycle_stock_demand) life_cycle_stock_demand -- 生命周期内库存需求
            , case when select_org_name in ('直营', '正价店')then max(refer_skc_sale_rate) else null end refer_skc_sale_rate -- 参照款上市至决策日销量占比
            , case when select_org_name in ('直营', '正价店') then max(refer_skc_lt_sale_rate) else null end refer_skc_lt_sale_rate -- 参照款LT销量占比
            , case when select_org_name in ('直营', '正价店') then max(refer_skc_st_sale_rate) else null end refer_skc_st_sale_rate -- 参照款ST销量占比
        FROM biz.qrs_multiple_product_overview_kpi as a
        inner join t2 as b on a.org_sk = b.org_sk
        WHERE a.day_date = #{day_date}
        GROUP BY select_org_name, skc_sk
        )
        select *
        from t1 as a
        left join t3 as b on a.skc_sk = b.skc_sk
        <if test="order_by">
            ${order_by}
        </if>
    </select>
</mapper>