from biz.utils.redis_client_tools import RedisClientTool
from pydantic import BaseModel
from biz.typedef import Result, AnyR<PERSON>ult
from typing import Optional
from datetime import datetime
from flask import request
import hashlib
import logging 
import copy
import itertools
logger = logging.getLogger(__name__)

from biz_module.quick_response.repeat_order.constants import write_legal_url,read_legal_url,delete_legal_url
from biz_module.quick_response.repeat_order.service.cache_request import CacheSkcOrgRepeatOrder
from biz.settings import config
import json 
from biz.utils.tools import str_to_md5
from concurrent.futures import ThreadPoolExecutor,ProcessPoolExecutor
from functools import partial

class Cache:
    def __init__(self):
        self.redis_client = RedisClientTool()
    

    def load_cache(self,key):
        result = self.redis_client.get(key)
        return result
    

    def set_cache(self,key,value):
        result = self.redis_client.set(key,value)
        return True

    def free_cache(self,key):
        self.redis_client.delete_key(key)
        return True

    def is_exists(self,key):
        return self.redis_client.exists(key)






 


class CacheKeyRequest(BaseModel):
    org_sk: int
    skc_sk: int
    channel_type: Optional[str] = '' 
    order_by: Optional[str] = ''
    select_type: Optional[str] = ''  # 设置的任意初始值
    flag:Optional[int] = 1




def get_unique_key(cache_key_info):
    key_str =  str(cache_key_info.org_sk) + str(cache_key_info.skc_sk)  
    if cache_key_info.channel_type:
        key_str = key_str + cache_key_info.channel_type 
    if cache_key_info.order_by:
        key_str = key_str + cache_key_info.order_by 
    if cache_key_info.select_type:
        key_str = key_str + cache_key_info.select_type
    return key_str


def gen_read_unique_key(request):
    cache_key_info = CacheKeyRequest(**request.json)
    key_str = get_unique_key(cache_key_info)
    key_str =  key_str+request.path
    logger.info(f"load key============={key_str}")
    return  'api_cache_'+ str_to_md5(key_str)



def free_cache(new_item,param):
    url,channel_type = param 
    item = copy.deepcopy(new_item)    
    if url == "/api/v5.2.2/quick-response/repeat-order/decision/otb":
        if not item.select_type:
            item.select_type = 'procurement_budget'
        
    if url == "/api/v5.2.2/quick-response/repeat-order/decision/skc-base-info":
        if not item.order_by:
            item.order_by = "mid_class,last_7_0_day_sale_qty desc"
        if item.channel_type:
            item.channel_type = ''
    else:
        if item.channel_type:
            item.channel_type = channel_type
        
    if url == '/api/v5.2.2/quick-response/repeat-order/decision-sum/get-history':
        if item.channel_type=='全部':
            item.channel_type = ''
    
    if url == '/api/v5.2.2/quick-response/repeat-order/decision/simulation_line':
        item.flag = 1

    key_str = get_unique_key(item) + url
    logger.info(f'clear key_str: {key_str}')
    key = 'api_cache_' + str_to_md5(key_str)
    logger.info(f"key====={key}")
    Cache().free_cache(key)
    logger.info(f'clear cache {key}')


def unset_cache(delete_legal_url,channel_type_lst,item):
    url_channel_zip = itertools.product(delete_legal_url, channel_type_lst)
    # for url in delete_legal_url:
    #     for channel_type in channel_type_lst:
    #         free_cache(item.copy(),url,channel_type)
    # for param in url_channel_zip:
        # free_cache(item.copy(), param) 


    with  ThreadPoolExecutor(20) as pool:
        pool.map(partial(free_cache,item),url_channel_zip)



def unset_cache_key(request):
    cache_info = CacheKeyRequest(**request.json)
    web_url = str(config.TENANT_SERVER_HOST)  + ":"+ str(config.TENANT_SERVER_POST)
    cache_skc_org_repeat_order = CacheSkcOrgRepeatOrder()
    org_channel_type_result = cache_skc_org_repeat_order.query_channel_type_by_org(web_url,cache_info.org_sk)
    channel_type_lst = [item['value'] for item in org_channel_type_result]
    unset_cache(delete_legal_url,channel_type_lst,cache_info)

    


def read_interceptor():

    if request.path in read_legal_url:
        logger.info(request.path)
        unique_key = gen_read_unique_key(request)
        key_exists = Cache().is_exists(unique_key)
        if key_exists:    
            data = Cache().load_cache(unique_key)
            data = json.loads(data)
            if data:
                logger.info(unique_key)
                logger.info('hit,return cache')
                logger.info(f"redis key without prefix is {unique_key}")
                return  AnyResult(data=data).dict()
            else:
                logger.info('hit,but data is none')
                logger.info(f"redis key without prefix is {unique_key}")
                return  AnyResult(data=data).dict()
               
        else:

            logger.info('not hit,normal return')


    else:
        logger.info(f"request.path is {request.path}")
        logger.info('this url not interceptor')


def write_interceptor(response):
    if request.path in write_legal_url:
        logger.info(request.path)
        unset_cache_key(request)
        # logger.info('set cache')
        # Cache().set_cache(unique_key,response.data)
    return response

