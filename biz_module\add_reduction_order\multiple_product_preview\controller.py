from flask import request, send_file

from biz import http
from biz.typedef import Result, AnyResult

from . import typedef
from .service.multiple_product_preview import multiple_product_preview_service
from .service.single_decision import single_decision_service
from .typedef import PageConfigIdReq
from ...base.page_config.typedef.page_config import GetConfigListReq
from ...unity_service.service.context import auth_region_service


class AddReductionOrderController(http.Controller):
    """加减单controller"""

    @http.route(
        "/api/v6.0.0/add-reduction-order/page-config/application", methods=["POST"]
    )
    def config_application(self):
        """
        应用
        :return:
        """
        params = request.json
        request_params = PageConfigIdReq(**params)
        response = multiple_product_preview_service.update_config_application(request_params)
        return Result(data=response).dict()

    @http.route(
        "/api/v6.0.0/add-reduction-order/page-config/list", methods=["POST"]
    )
    def list_page_config(self):
        """
        查询
        :return:
        """
        params = request.json
        request_params = GetConfigListReq(**params)
        response = multiple_product_preview_service.list_page_config(request_params)
        return Result(data=response).dict()

    # @http.route(
    #     "/api/v6.0.0/quick-response/add-order/add-feature/config", methods=["POST"]
    # )
    # def multiple_product_preview_add_feature(self):
    #     """
    #     新增kip\tag
    #     :return:
    #     """
    #     params = request.json
    #     request_params = PageConfigSaveReq(**params)
    #     response = self.page_config_service.insert_feature_to_config_and_table(
    #         request_params
    #     )
    #     return Result(data=response).dict()

    # ============================== 多款预览 ===============================

    @http.route(
        "/api/v6.0.0/add-reduction-order/multiple-product-preview/filter",
        methods=["POST"],
    )
    def multiple_product_preview_filter(self):
        """翻单预览筛选"""

        query_info = typedef.MultipleProductPreviewFilterRequest(**request.json)
        data = multiple_product_preview_service.query_multiple_product_preview_filter(
            query_info
        )
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/add-reduction-order/multiple-product-preview/list",
        methods=["POST"],
    )
    def multiple_product_preview_list(self):
        """翻单预览筛选"""

        query_info = typedef.MultipleProductPreviewListRequest(**request.json)
        data = multiple_product_preview_service.query_multiple_product_preview_list(
            query_info
        )
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/add-reduction-order/multiple-product-preview/update",
        methods=["POST"],
    )
    def update_multiple_product_preview(self):
        """翻单预览筛选"""

        query_info = typedef.UpdateMultipleProductPreviewRequest(**request.json)
        data = multiple_product_preview_service.update_multiple_product_preview(
            query_info
        )
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/add-reduction-order/multiple-product-preview/save",
        methods=["POST"],
    )
    def save_multiple_product_preview(self):
        """翻单预览筛选"""

        query_info = typedef.SaveMultipleProductPreviewRequest(**request.json)
        data = multiple_product_preview_service.save_multiple_product_preview(
            query_info
        )
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/add-reduction-order/multiple-product-preview/confirm",
        methods=["POST"],
    )
    def confirm_multiple_product_preview(self):
        """翻单预览筛选"""

        query_info = typedef.ConfirmMultipleProductPreviewRequest(**request.json)
        data = multiple_product_preview_service.confirm_multiple_product_preview(
            query_info
        )
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/add-reduction-order/multiple-product-preview/export",
        methods=["POST"],
    )
    def export_multiple_product_preview(self):
        """翻单预览筛选"""

        data = multiple_product_preview_service.export_multiple_product_preview(
            request.json
        )
        return data

    @http.route(
        "/api/v6.0.0/add-reduction-order/multiple-product-preview/org-tree",
        methods=["POST"],
    )
    def query_multiple_product_preview_org_tree(self):
        """翻单预览筛选"""

        way = request.json.get("way", None)
        org_type = request.json.get("org_type", 2)  # 默认商品树

        data = auth_region_service.get_authorized_region_tree_v2(org_type, way=way)
        data["default"] = 1000020
        return AnyResult(data=data).dict()



    @http.route(
        "/api/v6.0.0/add-reduction-order/multiple-product-preview/detail-kpi",
        methods=["POST"],
    )
    def query_multiple_product_preview_detail_kpi(self):
        """翻单预览筛选"""

        req = typedef.MultipleProductPreviewDetailKpiRequest(**request.json)

        data = multiple_product_preview_service.query_multiple_product_preview_detail_kpi(req)
        return AnyResult(data=data).dict()



    @http.route(
        "/api/v6.0.0/add-reduction-order/single-decision/filter",
        methods=["POST"],
    )
    def single_decision_filter(self):
        """单款筛选"""

        query_info = typedef.SingleDecisionFilterRequest(**request.json)
        data = single_decision_service.query_single_decision_filter(
            query_info
        )
        return AnyResult(data=data).dict()



    @http.route(
        "/api/v6.0.0/add-reduction-order/single-decision/skc-info",
        methods=["POST"],
    )
    def single_decision_skc_info(self):
        """单款筛选"""

        query_info = typedef.SingleDecisionSkcInfoRequest(**request.json)
        data = single_decision_service.query_single_decision_skc_info(
            query_info
        )
        return AnyResult(data=data).dict()



    @http.route(
        "/api/v6.0.0/add-reduction-order/single-decision/decision-info",
        methods=["POST"],
    )
    def single_decision_detail_info(self):
        """单款筛选"""

        query_info = typedef.SingleDecisionSkcInfoRequest(**request.json)
        data = single_decision_service.query_single_decision_detail_info(
            query_info
        )
        return AnyResult(data=data).dict()



    @http.route(
        "/api/v6.0.0/add-reduction-order/single-decision/refer-skc",
        methods=["POST"],
    )
    def single_decision_refer_skc_info(self):
        """单款筛选"""

        query_info = typedef.SingleDecisionSkcInfoRequest(**request.json)
        data = single_decision_service.query_single_decision_refer_skc_info(
            query_info
        )
        return AnyResult(data=data).dict()



    @http.route(
        "/api/v6.0.0/add-reduction-order/single-decision/update",
        methods=["POST"],
    )
    def update_single_decision_detail_info(self):
        """单款筛选"""

        query_info = typedef.UpdateSingleDecisionDetailRequest(**request.json)
        data = single_decision_service.update_single_decision_detail_info(
            query_info
        )
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v1.1.0/system-notice/access_records_logs",
        methods=["POST"],
    )
    def access_records_logs(self):
        """翻单预览筛选"""



        data = []
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v1.1.0/system-notice/get",
        methods=["GET"],
    )
    def get_system_notice(self):
        """翻单预览筛选"""



        data = []
        return AnyResult(data=data).dict()





