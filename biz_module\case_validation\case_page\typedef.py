from datetime import datetime
from typing import List, Optional, Any, Dict, Union

from pydantic import BaseModel, validator

from biz.helpers import TableColumnOpMixin
from biz_module.common.page_filter.typedef import FilterParam


class CaseFilterParam(FilterParam):
    page_name: Optional[str] = 'biz_verify_page'


class CaseColumnParam(BaseModel):
    column_filters: Optional[List[dict]]
    column_orders: Optional[List[dict]]
    column_filter_str: Optional[str] = '1=1'
    column_orders_str: Optional[str]
    column_name: Optional[str]
    column_name_order: Optional[str]


class BizVerifyScreen(BaseModel):
    page_name: str
    filter_code: str
    group_code: str
    is_enable: int
    seq: int


class BizVerifyFormat(BaseModel):
    verify_id: Optional[int]
    verify_name: Optional[str]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if getattr(self, 'group_id', None):
            self.verify_id = getattr(self, 'group_id')
        elif getattr(self, 'case_id', None):
            self.verify_id = getattr(self, 'case_id')
        if getattr(self, 'group_name', None):
            self.verify_name = getattr(self, 'group_name')
        elif getattr(self, 'case_name', None):
            self.verify_name = getattr(self, 'case_name')


class BizVerifyTreeOrg(BaseModel):
    tree_org_sk: List[int]  # 组织筛选器
    public_org_sk: Optional[List[int]]  # 公开组织
    org_sk: Optional[List[int]]  #具体门店

    sort_flag: Optional[str] = 'exc_num'


class BizVerifyTreeDetailSub(BaseModel):
    case_id: int
    run_id: int


class BizVerifyTreeDetail(BaseModel):
    org_sk: List[int]
    case: List[BizVerifyTreeDetailSub]


class BizVerifyTreeDetailSubResponse(BaseModel):
    exc_num: int = 0
    all_exc_num: int = 0


class BizVerifyTreeDetailResponse(BaseModel):
    case_id: int
    exc: BizVerifyTreeDetailSubResponse


class BizVerifyTreeSubSubResponse(BaseModel):
    allow: int
    skc_sk: Optional[int]
    sku_sk: Optional[int]
    stockorg_sk: Optional[int]


class BizVerifyTreeSubResponse(BizVerifyFormat):
    unique_id: Optional[str] = 0

    case_id: int
    case_name: str
    priority: int
    dimension: str
    max_run_id: int
    pass_flag: Optional[int]
    exc_num: Optional[int] = 0
    all_exc_num: Optional[int] = 0
    subsub: Optional[List[BizVerifyTreeSubSubResponse]]


class BizVerifyTreeResponse(BizVerifyFormat):
    unique_id: Optional[str] = 0

    group_id: int
    group_name: str
    priority: int
    exc_num: Optional[int] = 0
    all_exc_num: Optional[int] = 0
    sub: List[BizVerifyTreeSubResponse]


class BizVerifyTreeSort(BaseModel):
    sort_flag: str
    tree: List[BizVerifyTreeResponse]


class BizVerifyRunCases(BaseModel):
    case_id: List[int]
    day_date: Optional[datetime]
    run_type: str
    # 以下是接口需要参数的占位，实际不传
    group_id: Optional[int]
    biz_action_type_code: Optional[str]
    biz_action_template_code: Optional[str]
    biz_action_template_name: Optional[str]
    case_name: Optional[str]
    template_code: Optional[str]
    template_version: Optional[str]
    belong_org_sk: Optional[int]


class BizVerifyCaseConf(BaseModel):
    dimension: str
    case_name: str
    column_mapping: dict


class BizVerifyPageOrder(BaseModel):
    column_name: str
    order: str


class BizVerifyExportAllRequest(CaseFilterParam):
    org_sk: List[int] = []
    case_id: List[int]
    tree_org_sk: Optional[List[int]]
    public_org_sk: Optional[List[int]]
    column_filter_sql: Optional[str]
    column_orders_sql: Optional[str]


class BizVerifyPageRequest(CaseFilterParam):
    export: bool = False
    org_sk: List[int] = []
    case_id: int
    case_name: Optional[str]
    page_size: Optional[int]
    page_no: Optional[int]
    # # 维度
    dimension: Optional[str]
    column_name: Optional[str]

    column_filters: Optional[List[dict]]
    column_orders: Optional[List[dict]]
    column_filter_sql: Optional[str] = '1=1'
    column_orders_sql: Optional[str]

    default_order: Optional[str]

    def __setattr__(self, key, value):
        super().__setattr__(key, value)
        if key == 'dimension':
            if self.dimension == 'org':
                self.default_order = " day_date desc, convert_to(exec_org_name, 'gbk'), convert_to(biz_action_template_name, 'gbk'), s_r_flag, convert_to(org_name, 'gbk') "
            elif self.dimension == 'skc':
                self.default_order = " day_date desc, convert_to(exec_org_name, 'gbk'), convert_to(biz_action_template_name, 'gbk'), convert_to(brand_name, 'gbk'), year_season desc, skc_code "
            elif self.dimension == 'skc-org':
                self.default_order = " day_date desc, convert_to(exec_org_name, 'gbk'), convert_to(biz_action_template_name, 'gbk'), s_r_flag, convert_to(org_name, 'gbk'), convert_to(brand_name, 'gbk'), year_season desc, skc_code "
            else:
                self.default_order = " day_date desc, convert_to(exec_org_name, 'gbk'), convert_to(biz_action_template_name, 'gbk'), s_r_flag, convert_to(org_name, 'gbk'), convert_to(brand_name, 'gbk'), year_season desc, skc_code, size_code"

    def __init__(self, **kwargs):
        i: Dict[str, str]
        for i in (kwargs.get('column_orders', ())):
            if BizVerifyPageResponse.__fields__[i['column_name']].type_ is str:
                i['column_name'] = f"convert_to({i['column_name']}, 'gbk')"
        super().__init__(**kwargs)


class BizVerifyPageResponse(BaseModel):
    day_date: Optional[Union[datetime, str]]
    exec_org_name: Optional[str]
    exec_org_sk: Optional[int]
    biz_action_template_name: Optional[str]
    biz_action_template_id: Optional[int]
    biz_action_type_code: Optional[str]
    total_row: int
    operating_unit_sk: Optional[int]
    batch_id: Optional[int]
    # org
    unit_org_name: Optional[str]
    stockorg_sk: Optional[int]
    org_name: Optional[str]
    org_code: Optional[str]
    s_r_flag: Optional[str]
    # skc
    skc_sk: Optional[int]
    skc_code: Optional[str]
    year_season: Optional[str]
    brand_name: Optional[str]
    brand_code: Optional[str]
    # sku
    sku_sk: Optional[int]
    size_name: Optional[str]
    # reserve
    reserve1: Any
    reserve2: Any
    reserve3: Any
    reserve4: Any
    reserve5: Any
    reserve6: Any
    reserve7: Any
    reserve8: Any
    reserve9: Any
    reserve10: Any


class BizVerifyUnion(BaseModel):
    """
    def mapping_row_data(row: Row, result_field: ModelField) -> dict:
    if (
        isinstance(result_field.type_, ModelMetaclass)
        or result_field.type_ in (Mapping, Dict)
        or result_field.shape in MAPPING_LIKE_SHAPES
    ):
        keys = map(str.lower, row.keys())
        result = {key: value for key, value in zip(keys, row)}
    else:
        result = row._data[0]
    return result
    这里的展开规则是isinstance(result_field.type_, ModelMetaclass)
    但union并不是ModelMetaclass子实例，所以要额外处理
    """
    def validate(cls, args):
        if len(args) == 1:
            return tuple(args.values())[0]
        return BizVerifyPageResponse(**args)


class BizVerifySingleCase(BaseModel):
    case_id: int


class BizVerifyCaseInfo(BaseModel):
    remark: Optional[str]
    upper_limit: Optional[str]
    is_merge: Optional[int]
    update_time: Optional[Union[datetime, str]]
    status: Optional[str]


class BizVerifyCaseStatistics(BaseModel):
    item: str
    exc_num: int
    exc_percent: Optional[float]


class BizVerifyExportCase(BaseModel):
    group_name: str
    case_id: int
    run_id: List[int]


class LabelValue(BaseModel):
    label: Optional[str]
    value: Optional[Any]


class BizVerifyOverviewReq(CaseFilterParam, CaseColumnParam, TableColumnOpMixin):
    org_sk: Optional[List[int]]
    stockorg_sk: Optional[int]
    skc_sk: Optional[int]
    tree_org_sk: Optional[List[int]] = []
    public_org_sk: Optional[List[int]] = []
    operating_unit_sk: Optional[str]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        column_filters = getattr(self, 'column_filters', [])
        self.column_filter_str = self.column_filter_sql(column_filters)
        column_orders = getattr(self, 'column_orders', [])
        self.column_orders_str = self.column_orders_sql(
            column_orders,
            func=lambda x: f"convert_to({x}, 'GBK')"
            if getattr(BizVerifyOverviewEgg.__fields__.get(x), 'type_', None) == str
            else x)
        self.column_name_order = f"convert_to({self.column_name}, 'GBK')" \
            if getattr(BizVerifyOverviewEgg.__fields__.get(self.column_name), 'type_', None) == str \
            else self.column_name


class BizVerifyOverviewEgg(BaseModel):
    # 验证概览
    case_id: int
    case_name: Optional[str]
    dimension: Optional[str]
    exception_num: Optional[int]
    pass_flag: Optional[str]
    unique_id: Optional[str]


class BizVerifyResultCheckReq(CaseFilterParam, CaseColumnParam, TableColumnOpMixin):
    skc_sk: int
    stockorg_sk: int
    tree_org_sk: Optional[List[int]] = []
    public_org_sk: Optional[List[int]] = []
    org_sk: Optional[List[int]]
    operating_unit_sk: Optional[str]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        column_filters = getattr(self, 'column_filters', [])
        self.column_filter_str = self.column_filter_sql(column_filters)
        column_orders = getattr(self, 'column_orders', [])
        self.column_orders_str = self.column_orders_sql(
            column_orders,
            func=lambda x: f"convert_to({x}, 'GBK')"
            if getattr(BizVerifyResultCheckEgg.__fields__.get(x), 'type_', None) == str
            else x)
        self.column_name_order = f"convert_to({self.column_name}, 'GBK')" \
            if getattr(BizVerifyResultCheckEgg.__fields__.get(self.column_name), 'type_', None) == str \
            else self.column_name


class HelpCheckParam(BaseModel):
    """
    辅助排查接口传参
    """
    biz_action_template_id: Optional[int]
    org_code: Optional[str]
    biz_action_type_code: Optional[str]
    help_check_dim: Optional[str]
    skc_code: Optional[str]
    skc_sk: Optional[int]
    belong_org_sk: Optional[str]
    day_date: Optional[str]


class BizVerifyResultCheckEgg(HelpCheckParam):
    case_name: Optional[str]
    biz_action_template_name: Optional[str]
    size_name: Optional[str]
    check_send_side: Optional[str]
    check_receive_side_rep: Optional[str]
    check_receive_side_trans: Optional[str]
    batch_id: Optional[int]
    exec_org_sk: Optional[int]


class SkcSkEgg(BaseModel):
    skc_sk: int
