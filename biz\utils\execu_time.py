import logging, time
from functools import wraps


def interface_execu_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        logging.info("***********INTERFACE_EXECU_TIME**************"
                     " function: {} , elapsed_time: {}s".format(wrapper.__name__, round((end - start),3)))
        return result

    return wrapper
