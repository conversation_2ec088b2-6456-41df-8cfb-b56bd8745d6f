from typing import List

import math
import logging
from flask_login import current_user
from ourbatis import transaction_manager
from pandas import DataFrame

from biz import config, cache
from biz.api_exception import ParameterException
from biz.base import BaseService
from biz.typedef import PageResultInfo
from biz.utils import tools
from biz_module.base.authorization.role.dao import <PERSON><PERSON><PERSON>, PageDao
from .page_service import page_service
from biz_module.base.authorization.role.typedef import (
    QueryRolePage,
    UpdateRoleInfoRequest,
    SaveRoleRequest,
    RolePageRequest,
    OperationType,
    RoleResult,
    UpdateRoleIsActiveRequest,
    RoleUser,
    SaveRoleUserRequest,
    SaveRoleActionRequest,
    ROLE_PAGE_ID,
)
from biz_module.base.authorization.user.service import user_service
from ...base.signals import unbind_role_user_finished
from ...base.tools import UserAuthCache, page_permission_rw_checker
from ...base.typedef import CheckedEnum, SwitchAuthCacheKeyEnum


class RoleService(BaseService):
    max_name_length = 64

    max_remark_length = 256

    def __init__(self):
        super(RoleService, self).__init__()
        self.role_dao = RoleDao()
        self.page_dao = PageDao()
        self.logger = logging.getLogger(__name__)
        self.user_auth_cache = UserAuthCache()

    @page_permission_rw_checker(ROLE_PAGE_ID, "r")
    def list_role_with_page(self, query_param: QueryRolePage):
        """
        获取用户列表
        """
        page_size = query_param.page_size
        offset = (query_param.page_no - 1) * page_size
        query_str = query_param.query_str
        query_like = f"%{query_str}%" if query_str else None
        is_active = query_param.is_active
        roles = self.role_dao.list_role_with_page(page_size, offset, query_like, is_active)

        total_row = self.role_dao.role_total_row(query_like, is_active)
        data = dict(
            page_no=query_param.page_no,  # 当前页
            page_size=query_param.page_size,  # 每页数量
            total_rows=total_row,  # 总记录数
            total_pages=math.ceil(total_row / query_param.page_size),  # 总页数
            items=roles or [],  # 具体记录
        )
        return PageResultInfo(**data)

    def select_by_id(self, user_id: int):
        """
        根据id查找用户
        """
        return self.role_dao.find_by_id(user_id)

    @page_permission_rw_checker(ROLE_PAGE_ID, "w")
    def update_role_info(self, role_request: UpdateRoleInfoRequest):
        """
        更新
        @param role_request:
        @return:
        """
        if not role_request.role_name:
            raise ParameterException(f"请输入正确的角色名称")

        if len(role_request.role_name) > self.max_name_length:
            raise ParameterException(f"角色长度不能超过{self.max_name_length}字符")

        if role_request.remark and len(role_request.remark) > self.max_remark_length:
            raise ParameterException(f"备注长度不能超过{self.max_remark_length}字符")

        is_exist = self.role_dao.role_find_id_exists(role_request.role_id)
        if not is_exist:
            raise ParameterException("角色不存在或已删除")

        # 校验role_name是否唯一
        role = self.role_dao.find_by_role_name(role_request.role_name, role_request.role_id)
        if role:
            raise ParameterException("角色名称已存在")

        result = self.role_dao.update_role_info(role_request)
        return {"role_id": role_request.role_id}

    @page_permission_rw_checker(ROLE_PAGE_ID, "w")
    def update_role_is_active_status(self, update_param: UpdateRoleIsActiveRequest):
        """
        更新角色启用禁用
        @param update_param:UpdateRoleIsActiveRequest
        @return:
        """
        is_exist = self.role_dao.role_find_id_exists(update_param.role_id)
        if not is_exist:
            raise ParameterException("角色不存在或已删除")

        if update_param.is_active is False:
            role_users = self.role_dao.find_role_users(update_param.role_id)
            if role_users:
                raise ParameterException("角色被用户占用,无法禁用")

            if self.role_dao.is_role_take_by_auth_record(update_param.role_id):
                raise ParameterException("角色被权限组合占用,无法禁用")

        result = self.role_dao.update_role_is_active_status(update_param)
        return {"role_id": update_param.role_id}

    def check_id_exists(self, role_id):
        return self.role_dao.role_find_id_exists(role_id)

    @page_permission_rw_checker(ROLE_PAGE_ID, "w")
    def remove_role(self, role_id: int):
        is_exist = self.role_dao.role_find_id_exists(role_id)
        if not is_exist:
            raise ParameterException("角色不存在或已删除")

        # 是否被占用
        users = self.role_dao.find_role_users(role_id)
        if users:
            raise ParameterException("角色已被用户占用，无法删除")

        if self.role_dao.is_role_take_by_auth_record(role_id):
            raise ParameterException("角色被权限组合占用,无法删除")

        result = self.role_dao.remove_role(role_id)
        return {"role_id": role_id}

    @page_permission_rw_checker(ROLE_PAGE_ID, "w")
    def remove_user_role(self, role_id, user_id):
        return self.role_dao.remove_role_user(role_id, user_id)

    @page_permission_rw_checker(ROLE_PAGE_ID, "w")
    def save_role(self, role_request: SaveRoleRequest):
        """
        保存角色
        """
        if not role_request.role_name:
            raise ParameterException(f"请输入正确的角色名称")

        if len(role_request.role_name) > self.max_name_length:
            raise ParameterException(f"角色名称长度不能超过{self.max_name_length}字符")

        if role_request.remark and len(role_request.remark) > self.max_remark_length:
            raise ParameterException(f"备注长度不能超过{self.max_remark_length}字符")

        # 校验role_name是否唯一
        role = self.role_dao.find_by_role_name(role_request.role_name)
        if role:
            raise ParameterException("角色名称已存在！")

        role_id = self.role_dao.save_role(role_request)

        return {"role_id": role_id}

    def list_role_users(self, role_id: int):
        """
        获取角色用户
        """
        active_users = user_service.find_users_by_active(is_active=True)
        user_ids = self.role_dao.find_role_users(role_id) or []

        all_users = [
            RoleUser(role_id=role_id, user_id=user.user_id, username=user.username, name=user.name) for user in active_users
        ]
        return {"users": all_users, "selected_user_ids": user_ids}

    def upsert_role_user(self, role_id: int, user_id: int):
        """
        存在则更新 不存在就插入
        @param role_id:
        @param user_id:
        @return:
        """
        role_user = self.role_dao.find_role_user_exists(role_id, user_id)
        if not role_user:
            role_user = self.role_dao.insert_role_user(role_id, user_id)
        return True

    @page_permission_rw_checker(ROLE_PAGE_ID, "w")
    def save_role_user(self, role_user_request: SaveRoleUserRequest):
        """
        保存角色用户
        """
        role_id = role_user_request.role_id
        user_ids = role_user_request.user_ids or []
        is_exist = self.role_dao.role_find_id_exists(role_id)
        if not is_exist:
            raise ParameterException("角色不存在或已删除")

        if user_ids:
            users = user_service.find_by_ids(user_ids)
            if len(users) != len(user_ids):
                raise ParameterException("用户不存在或已删除")

        db_role_user_ids = self.role_dao.find_role_users(role_id) or []
        db_role_users = set(db_role_user_ids)
        req_role_users = set(user_ids)
        should_removed_user_ids = list(db_role_users.difference(req_role_users))
        should_insert_user_ids = list(req_role_users.difference(db_role_users))

        with transaction_manager():
            if should_removed_user_ids:
                removed_user_ids = self.role_dao.remove_role_users(role_id, user_ids=should_removed_user_ids)
                unbind_role_user_finished.send(self, role_id=role_id, user_ids=removed_user_ids)

            if should_insert_user_ids:
                for user_id in should_insert_user_ids:
                    self.role_dao.insert_role_user(role_user_request.role_id, user_id)

        return {"role_users": role_user_request}

    def get_role_page_tree(self, role_id: int):
        """
        获取角色页面树
        """
        is_exist = self.role_dao.role_find_id_exists(role_id)
        if not is_exist:
            raise ParameterException("角色不存在或已删除")
        data = page_service.get_role_page_tree(role_id)
        return data

    @page_permission_rw_checker(ROLE_PAGE_ID, "w")
    def save_role_page(self, perms: RolePageRequest):
        """
        保存角色页面树
        """
        is_exist = self.role_dao.role_find_id_exists(perms.role_id)
        if not is_exist:
            raise ParameterException("角色不存在或已删除")

        data = page_service.save_role_page(perms)
        return data

    def list_role_biz_action(self, role_id):
        """
        获取角色功能权限列表
        暂时未保存这部分内容放到数据库
        """
        is_exist = self.role_dao.role_find_id_exists(role_id)
        if not is_exist:
            raise ParameterException("角色不存在或已删除")

        tree = []
        checked_list = []
        actions = self.role_dao.list_role_action(role_id)
        if actions:
            action_df = DataFrame([action.__dict__ for action in actions])
            checked_list = action_df[action_df["operation_type"] == OperationType.WRITE]["id"].to_list()
            tree, _ = tools.get_tree(action_df, "action_code", "parent_action_code")
        return {"tree": tree, "checked_list": checked_list}

    @page_permission_rw_checker(ROLE_PAGE_ID, "w")
    def save_role_biz_action(self, params: SaveRoleActionRequest):
        """
        保存角色功能权限列表
        """
        role_id = params.role_id
        action_ids = params.action_ids
        is_exist = self.role_dao.role_find_id_exists(role_id)
        if not is_exist:
            raise ParameterException("角色不存在或已删除")

        if len(action_ids) == 0:
            self.role_dao.remove_role_biz_action(role_id, current_user.user_id)
            return {"role_action": params}

        action_list = self.role_dao.find_action_by_ids(action_ids)
        if action_list is None or len(action_list) != len(action_ids):
            raise ParameterException("功能权限参数错误")

        with transaction_manager():
            self.role_dao.remove_role_biz_action(role_id, current_user.user_id)
            for action_id in action_ids:
                self.role_dao.save_role_biz_action(role_id, action_id, current_user.user_id)
        return {"role_action": params}

    def get_current_user_role_id(self):
        """
        获取当前登录用户的权限角色
        """

        auth_record_id = self.user_auth_cache.change_cache_key(SwitchAuthCacheKeyEnum.AUTH_RECORD_MODE).get()

        if not auth_record_id:
            # 尝试获取切换的角色
            role_id = self.user_auth_cache.change_cache_key(SwitchAuthCacheKeyEnum.ROLE_MODE).get()
            self.logger.warning(f"获取当前登录用户的权限组合角色失败,尝试获取缓存中的角色,role_id:{role_id}")
            return role_id

        from biz_module.base.authorization.auth_record.service import auth_record_service

        auth_record = auth_record_service.find_auth_record_by_id(auth_record_id)
        if not auth_record:
            self.logger.warning(f"获取当前登录用户的权限角色失败,权限组合ID:{auth_record_id}")
            return 0

        return auth_record.role_id

    def get_current_user_page_permission(self, page_id):
        """
        获取当前权限组合的某个页面权限
        """
        if current_user.is_admin:
            return OperationType.WRITE.value
        role_id = self.get_current_user_role_id()
        if not role_id:
            return OperationType.EMPTY.value

        result = self.page_dao.get_role_page_permission(role_id, page_id)
        return result or OperationType.EMPTY.value

    def get_current_user_action(self, action_code):
        """
        获取当前权限组合的某个功能权限
        """
        if current_user.is_admin:
            return OperationType.WRITE.value
        role_id = self.get_current_user_role_id()
        if not role_id:
            return OperationType.EMPTY.value

        actions = self.list_role_biz_action(role_id)
        if not actions or not len(actions["tree"]):
            return OperationType.EMPTY.value

        for action in actions["tree"][0]["children"]:
            if action["action_code"] == action_code:
                return action["operation_type"]
        else:
            return OperationType.EMPTY.value

    def list_role(self):
        role_list = self.role_dao.list_role()
        return role_list or []

    def find_by_role_id(self, role_id) -> RoleResult:
        return self.role_dao.find_by_role_id(role_id)

    def find_by_role_ids(self, role_ids: List[int]):
        roles = self.role_dao.find_by_role_ids(role_ids)
        return roles or []

    # def find_user_roles(self, user_id):
    #     return self.role_dao.find_user_roles(user_id)

    def find_user_role(self, user_id):
        role_list = self.role_dao.find_user_role(user_id)
        return role_list or []

    def find_user_role_with_page_permission(self, user_id):
        role_list = self.role_dao.find_user_role_with_page_permission(user_id)
        return role_list or []

    def find_role_with_page_permission(self, role_id):
        """
        查找角色的页面权限
        @param role_id:
        @return:
        """
        role_list = self.role_dao.find_role_with_page_permission(role_id)
        return role_list or []


role_service = RoleService()
