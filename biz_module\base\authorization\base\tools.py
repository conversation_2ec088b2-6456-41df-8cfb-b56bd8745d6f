import logging
from enum import IntEnum
import functools
from typing import Optional
from flask import current_app, request
from flask_login import current_user
from pydantic import BaseModel

from biz import cache
from biz.api_exception import ParameterException
from biz_module.base.authorization.base.typedef import SwitchAuthCacheKeyEnum


class AuthInSnapParams(BaseModel):
    """ """

    region_auth_way: Optional[str]
    product_dim: Optional[str]
    product_field: Optional[str]
    region_field: Optional[str] = "org_sk"
    operating_unit_field: Optional[str]
    auth_record_id: Optional[str]


def auth_param_extend(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        auth_record_id = UserAuthCache().get()  # 当前用户权限
        extend_kwargs = dict(_auth_record_id=auth_record_id, _is_admin=current_user.is_admin)
        return func(*args, extend_kwargs=extend_kwargs, **kwargs)

    return wrapper


class RequestLimiter:
    """
    限制用户频繁请求接口
    一次有效的请求后才能接受下一次的请求
    """

    loading_msg = "请求正在处理中, 请耐心等待~"

    def __init__(self, v_func, default_timeout=20):
        self.cache = cache
        self.v_func = v_func
        self.default_timeout = default_timeout  # 缓存默认失效时间
        self.logger = logging.getLogger(__name__)
        functools.update_wrapper(self, v_func)

    @property
    def _cache_key(self):
        return f"user_{current_user.user_id}_request_{self.v_func.__name__}"

    def __call__(self, *args, **kwargs):
        result = self.cache.get(self._cache_key)
        if result:
            self.logger.warning(f"重复请求接口{request.url},cache_key:{self._cache_key}已存在于缓存中")
            raise ParameterException(self.loading_msg)

        result = self.cache.add(self._cache_key, 1, self.default_timeout)
        if not result:
            self.logger.warning(f"重复请求接口{request.url},cache_key:{self._cache_key}已存在于缓存中")
            raise ParameterException(self.loading_msg)

        try:
            data = self.v_func(*args, **kwargs)
        finally:
            self.cache.delete(self._cache_key)

        return data


class UserAuthCache:
    """用户权限缓存"""

    def __init__(self):
        self.user_id = 0
        """
        权限组合id或角色id  
        登录成功或者切换后 用户有有效权限组合的情况下 保存权限组合ID 
        无权限组合
        而有角色时保存的是角色ID
        """
        self.cache = cache
        self.logger = logging.getLogger(__name__)
        self._timeout = -1
        self._cache_key = SwitchAuthCacheKeyEnum.AUTH_RECORD_MODE.value

    @property
    def timeout(self):
        return self._timeout if self._timeout > 0 else current_app.config.get("PERMANENT_SESSION_LIFETIME")

    def set(self, auth_id: int, user_id: int = None, timeout: int = None) -> bool:
        """
        设置用户的权限组合id到cache中
        @param auth_id:
        @param user_id:
        @param timeout:
        @return:
        """
        if not auth_id:
            self.logger.warning(f"设置用户:{user_id}权限记录缓存失败,auth_id:{auth_id}")
            return False

        self.set_user_id(user_id)

        if timeout is not None:
            self._timeout = timeout

        result = self.cache.set(self.cache_key, auth_id, self.timeout)
        if not result:
            self.logger.warning(f"设置用户:{user_id}权限记录缓存失败, key:{self.cache_key}，value:{auth_id}, 结果:{result}")
        return True if result else False

    def get(self, user_id: int = None) -> int:
        """
        获取用户的权限记录id
        @return: 无记录 返回0
        """
        self.set_user_id(user_id)

        auth_id = self.cache.get(self.cache_key)
        if not auth_id:
            auth_id = 0
        self.logger.info(f"获取权限记录缓存,key:{self.cache_key},value:{auth_id}")
        return auth_id

    @property
    def cache_key(self):
        return f"user_{self.user_id}_{self._cache_key}"

    def delete(self, user_id: int = None):
        """
        删除缓存
        @param user_id:
        @return:
        """
        if user_id is not None:
            self.user_id = user_id

        result = self.cache.delete(self.cache_key)
        self.logger.info(f"删除权限记录缓存,key:{self.cache_key},result:{result}")

    def set_user_id(self, user_id: int = None):
        self.user_id = user_id if user_id else current_user.user_id

    def change_cache_key(self, cache_key: SwitchAuthCacheKeyEnum):
        self._cache_key = cache_key.value
        return self


def page_permission_rw_checker(page_id: int, mode: str = "w"):
    err_msgs = {"w": "无页面可写权限", "r": "无页面可读权限"}
    err_msg = err_msgs.get(mode)
    operation_type = 2 if mode == "w" else 1

    def decorator(f):
        @functools.wraps(f)
        def wrapper(*arg, **kwargs):
            from biz_module.base.authorization.role.service import role_service

            auth_type = role_service.get_current_user_page_permission(page_id)
            if auth_type < operation_type:
                raise ParameterException(err_msg)
            data = f(*arg, **kwargs)
            return data

        return wrapper

    return decorator


def connect_signal(signal, sender):
    """
    注册callback到signal,只注册一次
    @param signal:
    @param sender:
    @param f: 回调函数
    @return:
    """

    def decorator(f):
        if not signal.has_receivers_for(sender):
            signal.connect(f, sender)
        return f

    return decorator
