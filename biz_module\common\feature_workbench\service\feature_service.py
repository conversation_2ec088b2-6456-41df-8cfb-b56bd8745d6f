import copy
import logging
import json
from collections import defaultdict

from fastboot.utils.config import ConfigMeta
from linezone_commonserver.services.feature.service import FeatureService as CommonFeatureService
from linezone_commonserver.services.meta_data.service import MetaDataService
from datetime import datetime
from typing import List

from flask_login import current_user
from biz.base import BaseService
from .curd_logic_core import LogicProcessor
from biz_module.common.feature_workbench.enums import (
    FeatureTypeEnum,
    CalculationTypeEnum,
    FeatureSubtypeEnum,
    DataTypeEnum,
    IsAutoSql,
    OperatePermissionEnum,
)
from ..constants import ACTION_DELETE, ACTION_UPDATE, ACTION_CREATE
from ..dao import FeatureDao
from biz.api_exception import ParameterException
from ..tools import FeatureOperatePermissionHelper
from ..typedef import DataTypeExtension, FeatureExpression
from ...expression.expression import parse_expression
from ...feature_template.service.feature_template_service import FeatureTemplateService
from ...feature_workbench import typedef as tf
from linezone_commonserver.services.filter.service import CommonFilterService
from fastboot.utils.nest import egg
from ...feature_workbench import constants as ct
from ourbatis.session import transaction_manager

from biz_module.common.feature_workbench.wh_service.feature_define import FeatureDefine

logger = logging.getLogger(__name__)


@egg
class FeatureService(BaseService):
    """
    服务
    """
    filter_common_service: CommonFilterService
    dao = FeatureDao()
    schema_prefix: str = ConfigMeta(name="schema_prefix")
    feature_template_service: FeatureTemplateService
    common_meta_service: MetaDataService
    common_feature_service: CommonFeatureService
    permission_helper = FeatureOperatePermissionHelper()

    def change_feature_name_to_feature_expr(self, feature_name: str) -> str:
        feature_config = self.dao.query_feature_config_by_name(feature_name)
        if feature_config is None:
            raise Exception(f"'{feature_name}'不存在")
        feature_expr = f"{feature_config.feature_code}.feature_value"

        return feature_expr

    def feature_expression(self, params: FeatureExpression):
        expr_result = parse_expression(params.feature_expression, self.change_feature_name_to_feature_expr)
        return {
            "expr_result": f"({expr_result.expression}) as feature_value",
            "error_messages": expr_result.error_messages
        }

    def query_feature_list_by_filter(self, query_info: tf.FeatureListByFilterReq):
        # 分页操作
        if query_info.page_no and query_info.page_size:
            query_info.page_no = (query_info.page_no - 1) * query_info.page_size

        query_info.operate_permission = self.permission_helper.gen_permission_sql_snippet(OperatePermissionEnum.READ)
        obj_list = self.dao.query_feature_list_by_filter(query_info)

        items = []
        for l in obj_list:
            if l.data_type_extension:
                l.data_type = l.data_type_extension["data_type_enum"]
            l_dict = l.dict(exclude={"data_type_extension"})
            items.append(l_dict)

        headers = sorted(ct.FEATURE_TABLE_HEADER.values(), key=lambda x: x["order"])
        total_rows = self.dao.count_feature_by_filter(query_info)
        return {"total_rows": total_rows, "items": items, "header": headers}

    def query_feature_filter_list(self):
        result = {"feature_type": FeatureTypeEnum.label_values(), "data_type": DataTypeEnum.label_values()}
        return result

    def feature_config_edit(self, query_info: tf.FeatureConfigById):
        """
        编辑特征配置
        args:
            feature_config_id
        return:
            display value
        """
        feature_config = self.dao.query_feature_config_by_id(query_info)
        feature_config.data_type = (
            feature_config.data_type_extension["data_type_enum"]
            if feature_config.data_type_extension
            else feature_config.data_type
        )
        result = feature_config.dict(exclude={"data_type_extension"})

        feature_vals = self.query_feature_value_by_config_id(query_info) or []
        result['sql_edit'] = ''
        if feature_config.feature_subtype == 'manual' and len(feature_vals) > 0:
            result['sql_edit'] = feature_vals[0].hit_sql

        filter_ids = [fv.filter_instance_id for fv in feature_vals if fv.filter_instance_id] or [-1]
        inst_filters = self.dao.query_feature_filter_by_ids(tf.FeatureFilterByIdsReq(inst_filter_ids=filter_ids)) or []
        inst_filters_map = {inst_filter.id: inst_filter for inst_filter in inst_filters if inst_filter}

        result['feature_values'] = []
        for feature_val in feature_vals:
            if not self.permission_helper.has_permission(feature_val.operate_permission, OperatePermissionEnum.READ):
                continue

            feature_val_dict = dict(feature_val)
            feature_val_dict['filter_instance'] = {}
            if feature_val.filter_instance_id:
                feature_val_dict['filter_instance'] = inst_filters_map[feature_val.filter_instance_id]

            result['feature_values'].append(
                feature_val_dict
            )

        return result

    def feature_config_display(self, query_info: tf.FeatureTypeReq):
        from biz_module.common.biz_view.service.biz_view_service import biz_view_service
        v_combine = biz_view_service.list_biz_view_combine()
        DISPLAY_CONFIG = copy.deepcopy(ct.TAG_CONFIG_DISPLAY)
        if query_info.feature_type == 'tag':
            for tc in DISPLAY_CONFIG:
                if tc['model'] == 'biz_view_id':
                    tc['selector_values'] = [{'label': vc.biz_view_name,
                                              'value': vc.biz_view_id} for vc in v_combine]
                if tc['model'] == 'biz_view_dimension_id':
                    for vc in v_combine:
                        v_dim = [
                            {
                                'label': d.dimension_title,
                                'value': d.dimension_id,
                                'biz_view_id': d.biz_view_id
                            } for d in vc.biz_view_dimensions]
                        tc['selector_values'].extend(v_dim)
            return DISPLAY_CONFIG
        else:
            raise ParameterException('该特征还未实现！现实现的特征：tag（标签）')

    def generate_feature_code(self, query_info: tf.FeatureConfigEgg):
        """
        计算特征code
        """
        fill_id = str(query_info.id).zfill(5)
        query_info.feature_code = '{feature_type}${feature_id}${time}'.format(
            feature_type=query_info.feature_type,
            feature_id=fill_id,
            time=datetime.now().strftime('%Y%m%d%H%M%S')
        )
        return self.dao.update_feature_code(query_info)

    @staticmethod
    def get_element_id(query_info: tf.FeatureConfigEgg) -> str:
        # todo 优化
        if query_info.feature_type == FeatureTypeEnum.TAG.value:
            return "002"
        else:
            if query_info.data_type in (DataTypeEnum.TEXT.value, DataTypeEnum.TIMESTAMP.value):
                return "005"
            else:
                return "006"

    def generate_base_feature_filter(self, query_info: tf.FeatureConfigEgg):
        value_editor = [
            tf.MetaValueEditor(
                type="element",
                field=query_info.feature_code,
                options=[],
                # element_id=ct.VALUE_EDIT_ELEMENT_MAP[query_info.feature_type]
                element_id=self.get_element_id(query_info),
            ).dict()
        ]
        if query_info.feature_type == "kpi" and query_info.data_type == "text":
            data_type = "kpi_text"
        else:
            data_type = query_info.data_type
        applicable_operator = [
            tf.MetaApplicableOperator(
                type="operators",
                default=ct.APP_OPERATOR_DEFAULT_MAP[query_info.data_type],
                options=[tf.MetaAppOperator(**o) for o in ct.APP_OPERATOR_MAP[data_type]],
                element_id=ct.APP_OPERATOR_ELEMENT_MAP[query_info.feature_type],
            ).dict()
        ]
        input_parameter_editor = [tf.MetaInputParameterEditor().dict()]
        base_feature_filter_body = tf.BaseFeatureFilterEgg(
            feature_config_id=query_info.id,
            input_parameter_editor=json.dumps(input_parameter_editor),
            applicable_operator=json.dumps(applicable_operator),
            value_editor=json.dumps(value_editor),
            creator_id=current_user.user_id,
            modifier_id=current_user.user_id,
        )
        base_filter_id = self.dao.save_base_feature_filter(base_feature_filter_body)
        return base_filter_id

    def call_wh_script(self, feature_config_id, action_name, hit_sql=""):
        """
        调用数仓脚本
        新增编辑执行get_feature_define
        删除执行get_feature_define和get_feature_result_delete
        @param feature_config_id:
        @param action_name:
        @param hit_sql:
        @return:
        """
        feature_config = self.dao.query_config_and_dim(feature_config_id)
        if not feature_config:
            return

        feature_define = FeatureDefine(
            schema_prefix=self.schema_prefix,
            feature_code=feature_config.feature_code,
            feature_dim=feature_config.feature_dim,
            feature_type=feature_config.feature_type,
            feature_subtype=feature_config.feature_subtype,
            data_type=feature_config.data_type,
            is_value_multiple=feature_config.is_value_multiple,
            calculation_type=feature_config.calculation_type,
            hit_sql=hit_sql,
            is_compatible=feature_config.is_custom
        )
        sql = feature_define.get_feature_define()
        self.dao.exec_sql(sql)
        if action_name == ACTION_DELETE:
            delete_sql = feature_define.get_feature_result_delete()
            self.dao.exec_sql(delete_sql)

    def delete_feature_config(self, query_info: tf.FeatureConfigDeleteReq):
        query_info.modifier = current_user.user_id
        mid_type_feature_hit_sql_dict = {}
        for feature_id in query_info.feature_config_ids:
            validator = LogicProcessor.delete_req_with_basic_validation(feature_id)
            if validator.calculation_type_enum is CalculationTypeEnum.MID:
                hit_sql_list = self.get_hit_sql(feature_id, validator.feature_config_req.feature_name)
                mid_type_feature_hit_sql_dict[validator.feature_config.id] = hit_sql_list[0]  # mid类型hit sql只能为一个

        with transaction_manager():
            feature_config_ids = self.dao.delete_feature_config(query_info) or [-1]
            # 删除特征值和特征筛选条件
            feature_filter_ids = self.dao.clear_feature_value(
                tf.FeatureValueClearReq(feature_config_id=feature_config_ids)) or [-1]
            feature_filter_ids = [filter_id for filter_id in feature_filter_ids if filter_id] or [-1]
            self.dao.clear_feature_filter(tf.FeatureFilterClearReq(feature_filter_ids=feature_filter_ids))
            # 调用数仓脚本
            for feature_config_id in query_info.feature_config_ids:
                self.call_wh_script(
                    feature_config_id,
                    ACTION_DELETE,
                    mid_type_feature_hit_sql_dict.get(feature_config_id, "")
                )
        return True

    def get_hit_sql(self, feature_id, feature_name) -> List[str]:
        hit_sql_list: List[str] = self.dao.get_hit_sql_by_feature_id(feature_id)
        if not hit_sql_list:
            raise ParameterException(f"特征{feature_name} hit sql不存在, 请检查")
        return hit_sql_list

    def query_feature_value_by_config_id(self, query_info: tf.FeatureValueByConfigIdReq):
        feature_values = self.dao.query_feature_value_by_config_id(query_info)
        return feature_values

    def query_feature_filter_by_ids(self, query_info: tf.FeatureFilterByIdsReq):
        feature_filter = self.dao.query_feature_filter_by_ids(query_info)
        return feature_filter

    def feature_dim_to_str(self, feature_type, feature_dim):
        if feature_type == 'kpi':
            kpi_dim = [d.split('.')[-1] for d in feature_dim if d != "''"]
            kpi_dim = '-'.join([d.replace('_sk', '') for d in kpi_dim])
            return kpi_dim
        if feature_type == 'tag':
            tag_dim = '-'.join(feature_dim.split('_'))
            return tag_dim

    def query_feature_dim_by_type(self, query_info: tf.FeatureTypeReq):
        feature_dims = self.dao.query_feature_dim_by_type(query_info)
        new_feature_dims_map = defaultdict(list)
        for dim in feature_dims:
            label = self.feature_dim_to_str(query_info.feature_type, dim)
            value = '@'.join(dim) if query_info.feature_type == 'kpi' else dim
            new_feature_dims_map[label].append(value)
        new_feature_dims = [{'label': label, 'value': value} for label, value in new_feature_dims_map.items()]
        return sorted(new_feature_dims, key=lambda x: x['label'])

    def query_feature_lv_by_type_and_dim(self, query_info: tf.FeatureTypeDimReq):
        query_info.feature_dim = [i.replace("''", '') for i in query_info.feature_dim]
        return self.dao.query_feature_lv_by_type_and_dim(query_info)

    def query_feature_info_by_code_and_dim(self, query_info: tf.FeatureTypeCodeReq):
        feature_info = self.dao.query_feature_info_by_code_and_dim(query_info)
        feature_info.feature_type = query_info.feature_type
        return feature_info

    def list_feature_attribute_value(self, feature_code: str) -> List[tf.FeatureValueLabelValue]:
        label_values: List[tf.FeatureValueLabelValue] = []
        config_values = self.dao.list_feature_config_value_by_code(feature_code)

        if config_values:
            for config_value in config_values:
                label_value = tf.FeatureValueLabelValue(label=config_value, value=config_value)
                label_values.append(label_value)

        return label_values

    def update_feature(self, req: tf.UpdateFeatureReq):
        """
        更新特征

        5.7.0之前特征提交分为左右两部分, 本版本改为整体提交
        Parameters
        ----------
        req

        Returns
        -------

        """
        logic_processor = LogicProcessor.update_req_with_basic_validation(req)

        with transaction_manager():
            updated_config = self._update_config_body(logic_processor.feature_config_req)
            if logic_processor.req_new_value_eggs:
                self._create_value_body(updated_config, logic_processor.req_new_value_eggs)
            if logic_processor.req_old_value_eggs:
                self._update_value_body(logic_processor.req_old_value_eggs)
            if logic_processor.req_remove_value_egg_ids:
                self._delete_value_by_ids(logic_processor.req_remove_value_egg_ids)
            self.call_wh_script(logic_processor.feature_config_req.id, ACTION_UPDATE, logic_processor.value_hit_sql)

        return logic_processor.feature_config_req.id

    def _update_config_body(self, config_body: tf.FeatureConfigEgg):
        updated_config = tf.FeatureConfigEgg(
            id=config_body.id,
            feature_name=config_body.feature_name,
            is_value_multiple=config_body.is_value_multiple,
            feature_subtype=config_body.feature_subtype,
            remark=config_body.remark,
            modifier=current_user.user_id,
            history_data_keep_day=config_body.history_data_keep_day,
        )
        self.dao.update_feature_config(updated_config)
        return config_body

    def _delete_value_by_ids(self, value_ids: List[int]):
        if not value_ids:
            return
        self.dao.delete_feature_value_by_ids(value_ids)
        return True

    def _update_value_body(self, update_eggs: List[tf.FeatureValueEgg]):
        if not update_eggs:
            raise ParameterException("更新特征失败，列表为空")

        for update_egg in update_eggs:
            self.dao.update_feature_value(update_egg)
        return True

    def create_feature(self, req: tf.CreateFeatureReq):
        """
        创建特征

        5.7.0之前特征提交分为左右两部分, 本版本改为整体提交
        Parameters
        ----------
        req

        Returns
        -------

        """
        logic_processor = LogicProcessor.create_req_with_basic_validation(req)
        with transaction_manager():
            new_config = self._create_config_body(logic_processor.feature_config_req)
            # 构造特征作为筛选器选择项
            self.generate_base_feature_filter(new_config)
            self._create_value_body(new_config, logic_processor.req_new_value_eggs)
            # 调用数仓处理逻辑
            self.call_wh_script(new_config.id, ACTION_CREATE, logic_processor.value_hit_sql)
        return new_config.id

    @staticmethod
    def _get_is_auto_sql_value(feature_subtype):
        subtype_enum = FeatureSubtypeEnum(feature_subtype)
        if subtype_enum in (FeatureSubtypeEnum.FEATURE_TEMPLATE, FeatureSubtypeEnum.RULE_AUTO):
            return IsAutoSql.YES.value

        return IsAutoSql.NO.value

    def _create_config_body(self, req_config_body: tf.FeatureConfigEgg):
        config_body = tf.FeatureConfigEgg(**req_config_body.dict())
        config_body.is_auto_sql = self._get_is_auto_sql_value(config_body.feature_subtype)
        # todo 后续引入分类后替换
        config_body.feature_category_id = 2 if config_body.feature_type == FeatureTypeEnum.KPI.value else 3
        type_extension = DataTypeExtension(data_type_enum=config_body.data_type)
        config_body.data_type = type_extension.basic_data_type
        config_body.data_type_extension = type_extension.jsonify_extension_info()
        config_body.creator = current_user.user_id
        config_body.modifier = current_user.user_id
        new_created_permissions = [
            OperatePermissionEnum.READ,
            OperatePermissionEnum.EDIT_1,
            OperatePermissionEnum.DELETE,
            OperatePermissionEnum.APPEND_VALUE,
            OperatePermissionEnum.EDIT_2,
        ]
        config_body.operate_permission = self.permission_helper.give_permission(new_created_permissions)
        feature_config_id = self.dao.save_feature_config(config_body)
        if not feature_config_id:
            raise ParameterException("特征定义创建失败")
        config_body.id = feature_config_id
        return config_body

    @staticmethod
    def _value_req_to_value_egg(subtype_obj, req: List[tf.FeatureValueReq]) -> List[tf.FeatureValueEgg]:
        value_eggs = []
        for index, value_req in enumerate(req):
            value_egg = subtype_obj(value_req)
            value_egg.priority = value_egg.priority if value_egg.priority else index + 1
            value_egg.creator = current_user.user_id
            value_egg.modifier = current_user.user_id
            value_eggs.append(value_egg)

        return value_eggs

    def _create_value_body(self, feature_config: tf.FeatureConfigEgg, value_eggs: List[tf.FeatureValueEgg]):
        for value_egg in value_eggs:
            value_egg.feature_config_id = feature_config.id
            new_created_permissions = [
                OperatePermissionEnum.EDIT_1,
                OperatePermissionEnum.EDIT_2,
                OperatePermissionEnum.DELETE,
                OperatePermissionEnum.READ,
            ]
            value_egg.operate_permission = self.permission_helper.give_permission(new_created_permissions)
            value_id = self.dao.save_feature_value(value_egg)
            if not value_id:
                raise ParameterException("创建特征值失败了")
            value_egg.id = value_id

        return value_eggs

    def update_feature_name_by_code(self, feature_code: str, feature_name: str) -> bool:
        """
        根据特征code更新特征名字
        """
        if not all([feature_name, feature_code]):
            raise ParameterException("参数错误")

        feature = self.common_feature_service.get_feature_by_code(feature_code)
        if not feature:
            raise ParameterException("特征不存在")

        exists = self.dao.feature_config_exist_by_name(feature_name, exclude_feature_id=feature.id)
        if exists:
            raise ParameterException("特征名字已存在")

        self.dao.update_feature_name_by_code(feature_code, feature_name)
        return True


srv = FeatureService()
