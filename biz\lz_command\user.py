#!/usr/bin/env python
# encoding: utf-8
# @Time    : 10/24/18 2:25 AM
# <AUTHOR> wa<PERSON><PERSON><PERSON><PERSON>g

import string
import logging

from random import choice
from flask_script import Command, Option
from flask_script.commands import InvalidCommand
from biz.common.lz_db import db
from biz_module.base.auth.models import User

logging.getLogger("sqlalchemy").setLevel("WARN")


def generate_new_pass():
    return ''.join(choice(string.digits + string.ascii_letters) for _ in range(10))


class CreateUser(Command):
    """ Creates a new user
    """

    option_list = (
        Option('-u', '--username', dest='username', required=True,
               help='User name'),
        Option('-p', '--password', dest='password', required=False,
               help='User password'),
        Option('-r', '--rolename', dest='rolename', required=True,
               help='User role name'),
    )

    def run(self, username, password, rolename):
        # try:
        #     role = Role.filter_by(rolename=rolename).one()
        # except NoResultFound:
        #     raise InvalidCommand('Role with name `%s` not found' % rolename)

        if User.query.filter_by(username=username).first():
            raise InvalidCommand('User `%s` already exists' % username)

        if not password:
            password = generate_new_pass()
            print("New password: {}".format(password))

        u = User(username=username, password=password)
        db.session.add(u)
        db.session.commit()
