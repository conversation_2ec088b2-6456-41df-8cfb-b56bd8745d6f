import logging
from enum import IntEnum
from typing import List, Optional, Any

from linezone_commonserver.services.meta_data.service import MetaDataService
from ourbatis import run_sql_by_id
from pydantic import BaseModel

from biz.api_exception import ParameterException
from biz.utils.tools import PropertyBaseModel

logger = logging.getLogger(__name__)


class TemplateRunSqlParamReq(PropertyBaseModel):
    # 特征值表达式
    feature_value_expression: str
    # 业务视图关系sql
    view_sql: str
    # group by部分 eg. [dim_skc.skc_sk, a.org_sk]
    group_by_with_prefix_expression: str
    group_by_without_prefix_expression: str
    # group by部分 eg. skc_sk, org_sk
    # group_by_without_prefix: Optional[str] = None
    # 一般where 条件
    hit_condition_sql: Optional[str] = None
    # outer new created feature code
    outer_feature_code: Optional[str] = None
    # 业务视图where条件
    view_condition_sql: Optional[str] = None
    # 业务实体表达式 eg. table_a.org_sk as operating_unit_sk
    operating_unit_expression: Optional[str] = None
    # 商品表(dim_skc/dim_skc/dim_spu) 商品模版需要此参数
    product_table_name: Optional[str] = None
    # where条件部分的字段，目前关联特征的模版需要此参数
    hit_condition_sql_field_with_prefix: Optional[List[str]] = None
    value_expression_field: Optional[str]

    # @property
    # def group_by_without_prefix_expression(self):
    #     return ",".join(list(map(lambda x: x.split(".")[1], self.group_by_with_prefix)))
    #
    # @property
    # def group_by_with_prefix_expression(self):
    #     return ",".join(self.group_by_with_prefix)

    @property
    def hit_condition_sql_field_with_prefix_expression(self):
        return ','.join(self.hit_condition_sql_field_with_prefix)


class TemplateInfoReq(BaseModel):
    biz_object_table_names: List[str]
    feature_table_names: List[str]


def get_template_info(template_req: TemplateInfoReq):
    """
    获取参数绑定后的SQL
    @param template_req:
    @return:
    """
    template_obj = FeatureTemplate()
    template_obj.parse_req(template_req)
    return template_obj


def get_template_run_sql(template_id: str, template_param: TemplateRunSqlParamReq):
    if not template_id:
        logger.warning("生成特征模版SQL失败, template_id为空")
        raise ParameterException("生成特征模版SQL失败，参数错误")

    template_run_sql = run_sql_by_id(
        template_id,
        result_sig=str,
        only_sql_snippet=True,
        no_annotate=True,
        **template_param.dict()
    )
    template_run_sql = template_run_sql.replace('\n', ' ')
    return template_run_sql


_BIZ_OBJECT = "biz_object"
_FEATURE = "feature"


class TemplateCombineObject(BaseModel):
    object_code: str
    source_type: str


def get_template_by_combine_object(combine_object: List[TemplateCombineObject]):
    biz_object_table_names = []
    biz_object_codes = [
        biz_object.object_code
        for biz_object in combine_object
        if biz_object.source_type == _BIZ_OBJECT
    ]
    if biz_object_codes:
        biz_objects = MetaDataService().list_biz_object_by_codes(biz_object_codes)
        biz_object_table_names = [biz_object.include_table[0].table_name for biz_object in biz_objects]

    feature_codes = [
        feature.object_code
        for feature in combine_object
        if feature.source_type == _FEATURE
    ]

    if not biz_object_table_names and not feature_codes:
        logger.warning("获取特征模板出错了，biz_object_codes和feature_codes同时为空")
        raise ParameterException("获取特征模板错误")

    # 先根据业务对象判断是否存在款店模板
    # 业务对象中如果不存在款店那么直接无款无店模板走
    template_info = TemplateInfoReq(
        biz_object_table_names=biz_object_table_names,
        feature_table_names=feature_codes
    )

    feature_template = FeatureTemplate()
    feature_template.parse_req(template_info)
    return feature_template


class HitTemplateFlag(IntEnum):
    product_only_template = 1
    org_template = 2
    without_org_and_product_template = 3

    @staticmethod
    def get_title(flag):
        title = {
            HitTemplateFlag.org_template: "组织",
            HitTemplateFlag.product_only_template: "商品",
            HitTemplateFlag.without_org_and_product_template: "无组织无商品"
        }

        return title.get(flag, "未知模板类型")


# 特征模版类
class FeatureTemplate:
    # 维度包含product_dim_prefix说明包含商品，需要使用含有商品维度的模版
    __product_table_names = ("dim_skc", "dim_sku", "dim_spu")
    # 维度包含org_dim_prefix说明包含商品，需要使用含有组织维度的模版
    __org_table = ("dim_org_integration",)

    def __init__(self):
        """
        模版ID
        """
        self.template_id = None
        """
        模版SQL
        """
        self.template_sql = None
        """
        命中模版标示
        """
        self.hit_template_flag = None
        """
        当只命中商品模版时此时的商品表名称
        """
        self.product_table_name = ""

    def parse_req(self, template_req: TemplateInfoReq):

        """
        获取template id
        @param template_req:
        @return:
        """
        if template_req.feature_table_names:
            self.template_id = "without_org_and_product_template"
            self.hit_template_flag = HitTemplateFlag.without_org_and_product_template
            return

        _converted_dimension = self.__convert(template_req.biz_object_table_names)
        product_template_flag = any(dim_prefix in _converted_dimension for dim_prefix in self.__product_table_names)
        org_template_flag = any(dim_prefix in _converted_dimension for dim_prefix in self.__org_table)

        if org_template_flag:
            self.template_id = "org_template"
            self.hit_template_flag = HitTemplateFlag.org_template
        elif product_template_flag:
            self.template_id = "product_only_template"
            self.hit_template_flag = HitTemplateFlag.product_only_template
            self.product_table_name = self.__get_product_table_name_if_only_product(_converted_dimension)
        else:
            self.template_id = "without_org_and_product_template"
            self.hit_template_flag = HitTemplateFlag.without_org_and_product_template

    @staticmethod
    def __convert(dimensions: List[str]) -> str:
        """
        后续优化
        @param dimensions:
        @return:
        """
        dimension_str = ",".join([name for name in dimensions])
        return dimension_str

    def __get_product_table_name_if_only_product(self, dimension_str: str) -> str:
        """
        当命只命中商品模版时候，返回第一个命中的商品表名称
        @param dimension_str: dim_skc.skc_sk, dim_org_integration.org_sk
        @return:
        """
        table_name = ""
        for t_name in self.__product_table_names:
            if t_name in dimension_str:
                table_name = t_name
                break
        return table_name
