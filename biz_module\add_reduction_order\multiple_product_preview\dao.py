from typing import List, Dict

from fastboot.utils.nest import egg
from ourbatis import sql_func

from biz_module.add_reduction_order.multiple_product_preview import typedef as td


@egg
@sql_func(namespace='add_reduction_order_multiple_product_preview')
class MultipleProductPreviewDao(object):

    def query_qrs_page_config(self, params: td.PageConfigReq) -> dict:
        pass

    def get_page_config_by_id(self, page_id) -> dict:
        pass

    def get_page_config_basic_by_id(self, page_id) -> td.PageConfigReq:
        pass

    def add_user_page_config_mapping(self, params: td.PageConfigIdReq) -> int:
        pass

    def update_user_page_config_mapping(self, params: td.PageConfigSaveReq) -> int:
        pass

    def get_user_page_config_mapping(self, params: td.PageConfigReq) -> int:
        pass

    def query_multiple_product_preview_org_filter(self, req: td.MultipleProductPreviewFilterRequest) -> List[Dict]:

        ...

    def query_multiple_product_preview_filter(self, req: td.MultipleProductPreviewFilterRequest) -> List[Dict]:

        ...

    def query_multiple_product_preview_list(self, req: td.MultipleProductPreviewListRequest) -> List[Dict]:

        ...

    def query_multiple_product_preview_detail_kpi(self, req: td.MultipleProductPreviewDetailKpiRequest) -> Dict:

        ...

    def query_multiple_product_preview_by_uuid_list(self, params: str) -> List[Dict]:

        ...

    def update_multiple_product_preview(self, req: td.UpdateMultipleProductPreviewRequest):

        ...

    def save_multiple_product_preview(self, params: str, user_id):

        ...

    def confirm_multiple_product_preview(self, params, user_id, status, day_date):

        ...

    def query_export_data(self, params: td.MultipleProductPreviewListRequest) -> List[Dict]:
        ...

@egg
@sql_func(namespace='add_reduction_order_single_decision')
class SingleDecisionDao(object):

    def query_single_decision_org_filter(self, req: td.SingleDecisionFilterRequest) -> List[Dict]:

        ...


    def query_single_decision_filter(self, req: td.SingleDecisionFilterRequest) -> List[Dict]:

        ...

    def query_single_decision_detail_kpi(self, req: td.SingleDecisionSkcInfoRequest) -> Dict:
        ...

    def query_single_decision_detail_info(self, req: td.SingleDecisionSkcInfoRequest) -> Dict:

        ...

    def update_single_decision_detail_info(self, human_decision_qty, day_date, org_sk, skc_sk, user_id):

        ...

    def query_refer_skc_info(self, req: td.SingleDecisionSkcInfoRequest) -> List[Dict]:

        ...

    def get_page_config_by_id(self, page_id) -> dict:
        pass

    def get_page_config_basic_by_id(self, page_id) -> td.PageConfigReq:
        pass

    def add_user_page_config_mapping(self, params: td.PageConfigIdReq) -> int:
        pass

    def update_user_page_config_mapping(self, params: td.PageConfigSaveReq) -> int:
        pass

    def get_user_page_config_mapping(self, params: td.PageConfigReq) -> int:
        pass




    def query_multiple_product_preview_list(self, req: td.MultipleProductPreviewListRequest) -> List[Dict]:

        ...

    def query_multiple_product_preview_detail_kpi(self, req: td.MultipleProductPreviewDetailKpiRequest) -> dict:

        ...

    def query_multiple_product_preview_by_uuid(self, day_date, org_sk, skc_sk) -> Dict:

        ...

    def update_multiple_product_preview(self, req: td.UpdateMultipleProductPreviewRequest):

        ...

    def confirm_multiple_product_preview(self, params, user_id, status, day_date):

        ...

