<?xml version="1.0"?>
<mapper namespace="default">
    <sql id="feature_workbench_list_count_condition">
        where is_deleted = 0
        <if test="operate_permission">
           and ${operate_permission}
        </if>
        <if test="feature_name">
            and feature_name like '%${feature_name}%'
        </if>
        <if test="feature_type">
            and feature_type = #{feature_type}
        </if>
        <if test="data_type">
            and data_type = #{data_type}
        </if>
    </sql>
    <select id="query_feature_list_by_filter">
        select
        *
        from biz.base_feature_config
        <include refid="feature_workbench_list_count_condition"></include>
        order by create_time desc, modify_time desc
        <if test="page_size and page_no">
            limit ${page_size} offset ${page_no}
        </if>
    </select>

    <select id="count_feature_by_filter">
        select count(*)
        from biz.base_feature_config
        <include refid="feature_workbench_list_count_condition"></include>
    </select>

    <select id="query_feature_config_by_id">
        select * from biz.base_feature_config
        where id=${feature_config_id}
    </select>

    <select id="query_feature_config_by_name">
        select * from biz.base_feature_config
        where feature_name=#{feature_name}
    </select>

    <insert id="save_feature_config">
        insert into biz.base_feature_config (
            biz_view_id, 
            biz_view_dimension_id, 
            --biz_view_filter_id, 
            feature_code, 
            feature_name, 
            feature_type, 
            feature_subtype, 
            feature_category_id, 
            calculation_type, 
            data_type, 
            is_custom, 
            is_auto_sql, 
            is_value_multiple, 
            is_select, 
            fill_value, 
            calculation_cycle, 
            storage_type, 
            --belong_org_sk, 
            --public_org_sk, 
            is_active, 
            is_deleted, 
            creator, 
            modifier, 
            create_time, 
            modify_time,
            remark,
            data_type_extension,
            history_data_keep_day,
            operate_permission
        ) values (
            ${biz_view_id}, 
            ${biz_view_dimension_id},
            #{feature_code}, 
            #{feature_name}, 
            #{feature_type}, 
            #{feature_subtype},
            ${feature_category_id}, 
            #{calculation_type}, 
            #{data_type}, 
            ${is_custom}, 
            ${is_auto_sql}, 
            ${is_value_multiple}, 
            ${is_select}, 
            #{fill_value}, 
            #{calculation_cycle}, 
            #{storage_type}, 
            --belong_org_sk, 
            --public_org_sk, 
            ${is_active}, 
            ${is_deleted}, 
            ${creator}, 
            ${modifier}, 
            now(), 
            now(),
            #{remark},
            #{data_type_extension},
            #{history_data_keep_day},
            #{operate_permission}
        ) RETURNING id;
    </insert>

    <select id="feature_config_exist_by_name">
        select 1 from biz.base_feature_config
        where feature_name = #{feature_name}
            and is_deleted = 0
            <if test="exclude_feature_id">
                and id != #{exclude_feature_id}
            </if>
    </select>

    <update id="update_feature_code">
        update biz.base_feature_config set
            feature_code = #{feature_code}
        where id=${id}
        RETURNING feature_code;
    </update>

    <update id="update_feature_config">
        update biz.base_feature_config set
            <if test="feature_name">
                feature_name=#{feature_name},
            </if>
            <if test="feature_subtype">
                feature_subtype=#{feature_subtype},
            </if>
            <if test="feature_category_id">
                feature_category_id=#{feature_category_id},
            </if>
            <if test="calculation_type">
                calculation_type=#{calculation_type},
            </if>
            <if test="data_type">
                data_type=#{data_type},
            </if>
            <if test="fill_value">
                fill_value=#{fill_value},
            </if>
            <if test="history_data_keep_day">
                history_data_keep_day=#{history_data_keep_day},
            </if>
            remark=#{remark},
            is_value_multiple=#{is_value_multiple},
            modifier=#{modifier},
            modify_time=now()
        where id=#{id}
        RETURNING feature_code;
    </update>

    <update id="update_feature_name_by_code">
       update biz.base_feature_config set
            feature_name=#{feature_name},
            modify_time=now()
        where feature_code=#{feature_code}
        RETURNING id;
    </update>

    <mix id="save_base_feature_filter">
        update biz.base_feature_filter
        set input_parameter_editor=#{input_parameter_editor},
            applicable_operator=#{applicable_operator},
            value_editor=#{value_editor},
            modify_time = now(),
            modifier_id = #{modifier_id}
        where feature_config_id=${feature_config_id}
            and is_deleted = 0
        RETURNING id
        ;
        with query as (
            select 1 from biz.base_feature_filter where feature_config_id=${feature_config_id}
        )
        insert into biz.base_feature_filter(
            feature_config_id,
            input_parameter_editor, 
            applicable_operator,
            value_editor,
            creator_id,
            modifier_id,
            create_time,
            modify_time,
            is_deleted)
        select 
            ${feature_config_id},
            #{input_parameter_editor},
            #{applicable_operator},
            #{value_editor},
            ${creator_id},
            ${modifier_id},
            now(),
            now(),
            0
        where not exists( select 1 from query )
        returning id;
    </mix>

    <update id="delete_feature_config">
        update biz.base_feature_config set
            is_deleted = 1,
            modifier=${modifier},
            modify_time=now()
        where id in ${feature_config_ids} 
        RETURNING id;
    </update>

    <select id="query_feature_value_by_config_id">
        select * from biz.base_feature_config_value
        where feature_config_id = ${feature_config_id}
            and is_deleted = 0
        order by priority
    </select>

    <select id="query_feature_filter_by_ids">
        select * from biz.base_filter_instance
        where id in ${inst_filter_ids} and is_deleted = 0
    </select>

    <insert id="save_feature_filter">
        insert into biz.base_filter_instance (
            condition_set,
            hit_condition_sql,
            is_deleted,
            creator,
            modifier,
            create_time,
            modify_time
        ) values (
            #{condition_set},
            #{hit_condition_sql},
            0,
            ${creator},
            ${modifier},
            now(),
            now()
        ) RETURNING *;
    </insert>

    <insert id="save_feature_value">
        insert into biz.base_feature_config_value (
            feature_config_id,
            feature_value,
            priority,
            filter_instance_id,
            hit_sql,
            source_feature,
            template_id,
            template_params,
            is_deleted,
            creator,
            modifier,
            create_time,
            modify_time,
            operate_permission,
            is_runable
        ) values (
            ${feature_config_id},
            #{feature_value},
            ${priority},
            #{filter_instance_id},
            #{hit_sql},
            #{source_feature},
            #{template_id},
            #{template_params},
            0,
            ${creator},
            ${modifier},
            now(),
            now(),
            #{operate_permission},
            #{is_runable}
        ) RETURNING id;
    </insert>

    <update id="update_feature_filter">
        update biz.base_filter_instance set
            condition_set=#{condition_set},
            hit_condition_sql=#{hit_condition_sql},
            modifier=${modifier},
            modify_time=now()
        where id=${id} RETURNING *;
    </update>

    <update id="update_feature_value">
        update biz.base_feature_config_value set
            feature_value=#{feature_value},
            priority=${priority},
            filter_instance_id=#{filter_instance_id},
            hit_sql=#{hit_sql},
            source_feature=#{source_feature},
            template_id = #{template_id},
            template_params = #{template_params},
            modifier=${modifier},
            modify_time=now(),
            is_runable=1
        where id=${id}
        RETURNING id;
    </update>

    <insert id="upset_feature_value">
        update biz.base_feature_config_value set
            hit_sql=#{hit_sql},
            modifier=${modifier},
            modify_time=now()
        where feature_config_id=${feature_config_id}
        RETURNING id;

        with query as (
            select 1 from biz.base_feature_config_value where feature_config_id=${feature_config_id} and is_deleted = 0
        ) 
        insert into biz.base_feature_config_value (
            feature_config_id,
            hit_sql,
            priority,
            is_deleted,
            creator,
            modifier,
            create_time,
            modify_time
        ) select  
            ${feature_config_id},
            #{hit_sql},
            ${priority},
            0,
            ${creator},
            ${modifier},
            now(),
            now()
        where not exists( select 1 from query )
        RETURNING id;
    </insert>
    <select id="get_hit_sql_by_feature_id">
        select hit_sql
        from biz.base_feature_config_value
        where feature_config_id = #{feature_id}
        and is_deleted = 0
    </select>
    <delete id="clear_feature_value">
        update biz.base_feature_config_value set
            is_deleted = 1,
            modify_time = now()
        where feature_config_id in ${feature_config_id}
            <if test="feature_value_ids">
                and id not in ${feature_value_ids}
            </if>
        RETURNING filter_instance_id
    </delete>

    <delete id="delete_feature_value_by_ids">
        update biz.base_feature_config_value set
            is_deleted = 1,
            modify_time = now()
        where
             id in ${value_ids}
    </delete>

    <delete id="clear_feature_filter">
        update biz.base_filter_instance set
            is_deleted = 1,
            modify_time = now()
        where id in ${feature_filter_ids}
    </delete>

    <select id="query_feature_dim_by_type">
        <if test="feature_type == 'kpi'">
            select distinct kpi_dim from dm.dim_kpi_define
        </if>
        <if test="feature_type == 'tag'">
            select distinct object_type from edw.base_tag 
        </if>
    </select>

    <select id="query_feature_lv_by_type_and_dim">
        <if test="feature_type == 'kpi'">
            select
                kpi_name as label,
                kpi_code as value
            from dm.dim_kpi_define
            where REPLACE(array_to_string(kpi_dim, '@'), '''''', '') in ${feature_dim}
                and calculation_flag in (1, 3)
            order by convert_to(kpi_name,'GBK');
        </if>
        <if test="feature_type == 'tag'">
            select 
                tag_name as label,
                tag_code as value 
            from edw.base_tag
            where object_type in ${feature_dim}
            order by convert_to(tag_name,'GBK');
        </if>
    </select>

    <select id="query_feature_info_by_code_and_dim">
        <if test="feature_type == 'kpi'">
            select 
                kpi_name as feature_name,
                kpi_code as feature_code,
                kpi_dim as feature_dim,
                data_type as data_type
            from dm.dim_kpi_define 
            where kpi_code = #{feature_code}
        </if>
        <if test="feature_type == 'tag'">
            select 
                tag_name as feature_name,
                tag_code as feature_code,
                object_type as feature_dim,
                null as data_type
            from edw.base_tag
            where tag_code = #{feature_code}
        </if>
    </select>

    <select id="list_feature_config_value_by_code">
        with feature_info as (
            select id as feature_id
            from biz.base_feature_config
            where feature_code = #{feature_code}
            and is_deleted = 0
            and is_active = 1
        )
        select coalesce(feature_value,'') as feature_value
        from biz.base_feature_config_value fcv
        inner join feature_info fi
        on fcv.feature_config_id = fi.feature_id
        and fcv.is_deleted = 0
    </select>

    <select id="query_config_and_dim">
        select
            a.id,
            a.feature_code,
            feature_name,
            b.group_by as feature_dim,
            a.feature_type,
            a.feature_subtype,
            a.data_type,
            a.is_value_multiple,
            calculation_type,
            a.is_custom
        from biz.base_feature_config a 
        inner join biz.base_biz_view_dimension b on a.biz_view_dimension_id = b.dimension_id
        where a.id = ${feature_config_id}
    </select>

    <update id="exec_sql">
        ${exec_sql}
    </update>

    <select id="query_feature_value_by_id">
        select * from biz.base_feature_config_value where id = #{feature_value_id} and is_deleted = 0
    </select>

    <select id="has_feature_used_by_other_feature_value">
        select exists(select id from biz.base_feature_config_value where source_feature @> #{feature_codes} and is_deleted = 0)
    </select>

    <select id="has_feature_used_by_feature_mapping">
        select exists(select 1
              from biz.base_feature_mapping_rule
              where is_deleted = 0
                and object_type = 'dynamic_object'
                and (rule_data::text like #{feature_code} or
                     rule_data::text like #{feature_id}))
    </select>

    <select id="has_feature_used_by_biz_view">
        select exists(select id from biz.base_biz_view where view_code like #{feature_code} and is_deleted = 0)
    </select>

    <select id="query_filter_feature_code">
        select tag_code from biz.base_page_filter_item where tag_code = #{code}
    </select>

    <select id="find_common_filter_used_feature">
        select filter_name from biz.base_filter_detail
        where hit_condition_sql like #{feature_code} and is_deleted = 0
        limit 1
    </select>

    <select id="has_feature_used_by_rule_instance">
        select exists(
            select id from biz.base_rule_instance
            where (hit_condition_sql like #{feature_code} or hit_execute_action::text like #{feature_code})
            and is_deleted = 0
            limit 1
        )
    </select>
    <select id="has_feature_used_by_qr_store_group">
        select exists(
                select 1
                from biz.base_parameter_value,
                jsonb_to_recordset(parameter_value -> 'parameter_value') as item(store_tag text, tag_priority int)
                where parameter_code = 'qrs.store_group'
                and is_deleted = 0
                and store_tag = #{feature_code}
        )
    </select>

    <select id="get_parameter_name_with_sub_value_used_feature">
            select
            define.parameter_name
            from biz.base_parameter_define define
            inner join biz.base_parameter_sub_value sub_value_table
            on define.parameter_code = sub_value_table.parameter_code
            and define.is_deleted = 0 and sub_value_table.is_deleted = 0
            where sub_value::text like #{feature_code}
            limit 1
    </select>

    <select id="get_parameter_name_with_value_used_feature">
        with valid_parameter as (
            select parameter_code, parameter_name
            from biz.base_parameter_define
            where is_deleted = 0
        )
        select parameter_name
        from biz.base_parameter_value
        inner join valid_parameter on base_parameter_value.parameter_code = valid_parameter.parameter_code
        where is_deleted = 0
        and parameter_value::text like #{feature_code}
        limit 1
    </select>

    <select id="get_stock_up_config_used_feature">
        select config_name
        from sys.page_config
        where content::text like #{feature_code}
            and is_deleted=0 and module_name = 'stock_up'
    </select>

    <select id="get_all_feature_codes">
        select feature_code from biz.base_feature_config
        where is_deleted=0;
    </select>

    <update id="update_feature_config_operate_permission">
        update biz.base_feature_config set
            operate_permission = #{operate_permission}
        where id = #{feature_id}
    </update>

    <update id="update_config_value_operate_permission">
        update biz.base_feature_config_value set
            operate_permission = #{operate_permission}
        where feature_config_id = #{feature_id}
    </update>

    <update id="update_config_value_runable_status">
        update biz.base_feature_config_value set
            is_runable = #{is_runable}
        where feature_config_id = #{feature_id}
    </update>
</mapper>