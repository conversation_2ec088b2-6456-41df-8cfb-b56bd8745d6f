#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2022/10/20 17:01
# <AUTHOR> XXX
# @Site : 
# @File : model.py
# @Software: PyCharm
import io
from collections import defaultdict, OrderedDict
from dataclasses import dataclass, field, fields
from typing import Dict, Optional, List

import pandas as pd

from biz.utils import tools
from biz_module.base.component_excel.constants import SourceEnum


@dataclass
class ExcelColumn:
    id: int
    sheet_id: int
    name: str  # 原始列名
    code: str  # 编码
    alias: str  # 别名 用于用户动态修改
    annotation: Optional[str]  # 备注，在列名填写列说明信息
    source: Optional[str]  # 来源， 当前雪花基本基于业务，可扩展如， 特征， 属性，需要配合实现相应动态字段的数据组合服务
    idx: int  # 顺序
    value_type: str  # 值类型 text number / string number
    value_fmt: str  # 格式化， 当前只有对数字的格式 如 ##0.00 或 % etc.
    editable: int  # 是否可编辑 ， 实际雪花中 table-header 的 editable_fields 代替了此列功能，
    fold: int  # 折叠，
    hidden: int  # 隐藏
    display: int  # 是否显示
    is_size: int  # 是否尺码 未使用， 层级定义使用 multi_level 指定列表头层级
    width: float  # 宽度
    size_width: Optional[str]  # 尺码宽度 未使用
    size_fold: int  # 尺码默认折叠
    is_import: int  # 是否导入必须列  背景色修改， 缺少影响对应页面的导入逻辑
    parent_code: str  # 父级code 此列不为空 则 multi_level 大于0，是否虚拟， 是否动态 常常与有无父级有关系
    is_dynamic: int  # 是否动态 例如通过动态的日期 透视数据组，来增加对应列，合并对应行显示
    multi_level: int  # 多级 默认 0 ， 存在上级则依次递增， 通用逻辑当前实现2级
    is_virtual: int  # 是否需求， 用于兼容 组合共性列使用，当前未使用
    template_col: int  # 是否模版列， 用于控制模版应当展示的列
    header_fmt: Optional[Dict]  # 表头指定格式
    row_fmt: Optional[Dict]  # 行格式
    other: Optional[Dict]  # 其他扩展
    alias_disabled: int

    letter: Optional[str] = field(default='')

    _col_map: Optional[Dict] = field(default_factory=dict)

    children: Optional[List] = field(default_factory=list)

    def excel_map_val(self, v):
        if self.other.get('excel_value_mapping'):
            map_ = {v: k for k, v in self.other.get('excel_value_mapping', {}).items()}
            return map_.get(v)
        return v

    def excel_column_value_map(self):
        if self._col_map:
            return self._col_map
        if self.other.get('excel_value_mapping'):
            self._col_map = {k: v for k, v in self.other.get('excel_value_mapping', {}).items()}
            return self._col_map
        return self._col_map

    def edit_dict(self):
        res = {
            'column_id': self.id,
            'id': self.id,
            'serial': self.idx,
            'idx': self.idx,
            'ori_name': self.name,
            'en_name': self.code,
            'cn_name': self.alias,
            'width': self.width,
            'size_width': self.size_width,
            'editable': self.editable,
            'enable': self.display,
            'fold': self.fold,
            'hidden': self.hidden,
            'display': self.display,
            'name': self.name,
            'alias': self.alias,
            'code': self.code,
            "parent_code": self.parent_code,
            "multi_level": self.multi_level,
            "is_dynamic": self.is_dynamic,
            "is_virtual": self.is_virtual,
            "value_type": self.value_type,
            "value_fmt": self.value_fmt,
            "alias_disabled": self.alias_disabled,
        }

        return res


@dataclass
class ExcelColumnComp:
    id: int
    sheet_id: int
    name: str  # 原始列名
    code: str  # 编码
    alias: str  # 别名 用于用户动态修改
    annotation: Optional[str]  # 备注，在列名填写列说明信息
    source: Optional[str]  # 来源， 当前雪花基本基于业务，可扩展如， 特征， 属性，需要配合实现相应动态字段的数据组合服务
    idx: int  # 顺序
    value_type: str  # 值类型 text number / string number
    value_fmt: str  # 格式化， 当前只有对数字的格式 如 ##0.00 或 % etc.
    editable: int  # 是否可编辑 ， 实际雪花中 table-header 的 editable_fields 代替了此列功能，
    fold: int  # 折叠，
    hidden: int  # 隐藏
    display: int  # 是否显示
    is_size: int  # 是否尺码 未使用， 层级定义使用 multi_level 指定列表头层级
    width: float  # 宽度
    size_width: Optional[str]  # 尺码宽度 未使用
    size_fold: int  # 尺码默认折叠
    is_import: int  # 是否导入必须列  背景色修改， 缺少影响对应页面的导入逻辑
    parent_code: str  # 父级code 此列不为空 则 multi_level 大于0，是否虚拟， 是否动态 常常与有无父级有关系
    is_dynamic: int  # 是否动态 例如通过动态的日期 透视数据组，来增加对应列，合并对应行显示
    multi_level: int  # 多级 默认 0 ， 存在上级则依次递增， 通用逻辑当前实现2级
    is_virtual: int  # 是否需求， 用于兼容 组合共性列使用，当前未使用
    template_col: int  # 是否模版列， 用于控制模版应当展示的列
    header_fmt: Optional[Dict]  # 表头指定格式
    row_fmt: Optional[Dict]  # 行格式
    other: Optional[Dict]  # 其他扩展
    alias_disabled: int

    letter: Optional[str] = field(default='')

    _col_map: Optional[Dict] = field(default_factory=dict)

    def __init__(self,
                 col_id,
                 ws_id,
                 col_code,
                 col_name,
                 col_alias,
                 annotation,
                 source,
                 position,
                 value_type,
                 value_fmt,
                 value_src,
                 value_hook,
                 editable,
                 fold,
                 hidden,
                 display,
                 width,
                 width_unit,
                 template_col,
                 col_props,
                 **kwargs
                 ):
        self.id = col_id
        self.sheet_id = ws_id
        self.code = col_code
        self.name = col_name
        self.alias = col_alias
        self.annotation = annotation
        self.source = 'biz'
        self.idx = position
        self.value_type = value_type
        self.value_fmt = value_fmt
        self.editable = editable
        self.fold = fold
        self.hidden = hidden
        self.display = display
        self.is_size = 0
        self.width = float(width)/8
        self.size_width = None
        self.size_fold = 1
        self.is_import = editable
        self.parent_code = ''
        self.is_dynamic = kwargs.get('is_dynamic')
        self.multi_level = 0
        self.is_virtual = 0
        self.template_col = template_col
        self.header_fmt = {}
        self.row_fmt = {}
        self.other = col_props
        self.alias_disabled = 0
        self.parent_code = kwargs.get('parent_code')
        self.children = [ExcelColumnComp(**j) for j in kwargs.get('children', [])]
        self.data_source_only = kwargs.get('data_source_only')
        self.pivot_source = kwargs.get('pivot_source')
        self.unique_key = kwargs.get('unique_key')
        self.dynamic_source = kwargs.get('dynamic_source')
        self.level = kwargs.get('level')
        self.leaf = kwargs.get('leaf', True)

    def excel_map_val(self, v):
        if self.other.get('excel_value_mapping'):
            map_ = {v: k for k, v in self.other.get('excel_value_mapping', {}).items()}
            return map_.get(v)
        return v

    def excel_column_value_map(self):
        if self._col_map:
            return self._col_map
        if self.other.get('excel_value_mapping'):
            self._col_map = {k: v for k, v in self.other.get('excel_value_mapping', {}).items()}
            return self._col_map
        return self._col_map

    def edit_dict(self):
        res = {
            'column_id': self.id,
            'id': self.id,
            'serial': self.idx,
            'idx': self.idx,
            'ori_name': self.name,
            'en_name': self.code,
            'cn_name': self.alias,
            'width': self.width,
            'size_width': self.size_width,
            'editable': self.editable,
            'enable': self.display,
            'fold': self.fold,
            'hidden': self.hidden,
            'display': self.display,
            'name': self.name,
            'alias': self.alias,
            'code': self.code,
            "parent_code": self.parent_code,
            "multi_level": self.multi_level,
            "is_dynamic": self.is_dynamic,
            "is_virtual": self.is_virtual,
            "value_type": self.value_type,
            "value_fmt": self.value_fmt,
            "alias_disabled": self.alias_disabled,
        }

        return res

@dataclass
class ExcelSheetComp:
    id: int
    name: str
    book_id: int
    alias: str
    freeze_col: str
    freeze_row: str
    contain_size: int
    query_type: str
    func_code: str
    idx: int
    display: int
    other: Optional[Dict]
    order_by: Optional[Dict]
    # order_by.get('order_by')
    sort_columns: Optional[List[Dict]]
    # structure like: {col_alias: col_code}
    columns: List[ExcelColumnComp] = field(default_factory=list)

    def __init__(self,
                 ws_id,
                 comp_id,
                 ws_code,
                 ws_name,
                 ws_alias,
                 ws_props,
                 position,
                 hidden,
                 description, excel_columns):
        self.id = ws_id
        self.name = ws_name
        self.book_id = comp_id
        self.alias = ws_alias
        self.freeze_col = ws_props.get('freeze_col')
        self.freeze_row = ws_props.get('freeze_row')
        self.idx = position
        self.contain_size = 0
        self.query_type = ''
        self.func_code = ''
        self.display = 1 if not hidden else 0
        self.other = ws_props
        self.order_by = ws_props.get('order_by')
        self.columns = [ExcelColumnComp(**i) for i in excel_columns]
        self.sort_columns = ws_props.get('order_by')




    @property
    def sort_by(self):
        order_by = self.order_by.get('order_by')
        res = [i.get('field') for i in order_by]
        return res

    @property
    def sort_by_gbk(self):
        order_by = self.order_by.get('order_by')
        res = [f"{i.get('field')}_gbk" if i.get('value_type') == 'string' else i.get('field') for i in order_by]
        return res

    @property
    def sort_col(self):
        order_by = self.order_by.get('order_by')
        return order_by

    @property
    def ascending(self):
        order_by = self.order_by.get('order_by')
        res = [i.get('ascending') != 'desc' for i in order_by]
        return res

    @property
    def columns_dict(self):
        return {col.alias: col.code for col in self.columns if col.is_import and not col.multi_level}

    @property
    def level_columns_dict(self):
        return {col.alias: col.code for col in self.columns if col.is_import and col.multi_level}

    @property
    def dynamic_first_level_columns_dict(self):
        res = dict()
        for col in self.column_tree:
            if col.get("is_import"):
                res[col.get("alias")] = col.get("code")
        return res

    @property
    def dynamic_second_level_columns_dict(self):
        res = dict()
        for col in self.column_tree:
            if not col.get("children"):
                continue
            for col_2 in col.get("children"):
                if col_2.get("is_import"):
                    res[col_2.get("alias")] = col_2.get("code")
        return res

    @property
    def dynamic_third_level_columns_dict(self):
        res = dict()
        for col in self.column_tree:
            if not col.get("children"):
                continue
            for col_2 in col.get("children"):
                if not col_2.get("children"):
                    continue
                for col_3 in col_2.get("children"):
                    if col_3.get("is_import"):
                        res[col_3.get("alias")] = col_3.get("code")
        return res

    @property
    def dynamic_forth_level_columns_dict(self):

        res = dict()
        for col in self.column_tree:
            if not col.get("children"):
                continue
            for col_2 in col.get("children"):
                if not col_2.get("children"):
                    continue
                for col_3 in col_2.get("children"):
                    if not col_3.get("children"):
                        continue
                    for col_4 in col_3.get("children"):

                        if col_4.get("is_import"):
                            res[col_4.get("alias")] = col_4.get("code")
        return res

    def mean_source_columns(self, source: SourceEnum = SourceEnum.dim_skc):
        return [col for col in self.columns if col.source == source]

    def mean_source_column_codes(self, source: SourceEnum = SourceEnum.dim_skc):
        return [col.code for col in self.columns if col.source == source]

    def __post_init__(self):
        excel_col_map = {i.code: i for i in self.columns}
        sort_columns = self.order_by.get('order_by', [])
        for i in sort_columns:
            col = excel_col_map.get(i.get('field'))
            if col:
                i['value_type'] = col.value_type
                i['source'] = col.source
        self.sort_columns = sort_columns

        try:
            tree, _ = tools.get_tree(pd.DataFrame(self.columns), "code", "parent_code")

            self.column_tree = tree
        except Exception as e:
            # 如果列重复了实际会有问题，这里如果不报错，容易发现不了问题
            raise e


@dataclass
class ExcelSheet:
    id: int
    name: str
    book_id: int
    alias: str
    freeze_col: str
    freeze_row: str
    contain_size: int
    query_type: str
    func_code: str
    idx: int
    display: int
    other: Optional[Dict]
    order_by: Optional[Dict]
    # order_by.get('order_by')
    sort_columns: Optional[List[Dict]]
    # structure like: {col_alias: col_code}
    columns: List[ExcelColumn] = field(default_factory=list)

    column_tree: List[ExcelColumn] = field(default_factory=list)

    @property
    def sort_by(self):
        order_by = self.order_by.get('order_by')
        res = [i.get('field') for i in order_by]
        return res

    @property
    def sort_by_gbk(self):
        order_by = self.order_by.get('order_by')
        res = [f"{i.get('field')}_gbk" if i.get('value_type') == 'string' else i.get('field') for i in order_by]
        return res

    @property
    def sort_col(self):
        order_by = self.order_by.get('order_by')
        return order_by

    @property
    def ascending(self):
        order_by = self.order_by.get('order_by')
        res = [i.get('ascending') != 'desc' for i in order_by]
        return res

    @property
    def columns_dict(self):
        return {col.alias: col.code for col in self.columns if col.is_import and not col.multi_level}

    @property
    def level_columns_dict(self):
        return {col.alias: col.code for col in self.columns if col.is_import and col.multi_level}

    @property
    def dynamic_first_level_columns_dict(self):
        res = dict()
        for col in self.column_tree:
            if col.get("is_import"):
                res[col.get("alias")] = col.get("code")
        return res

    @property
    def dynamic_second_level_columns_dict(self):
        res = dict()
        for col in self.column_tree:
            if not col.get("children"):
                continue
            for col_2 in col.get("children"):
                if col_2.get("is_import"):
                    res[col_2.get("alias")] = col_2.get("code")
        return res

    @property
    def dynamic_third_level_columns_dict(self):
        res = dict()
        for col in self.column_tree:
            if not col.get("children"):
                continue
            for col_2 in col.get("children"):
                if not col_2.get("children"):
                    continue
                for col_3 in col_2.get("children"):
                    if col_3.get("is_import"):
                        res[col_3.get("alias")] = col_3.get("code")
        return res

    @property
    def dynamic_forth_level_columns_dict(self):

        res = dict()
        for col in self.column_tree:
            if not col.get("children"):
                continue
            for col_2 in col.get("children"):
                if not col_2.get("children"):
                    continue
                for col_3 in col_2.get("children"):
                    if not col_3.get("children"):
                        continue
                    for col_4 in col_3.get("children"):

                        if col_4.get("is_import"):
                            res[col_4.get("alias")] = col_4.get("code")
        return res

    def mean_source_columns(self, source: SourceEnum = SourceEnum.dim_skc):
        return [col for col in self.columns if col.source == source]

    def mean_source_column_codes(self, source: SourceEnum = SourceEnum.dim_skc):
        return [col.code for col in self.columns if col.source == source]

    def __post_init__(self):
        excel_col_map = {i.code: i for i in self.columns}
        sort_columns = self.order_by.get('order_by', [])
        for i in sort_columns:
            col = excel_col_map.get(i.get('field'))
            if col:
                i['value_type'] = col.value_type
                i['source'] = col.source
        self.sort_columns = sort_columns

        try:
            tree, _ = tools.get_tree(pd.DataFrame(self.columns), "code", "parent_code")

            self.column_tree = tree
        except Exception as e:
            # 如果列重复了实际会有问题，这里如果不报错，容易发现不了问题
            raise e


@dataclass
class ExcelComp:
    book_id: int
    book_name: str
    options: Dict = field(default_factory=defaultdict)
    sheets: List[ExcelSheetComp] = field(default_factory=list)

    DEFAULT_OPTIONS = {'constant_memory': True}

    def __post_init__(self):
        self.options = self.DEFAULT_OPTIONS


@dataclass
class ExcelConfig:
    book_id: int
    book_name: str
    options: Dict = field(default_factory=defaultdict)
    sheets: List[ExcelSheet] = field(default_factory=list)

    DEFAULT_OPTIONS = {'constant_memory': True}

    def __post_init__(self):
        self.options = self.DEFAULT_OPTIONS


@dataclass
class ExcelReader:
    file: io.BytesIO
    columns: Optional[Dict]
    sheet_names: List[str]


@dataclass
class DynamicExcelColumn:
    """

    """
    name: str
    code: str
    alias: str
    annotation: Optional[str]
    value_type: str
    value_fmt: str
    fold: int
    hidden: int
    display: int
    width: int
    is_import: int
    template_col: int
    header_fmt: Optional[Dict]
    row_fmt: Optional[Dict]

    letter: Optional[str] = field(default='')
    last_group_letter: Optional[str] = field(default='')
    children: Optional[List] = field(default_factory=list)
