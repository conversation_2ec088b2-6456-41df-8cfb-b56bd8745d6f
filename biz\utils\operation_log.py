from flask_login import current_user
from flask_babel import lazy_gettext as _
from biz.common.lz_db import db
# from biz.exception import OptLogTypeException
from biz.utils.client_ip import get_client_ip
from biz.biz_module.base.opt_log.models import Opt<PERSON><PERSON>


def set_opt_log(action, description):
    if current_user.is_authenticated:
        username = current_user.username
    else:
        username = str(_("Not log in"))

    if action not in [_("insert"), _("delete"), _("update")]:
        raise Exception("The action must be in [_('insert'), _('delete'), _('update')]")

    ip, __ = get_client_ip()
    mac = ''

    opt_log = OptLog(username=username, action=str(action), description=str(description), ip=ip, mac=mac)

    db.session.add(opt_log)
    db.session.commit()
