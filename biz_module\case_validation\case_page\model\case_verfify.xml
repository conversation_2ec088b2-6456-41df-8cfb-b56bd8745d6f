<?xml version="1.0"?>
<mapper namespace="default">
    <insert id="verify_case_add_screen">
        set local optimizer='off';
        insert into biz.base_page_filter_page_config
        (page_name, filter_code, group_code, is_enable, seq)
        values
        <foreach collection="conf" item="item" separator=",">
        (#{item.page_name}, #{item.filter_code}, #{item.group_code}, #{item.is_enable}, #{item.seq})
        </foreach>
    </insert>
    <delete id="verify_case_del_screen">
        set local optimizer='off';
        delete from biz.base_page_filter_page_config
        where page_name=#{page_name}
    </delete>
    <select id="get_biz_verify_day_date">
        select day_date from dm.dim_org_integration limit 1
    </select>
    <select id="get_biz_verify_tree">
        with s0 as (
        select case_id, max(run_id) max_run_id from test.biz_user_case_execute_record bu
        where status!= -99
        group by case_id
        )
        , s1 as (
        select bu.case_id, max_run_id, pass_flag from test.biz_user_case_execute_record bu
        inner join s0 on s0.case_id=bu.case_id and s0.max_run_id=bu.run_id
        where status!= -99
        )
        select "group_id", group_name, bc.priority priority,
        array_agg(json_build_object(
        'case_id', bv.case_id, 'case_name', case_name, 'priority', bv.priority, 'dimension', dimension, 'is_merge', bv.params->>'is_merge', 'pass_flag', pass_flag
        , 'max_run_id', coalesce(max_run_id, -1))) sub
        from s1
        right join test.biz_verification_assert_record bv
        on s1.case_id=bv.case_id
        inner join test.biz_case_group_record bc
        on bv.case_id=any(bc.user_cases)
        where (bc.org_sk=any(#{tree_org_sk}) or bc.public_org_sk=any(#{public_org_sk}))
        and bc.is_active=1 and bv.is_active=1
        and bc.is_deleted=0 and bv.is_deleted=0
        and bc.is_exception_show=1
        group by "group_id", group_name, bc.priority
    </select>
    <select id="get_biz_verify_tree_detail">
        with org_filter as (select unnest(#{org_sk}) stockorg_sk)
        , skc_filter as (
        select distinct skc_sk from dm.dim_skc ds
        where brand_code in (select brand_code from edw.dim_brand_mapping dbm where dbm.org_sk=any(table org_filter))
        )
        ,s1 as (
        select case_id, skc_sk, sku_sk, stockorg_sk from 
        test.biz_user_case_exception_record
        where 1=1
        <if test="case">
            and (
            <foreach collection="case" item="item" separator="or">
                (case_id=${item.case_id} and run_id=${item.run_id})
            </foreach>
            )
        </if>
        group by case_id, skc_sk, sku_sk, stockorg_sk, unique_key
        )
        , s2 as (
        select * from s1
        where (stockorg_sk is null or stockorg_sk=any(table org_filter))
        and (skc_sk is null or skc_sk=any(table skc_filter))
        )
        , res1 as (
        select 'all_exc_num' d, case_id from s1
        union all
        select 'exc_num' d, case_id from s2
        )
        , res2 as (
        select d, case_id, count(1) exc_num from res1
        group by d, case_id
        )
        select case_id, json_object_agg(d, exc_num) exc
        from res2
        group by case_id
    </select>
    <select id="biz_verify_page">
        with org as ( -- 范围大一点无所谓，反正inner join模型输出表
        SELECT doi.org_sk
        FROM dm.dim_org_integration doi
        where org_sk = any(#{org_sk})
        and ${no_alias_filter_items.no_alias_dim_org_integration_filter}
        )
        , s1 as (
        select batch_id, sku_sk, skc_sk, stockorg_sk,bu.biz_action_template_code,
        reserve1,reserve2,reserve3,reserve4,reserve5,reserve6,reserve7,reserve8,reserve9,reserve10,
        operating_unit_name unit_org_name, bu.operating_unit_sk, bv.template_code, column_mapping, s_r_flag,
        bu.org_name exec_org_name, bu.org_sk exec_org_sk,
        day_date
        from test.biz_user_case_exception_record bu
        inner join test.biz_verification_assert_record bv
        on bu.case_id = bv.case_id
        where is_deleted=0
        and bu.case_id=#{case_id}
        and bu.run_id=(
        select max(run_id) from test.biz_user_case_execute_record
        where case_id=#{case_id} and status!= -99
        )
        and (stockorg_sk is null or stockorg_sk=any(table org))
        )
        <if test="'sk' in dimension">
            <if test="'skc' in dimension">
                ,skc_u as (
                select distinct skc_sk, skc_code, dim_skc.brand_code, dim_skc.brand_name, product_year||product_quarter year_season
                from dm.dim_skc dim_skc
            </if>
            <if test="'sku' in dimension">
                ,skc_u as (
                select distinct sku_sk, skc_sk, skc_code, dim_skc.brand_code, dim_skc.brand_name, product_year||product_quarter year_season, size_code, size_name
                from dm.dim_sku dim_skc
            </if>
            where brand_code in (select distinct brand_code from edw.dim_brand_mapping dbm where org_sk = any(table org))
            and ${filter_items.dim_skc_filter}
            )
        </if>
        ,s2 as (
        select
        <if test="'sk' in dimension">
            -- skc, skc-org, sku-org
            skc_u.*,
        </if>
        <if test="'org' in dimension">
            -- org
            doi.org_name, doi.org_code,
        </if>
        batch_id, stockorg_sk, biz_action_template_code,
        reserve1,reserve2,reserve3,reserve4,reserve5,reserve6,reserve7,reserve8,reserve9,reserve10,
        unit_org_name, s1.operating_unit_sk, template_code, column_mapping, s_r_flag, exec_org_sk, exec_org_name, s1.day_date
        from s1
        <if test="'sku' in dimension">
            inner join skc_u
            on s1.sku_sk=skc_u.sku_sk
        </if>
        <if test="'skc' in dimension">
            inner join skc_u
            on s1.skc_sk=skc_u.skc_sk
        </if>
        <if test="'org' in dimension">
            inner join dm.dim_org_integration doi
            on s1.stockorg_sk=doi.org_sk
        </if>
        )
        , res0 as (
        select
            biz_action_template_name, biz_action_type_code, id biz_action_template_id,
            s2.*, count(1) over() total_row
            from s2
            inner join biz.gto_biz_action_template
            on s2.biz_action_template_code::int4 =gto_biz_action_template.id
            where ${filter_items.gto_biz_action_template_filter}
        )
        , res as (
        select res0.* from res0
        )
        <if test="not column_name">
            select * from res
            where ${column_filter_sql}  -- 列筛选
            order by
            <if test="column_orders">
                ${column_orders_sql},
            </if>
            ${default_order}
        </if>
        <if test="column_name">
            select distinct ${column_name} from res
        </if>
        <if test="page_size">
            limit #{page_size} offset #{page_size} * (#{page_no} - 1)
        </if>
    </select>
    <select id="biz_verify_page_feature">
         with s1 as (
        select batch_id, sku_sk, skc_sk, stockorg_sk,bu.biz_action_template_code,
        reserve1,reserve2,reserve3,reserve4,reserve5,reserve6,reserve7,reserve8,reserve9,reserve10,
        operating_unit_name unit_org_name, bu.operating_unit_sk, bv.template_code, s_r_flag,
        bu.org_name exec_org_name, bu.org_sk exec_org_sk,
        day_date
        from test.biz_user_case_exception_record bu
        inner join test.biz_verification_assert_record bv
        on bu.case_id = bv.case_id
        where is_deleted=0
        and bu.case_id=#{case_id}
        and bu.run_id=(
        select max(run_id) from test.biz_user_case_execute_record
        where case_id=#{case_id} and status!= -99
        )
        )
        -- 用filter 查的字段多，
        -- 不用filter 分组没法做
        , filt as (
        select distinct
        <if test="'org' == dimension">
            store_sk
        </if>
        <if test="'skc' == dimension">
            skc_sk
        </if>
        <if test="'-' in dimension">
            store_sk, skc_sk
        </if>
        ,gs.org_name, gs.org_code, gs.brand_name, product_year||product_quarter year_season,
        gs.day_date,operating_unit_sk, gs.skc_code
        from adm.gto_skc_store_step_kpi_summary gs
        where ${no_alias_filter_items.no_alias_dim_org_integration_filter}
        and ${no_alias_filter_items.no_alias_base_skc_tag_pivot_filter}
        and ${no_alias_filter_items.no_alias_base_org_tag_pivot_filter}
        and ${filter_items.base_org_skc_tag_pivot_filter}
        and store_sk = any(#{org_sk})
        and brand_code in (select distinct brand_code from edw.dim_brand_mapping dbm where org_sk = any(#{org_sk}))
        )
        , res0 as (
        select s1.*, gto_biz_action_template.biz_action_template_name, gto_biz_action_template.biz_action_type_code, gto_biz_action_template.id biz_action_template_id,
        filt.org_name, filt.org_code, filt.brand_name, filt.year_season,
        filt.skc_code
        from s1
        inner join filt
        <if test="'org' == dimension">
            on s1.stockorg_sk=filt.store_sk
        </if>
        <if test="'skc' == dimension">
            on s1.skc_sk=filt.skc_sk
        </if>
        <if test="'-' in dimension">
            on s1.stockorg_sk=filt.store_sk and s1.skc_sk=filt.skc_sk
        </if>
         and s1.day_date=filt.day_date
        and filt.operating_unit_sk=s1.operating_unit_sk
        inner join biz.gto_biz_action_template
        on s1.biz_action_template_code::int4 =gto_biz_action_template.id
        where ${filter_items.gto_biz_action_template_filter}
        )
        , res as (
        <if test="'sku' in dimension">
            select res0.* , ds.size_name, ds.size_code
        from res0
        inner join dm.dim_sku ds
        on res0.sku_sk=ds.sku_sk
        </if>
        <if test="'sku' not in dimension">
            select res0.*
        from res0
        </if>
        )
        <if test="not column_name">
            select *, count(1) over() total_row from res
            where ${column_filter_sql}  -- 列筛选
            <if test="not column_orders">
                order by
                <if test="column_orders">
                    ${column_orders_sql},
                </if>
                ${default_order}
            </if>
        </if>
        <if test="column_name">
            select distinct ${column_name} from res
            order by convert_to(${column_name}::text, 'gbk')
        </if>
        <if test="page_size">
            limit #{page_size} offset #{page_size} * (#{page_no} - 1)
        </if>
    </select>
    <select id="get_biz_verify_case_mapping">
        select dimension,
        (case when column_mapping is null then '{}'
        when column_mapping='' then '{}'
        else column_mapping end)::json column_mapping, case_name
        from test.biz_verification_assert_record
        where case_id=#{case_id}
    </select>
    <select id="biz_verify_case_info">
        set local optimizer='off';
        with s1 as (
        select remark,
        params->>'upper_limit' upper_limit,
        case params->>'is_merge'
        when 'no' then 0
        else 1 end is_merge
        from test.biz_verification_assert_record
        where case_id=#{case_id} and is_deleted=0
        )
        , s2 as (
        select execute_start_time update_time,
        case status
        when -1 then '执行中'
        when 0 then '执行失败'
        when 1 then '执行成功'
        when 3 then '执行失败'
        else 'N/A' end status
        from test.biz_user_case_execute_record
        where case_id=#{case_id} and status!= -99 order by run_id desc limit 1
        )
        select remark, upper_limit, is_merge, update_time, status
        from s1 full join s2 on 1=1
    </select>
    <select id="biz_verify_statistics_feature">
        select ${exc_type} item, count(1) exc_num from test.biz_exception_record_analysis br
        inner join adm.gto_skc_store_step_kpi_summary gs
        on br.stockorg_sk=gs.store_sk and br.skc_sk=gs.skc_sk
        and br.operating_unit_sk=gs.operating_unit_sk
        inner join biz.gto_biz_action_template
        on br.biz_action_template_code::int4 =gto_biz_action_template.id

        -- inner join biz.gto_action_record as gar on gar.batch_id = br.batch_id
        -- inner join biz.gto_biz_action_template on gar.biz_action_template_id = gto_biz_action_template.id

        where ${filter_items.gto_biz_action_template_filter}
        and ${no_alias_filter_items.no_alias_dim_org_integration_filter}
        and ${no_alias_filter_items.no_alias_base_skc_tag_pivot_filter}
        and ${no_alias_filter_items.no_alias_base_org_tag_pivot_filter}
        and ${filter_items.base_org_skc_tag_pivot_filter}
        and store_sk = any(#{org_sk})
        and brand_code in (select distinct brand_code from edw.dim_brand_mapping dbm where org_sk = any(#{org_sk}))

        and ${exc_type} is not null
        and case_id=#{case_id}
        and run_id=(
        select max(run_id) from test.biz_user_case_execute_record
        where case_id=#{case_id} and status!= -99
        )
        and ${exc_type} not like '【%'
        group by ${exc_type}
        order by exc_num desc, item
    </select>
    <select id="biz_verify_statistics">
        with org2 as ( -- 范围大一点无所谓，反正inner join模型输出表
        SELECT doi.org_sk, operating_unit_sk
        FROM dm.dim_org_integration doi
        where org_sk = any(#{org_sk})
        and ${no_alias_filter_items.no_alias_dim_org_integration_filter}
        )
        , skc as (
        select distinct skc_sk from edw.dim_brand_mapping dbm
        inner join dm.dim_skc
        on dbm.brand_code = dim_skc.brand_code
        where org_sk = any(select org_sk from org2)
        and ${filter_items.dim_skc_filter}
        and dim_skc.brand_code in (select distinct brand_code from edw.dim_brand_mapping dbm where org_sk = any(#{org_sk}))
        )
        select ${exc_type} item, count(1) exc_num, max(br.biz_action_template_code)::int4 biz_action_template_code
        from test.biz_exception_record_analysis br
        inner join org2 on org2.org_sk = br.stockorg_sk and org2.operating_unit_sk=br.operating_unit_sk
        inner join skc on skc.skc_sk =br.skc_sk
        inner join biz.gto_biz_action_template
        on br.biz_action_template_code::int4=gto_biz_action_template.id
        where ${exc_type} not like '【%'
        and ${exc_type} is not null
        and br.case_id=#{case_id}
        and ${filter_items.gto_biz_action_template_filter}
        and br.stockorg_sk = any(#{org_sk})
        and br.run_id=(
        select max(run_id) from test.biz_user_case_execute_record
        where case_id=#{case_id} and status!= -99
        )
        and (br.stockorg_sk is null or br.stockorg_sk in (select org_sk from org2))
        and (br.skc_sk is null or br.skc_sk=any(table skc))
        group by ${exc_type}
        order by exc_num desc, item
    </select>

    <select id="biz_verify_export_case">
        with sub as (
        select case_id, run_id, ROW_NUMBER() over(partition by case_id order by run_id desc) n
        from test.biz_user_case_exception_record bu
        where
        --bu.case_id=any(#{case_id})
        group by case_id, run_id
        )
        select case_id, array_agg(run_id) run_id from sub
        where n &lt; 3
        group by case_id
    </select>
    <select id="biz_verify_export_all">
        with s1 as (
        select case_id, run_id, ROW_NUMBER() over(partition by case_id order by run_id desc) n
        from test.biz_user_case_execute_record
        group by case_id, run_id
        )
        , s2 as (
        select array_agg(group_name order by group_name) group_name, case_id, run_id from s1
        inner join test.biz_case_group_record bc
        on s1.case_id=any(bc.user_cases)
        where n &lt; 3  -- 取最后2个
        group by case_id, run_id
        )
        , s3 as (
        select array_to_string(group_name, ',') group_name, bv.case_name, s2.run_id, case when pass_flag=0 then '是' else null end pass_flag,
        bu.day_date, doi.org_name act_org_name, s_r_flag, bu.batch_id, skc_sk,sku_sk, stockorg_sk,
        bv.biz_action_template_code, gb.biz_action_template_name, exception_desc,
        reserve1,reserve2,reserve3,reserve4,reserve5,reserve6,reserve7,reserve8,reserve9,reserve10
        from s2
        inner join test.biz_user_case_exception_record bu
        on s2.case_id=bu.case_id and s2.run_id=bu.run_id
        inner join test.biz_verification_assert_record bv
        on s2.case_id=bv.case_id
        inner join test.biz_user_case_execute_record bu2
        on s2.case_id=bu2.case_id and s2.run_id=bu2.run_id
        left join dm.dim_org_integration doi
        on bu.org_sk=doi.org_sk
        inner join biz.gto_action_record ga
        on bu.batch_id=ga.batch_id
        inner join biz.gto_biz_action_template gb
        on ga.biz_action_template_id=gb.id
        )
        select s3.*, org_name, org_code, coalesce(dsc.skc_code, dsu.skc_code) skc_code, dsu.size_code from s3
        left join dm.dim_org_integration doi
        on s3.stockorg_sk=doi.org_sk
        left join dm.dim_skc dsc
        on s3.skc_sk=dsc.skc_sk
        left join dm.dim_sku dsu
        on s3.sku_sk=dsu.sku_sk
        order by case_name, run_id, batch_id desc, s_r_flag, org_name, skc_code, size_code
    </select>

    <sql id="biz_verify_overview_snippet">
        with
        a as (
            select case_id, 
                   max(run_id) as run_id,
                   max(case_group.group_id) as group_id
            from test.biz_user_case_execute_record as execute_record
            inner join test.biz_case_group_record as case_group
            on execute_record.case_id = any(case_group.user_cases)
                and case_group.is_exception_show = 1
                and execute_record.status != -99
                and case_group.is_active = 1
                and case_group.is_deleted = 0
                and (case_group.org_sk = any(#{tree_org_sk})
                    or case_group.public_org_sk = any(#{public_org_sk}))
            group by case_id
        ), data as (
            select 	assert_record.case_id,
                    assert_record.case_name,
                    assert_record.dimension,
                    case when execute_record.pass_flag = 1 then '是' else '否' end as pass_flag,
                    max(a.group_id) as group_id
            from test.biz_user_case_exception_record as case_exception
            inner join test.biz_user_case_execute_record as execute_record
                on execute_record.run_id = case_exception.run_id
                and execute_record.case_id = case_exception.case_id
            inner join a on a.case_id = case_exception.case_id and a.run_id = case_exception.run_id
            inner join test.biz_verification_assert_record as assert_record
            on case_exception.case_id = assert_record.case_id and assert_record.is_deleted = 0
                and assert_record.is_active = 1
            where 1=1
            <if test="stockorg_sk">
                and case_exception.stockorg_sk = ${stockorg_sk}
                and case_exception.operating_unit_sk = #{operating_unit_sk}
            </if>
            <if test="skc_sk">
                and case_exception.skc_sk = ${skc_sk}
                and (case_exception.stockorg_sk = any(#{org_sk})
                     or case_exception.stockorg_sk isnull
                    )
            </if>
            group by case_exception.unique_key,
                     assert_record.case_id,
                     assert_record.case_name,
                     assert_record.dimension,
                     execute_record.pass_flag
        )
        select
            case_id,
            case_name,
            dimension,
            count(1) as exception_num,
            pass_flag,
            concat_ws('_', max(group_id)::text, case_id::text) as unique_id
        from data
        group by
            case_id,
            case_name,
            dimension,
            pass_flag

    </sql>

    <select id="biz_verify_overview">
        with data as (
             @{biz_verify_overview_snippet}
        )
        select * from data
        where ${column_filter_str}
        <if test="column_orders_str == 'true'">
            order by exception_num desc, convert_to(case_name,'GBK') asc
        </if>
        <if test="column_orders_str != 'true'">
            order by ${column_orders_str}
        </if>
    </select>

    <select id="biz_verify_overview_column">
        with data as (
             @{biz_verify_overview_snippet}
        )
        select distinct ${column_name} as value, ${column_name} as label from data
        where ${column_filter_str}
        order by ${column_name_order}
    </select>

    <sql id="biz_verify_result_check_snippet">
        with case_execute as (
            select execute_record.case_id, 
                   max(run_id) as run_id,
                   assert_record.case_name
            from test.biz_user_case_execute_record as execute_record
            inner join test.biz_case_group_record as case_group
            on execute_record.case_id = any(case_group.user_cases)
                and case_group.is_exception_show = 1
                and case_group.is_active = 1
                and case_group.is_deleted = 0
                and execute_record.status != -99
                and (case_group.org_sk = any(#{tree_org_sk})
                    or case_group.public_org_sk = any(#{public_org_sk}))
            inner join test.biz_verification_assert_record as assert_record
                on assert_record.case_id = execute_record.case_id
                    and assert_record.is_deleted = 0
                    and assert_record.is_active = 1
            group by execute_record.case_id, assert_record.case_name
        )
        select case_execute.case_name,
               case_execute.case_id,
               gto_biz_action_template.biz_action_template_name,
               gto_biz_action_template.id as biz_action_template_id,
               gto_biz_action_template.biz_action_type_code,
               ds.size_name,
               ds.size_code,
               ra.check_send_side,
               ra.check_receive_side_rep,
               ra.check_receive_side_trans,
               org.org_code,
               'pathData' as help_check_dim,
               ds.skc_code,
               ds.skc_sk,
               biz_user_case_execute_record.belong_org_sk,
               gar.org_sk as exec_org_sk,
               to_char(exception_record.day_date,'YYYY-MM-DD') as day_date,
               ra.batch_id
        from test.biz_exception_record_analysis as ra
        left join (select distinct run_id, case_id, day_date
             from test.biz_user_case_exception_record
             where stockorg_sk=${stockorg_sk}
        ) as exception_record on exception_record.case_id = ra.case_id and exception_record.run_id = ra.run_id
        inner join case_execute on case_execute.case_id = ra.case_id and case_execute.run_id = ra.run_id
            and ra.stockorg_sk = ${stockorg_sk}
        inner join test.biz_user_case_execute_record
            on biz_user_case_execute_record.run_id = ra.run_id
            and biz_user_case_execute_record.case_id = ra.case_id
        inner join dm.dim_sku as ds on ra.sku_sk = ds.sku_sk
            and ds.skc_sk = ${skc_sk}
        inner join dm.dim_org_integration as org on ra.stockorg_sk = org.org_sk and org.operating_unit_sk = ra.operating_unit_sk
            and org.org_sk = ${stockorg_sk}
        left join biz.gto_action_record as gar on gar.batch_id = ra.batch_id
        inner join biz.gto_biz_action_template on ra.biz_action_template_code::int = gto_biz_action_template.id
        where ra.operating_unit_sk = #{operating_unit_sk}
    </sql>

    <select id="biz_verify_result_check">
        with data as (
             @{biz_verify_result_check_snippet}
        )
        select * from data
        where ${column_filter_str}
        <if test="column_orders_str == 'true'">
           order by convert_to(case_name,'GBK') asc,
                    convert_to(biz_action_template_name, 'GBK') asc,
                    size_code asc
        </if>
        <if test="column_orders_str != 'true'">
            order by ${column_orders_str}
        </if>

    </select>

    <select id="biz_verify_result_check_column">
        with data as (
             @{biz_verify_result_check_snippet}
        )
        select distinct ${column_name} as value, ${column_name} as label from data
        where ${column_filter_str}
        order by ${column_name_order}
    </select>

    <select id="test_query_skc_sk_list">
        select skc_sk from dm.dim_skc
    </select>
</mapper>