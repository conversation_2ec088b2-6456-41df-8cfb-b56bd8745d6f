from datetime import datetime
from typing import List

import arrow
import pydantic


def converter_factory(convert_func):
    def _converter_factory(field: str) -> classmethod:
        decorator = pydantic.validator(field, allow_reuse=True, pre=True)
        converter = decorator(convert_func)
        return converter

    return _converter_factory


def str_to_int_list(cls, value: str) -> List[int]:
    if not value:
        return []
    return list(map(int, value.split(",")))


def str_to_str_list(cls, value: str) -> List[str]:
    if not value:
        return []
    return list(value.split(","))


def str_to_datetime(cls, date_str: str) -> datetime:
    if not date_str:
        return None
    date_info = arrow.get(date_str, tzinfo="local").datetime
    return date_info


def str_to_datetime_list(cls, value: str) -> List[datetime]:
    result = []
    if not value.strip():
        return result
    for date_str in value.strip().split(","):
        date_info = str_to_datetime(cls, date_str)
        result.append(date_info)
    return result


def remove_str_space(cls, value: str) -> str:
    return value.replace(" ", "")


def transform_special_char(cls, value: str) -> str:
    """转义特殊字符"""
    trans_map = {
        ord("_"): "\\_",
        ord("%"): "\\%",
    }
    return value.translate(trans_map)


def htmlspecialchars_converter(cls, value: str):
    """html转义特殊字符"""
    trans_map = {
        ord("<"): "&lt;",
        ord(">"): "&gt;",
        ord("&"): "&amp;",
    }
    return value.translate(trans_map)


#  '1,2,3' -> [1,2,3]
str_to_int_list_converter = converter_factory(str_to_int_list)
#  '1,2,3' -> ['1','2','3']
str_to_str_list_converter = converter_factory(str_to_str_list)
#  '2021-05-26' -> datetime | '2021-05-26 18:00:00' -> datetime
str_to_datetime_converter = converter_factory(str_to_datetime)
# '2021-05-26,2021-05-27' -> [datetime, datetime]
str_to_datetime_list_converter = converter_factory(str_to_datetime_list)

remove_str_space_converter = converter_factory(remove_str_space)
db_special_char_converter = converter_factory(transform_special_char)
html_special_char_converter = converter_factory(htmlspecialchars_converter)
