from datetime import datetime, date
from typing import List, Optional, Union, Dict
from pydantic import BaseModel, validator, root_validator
from werkzeug.datastructures import FileStorage

from biz.typedef import CommonBaseModel


class AggregationDimensionFilterRequest(BaseModel):

    filter_key: Optional[str]
    channel_type: Optional[str]
    large_region: Optional[str]
    org_sk: Optional[str]
    is_include_group_buy: Optional[str]
    big_class: Optional[str]
    product_range: Optional[str]
    product_year_quarter: Optional[str]
    product_belong_name: Optional[str]
    ps_mid_class: Optional[str]
    sale_mid_tiny_class: Optional[str]
    gender: Optional[str]
    product_name: Optional[str]
    real_listing_date: Optional[str]
    skc_sk: Optional[str]
    org_sk_list: Optional[List[int]]
    sort_filter: Optional[List[Dict]]
    page_size: Optional[int] = 10
    page_no: Optional[int] = 1
    page_offset: Optional[int] = 0
    column_orders_str: Optional[str]
    is_admin: Optional[bool] = False
    user_id: Optional[int]

class AggregationDimensionBaseKpiRequest(AggregationDimensionFilterRequest):

    is_current_quarter: Optional[Union[int, str]]

class AggregationDimensionLineChartRequest(AggregationDimensionFilterRequest):

    dimension: str
    kpi_key: str
    is_current_quarter: Optional[Union[int, str]]


class AggregationDimensionListRequest(AggregationDimensionFilterRequest):

    dimension: str
    is_current_quarter: Optional[Union[int, str]]


class PageConfigReq(BaseModel):

    config_name: Optional[str]  # 页面配置名
    module_name: Optional[str]
    page_name: Optional[str]
    user_id: Optional[int]
    is_product: Optional[bool]
    is_tenant: Optional[bool]
    is_common: Optional[bool]




class ReferSkcFilterRequest(BaseModel):

    filter_key: Optional[str]
    channel_type: Optional[str]
    org_sk: Optional[str]
    big_class: Optional[List[str]]
    product_range: Optional[List[str]]
    product_year_quarter: Optional[List[str]]
    product_belong_name: Optional[List[str]]
    ps_mid_class: Optional[List[str]]
    sale_mid_tiny_class:Optional[List[str]]
    gender:Optional[List[str]]
    product_name: Optional[List[str]]
    skc_sk: Optional[Union[List[int], str]]
    org_sk_list: Optional[List[int]]

    @root_validator(pre=True)
    def change_str_to_list(cls, values):
        if isinstance(values.get('big_class'), str):
            values['big_class'] = [values['big_class']]
        if isinstance(values.get('product_range'), str):
            values['product_range'] = [values['product_range']]
        if isinstance(values.get('product_year_quarter'), str):
            values['product_year_quarter'] = [values['product_year_quarter']]
        if isinstance(values.get('product_belong_name'), str):
            values['product_belong_name'] = [values['product_belong_name']]
        if isinstance(values.get('ps_mid_class'), str):
            values['ps_mid_class'] = [values['ps_mid_class']]
        if isinstance(values.get('sale_mid_tiny_class'), str):
            values['sale_mid_tiny_class'] = [values['sale_mid_tiny_class']]
        if isinstance(values.get('gender'), str):
            values['gender'] = [values['gender']]
        if isinstance(values.get('product_name'), str):
            values['product_name'] = [values['product_name']]
        if isinstance(values.get('skc_sk'), int):
            values['skc_sk'] = [values['skc_sk']]
        return values



class ReferSkcListRequest(ReferSkcFilterRequest):

    sort_filter: Optional[List[Dict]]
    page_size: Optional[int] = 10
    page_no: Optional[int] = 1
    page_offset: Optional[int] = 0
    column_orders_str: Optional[str]
    refer_skc_code: Optional[List[str]]
    is_admin: Optional[bool] = False
    user_id: Optional[int]
    current_quarter_skc_sk: Optional[List[int]]


class ReferSkcFilterRequest(BaseModel):

    filter_key: Optional[str]
    channel_type: Optional[str]
    org_sk: Optional[str]
    big_class: Optional[List[str]]
    product_range: Optional[List[str]]
    product_year_quarter: Optional[List[str]]
    product_belong_name: Optional[List[str]]
    ps_mid_class: Optional[List[str]]
    sale_mid_tiny_class:Optional[List[str]]
    gender:Optional[List[str]]
    product_name: Optional[List[str]]
    skc_sk: Optional[List[int]]
    org_sk_list: Optional[List[int]]
    is_admin: Optional[bool] = False
    user_id: Optional[int]

    @root_validator(pre=True, allow_reuse=True)
    def change_str_to_list(cls, values):
        if isinstance(values.get('big_class'), str):
            values['big_class'] = [values['big_class']]
        if isinstance(values.get('product_range'), str):
            values['product_range'] = [values['product_range']]
        if isinstance(values.get('product_year_quarter'), str):
            values['product_year_quarter'] = [values['product_year_quarter']]
        if isinstance(values.get('product_belong_name'), str):
            values['product_belong_name'] = [values['product_belong_name']]
        if isinstance(values.get('ps_mid_class'), str):
            values['ps_mid_class'] = [values['ps_mid_class']]
        if isinstance(values.get('sale_mid_tiny_class'), str):
            values['sale_mid_tiny_class'] = [values['sale_mid_tiny_class']]
        if isinstance(values.get('gender'), str):
            values['gender'] = [values['gender']]
        if isinstance(values.get('product_name'), str):
            values['product_name'] = [values['product_name']]
        if isinstance(values.get('skc_sk'), int):
            values['skc_sk'] = [values['skc_sk']]
        return values


class ReferSkcReferSkcListRequest(ReferSkcFilterRequest):

    current_quarter_skc_sk: int
    current_quarter_skc_code: str
    page_size: Optional[int] = 10
    page_no: Optional[int] = 1
    page_offset: Optional[int] = 0
    column_orders_str: Optional[str]


class AddOrRemoveReferSkcRequest(BaseModel):

    channel_type: str
    org_sk: int
    current_quarter_skc_sk: int
    refer_skc_sk: int
    user_id: Optional[int]
    type: Optional[str]


class SaveReferSkcRequest(BaseModel):

    channel_type: str
    org_sk: int
    current_quarter_skc_sk: int
    add_refer_skc_sk: Optional[List[int]]
    delete_refer_skc_sk: Optional[List[int]]
    user_id: Optional[int]