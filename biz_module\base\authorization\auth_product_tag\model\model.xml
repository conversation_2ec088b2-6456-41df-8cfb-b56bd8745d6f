<?xml version="1.0"?>
<mapper namespace="default">
    <!-- 查询授权的商品标签 -->
    <select id="list_auth_record_product_tag">
        select
            product_tag_id,
            base_tag.tag_value as product_tag_name,
            auth_record_id
        from biz.base_product_tag_auth auth_tag
        inner join edw.base_tag_value base_tag
        on auth_tag.product_tag_id = base_tag.id
        where auth_tag.auth_record_id in ${auth_record_ids}
        and auth_tag.is_deleted = 0
        order by convert_to(tag_value,'GBK');
        <!-- <foreach item="record_id"  collection="auth_record_ids" open="(" separator="," close=")">
             #{record_id}
        </foreach> -->
    </select>

    <insert id="save_auth_record_product_tag">
         insert into biz.base_product_tag_auth(
                auth_record_id,
                product_tag_id,
                create_time,
                creator_id
                )
        values(
                #{params.auth_record_id},
                #{product_tag_id},
                #{create_time},
                #{creator_id}
            )returning id;
    </insert>

    <mix id="upsert_product_tag">
        with upsert as (
            update biz.base_product_tag_auth
            set modify_time = now(),
            modifier_id = #{modifier_id}
            where auth_record_id = #{params.auth_record_id}
            and is_deleted = 0
            and product_tag_id = #{product_tag_id}
            returning *
        )
        insert into biz.base_product_tag_auth(auth_record_id, product_tag_id,create_time,creator_id)
        select #{params.auth_record_id},#{product_tag_id},#{create_time},#{creator_id}
        where not exists( select 1 from upsert where auth_record_id = #{params.auth_record_id} and product_tag_id = #{product_tag_id})
        returning id;
    </mix>


    <select id="list_auth_product_tag">
        with parent_tag as (
            select id,
                   tag_code,
                   ''::text as parent_tag_code,
                   tag_name,
                   0 as operation_type
            from edw.base_tag
            where tag_code = 'product_auth_tag'
        ), children_tag as (
            select
                tv.id,
                tag_value||tv.tag_code as tag_code,
                tv.tag_code as parent_tag_code,
                tag_value as tag_name
            from edw.base_tag_value tv
            inner join parent_tag p
            on tv.tag_code = p.tag_code
        ),auth_tags as (
            select
                product_tag_id,
                1 as operation_type
            from biz.base_product_tag_auth
            where auth_record_id = #{auth_record_id}
            and is_deleted = 0
        ),all_tags as (
            select * from parent_tag
            UNION ALL
            select
                id,
                tag_code,
                parent_tag_code,
                tag_name,
                COALESCE(at.operation_type,0) as operation_type
            from children_tag ct
            left join auth_tags at
            on ct.id = at.product_tag_id
        )select
            tag_code as id,
            all_tags.id as tag_id, -- 防止重复 保存时以此id为值
            tag_code,
            parent_tag_code,
            tag_name,
            operation_type
        from all_tags
        order by convert_to(tag_name,'GBK');
    </select>


    <select id="list_product_tag_define">
        select id , tag_code as tag_group_code, tag_name as tag_group_name
        from edw.base_tag
        where tag_code = 'product_auth_tag'
    </select>

    <select id="list_auth_tag_values">
        select id , tag_code , tag_value
        from edw.base_tag_value
        where tag_code = 'product_auth_tag'
    </select>

    <delete id="remove_auth_product_tags">
        update biz.base_product_tag_auth
            set modify_time = #{body.modify_time},
            modifier_id = #{body.modifier_id},
            is_deleted = 1
            where auth_record_id = #{body.auth_record_id}
            returning id;
    </delete>
</mapper>
