import json
import logging
from typing import List

from fastboot.utils.nest import egg
from flask_login import current_user
from pydantic.utils import defaultdict

from biz import BaseService
from biz.api_exception import ParameterException
from biz.utils.image_utils import get_images_name
from biz.utils.tools import check_non_negative_integer, check_integer
from biz_module.base.component_excel.tools import make_file_response
from .export_task import sync_multiple_product_preview_export_task
from ..constants import AddReductionOrderDecisionSuggestEnum, AddReductionOrderStatusEnum
from ..dao import MultipleProductPreviewDao
from biz_module.add_reduction_order.multiple_product_preview.service.page_config import PageComponentConfigService
from biz_module.add_reduction_order.multiple_product_preview import typedef as td

from biz_module.base.page_config.typedef.page_config import GetConfigListReq


logger = logging.getLogger(__name__)


@egg
class MultipleProductPreviewService(BaseService):

    dao: MultipleProductPreviewDao
    page_config_service: PageComponentConfigService

    org_filter = ["channel_type", "org_sk"]
    skc_filter = ["day_date", "big_class","product_range","product_year_quarter","product_belong_name"
        ,"ps_mid_class","sale_mid_tiny_class","gender","product_name", "skc_sk"]

    @classmethod
    def column_order_sql_build(cls, column_orders: List[dict], filed_table_alis_dict):
        order_sqls = []
        error_msg = []
        for column_order in column_orders:
            field_column = filed_table_alis_dict.get(column_order["size_code"])
            if field_column.get("is_calculate"):
                error_msg.append(f'指标:{field_column["cn_name"]}正在计算中，不支持排序')
                continue
            table_alias = field_column.get("table_alias")
            value_type = field_column.get("value_type")
            column_order["value_type"] = value_type
            data_type = field_column.get("data_type")
            column_order["data_type"] = data_type

            if table_alias:
                alias_name = table_alias + "."
            else:
                alias_name = ""

            # if column_order["order"].upper() == "DESC":
            #     order_type = " nulls last"
            # else:
            #     order_type = " nulls first"

            order_type = " nulls last"
            column_order["order_type"] = order_type

            column_order["alias_name"] = alias_name
            if data_type in ("numeric", "int"):
                order_sql = (
                    "{alias_name}{size_code}::{data_type} {order} {order_type}"
                )
            elif data_type == "text":
                order_sql = (
                    "convert_to({alias_name}{size_code}, 'GBK') {order} {order_type}"
                )
            else:
                order_sql = "{alias_name}{size_code} {order} {order_type}"
            order_sqls.append(order_sql.format(**column_order))
        if error_msg:
            raise ParameterException("; ".join(error_msg))
        return ",".join(order_sqls)

    @staticmethod
    def add_picture(item: dict):
        """添加图片"""
        if item.get("skc_code"):
            item["image_url"] = get_images_name(item["skc_code"])
        else:
            item["image_url"] = get_images_name(item["_skc_code"])
    def update_config_application(self, params: td.PageConfigIdReq):
        result = self.dao.get_page_config_basic_by_id(params.page_id)
        if not result:
            logger.error("page_id 不存在 {}".format(params.page_id))
            raise ParameterException("非法操作！")
        params.user_id = current_user.user_id
        params.page_name = result.page_name
        params.module_name = result.module_name
        self.dao.add_user_page_config_mapping(params)
        return {"msg": "操作成功"}

    def list_page_config(self, params: GetConfigListReq):
        params.user_id = current_user.user_id
        from .page_config import page_config_service as conf_srv
        res = conf_srv.get_config_list(params)
        res["page_id"] = None
        user_page = td.PageConfigReq(user_id=current_user.user_id)
        user_page.page_name = params.page_name
        user_page.module_name = params.module_name
        page_id = self.dao.get_user_page_config_mapping(user_page)
        if page_id is not None:
            for data in res.get("content", []):
                if data.get("page_id") == page_id:
                    res["page_id"] = page_id
                    break
        return res


    def query_multiple_product_preview_filter(self, query_info: td.MultipleProductPreviewFilterRequest):

        filter_key = query_info.filter_key
        data = []
        # 如果组织筛选 则需要过滤时间
        if filter_key in self.org_filter:
            index = self.org_filter.index(filter_key)
            for i in self.org_filter[index+1:]:
                setattr(query_info, i, None)
            from biz_module.base.authorization.auth_region.service import auth_region_service
            org_sk_list = auth_region_service.list_authorized_region_org_sk()
            query_info.org_sk_list = org_sk_list
            data = self.dao.query_multiple_product_preview_org_filter(query_info)

        elif filter_key in self.skc_filter:
            index = self.skc_filter.index(filter_key)
            # 将index更大的值置为None
            for i in self.skc_filter[index+1:]:
                setattr(query_info, i, None)
            query_info.is_admin = current_user.is_admin
            query_info.user_id = current_user.user_id
            data = self.dao.query_multiple_product_preview_filter(query_info)
        elif filter_key == 'decision_suggest':
            data = AddReductionOrderDecisionSuggestEnum.get_decision_suggest_list()
        elif  filter_key == 'status':
            data = AddReductionOrderStatusEnum.return_status_list()

        return data

    def query_multiple_product_preview_list(self, query_info: td.MultipleProductPreviewListRequest):

        req = td.PageConfigReq(
            module_name="multiple_product_preview",
            page_name="base_kpi",
            user_id=current_user.user_id,
        )
        (
            header_dict_list,
            table_alis_filed_dict,
            order_by,
            filed_table_alis_dict,
        ) = self.page_config_service.parse_page_config_field(req)

        day_date_set = {
            i.get("code") for i in header_dict_list if i.get("data_type") == "timestamp"
        }
        header_dict_list.insert(
            0, self.page_config_service.get_image_url_node("image_url", "图片")
        )
        page_size = query_info.page_size
        page_no = query_info.page_no
        query_info.page_offset = (page_no - 1) * page_size

        column_orders = query_info.sort_filter
        column_orders_str = ""
        if column_orders and column_orders != [{}]:
            column_orders_str = self.column_order_sql_build(
                column_orders, filed_table_alis_dict
            )
        query_info.column_orders_str = (
            column_orders_str if column_orders_str else order_by[0]
        )

        # 查询
        query_info.is_admin = current_user.is_admin
        query_info.user_id = current_user.user_id
        data: [] = self.dao.query_multiple_product_preview_list(query_info)
        total_count = data[0].get('total_count') if data else 0
        result = []
        # 添加图片属性
        for item in data:
            status = item.get("status")
            item[ "status"] =  AddReductionOrderStatusEnum.get_cn_status(status)
            refer_skc_qty = item.get("refer_skc_qty")
            item[ "refer_skc_qty"] =  f'{refer_skc_qty} <a style="color:#1677ff;float:right;">查看</a>'
            # self.add_picture(item)
            for i in day_date_set:
                if item.get(i):
                    item[i] = item[i].strftime("%Y-%m-%d")

            result.append(item)

        return {"data_list": result, "header_list": header_dict_list, "page_size":  page_size, "page_no": page_no, "total_count": total_count}


    def update_multiple_product_preview(self, req: td.UpdateMultipleProductPreviewRequest):
        uuid =  req.uuid
        org_sk, skc_sk, day_date = uuid.split('@@')
        req.org_sk = org_sk
        req.skc_sk = skc_sk
        req.day_date = day_date
        # 查询数据
        data = self.dao.query_multiple_product_preview_by_uuid(day_date, org_sk, skc_sk)
        if not data:
            raise ParameterException("数据不存在")
        decision_suggest = req.decision_suggest
        sys_decision_suggest = data.get('decision_suggest')
        change_reason = req.change_reason
        if decision_suggest != sys_decision_suggest and not change_reason:
            raise ParameterException("请填写调整原因")

        if decision_suggest and decision_suggest not in AddReductionOrderDecisionSuggestEnum.get_all_decision_suggest():
            raise ParameterException("请选择正确的调整建议")

        # 校验人工决策量
        if req.human_decision_qty and not check_integer(req.human_decision_qty):
            raise ParameterException("请填写正确的人工决策量")

        req.user_id = current_user.user_id
        self.dao.update_multiple_product_preview(req)

        return {"msg": "修改成功"}

    def save_multiple_product_preview(self, req: td.SaveMultipleProductPreviewRequest):

        data = req.data
        error_list = defaultdict(list)
        params = []
        # 查询系统数据
        for i in data:
            uuid = i.uuid
            org_sk, skc_sk, day_date = uuid.split('@@')
            params.append({"org_sk": org_sk, "skc_sk": skc_sk, "day_date": day_date})
        params_str = json.dumps(params)
        sys_data = self.dao.query_multiple_product_preview_by_uuid_list(params_str)
        sys_data = {i.get('uuid'): i for i in sys_data}
        result = []
        for i in data:
            uuid =  i.uuid
            org_sk, skc_sk, day_date = uuid.split('@@')
            decision_suggest = i.decision_suggest
            human_decision_qty = i.human_decision_qty
            change_reason = i.change_reason
            sys_data_item = sys_data.get(uuid, {})
            sys_decision_suggest = sys_data_item.get('decision_suggest')
            if decision_suggest != sys_decision_suggest and not change_reason:
                error_list[f"skc:{sys_data_item.get('skc_code')}"].append("调整原因未填写")

            if decision_suggest and decision_suggest not in AddReductionOrderDecisionSuggestEnum.get_all_decision_suggest():
                error_list[f"skc:{sys_data_item.get('skc_code')}"].append("调整建议错误")

            # 校验人工决策量
            if human_decision_qty and not check_integer(human_decision_qty):
                error_list[f"skc:{sys_data_item.get('skc_code')}"].append("人工决策量填写错误")

            result.append({"org_sk": org_sk, "skc_sk": skc_sk, "day_date": day_date, "human_decision_qty": human_decision_qty, "decision_suggest": decision_suggest, "change_reason": change_reason})
        warning_msg = ''
        if error_list:
            for msg, v in error_list.items():
                warning_msg += f"{msg}\n {', '.join(v)}\n"
            if warning_msg:
                raise ParameterException(warning_msg)
            raise ParameterException(error_list)
        result_str = json.dumps(result)
        self.dao.save_multiple_product_preview(result_str, current_user.user_id)

        return {"msg": "保存成功"}


    def confirm_multiple_product_preview(self, req: td.ConfirmMultipleProductPreviewRequest):
        day_date = req.day_date
        all = req.all
        if all:
            req.page_size = 0
            req.is_admin = current_user.is_admin
            req.user_id = current_user.user_id
            data = self.dao.query_multiple_product_preview_list(req)
            uuid_list = [i.get('uuid') for i in data]
        else:
            uuid_list = req.uuid_list

        confirm_data = []
        for uuid in uuid_list:
            org_sk, skc_sk, day_date = uuid.split('@@')
            confirm_data.append({"org_sk": int(org_sk), "skc_sk": int(skc_sk)})
        confirm_data_str = json.dumps(confirm_data)
        self.dao.confirm_multiple_product_preview(confirm_data_str, current_user.user_id, req.target_status, day_date)
        return {"msg": f"确认成功{len(uuid_list)}"}

    def export_multiple_product_preview(self, req: dict):

        # 获取page_id
        page_id = self.page_config_service.get_page_id_by_page_code('multiple_product_preview')
        req['page_id'] = page_id
        result, file_name = sync_multiple_product_preview_export_task(req)

        return make_file_response(result, file_name)

    def query_export_data(self, req: td.MultipleProductPreviewListRequest):
        req.is_admin = current_user.is_admin
        req.user_id = current_user.user_id
        return self.dao.query_export_data(req)

    def query_multiple_product_preview_detail_kpi(self, query_info: td.MultipleProductPreviewDetailKpiRequest):

        req = td.PageConfigReq(
            module_name="multiple_product_preview",
            page_name="detail_kpi",
            user_id=current_user.user_id,
        )
        (
            header_dict_list,
            table_alis_filed_dict,
            order_by,
            filed_table_alis_dict,
        ) = self.page_config_service.parse_page_config_field(req)
        day_date_set = {
            j.get("code")
            for i in header_dict_list
            for j in i.get("options", {})
            if j.get("data_type") == "timestamp"
        }

        data = self.dao.query_multiple_product_preview_detail_kpi(query_info)

        for header_dict in header_dict_list:
            option_list = header_dict.get("options")
            for option_dict in option_list:
                if option_dict["code"] in day_date_set:
                    if data.get(option_dict.get("code")):
                        option_dict["value"] = data.get(
                            option_dict.get("code")
                        ).strftime("%Y-%m-%d")
                else:
                    option_dict["value"] = data.get(option_dict.get("code")) if data else None

        return {"data_list": header_dict_list}



multiple_product_preview_service = MultipleProductPreviewService()

