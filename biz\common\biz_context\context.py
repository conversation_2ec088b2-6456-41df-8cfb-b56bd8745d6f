#!/usr/bin/env python
# encoding: utf-8
# <AUTHOR> liuhuanchao
import threading
from flask_log_request_id import current_request_id


class Context:
    def __init__(self):
        self._thread_local = threading.local()

    @property
    def request_id(self):
        request_id = current_request_id()
        if request_id:
            ctx.request_id = request_id
            return request_id
        return getattr(self._thread_local, 'request_id', None)

    @request_id.setter
    def request_id(self, value):
        self._thread_local.request_id = value


ctx = Context()
