import logging
from biz import BaseService
from biz_module.base.users_record.dao import UsersRecordDao
from biz_module.base.users_record.typedef import UsersRecordModel
import threading, queue, time


log = logging.getLogger()


class UsersRecordService(BaseService):
    def __init__(self):
        super(UsersRecordService, self).__init__()
        self.dao = UsersRecordDao()

    def save_users_record_service(self, user_record_info_dict):
        user_record_info = UsersRecordModel(**user_record_info_dict)
        self.dao.insert_user_record(user_record_info)


users_record_service = UsersRecordService()

q = queue.Queue()


def put_task(user_record_info_dict):
    q.put(user_record_info_dict)


def get_task():
    while True:
        try:
            if not q.empty():
                user_record_info_dict = q.get()
                users_record_service.save_users_record_service(user_record_info_dict)
            time.sleep(1)
        except Exception as e:
            log.error("用户访问记录保存失败！")
            log.error(e)


t = threading.Thread(target=get_task)
t.start()
