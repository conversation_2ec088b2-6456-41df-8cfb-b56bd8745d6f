from datetime import datetime
from typing import List, Optional, Union, Callable

from pydantic import BaseModel


class ColumnFilterEgg(BaseModel):
    column_name: str
    type: str
    operator: Optional[str]
    value: Optional[Union[str, int, list]]
    value1: Optional[Union[str, int]]
    value2: Optional[Union[str, int]]
    size_code: Optional[str]
    # 如果是jsonb类型的列则需要在条件筛选时进行类型强转,其他类型的列可不传该字段
    data_type: Optional[str]


class ColumnSortEgg(BaseModel):
    size_code: Optional[str]
    # order: asc desc
    order: str
    column_name: str
    data_type: Optional[str]


class TableColumnOpMixin:

    size_template = " ({table_alias}{column_name} ->> '{size_code}'){data_type} {operator} {value} "
    size_template_1 = " ({table_alias}{column_name} ->> '{size_code}'){data_type} {operator} {value1} "
    size_template_2 = " ({table_alias}{column_name} ->> '{size_code}'){data_type} {operator} {value2} "
    size_null_template_1 = " ({table_alias}{column_name}::jsonb ->> '{size_code}') isnull "
    size_null_template_2 = " ({table_alias}{column_name}::jsonb ->> '{size_code}') in ('', 'null') "
    field_template = " {table_alias}{column_name} {operator} {value} "
    field_template_1 = " {table_alias}{column_name} {operator} {value1} "
    field_template_2 = " {table_alias}{column_name} {operator} {value2} "
    field_null_template_1 = " {table_alias}{column_name} isnull "
    field_null_template_2 = " {table_alias}{column_name} in ('', 'null') "

    @classmethod
    def build_null_sql_template(cls, data_type, size_code):
        if size_code and data_type == 'text':
            return '(' + cls.size_null_template_1 + 'or' + cls.size_null_template_2 + ')'
        elif size_code and data_type != 'text':
            return cls.size_null_template_1
        elif not size_code and data_type == 'text':
            return '(' + cls.field_null_template_1 + 'or' + cls.field_null_template_2 + ')'
        elif not size_code and data_type != 'text':
            return cls.field_null_template_1
        else:
            return ''

    @classmethod
    def template_format_val(cls, cf_egg: ColumnFilterEgg, table_alias):
        table_alias = table_alias + '.' if table_alias else ''
        data_type = '::' + cf_egg.data_type if cf_egg.data_type else '::int'

        value = cf_egg.value
        if not value:
            operator = ''
            f_val = ''
        elif isinstance(value, list):
            # 查询类型 eg: in/=
            operator = 'in'
            if isinstance(value[0], int):
                f_val = '(' + ','.join([f'{i}' for i in value]) + ')'
            else:
                f_val = '(' + ','.join([f'\'{i}\'' for i in value]) + ')'
        else:
            operator = '='
            if isinstance(value, int):
                f_val = value
            else:
                f_val = f'\'{value}\''

        operator = cf_egg.operator if cf_egg.operator in ('<', '>', '=', '>=', '<=') else operator

        format_dict = {
            'table_alias': table_alias,
            'column_name': cf_egg.column_name,
            'size_code': cf_egg.size_code,
            'data_type': data_type,
            'operator': operator,
            'value': f_val,
            'value1': cf_egg.value1,
            'value2': cf_egg.value2
        }
        return format_dict

    @classmethod
    def _list_or_compare(cls, cf_egg: ColumnFilterEgg, table_alias=''):
        null_sql = ''
        if isinstance(cf_egg.value, list) and None in cf_egg.value:
            null_sql = cls.build_null_sql_template(cf_egg.data_type, cf_egg.size_code)
            cf_egg.value.remove(None)

        format_dict = cls.template_format_val(cf_egg, table_alias)

        if not cf_egg.value:
            return 'and' + null_sql.format(**format_dict)

        null_sql = null_sql if not null_sql else ' or ' + null_sql

        sql_part_str = ''
        if cf_egg.size_code:
            sql_part_str += ' and ' + ('(' + cls.size_template + null_sql + ')')
        else:
            sql_part_str += ' and ' + ('(' + cls.field_template + null_sql + ')')
        return sql_part_str.format(**format_dict)

    @classmethod
    def _range(cls, cf_egg: ColumnFilterEgg, table_alias=''):
        format_dict = cls.template_format_val(cf_egg, table_alias)
        sql_part_str = ''
        if cf_egg.size_code:
            format_dict['operator'] = '>'
            sql_part_str += ' and ' + cls.size_template_1.format(**format_dict)
            format_dict['operator'] = '<='
            sql_part_str += ' and ' + cls.size_template_2.format(**format_dict)
        else:
            format_dict['operator'] = '>'
            sql_part_str += 'and' + cls.field_template_1.format(**format_dict)
            format_dict['operator'] = '<='
            sql_part_str += 'and' + cls.field_template_2.format(**format_dict)
        return sql_part_str

    @classmethod
    def column_filter_sql(cls, param: List[dict],
                          table_alias='') -> str:
        """处理列筛选
           筛选合计注意事项：
            如果是sku粒度的表则需要提前计算好合计，此函数将默认过滤“指标_sum”字段
        Args:
            param: sql模板
                [{
                    "column_name": "scene_name",
                    "type": "list",
                    "operator": ">",
                    "value": ["1"],
                    "value2: 1, 当过滤为区间时需要传该值
                    "data_type": "int" 可不传
                    "size_code": "total" 尺码列需要添加该属性
                }]
                type: list、compare、range
                如果有该字段，则在筛选尺码对应的字段时需要该字段进行过滤
            table_alias: 表别名
        Return:
            sql segment
        """
        if not param:
            return '1=1'

        sql_part_str = ' 1=1 '
        for p in param:
            cf_egg = ColumnFilterEgg(**p)
            if cf_egg.type in ('list', 'compare'):
                if not cf_egg.value:
                    continue
                sql_part_str += cls._list_or_compare(cf_egg, table_alias)
            elif cf_egg.type == 'range':
                sql_part_str += cls._range(cf_egg, table_alias)
            else:
                continue
        return sql_part_str

    @classmethod
    def column_orders_sql(cls, param: List[dict], func: Callable = None, table_alias=''):
        """
        Args:
            param:
                {size_code: "", order: "asc", column_name: "human_allot_in_org_name", "data_type": "int"}
                data_type: 尺码排序时需要强转，默认视尺码为int
            table_alias
            func: 处理排序字段，eg: lambda x:  if x 'convert_to({a}, 'GBK')' == 'a'
        Return:
            sql segment
        """
        if not param:
            return 'true'

        table_alias = table_alias + '.' if table_alias else ''

        sql_part_str = 'true'
        for p in param:
            cs_egg = ColumnSortEgg(**p)

            # 尺码排序时需要强转，默认视尺码为int
            data_type = cs_egg.data_type if cs_egg.data_type else 'int'

            null_area = 'nulls first' if cs_egg.order == 'asc' else 'nulls last'

            if cs_egg.size_code:
                sql_part_str += f', ({table_alias}{cs_egg.column_name} ->> \'{cs_egg.size_code}\')::{data_type} {cs_egg.order} {null_area}'
            elif func:
                sort_field = f'{table_alias}{cs_egg.column_name}'
                sql_part_str += f", {func(sort_field)} {cs_egg.order} {null_area}"
            else:
                sql_part_str += f", {table_alias}{cs_egg.column_name} {cs_egg.order} {null_area}"

        return sql_part_str


class EarlyWarningRecordMix:
    SIZE_FLAG = "{attr}#{size}@{size_group}$style"
    SIZE_TOTAL_FLAG = "{attr}#total$style"
    ATTR_FLAG = "{attr}$style"
    MAIN_SIZE_DOT = "{attr}#{size_code}@{size_group_code}_dot"
    MAIN_SIZE_COLOR = {'color': 'red'}
    SYMBOL = {"区间": "{} <= {} <= {}"}

    @staticmethod
    def handler_style(row_conf, old_style):
        field_style = 'color' if row_conf['warning_type'] == 'font' else 'background'
        if row_conf['warning_locate'] == 'row':
            return {
                'background': row_conf['color']
            }
        elif row_conf['warning_type'] == 'font-background':
            old_style['background'] = row_conf['color']
            return old_style
        else:
            if 'style' not in old_style:
                old_style['style'] = {}
            old_style['style'][field_style] = row_conf['color']
            return old_style

    def early_warning_to_record(self, record, conf):
        for row in conf['condition_rows']:
            # 预警位置
            locate = row['warning_locate']
            conf_value = row['warning_value']
            conf_range = row['range']
            # 判断是否为单元格
            if locate == 'cell':
                locate = row['condition']

            # 判断是否为当行
            if locate == 'row':
                locate = 'style'
            if 'style' not in record:
                record['style'] = 'null'

            if locate not in record:
                continue

            if row['condition'] not in record or locate not in record:
                continue
            record_value = record[row['condition']]
            locate_value = record[locate]

            # 判断该预警值是否为尺码
            if isinstance(record_value, dict) and 'total' in record_value:
                record_value = record_value['total']

            if isinstance(locate_value, dict) and locate != 'style':
                size_flag = True
            else:
                size_flag = False

            if record_value is None:
                continue

            if len(conf_value) == 2:
                # 如果是区间则必须要使用数值类型进行比较
                shape = self.SYMBOL[conf_range].format(conf_value[0], record_value, conf_value[1])
                try:
                    shape_flag = eval(shape)
                except:
                    shape_flag = False
                if shape_flag:
                    if not size_flag:
                        flag = 'style' if locate == 'style' else self.ATTR_FLAG.format(attr=locate)
                        record[flag] = self.handler_style(row, record.get(flag, {}))
                    else:
                        flag = self.SIZE_TOTAL_FLAG.format(attr=locate)
                        record[flag] = self.handler_style(row, record.get(flag, {}))
            else:
                # 根据补调单对应值的类型是否为字符串进行比较
                if isinstance(record_value, (str, datetime)):
                    shape = "'{}' {} '{}'".format(record_value, conf_range, conf_value[0])
                else:
                    shape = "{} {} {}".format(record_value, conf_range, conf_value[0])
                try:
                    shape_flag = eval(shape)
                except Exception:
                    shape_flag = False
                if shape_flag:
                    if not size_flag:
                        flag = 'style' if locate == 'style' else self.ATTR_FLAG.format(attr=locate)
                        record[flag] = self.handler_style(row, record.get(flag, {}))
                    else:
                        flag = self.SIZE_TOTAL_FLAG.format(attr=locate)
                        record[flag] = self.handler_style(row, record.get(flag, {}))
        # 去掉无用的整行标记
        if 'style' in record and record['style'] == 'null':
            del record['style']
