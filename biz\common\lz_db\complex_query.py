import logging
import jsonschema
from oslo_utils import timeutils
from wsme import types as wtypes
from biz import api_exception, types

LOG = logging.getLogger("lzdt")


class ComplexQuery(wtypes.Base):
    """Holds a sample query encoded in json."""

    filter = types.jsontype
    "The filter expression encoded in json."

    aggr = types.jsontype
    "Ther aggr expression encoded in json"

    orderby = types.jsontype
    "List of single-element dicts for specifing the ordering of the results."

    limit = int
    "The maximum number of results to be returned."

    skip = int
    "The skip number of results to be returned."

    @classmethod
    def sample(cls):
        return cls(filter={
            "and": [{
                "and": [{
                    "=": {
                        "counter_name": "cpu_util"
                    }
                },
                    {
                        ">": {
                            "counter_volume": 0.23
                        }
                    },
                    {
                        "<": {
                            "counter_volume": 0.26
                        }
                    }]
            },
                {
                    "or": [{
                        "and": [{
                            ">": {
                                "timestamp": "2013-12-01T18:00:00"
                            }
                        },
                            {
                                "<": {
                                    "timestamp": "2013-12-01T18:15:00"
                                }
                            }]
                    },
                        {
                            "and": [{
                                ">": {
                                    "timestamp": "2013-12-01T18:30:00"
                                }
                            },
                                {
                                    "<": {
                                        "timestamp": "2013-12-01T18:45:00"
                                    }
                                }]
                        }]
                }]
        },
            orderby=[{"counter_volume": "ASC"}, {"timestamp": "DESC"}],
            skip=10,
            limit=42
        )


def _list_to_regexp(items, regexp_prefix=""):
    regexp = ["^%s$" % item for item in items]
    regexp = regexp_prefix + "|".join(regexp)
    return regexp


class ValidatedComplexQuery(object):
    complex_operators = ["and", "or"]
    order_directions = ["asc", "desc"]
    simple_ops = ["=", "!=", "<", ">", "<=", "=<", ">=", "=>", "=~"]
    aggr_ops = ["sum", "count", "min", "max", "avg"]
    regexp_prefix = "(?i)"

    complex_ops = _list_to_regexp(complex_operators, regexp_prefix)
    simple_ops = _list_to_regexp(simple_ops, regexp_prefix)
    order_directions = _list_to_regexp(order_directions, regexp_prefix)
    aggr_ops = _list_to_regexp(aggr_ops, regexp_prefix)

    timestamp_fields = ["timestamp", "state_timestamp"]

    def __init__(self, query, db_model, additional_name_mapping=None,
                 metadata_allowed=False):
        additional_name_mapping = additional_name_mapping or {}
        self.name_mapping = {}
        self.name_mapping.update(additional_name_mapping)
        self.allow_order_fields = None
        valid_keys = [m.key for m in db_model.__table__.columns]
        valid_keys = list(valid_keys) + list(self.name_mapping.keys())
        valid_fields = _list_to_regexp(valid_keys, self.regexp_prefix)

        if metadata_allowed:
            valid_filter_fields = valid_fields + "|^metadata\.[\S]+$"
        else:
            valid_filter_fields = valid_fields

        schema_value = {
            "oneOf": [{"type": "string"},
                      {"type": "number"},
                      {"type": "boolean"}],
            "minProperties": 1,
            "maxProperties": 1}

        schema_value_in = {
            "type": "array",
            "items": {"oneOf": [{"type": "string"},
                                {"type": "number"}]},
            "minItems": 1}

        schema_field = {
            "type": "object",
            "patternProperties": {valid_filter_fields: schema_value},
            "additionalProperties": False,
            "minProperties": 1,
            "maxProperties": 1}

        schema_field_in = {
            "type": "object",
            "patternProperties": {valid_filter_fields: schema_value_in},
            "additionalProperties": False,
            "minProperties": 1,
            "maxProperties": 1}

        schema_leaf_in = {
            "type": "object",
            "patternProperties": {"(?i)^in$": schema_field_in},
            "additionalProperties": False,
            "minProperties": 1,
            "maxProperties": 1}

        schema_leaf_simple_ops = {
            "type": "object",
            "patternProperties": {self.simple_ops: schema_field},
            "additionalProperties": False,
            "minProperties": 1,
            "maxProperties": 1}

        schema_and_or_array = {
            "type": "array",
            "items": {"$ref": "#"},
            "minItems": 2}

        schema_and_or = {
            "type": "object",
            "patternProperties": {self.complex_ops: schema_and_or_array},
            "additionalProperties": False,
            "minProperties": 1,
            "maxProperties": 1}

        schema_not = {
            "type": "object",
            "patternProperties": {"(?i)^not$": {"$ref": "#"}},
            "additionalProperties": False,
            "minProperties": 1,
            "maxProperties": 1}

        self.schema = {
            "oneOf": [{"$ref": "#/definitions/leaf_simple_ops"},
                      {"$ref": "#/definitions/leaf_in"},
                      {"$ref": "#/definitions/and_or"},
                      {"$ref": "#/definitions/not"}],
            "minProperties": 1,
            "maxProperties": 1,
            "definitions": {"leaf_simple_ops": schema_leaf_simple_ops,
                            "leaf_in": schema_leaf_in,
                            "and_or": schema_and_or,
                            "not": schema_not}}

        self.orderby_schema = {
            "type": "array",
            "items": {
                "type": "object",
                "patternProperties":
                    {valid_fields: {"type": "string", "pattern": self.order_directions}},
                "additionalProperties": False,
                "minProperties": 1,
                "maxProperties": 1}}

        self.aggr_schema = {
            "type": "object",
            "properties": {
                "groupby": {
                    "description": "The key of resource by",
                    "type": "array",
                    "items": {
                        "type": "string",
                        "pattern": valid_fields
                    },
                    "minItems": 1
                },
                "aggrs": {
                    "description": "aggr list",
                    "type": "array",
                    "items": {
                        "type": "object",
                        "patternProperties": {
                            self.aggr_ops: {
                                "type": "array",
                                "items": {
                                    "type": "string",
                                    "pattern": valid_fields
                                }
                            }
                        },
                        "additionalProperties": False,
                        "minProperties": 1,
                        "maxProperties": 1
                    },
                    "minItems": 1
                },
                "rollup": {
                    "description": "Enable resource by rollup",
                    "type": "boolean"
                }
            },
            "required": [
                "groupby",
                "aggrs"
            ]
        }
        self.original_query = query

    def validate(self):
        """Validates the query content and does the necessary conversions."""
        if self.original_query.filter is wtypes.Unset:
            self.filter_expr = None
        else:
            try:
                # self.filter_expr = json.loads(self.original_query.filter)
                self.filter_expr = self.original_query.filter
                self._validate_filter(self.filter_expr)
            except (ValueError, jsonschema.exceptions.ValidationError) as e:
                raise api_exception.ParameterException(
                    "Filter expression not valid: %s" % e.message)
            self._replace_isotime_with_datetime(self.filter_expr)
            self._convert_operator_to_lower_case(self.filter_expr)
            self._normalize_field_names_for_db_model(self.filter_expr)

        if self.original_query.aggr is wtypes.Unset:
            self.aggr_expr = None
        else:
            try:
                # self.aggr_expr = json.loads(self.original_query.aggr)
                self.aggr_expr = self.original_query.aggr
                self._validate_aggr(self.aggr_expr)
            except (ValueError, jsonschema.exceptions.ValidationError) as e:
                raise api_exception.ParameterException(
                    "Aggrs expression not valid: %s" % e)
            # self._convert_orderby_to_lower_case(self.orderby)
            # self._normalize_field_names_in_orderby(self.orderby)
            self.allow_order_fields = [attr for attr in
                                       self.aggr_expr["groupby"]]
            for aggr in self.aggr_expr["aggrs"]:
                fun_name, fields = list(aggr.items())[0]
                for field in fields:
                    label_name = field + '_' + fun_name
                    self.allow_order_fields.append(label_name)

        if self.original_query.orderby is wtypes.Unset:
            self.orderby = None
        else:
            try:
                # self.orderby = json.loads(self.original_query.orderby)
                self.orderby = self.original_query.orderby
                self._validate_orderby(self.orderby)
            except (ValueError, jsonschema.exceptions.ValidationError) as e:

                raise api_exception.ParameterException(
                    "Order-by expression not valid: %s" % e)
            self._convert_orderby_to_lower_case(self.orderby)
            self._normalize_field_names_in_orderby(self.orderby)

        if self.original_query.limit is wtypes.Unset:
            self.limit = None
        else:
            self.limit = self.original_query.limit

        if self.limit is not None and self.limit <= 0:
            msg = 'Limit should be positive'
            raise api_exception.ParameterException(msg)

        if self.original_query.skip is wtypes.Unset:
            self.skip = None
        else:
            self.skip = self.original_query.skip

        if self.skip is not None and self.skip <= 0:
            msg = 'Skip should be positive'
            raise api_exception.ParameterException(msg)

    @staticmethod
    def lowercase_values(mapping):
        """Converts the values in the mapping dict to lowercase."""
        items = mapping.items()
        for key, value in items:
            mapping[key] = value.lower()

    def _convert_orderby_to_lower_case(self, orderby):
        for orderby_field in orderby:
            self.lowercase_values(orderby_field)

    def _normalize_field_names_in_orderby(self, orderby):
        for orderby_field in orderby:
            self._replace_field_names(orderby_field)

    def _traverse_postorder(self, tree, visitor):
        op = list(tree.keys())[0]
        if op.lower() in self.complex_operators:
            for i, operand in enumerate(tree[op]):
                self._traverse_postorder(operand, visitor)
        if op.lower() == "not":
            self._traverse_postorder(tree[op], visitor)

        visitor(tree)

    def _check_cross_project_references(self, own_project_id,
                                        visibility_field):
        """Do not allow other than own_project_id."""

        def check_project_id(subfilter):
            op, value = list(subfilter.items())[0]
            if (op.lower() not in self.complex_operators
                    and list(value.keys())[0] == visibility_field
                    and value[visibility_field] != own_project_id):
                raise api_exception.ParameterException(value[visibility_field])

        self._traverse_postorder(self.filter_expr, check_project_id)

    def _replace_isotime_with_datetime(self, filter_expr):
        def replace_isotime(subfilter):
            op, value = list(subfilter.items())[0]
            if op.lower() not in self.complex_operators:
                field = list(value.keys())[0]
                if field in self.timestamp_fields:
                    date_time = self._convert_to_datetime(subfilter[op][field])
                    subfilter[op][field] = date_time

        self._traverse_postorder(filter_expr, replace_isotime)

    def _normalize_field_names_for_db_model(self, filter_expr):
        def _normalize_field_names(subfilter):
            op, value = list(subfilter.items())[0]
            if op.lower() not in self.complex_operators:
                self._replace_field_names(value)

        self._traverse_postorder(filter_expr,
                                 _normalize_field_names)

    def _replace_field_names(self, subfilter):
        field, value = list(subfilter.items())[0]
        if field in self.name_mapping:
            del subfilter[field]
            subfilter[self.name_mapping[field]] = value
        if field.startswith("metadata."):
            del subfilter[field]
            subfilter["resource_" + field] = value

    @staticmethod
    def lowercase_keys(mapping):
        """Converts the values of the keys in mapping to lowercase."""
        items = mapping.items()
        for key, value in items:
            del mapping[key]
            mapping[key.lower()] = value

    def _convert_operator_to_lower_case(self, filter_expr):
        self._traverse_postorder(filter_expr, self.lowercase_keys)

    @staticmethod
    def _convert_to_datetime(isotime):
        try:
            date_time = timeutils.parse_isotime(isotime)
            date_time = date_time.replace(tzinfo=None)
            return date_time
        except ValueError:
            LOG.exception("String %s is not a valid isotime", isotime)
            msg = 'Failed to parse the timestamp value %s' % isotime
            raise api_exception.ParameterException(msg)

    def _validate_filter(self, filter_expr):
        jsonschema.validate(filter_expr, self.schema)

    def _validate_orderby(self, orderby_expr):
        if self.allow_order_fields:
            pattern_properties = \
                self.orderby_schema["items"]["patternProperties"]
            fields, direct = list(pattern_properties.items())[0]
            fields = _list_to_regexp(self.allow_order_fields,
                                     self.regexp_prefix)
            new_pattern_properties = {fields: direct}
            self.orderby_schema["items"]["patternProperties"] = \
                new_pattern_properties

        jsonschema.validate(orderby_expr, self.orderby_schema)

    def _validate_aggr(self, aggr_expr):
        jsonschema.validate(aggr_expr, self.aggr_schema)
