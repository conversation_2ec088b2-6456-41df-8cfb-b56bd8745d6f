from functools import wraps
from flask import request, current_app
from biz.api_exception import T2NotFound


def get_client_ip():
    ip_type = 'Client_Real_IP'
    ip = request.headers.get(ip_type, '')
    if ip == '':
        ip_type = 'X_Real_IP'
        ip = request.headers.get(ip_type, '')
    if ip == '':
        ip_type = 'X-Forwarded-For'
        ip = request.headers.get(ip_type, '')
    if ip == '':
        ip_type = 'remote_addr'
        ip = getattr(request, ip_type, '')
    if ip == '':
        ip_type = ''
    return ip, ip_type


def only_ip_whitelist(f):
    @wraps(f)
    def decorator(*args, **kwargs):
        ip, _ = get_client_ip()
        ip_whitelist = current_app.config["IP_WHITELIST"]
        if ip_whitelist and ip not in ip_whitelist:
            raise T2NotFound()
        return f(*args, **kwargs)
    return decorator