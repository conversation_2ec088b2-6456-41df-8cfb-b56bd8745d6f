drop table if exists tenant_testno2_biz.base_excel_book_conf;
create table tenant_testno2_biz.base_excel_book_conf
(
    id          serial primary key,
    conf_code   text              not null,
    conf_name   text              not null,
    conf_alias  text              not null,
    conf_type   text              not null,
    module_code text              not null,
    user_id     integer           not null,
    file_name   text              not null,
    fmt         text,
    other       json,
    creator     integer           not null,
    modifier    integer,
    is_deleted  integer default 0 not null,
    create_time timestamp         not null,
    modify_time timestamp
)
    tablespace tenant_testno2_dev1_product_dev
    distributed by (id);
select setval('tenant_testno2_biz.base_excel_book_conf_id_seq', 100000);


comment on table tenant_testno2_biz.base_excel_book_conf is 'excel_book配置';
comment on column tenant_testno2_biz.base_excel_book_conf.id is '主键ID';
comment on column tenant_testno2_biz.base_excel_book_conf.conf_code is '配置代码';
comment on column tenant_testno2_biz.base_excel_book_conf.conf_name is '配置名称';
comment on column tenant_testno2_biz.base_excel_book_conf.conf_alias is '别名';
comment on column tenant_testno2_biz.base_excel_book_conf.conf_type is '配置类型';
comment on column tenant_testno2_biz.base_excel_book_conf.module_code is '模块名称';
comment on column tenant_testno2_biz.base_excel_book_conf.user_id is '用户id';
comment on column tenant_testno2_biz.base_excel_book_conf.file_name is '文件名称';
comment on column tenant_testno2_biz.base_excel_book_conf.fmt is '格式化显示格式';
comment on column tenant_testno2_biz.base_excel_book_conf.other is '其他扩展信息';


drop table if exists tenant_testno2_biz.base_excel_sheet_conf;
create table tenant_testno2_biz.base_excel_sheet_conf
(
    id           serial primary key,
    book_id      integer   not null,
    sheet_name   text      not null,
    alias_name   text      not null,
    freeze_col   text      not null,
    freeze_row   text      not null,
    contain_size integer   not null default 0,
    query_type   text      not null,
    func_code    text      not null,
    idx          text      not null,
    display      integer   not null default 1,
    other        json,
    creator      integer   not null,
    modifier     integer,
    is_deleted   integer            default 0 not null,
    create_time  timestamp not null,
    modify_time  timestamp
)
    tablespace tenant_testno2_dev1_product_dev
    distributed by (id);

select setval('tenant_testno2_biz.base_excel_sheet_conf_id_seq', 100000);

comment on table tenant_testno2_biz.base_excel_sheet_conf is 'excel_sheet配置';

comment on column tenant_testno2_biz.base_excel_sheet_conf.id is '主键ID';
comment on column tenant_testno2_biz.base_excel_sheet_conf.book_id is 'excel_book id';
comment on column tenant_testno2_biz.base_excel_sheet_conf.sheet_name is 'excel表单名称';
comment on column tenant_testno2_biz.base_excel_sheet_conf.alias_name is 'excel表单别名';
comment on column tenant_testno2_biz.base_excel_sheet_conf.freeze_col is '锁定列';
comment on column tenant_testno2_biz.base_excel_sheet_conf.freeze_row is '锁定行';
comment on column tenant_testno2_biz.base_excel_sheet_conf.contain_size is '是否包含尺码';
comment on column tenant_testno2_biz.base_excel_sheet_conf.query_type is '查询类型';
comment on column tenant_testno2_biz.base_excel_sheet_conf.func_code is '查询方法名称';
comment on column tenant_testno2_biz.base_excel_sheet_conf.idx is '序号';
comment on column tenant_testno2_biz.base_excel_sheet_conf.display is '是否显示';
comment on column tenant_testno2_biz.base_excel_sheet_conf.other is '其他扩展信息';

drop table if exists tenant_testno2_biz.base_excel_columns_conf;
create table tenant_testno2_biz.base_excel_columns_conf
(
    id          serial primary key,
    sheet_id    integer   not null,
    col_code    text      not null,
    col_name    text      not null,
    col_alias   text      not null,
    idx         text      not null,
    value_type  text      not null,
    value_fmt   text      not null,
    editable    integer   not null default 0,
    display     integer   not null default 1,
    is_size     integer   not null default 0,
    width       float     not null default 8,
    size_width  float,
    size_fold   integer   not null default 1,
    is_hide     integer   not null default 0,
    is_import   integer   not null default 0,
    header_fmt  json,
    row_fmt     json,
    other       json,
    creator     integer   not null,
    modifier    integer,
    is_deleted  integer            default 0 not null,
    create_time timestamp not null,
    modify_time timestamp
)
    tablespace tenant_testno2_dev1_product_dev
    distributed by (id);

comment on table tenant_testno2_biz.base_excel_columns_conf is 'excel_sheet配置';
comment on column tenant_testno2_biz.base_excel_columns_conf.id is '主键ID';
comment on column tenant_testno2_biz.base_excel_columns_conf.sheet_id is 'excel_sheet 代码';
comment on column tenant_testno2_biz.base_excel_columns_conf.col_code is '列名称code';
comment on column tenant_testno2_biz.base_excel_columns_conf.col_name is '列名称';
comment on column tenant_testno2_biz.base_excel_columns_conf.col_alias is '列别名';
comment on column tenant_testno2_biz.base_excel_columns_conf.idx is '列顺序';
comment on column tenant_testno2_biz.base_excel_columns_conf.value_type is '值类型';
comment on column tenant_testno2_biz.base_excel_columns_conf.value_fmt is '格式化';
comment on column tenant_testno2_biz.base_excel_columns_conf.editable is '是否可编辑';
comment on column tenant_testno2_biz.base_excel_columns_conf.display is '是否显示';
comment on column tenant_testno2_biz.base_excel_columns_conf.is_size is '是否尺码列';
comment on column tenant_testno2_biz.base_excel_columns_conf.width is '列宽';
comment on column tenant_testno2_biz.base_excel_columns_conf.size_width is '宽度';
comment on column tenant_testno2_biz.base_excel_columns_conf.size_fold is '是否折叠列';
comment on column tenant_testno2_biz.base_excel_columns_conf.is_hide is '是否隐藏';
comment on column tenant_testno2_biz.base_excel_columns_conf.is_hide is '是否必须导入列';
comment on column tenant_testno2_biz.base_excel_columns_conf.header_fmt is '表头列样式';
comment on column tenant_testno2_biz.base_excel_columns_conf.row_fmt is '数据行样式';
comment on column tenant_testno2_biz.base_excel_columns_conf.other is '其他扩展信息';


select * from tenant_testno2_biz.base_table_conf;
drop table if exists tenant_testno2_biz.base_table_conf;
create table tenant_testno2_biz.base_table_conf
(
    id           serial primary key,
    page_code    text      not null,
    code         text      not null,
    name         text      not null,
    type         text      not null,
    alias        text      not null,
    contain_size integer   not null default 0,
    idx          text      not null,
    other        json,

    creator      integer   not null,
    modifier     integer,
    is_deleted   integer            default 0 not null,
    create_time  timestamp not null,
    modify_time  timestamp
)
    tablespace tenant_testno2_dev1_product_dev
    distributed by (id);

drop table if exists tenant_testno2_biz.base_table_columns_conf;
create table tenant_testno2_biz.base_table_columns_conf
(
    id          serial primary key,
    code        text      not null,
    name        text      not null,
    alias       text      not null,
    hidden      integer   not null default 0,
    display     integer   not null default 1,
    editable    integer   not null default 0,
    width       float     not null default 8,
    is_size     integer   not null default 0,
    value_type  text      not null,
    value_fmt   text      not null,
    size_fold   integer   not null default 1,
    size_width  float,
    idx         text      not null,

    other       json,

    creator     integer   not null,
    modifier    integer,
    is_deleted  integer            default 0 not null,
    create_time timestamp not null,
    modify_time timestamp
)
    tablespace tenant_testno2_dev1_product_dev
    distributed by (id);