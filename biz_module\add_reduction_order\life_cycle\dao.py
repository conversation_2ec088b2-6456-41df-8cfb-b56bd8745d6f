from typing import List, Dict

from fastboot.utils.nest import egg
from ourbatis import sql_func

from biz_module.add_reduction_order.life_cycle import typedef as td


@egg
@sql_func(namespace='life_cycle_aggregation_dimension')
class AggregationDimensionDao(object):

    def query_aggregation_dimension_org_filter(self, req: td.AggregationDimensionFilterRequest) -> List[Dict]:

        ...

    def query_aggregation_dimension_filter(self, req: td.AggregationDimensionFilterRequest) -> List[Dict]:

        ...

    def query_aggregation_dimension_list(self, req: td.AggregationDimensionListRequest) -> List[Dict]:

        ...

    def query_aggregation_dimension_base_kpi(self, req: td.AggregationDimensionFilterRequest) -> Dict:

        ...


    def query_aggregation_dimension_line_chart(self, req: td.AggregationDimensionLineChartRequest) -> List[Dict]:

        ...

    def query_aggregation_dimension_skc_list(self, req: td.AggregationDimensionFilterRequest) -> List[Dict]:

        ...


    def query_aggregation_dimension_refer_skc_list(self, req: td.AggregationDimensionFilterRequest) -> List[Dict]:

        ...


@egg
@sql_func(namespace='life_cycle_refer_skc')
class ReferSkcDao(object):

    def query_refer_skc_org_filter(self, req: td.ReferSkcFilterRequest) -> List[Dict]:

        ...
    def query_refer_skc_filter(self, req: td.ReferSkcFilterRequest) -> List[Dict]:

        ...

    def query_refer_skc_kpi(self, req) -> List[Dict]:

        ...

    def query_refer_skc_week_kpi(self, req) -> List[Dict]:

        ...

    def query_refer_skc_refer_skc_filter(self, req: td.ReferSkcFilterRequest) -> List[Dict]:

        ...

    def query_refer_skc_refer_skc_list(self, req: td.ReferSkcReferSkcListRequest) -> List[Dict]:

        ...

    def is_exist_refer_skc(self, req: td.AddOrRemoveReferSkcRequest) -> bool:

        ...

    def add_refer_skc(self, req: td.AddOrRemoveReferSkcRequest):

        ...

    def add_refer_skc_list(self, req):

        ...

    def remove_refer_skc(self, req: td.AddOrRemoveReferSkcRequest):

        ...

    def remove_refer_skc_list(self, req: td.SaveReferSkcRequest):

        ...


