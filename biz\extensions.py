from flask_caching import C<PERSON>
from flask_babel import Babel
from flask_login import <PERSON><PERSON><PERSON>ana<PERSON>
from flask_session import Session
from biz.common.lz_log.log import LZLog
from biz.modules import AddIns
from flask_mail import Mail
from biz.common.lz_api_log import APILog
from flask_limiter import Limiter
from flask_limiter.util import get_ipaddr
# Setup flask cache
cache = Cache()
babel = Babel()
mail = Mail()
session = Session()
lzlog = LZLog("biz")
api_log = APILog()
add_ins = AddIns()

login_manager = LoginManager()
login_manager.login_view = "main.login"
login_manager.login_message_category = "warning"

# @login_manager.user_loader
# def load_user(user_id):
#     from biz_module.base.auth.models import User
#
#     return User.query.get(user_id)

limiter = Limiter(key_func=get_ipaddr)