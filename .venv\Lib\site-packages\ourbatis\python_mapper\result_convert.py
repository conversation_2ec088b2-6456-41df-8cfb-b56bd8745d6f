from typing import Any, List, Union, Mapping, Dict

from pandas import Data<PERSON>rame
from pydantic.fields import MAPPING_LIKE_SHAPES
from pydantic.fields import <PERSON><PERSON>ield
from pydantic.main import ModelMetaclass
from sqlalchemy.engine import Row
from sqlalchemy.engine.cursor import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from ourbatis.exceptions import ResultValidFailed
from ourbatis.const import LIST_LIKE_SHAPES
from ourbatis.utils import logger


def mapping_row_data(row: Row, result_field: ModelField) -> dict:
    if (
        isinstance(result_field.type_, ModelMetaclass)
        or result_field.type_ in (Mapping, Dict)
        or result_field.shape in MAPPING_LIKE_SHAPES
    ):
        keys = map(str.lower, row.keys())
        result = {key: value for key, value in zip(keys, row)}
    else:
        result = row._data[0]
    return result


def mapping_result_data(
    result_field: ModelField, result_data: Union[Row, List[Row]]
) -> Any:
    if result_data is None:
        logger.info("have no return data")
        return None

    if isinstance(result_data, list):
        result_data = [mapping_row_data(data, result_field) for data in result_data]
    else:
        result_data = mapping_row_data(result_data, result_field)

    result, errors = result_field.validate(
        result_data,
        {},
        loc=(
            f"get value: {result_data!r}",
            f"check type: {result_field.type_}",
            result_field.alias,
        ),
    )
    if errors:
        # 返回结果无法转换为函数定义的类型,直接报错,避免错误隐藏到后续流程
        logger.error(f"param parse error info==>{errors}")
        raise ResultValidFailed

    return result


def get_call_return(result_field: ModelField, result_data: CursorResult) -> Any:

    result = result_data
    if not result_field:
        logger.debug("have not set return signature")
        return result

    if result_field.shape in LIST_LIKE_SHAPES:
        result_data = result.fetchall()
        result = mapping_result_data(result_field, result_data)
    elif result_field.type_ == DataFrame:
        data, names = result.fetchall(), result.keys()
        result = DataFrame.from_records(data, columns=names, coerce_float=True)
    else:
        result_data = result.fetchone()
        result = mapping_result_data(result_field, result_data)

    return result
