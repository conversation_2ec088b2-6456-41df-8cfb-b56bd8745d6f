<?xml version="1.0"?>
<mapper namespace="default">
    <!--todo 优化 -->
    <mix id="upsert_auth_region">
        with upsert as (
            update biz.base_region_auth
            set modify_time = now(),
            modifier_id = #{body.modifier_id}
            where auth_record_id = #{body.auth_record_id}
            and is_deleted = 0
            and org_type = #{body.org_type}
            and operating_unit_sk = #{body.operating_unit_sk}
            and org_sk = #{body.org_sk}
            returning *
        )
        insert into biz.base_region_auth(auth_record_id, org_sk, operating_unit_sk,org_type,create_time,creator_id,is_deleted)
        select #{body.auth_record_id},#{body.org_sk},#{body.operating_unit_sk},#{body.org_type},now(),#{body.creator_id}, #{body.is_deleted}
        where not exists( select 1 from upsert where auth_record_id = #{body.auth_record_id} and org_sk = #{body.org_sk} and operating_unit_sk=#{body.operating_unit_sk})
        returning id;
    </mix>
    <select id="list_region_tree_type">
        select distinct org_type,
        case
            when org_type = 2 then '商品管理权限'
            when org_type = 3 then '零售管理权限'
            else org_name
        end as org_name
        from dm.dim_org_integration
        where org_type != 1 -- 排除集团公司
        order by org_type asc
    </select>
    <!--根据org_type role_id获取树 包括门店 -->
    <select id="list_region_by_org_type">
        select
            org_sk,
            org_name,
            COALESCE(parent_org_sk,0) as parent_org_sk,
            COALESCE(operating_unit_sk ,0) as operating_unit_sk,
            0 as operating_type,
            COALESCE(org_flag,0) as org_flag
        from dm.dim_org_integration
        where org_type = #{org_type}
        and   org_type != 1   -- 排除集团公司
        and  org_flag in (1,2,3) -- 层级 + 门店 包括仓库
        and status = '正常'
        order by convert_to(org_name,'GBK');
    </select>
    <!--获取用户的auth region -->
    <select id="list_auth_record_region_by_org_type">
        select
            org_sk,
            operating_unit_sk
        from biz.base_region_auth
        where auth_record_id = #{auth_record_id}
        and org_type = #{org_type}
        and  is_deleted = 0
    </select>
    <select id="admin_get_region_tree">
        select org_sk,
            org_name,
            COALESCE(parent_org_sk,0) as parent_org_sk,
            1 as operation_type
            from dm.dim_org_integration
            where org_type = #{org_type}
            and  org_flag is null -- 过滤掉门店仓库
            and status = '正常'
            order by convert_to(org_name,'GBK');
    </select>

    <select id="get_authorized_region_list">
        with a as (
            select
                org_sk, -- 只有门店会重复sk 下面过滤掉门店后 不用担心重复问题
                org_name,
                parent_org_sk,
                org_long_sk,
                org_code,
                operating_unit_sk
            from dm.dim_org_integration
            where org_type = #{org_type}
            and  org_flag = 3 -- 过滤掉门店仓库
            and status = '正常'
        ),all_org as (
            select
                a.org_sk,
                a.org_code,
                org_name,
                org_long_sk,
                parent_org_sk,
                a.operating_unit_sk,
                <if test=" _is_admin">
                    'down' as way
                </if>
                <if test="not _is_admin">
                    way
                </if>
            from a
            @{auth_sql_inner_join_snap(
                            region_field=a.org_sk,
                            operating_unit_field=a.operating_unit_sk
                )}
        ) select
                org_sk,
                org_code,
                org_name,
                org_long_sk,
                COALESCE(parent_org_sk, 0) as parent_org_sk,
                COALESCE(operating_unit_sk, 0) as operating_unit_sk,
                string_agg(way,'|') as disabled
            from all_org
            group by org_sk,org_code,org_name,parent_org_sk,operating_unit_sk,org_long_sk
            order by convert_to(org_name,'GBK');
    </select>

    <select id="get_authorized_region_list_v2">
        with a0 as (
            SELECT a.org_sk, a.org_name, org_long_code, parent_org_sk, org_code, array_length(string_to_array(a.org_long_code, ':'), 1) org_length, org_long_sk, operating_unit_sk
            FROM dm.dim_org_integration AS a
            WHERE array_length(string_to_array(a.org_long_code, ':'), 1) &lt;=3 AND a.org_flag =3
            AND org_name != '奥莱店'
        )
        , a as(
            SELECT *
            FROM a0
            WHERE org_length &lt;= 2 OR '直营' = ANY(string_to_array(org_long_code, ':'))
            )
        ,all_org as (
            select
                a.org_sk,
                a.org_code,
                org_name,
                org_long_sk,
                parent_org_sk,
                a.operating_unit_sk,
                <if test=" _is_admin">
                    'down' as way
                </if>
                <if test="not _is_admin">
                    way
                </if>
            from a
            @{auth_sql_inner_join_snap(
                            region_field=a.org_sk,
                            operating_unit_field=a.operating_unit_sk
                )}
        ) select
                org_sk,
                org_code,
                org_name,
                org_long_sk,
                COALESCE(parent_org_sk, 0) as parent_org_sk,
                COALESCE(operating_unit_sk, 0) as operating_unit_sk,
                string_agg(way,'|') as disabled
            from all_org
            group by org_sk,org_code,org_name,parent_org_sk,operating_unit_sk,org_long_sk
            order by convert_to(org_name,'GBK') desc;
    </select>

    <select id="get_region_list">
        with all_org as (
            select
                org_sk, -- 只有门店会重复sk 下面过滤掉门店后 不用担心重复问题
                org_name,
                parent_org_sk,
                org_long_sk,
                org_code,
                operating_unit_sk,
                'down' as way
            from dm.dim_org_integration
            where org_type = 2
            and  org_flag = 3 -- 过滤掉门店仓库
            and status = '正常'
        ) select
                org_sk,
                org_code,
                org_name,
                org_long_sk,
                COALESCE(parent_org_sk, 0) as parent_org_sk,
                COALESCE(operating_unit_sk, 0) as operating_unit_sk,
                string_agg(way,'|') as disabled
            from all_org
            group by org_sk,org_code,org_name,parent_org_sk,operating_unit_sk,org_long_sk
            order by convert_to(org_name,'GBK');
    </select>

    <select id="list_authorized_region_org_sk">
        select org_sk from dm.dim_org_integration
        where org_flag = 3 and status = '正常' @{auth_filter_and_in_all(region_field=org_sk)}
    </select>

    <select id="list_authorized_region_org_sk_include_store">
        select org_sk from dm.dim_org_integration
        where status = '正常' @{auth_filter_and_in_all(region_field=org_sk)}
    </select>

    <select id="list_authorized_region_org_sk_by_brand_code">
        select it.org_code from dm.dim_org_integration as it
        inner join edw.dim_brand_mapping as map 
       	on it.operating_unit_sk = map.org_sk and map.mapping_type = 1 and it.org_type = 2 
        where it.org_flag = 3 and map.brand_code = #{brand_code} and 
              status = '正常' @{auth_filter_and_in_all(region_field=it.org_sk)}
    </select>

    <select id="list_authorized_region_store_sk">
        select it.org_sk from dm.dim_org_integration as it
        inner join edw.dim_brand_mapping as map 
       	on it.operating_unit_sk = map.org_sk and map.mapping_type = 1 and it.org_type = 2
        where it.org_flag = 1 
              <if test="brand_code">
                <if test="brand_code.__class__.__name__ == 'list'">
                    and map.brand_code in ${brand_code}
                </if>
                <if test="brand_code.__class__.__name__ != 'list'">
                    and map.brand_code = #{brand_code}
                </if> 
              </if>
              and status = '正常' @{auth_filter_and_in_all(region_field=it.org_sk)}
    </select>

    <select id="list_authorized_region_warehouse_sk">
        select it.org_sk from dm.dim_org_integration as it
        inner join edw.dim_brand_mapping as map
        on it.operating_unit_sk = map.org_sk and map.mapping_type = 1 and it.org_type = 2
        where it.org_flag = 2 
              <if test="brand_code">
                <if test="brand_code.__class__.__name__ == 'list'">
                    and map.brand_code in ${brand_code}
                </if>
                <if test="brand_code.__class__.__name__ != 'list'">
                    and map.brand_code = #{brand_code}
                </if> 
              </if> 
              and it.status = '正常' @{auth_filter_and_in_all(region_field=it.org_sk)} 
    </select>

    <select id="list_authorized_region_org">
        select it.org_sk, it.org_code, it.org_flag from dm.dim_org_integration as it
        inner join edw.dim_brand_mapping as map
       	on it.operating_unit_sk = map.org_sk and map.mapping_type = 1 and it.org_type = 2
        where 1 = 1
              <if test="brand_code">
                <if test="brand_code.__class__.__name__ == 'list'">
                    and map.brand_code in ${brand_code}
                </if>
                <if test="brand_code.__class__.__name__ != 'list'">
                    and map.brand_code = #{brand_code}
                </if>
              </if>
              and status = '正常' @{auth_filter_and_in_all(region_field=it.org_sk)}
        group by it.org_sk, it.org_code, it.org_flag
    </select>

    <select id="list_authorized_region_org_code">
        select org_code from dm.dim_org_integration
        where org_flag = 3 and status = '正常' @{auth_filter_and_in_all(region_field=org_sk)} 
    </select>

    <select id="list_auth_region_tree">
        select
        oi.org_sk::varchar||oi.operating_unit_sk::varchar as id,
        oi.org_sk,
        oi.org_sk::varchar||oi.operating_unit_sk::varchar as tree_unique_sk,
        oi.parent_org_sk::varchar||oi.operating_unit_sk::varchar as parent_org_sk,
        oi.operating_unit_sk,
        org_name,
        <!--COALESCE(oi.parent_org_sk,0) as parent_org_sk,-->
        case when a.id is null then 0
        else 1
        end as operation_type,
        oi.org_type
        from dm.dim_org_integration oi
        left join biz.base_region_auth a
        on oi.org_sk = a.org_sk
        and oi.operating_unit_sk = a.operating_unit_sk
        and a.auth_record_id = #{auth_record_id}
        and a.is_deleted = 0
        where oi.org_type = #{org_type}
        and oi.org_flag =3
        and status = '正常'
        order by org_name;
    </select>
    <!-- 查询授权的组织层级 -->
    <select id="list_record_auth_region">
        with region_mapping as (
            select
                org_sk, operating_unit_sk, auth_record_id
            from biz.base_region_auth
            where is_deleted = 0
            and auth_record_id in ${auth_record_ids}
        ),
        auth_region_infos as (
            select
                org_name,
                mapping.org_sk,
                mapping.operating_unit_sk,
                mapping.auth_record_id
            from region_mapping mapping
                inner join dm.dim_org_integration org
                on mapping.org_sk = org.org_sk
                and mapping.operating_unit_sk = org.operating_unit_sk
             )
        select
            infos.org_sk,
            infos.org_name,
            infos.auth_record_id,
            infos.operating_unit_sk,
            org.org_name as operating_unit_name,
            org_type,
            case
                when org.org_type = 2 then '商品管理权限'
                when org.org_type = 3 then '零售管理权限'
            end as org_type_name
        from auth_region_infos infos
            inner join dm.dim_org_integration org on infos.operating_unit_sk = org.org_sk
    </select>

    <select id="get_auth_regions_by_auth_record_id">
            select
                org_sk, operating_unit_sk, org_type
            from biz.base_region_auth
            where is_deleted = 0
            and auth_record_id = #{auth_record_id}
    </select>

    <select id="get_auth_record_down_region_from_view">
         select
                org_sk, operating_unit_sk
            from biz.base_region_auth_mapping
            where auth_record_id = #{auth_record_id}
            and way= 'down'
            and org_sk in ${org_sks} and operating_unit_sk in ${operating_unit_sks}
    </select>


    <insert id="save_auth_record_region">
         insert into biz.base_region_auth(
                auth_record_id,
                org_sk,
                operating_unit_sk,
                org_type,
                create_time,
                creator_id,
                is_deleted
            )
         values(
                #{body.auth_record_id},
                #{body.org_sk},
                #{body.operating_unit_sk},
                #{body.org_type},
                now(),
                #{body.creator_id},
                #{body.is_deleted}
        )returning id;
    </insert>


    <mix id="update_region_auth_mapping_up">
        BEGIN;
        DELETE FROM biz.base_region_auth_mapping where auth_record_id = #{auth_record_id} and way = 'up';
        WITH RECURSIVE tree(org_sk, org_code ,org_hierarchy_order, operating_unit_sk, parent_org_sk)
        AS (
                SELECT ro.org_sk, ro.org_code, ro.org_hierarchy_order, ro.operating_unit_sk, parent_org_sk
                FROM dm.dim_org_integration ro
                INNER JOIN biz.base_region_auth ra
                ON ra.org_sk = ro.org_sk
                AND ra.operating_unit_sk = ro.operating_unit_sk
                AND auth_record_id = #{auth_record_id}
                AND ra.is_deleted = 0
                UNION ALL
                SELECT  t.org_sk, t.org_code, t.org_hierarchy_order, t.operating_unit_sk, t.parent_org_sk
                FROM dm.dim_org_integration t,
                     tree
                WHERE t.org_sk = tree.parent_org_sk
                  and t.operating_unit_sk = tree.operating_unit_sk
        )

        INSERT INTO biz.base_region_auth_mapping
            (auth_record_id, org_sk, org_code, way, level_code, operating_unit_sk)
        SELECT DISTINCT
                        #{auth_record_id}, tree.org_sk, tree.org_code,
                        'up' as way,org_hierarchy_order as level_code,operating_unit_sk
        FROM tree;
        COMMIT;
    </mix>
    <mix id="update_region_auth_mapping_down">
        BEGIN;
        DELETE FROM biz.base_region_auth_mapping where auth_record_id = #{auth_record_id} and way = 'down';
        WITH RECURSIVE tree(org_sk, org_code ,org_hierarchy_order, operating_unit_sk, parent_org_sk)
        AS (
                SELECT ro.org_sk, ro.org_code, ro.org_hierarchy_order, ro.operating_unit_sk, parent_org_sk
                FROM dm.dim_org_integration ro
                INNER JOIN biz.base_region_auth ra
                ON ra.org_sk = ro.org_sk
                AND ra.operating_unit_sk = ro.operating_unit_sk
                AND auth_record_id = #{auth_record_id}
                AND ra.is_deleted = 0
                UNION ALL
                SELECT  t.org_sk, t.org_code, t.org_hierarchy_order, t.operating_unit_sk, t.parent_org_sk
                FROM dm.dim_org_integration t,
                     tree
                WHERE t.parent_org_sk = tree.org_sk
                  and t.operating_unit_sk = tree.operating_unit_sk
        )
        INSERT INTO biz.base_region_auth_mapping
            (auth_record_id, org_sk, org_code, way, level_code, operating_unit_sk)
        SELECT DISTINCT
                        #{auth_record_id}, tree.org_sk, tree.org_code,
                        'down' as way,org_hierarchy_order as level_code,operating_unit_sk
        FROM tree;
        COMMIT;
    </mix>

    <select id="list_parent_sk">
        select parent_org_sk from dm.dim_org_integration
        where org_sk in
                <foreach item="org_sk"  collection="org_sk_list"
                      open="(" separator="," close=")">
                        #{org_sk}
                </foreach>
    </select>

    <delete id="remove_record_auth_region">
        update biz.base_region_auth
            set modify_time = #{body.modify_time},
            modifier_id = #{body.modifier_id},
            is_deleted = 1
            where auth_record_id = #{body.auth_record_id}
            returning id
    </delete>

    <select id="list_inherit_region_sk">
        WITH RECURSIVE tree( org_sk, operating_unit_sk, parent_org_sk, org_flag) AS (
            SELECT ro.org_sk, ro.operating_unit_sk, parent_org_sk, org_flag
            FROM dm.dim_org_integration ro
            WHERE
            org_sk in
            <foreach item="org_sk"  collection="org_sk_list"
                  open="(" separator="," close=")">
                    #{org_sk}
            </foreach>

            <if test="operating_unit_sk">
                and operating_unit_sk = #{operating_unit_sk}
            </if>
            UNION ALL
            SELECT
                t.org_sk, t.operating_unit_sk, t.parent_org_sk, t.org_flag
            FROM dm.dim_org_integration t,
                 tree
            WHERE t.operating_unit_sk = tree.operating_unit_sk

                <if test="way == 'up'">
                    and t.org_sk = tree.parent_org_sk
                </if>

                <if test="way == 'down'">
                    and t.parent_org_sk = tree.org_sk
                </if>
            )
        select org_sk from tree
        <if test="org_flags">
            where org_flag in
            <foreach item="org_flag"  collection="org_flags"
                  open="(" separator="," close=")">
                    #{org_flag}
            </foreach>
        </if>
    </select>

    <select id="list_region_info_by_sks" >
        select
            org_sk, operating_unit_sk, org_code, org_name,
            org_type, org_hierarchy_order
        from dm.dim_org_integration
        where org_sk in
            <foreach item="org_sk"  collection="org_sk_list"
                  open="(" separator="," close=")">
                    #{org_sk}
            </foreach>
        and org_flag = 3
        order by org_hierarchy_order
        ;
    </select>

    <select id="list_region_by_sks_org_order" >
        select
            org_sk, operating_unit_sk, org_code, org_name,
            org_type, org_hierarchy_order, org_order
        from dm.dim_org_integration
        where org_sk in
            <foreach item="org_sk"  collection="org_sk_list"
                  open="(" separator="," close=")">
                    #{org_sk}
            </foreach>
        and org_flag = 3
        order by org_order
        ;
    </select>

    <select id="list_org_and_store_children">
        select distinct org_sk from edw.dim_org_integration
        where org_sk in ${org_sks} or (parent_org_sk in ${org_sks} and org_flag != 3)
    </select>


</mapper>
