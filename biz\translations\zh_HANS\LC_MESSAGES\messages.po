# Chinese (Simplified) translations for PROJECT.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2019-12-12 17:08+0800\n"
"PO-Revision-Date: 2019-12-12 16:25+0800\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_<PERSON>\n"
"Language-Team: zh_<PERSON> <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.7.0\n"

#: biz/api_exception.py:25
msgid "服务器内部异常（失败）"
msgstr "服务器内部异常（失败）"

#: biz/api_exception.py:27
msgid "System busy, please contact system admin or try again later"
msgstr "系统繁忙，请联系系统管理员或稍后重试"

#: biz/api_exception.py:175
msgid "DB access failed"
msgstr "数据库访问失败"

#: biz/api_exception.py:180
msgid "Query failed"
msgstr "查询失败"

#: biz/api_exception.py:185
msgid "Invalid parameter"
msgstr "参数无效"

#: biz/api_exception.py:190
msgid "The server is busy, please try again later"
msgstr "服务器繁忙，请稍后重试"

#: biz/api_exception.py:195
msgid "Request content does not exist"
msgstr "请求的内容不存在"

#: biz/api_exception.py:200
msgid "The database is busy, please try again later"
msgstr "数据库繁忙，请稍后重试"

#: biz/api_exception.py:205
msgid "Query error"
msgstr "查询失败"

#: biz/api_exception.py:210 biz/api_exception.py:215
#: biz_module/base/auth/types.py:45 biz_module/base/auth/types.py:66
msgid "Invalid username or password"
msgstr "用户名或密码无效"

#: biz/api_exception.py:220
msgid "This user already exists"
msgstr "用户已经存在"

#: biz/api_exception.py:225
msgid "Captcha is error"
msgstr "验证码错误"

#: biz/api_exception.py:230
msgid "The password is not legal."
msgstr "密码不合法"

#: biz/api_exception.py:235
msgid "The new password is not legal."
msgstr "新密码不合法"

#: biz/api_exception.py:240
msgid "The password entered twice is different."
msgstr "密码和确认密码不一致"

#: biz/api_exception.py:245
msgid "Invalid user or mailbox"
msgstr "无效的用户或邮箱"

#: biz/api_exception.py:250
msgid "Invalid csrf token"
msgstr "csrf token 无效"

#: biz/api_exception.py:255
msgid "You need to sign in to use this feature"
msgstr "您需要先登录才能使用此功能"

#: biz/api_exception.py:260
msgid "The resource type already exists"
msgstr "资源类型已经存在"

#: biz/api_exception.py:265
msgid "The resource needs to be empty"
msgstr "这个资源需要为空"

#: biz/api_exception.py:270
msgid "The resource type does not exist"
msgstr "资源类型不存在"

#: biz/api_exception.py:275
msgid "The resource does not exist"
msgstr "资源不存在"

#: biz/api_exception.py:280
msgid "The resource already exists"
msgstr "资源已存在"

#: biz/api_exception.py:285
msgid "The role does not exist"
msgstr "角色不存在"

#: biz/api_exception.py:290
msgid "The user already forbidden"
msgstr "用户已经被禁用"

#: biz/api_exception.py:295
msgid "This data already exists"
msgstr "数据已经存在"

#: biz/api_exception.py:300
msgid "Field type error"
msgstr "文件类型错误"

#: biz/api_exception.py:305
msgid "This resource_type_mapping already exists"
msgstr "resource_type_mapping 已经存在"

#: biz/api_exception.py:310
msgid "This resource_type_mapping not exists"
msgstr "resource_type_mapping 不存在"

#: biz/api_exception.py:315
msgid "The field cannot be empty"
msgstr "这个域不能为空"

#: biz/api_exception.py:320
msgid "The resource attribute does not exist"
msgstr "这个资源属性不存在"

#: biz/api_exception.py:325
msgid "varchar type field length must exist"
msgstr "varchar 类型的域必须有长度"

#: biz/api_exception.py:330
msgid "The file type error"
msgstr "文件类型错误"

#: biz/api_exception.py:335
msgid "Insufficient permissions"
msgstr "没有权限"

#: biz/api_exception.py:340
msgid "This role is in use and cannot be deleted"
msgstr "这个角色还在使用不能删除"

#: biz/api_exception.py:345
msgid "This user is in use and cannot be deleted"
msgstr "这个用户还在使用不能删除"

#: biz/api_exception.py:350
msgid "This email already exists"
msgstr "邮箱已经存在"

#: biz/api_exception.py:355
msgid "This phone number already exists"
msgstr "号码已经存在"

#: biz/types.py:134
msgid "Fail"
msgstr "失败"

#: biz/utils/operation_log.py:13
msgid "Not log in"
msgstr "没有登录"

#: biz/utils/operation_log.py:15 biz_module/base/resource/views.py:54
#: biz_module/base/resource/views.py:171
#: biz_module/base/resource/views.py:386
msgid "insert"
msgstr "插入"

#: biz/utils/operation_log.py:15 biz_module/base/resource/views.py:67
#: biz_module/base/resource/views.py:276
msgid "delete"
msgstr "删除"

#: biz/utils/operation_log.py:15 biz_module/base/resource/views.py:97
#: biz_module/base/resource/views.py:262
#: biz_module/base/resource/views.py:293
#: biz_module/base/resource/views.py:465
msgid "update"
msgstr "更新"

#: biz_module/base/auth/models.py:549 biz_module/base/role/models.py:413
msgid "column name error"
msgstr "column name 错误"

#: biz_module/base/auth/models.py:554
msgid "not found import excel file"
msgstr "没有找到导入的 excel 文件"

#: biz_module/base/auth/types.py:106 biz_module/base/auth/types.py:152
#: biz_module/base/auth/types.py:207
msgid "The email is not legal."
msgstr "邮箱不合法"

#: biz_module/base/auth/types.py:109 biz_module/base/auth/types.py:160
#: biz_module/base/auth/types.py:215
msgid "The phone is not legal."
msgstr "手机号不合法"

#: biz_module/base/auth/types.py:138
msgid "username is not legal"
msgstr "用户名不合法"

#: biz_module/base/auth/types.py:141 biz_module/base/auth/types.py:202
msgid "name is not legal"
msgstr "名字不合法"

#: biz_module/base/auth/types.py:192 biz_module/base/auth/types.py:267
#: biz_module/base/auth/types.py:281 biz_module/base/auth/types.py:294
#: biz_module/base/auth/types.py:308
msgid "current user don't exist"
msgstr "当前用户不存在"

#: biz_module/base/auth/views.py:118
msgid "Login success"
msgstr "登录成功"

#: biz_module/base/auth/views.py:132
msgid ""
"The password has not been modified for a long time. Please change the "
"password as soon as possible."
msgstr "您的密码已经有很长时间没有改过了，请尽快修改"

#: biz_module/base/auth/views.py:165
msgid "logout success"
msgstr "登出成功"

#: biz_module/base/auth/views.py:175
msgid "registration success"
msgstr "注册成功"

#: biz_module/base/auth/views.py:202
msgid "add user success"
msgstr "添加用户成功"

#: biz_module/base/auth/views.py:212
msgid "get user name success"
msgstr "获取用户名称成功"

#: biz_module/base/auth/views.py:222
msgid "get user info success"
msgstr "获取用户信息成功"

#: biz_module/base/auth/views.py:229 biz_module/base/auth/views.py:235
msgid "get users success"
msgstr "获取用户成功"

#: biz_module/base/auth/views.py:249
msgid "update user success"
msgstr "更新用户成功"

#: biz_module/base/auth/views.py:259
msgid "update user status success"
msgstr "更新用户状态成功"

#: biz_module/base/auth/views.py:269 biz_module/base/auth/views.py:283
msgid "delete user success"
msgstr "删除用户成功"

#: biz_module/base/auth/views.py:298
msgid "send email success"
msgstr "发送邮件成功"

#: biz_module/base/auth/views.py:331
msgid "change password success"
msgstr "修改密码成功"

#: biz_module/base/auth/views.py:341
msgid "reset password success"
msgstr "重置密码成功"

#: biz_module/base/auth/views.py:376
msgid "import user info fail"
msgstr "导入用户信息失败"

#: biz_module/base/auth/views.py:378
msgid "import user info success"
msgstr "导入用户信息成功"

#: biz_module/base/query_label/types.py:23
msgid "success"
msgstr "成功"

#: biz_module/base/query_label/types.py:40
#: biz_module/base/query_label/types.py:47
#: biz_module/base/query_label/types.py:49
#: biz_module/base/query_label/types.py:51
#: biz_module/base/query_label/types.py:63
#: biz_module/base/query_label/types.py:66
#: biz_module/base/query_label/types.py:70
#: biz_module/base/query_label/types.py:93
#: biz_module/base/query_label/types.py:96
#: biz_module/base/query_label/types.py:100
#: biz_module/base/query_label/types.py:108
msgid "params error"
msgstr "参数错误"

#: biz_module/base/resource/views.py:44
msgid "Add resource type"
msgstr "添加资源类型"

#: biz_module/base/resource/views.py:57
msgid "add resource type success"
msgstr "添加资源类型成功"

#: biz_module/base/resource/views.py:68
msgid "Delete resource type"
msgstr "删除资源类型"

#: biz_module/base/resource/views.py:71
msgid "delete resource type success"
msgstr "删除资源类型成功"

#: biz_module/base/resource/views.py:83
msgid "Update resource type"
msgstr "更新资源类型"

#: biz_module/base/resource/views.py:86
#, python-format
msgid " %(resource_type_name_old)s to %(resource_type_name)s"
msgstr " %(resource_type_name_old)s to %(resource_type_name)s"

#: biz_module/base/resource/views.py:91 biz_module/base/resource/views.py:195
#, python-format
msgid " parent_id %(parent_id_old)s to %(parent_id)s"
msgstr " parent_id %(parent_id_old)s to %(parent_id)s"

#: biz_module/base/resource/views.py:100
msgid "update resource type success"
msgstr "更新资源类型成功"

#: biz_module/base/resource/views.py:172
#, python-format
msgid "Add resource %(resource_name)s"
msgstr "添加资源 %(resource_name)s"

#: biz_module/base/resource/views.py:175
msgid "add resource success"
msgstr "添加资源成功"

#: biz_module/base/resource/views.py:187
msgid "Update resource"
msgstr "更新资源"

#: biz_module/base/resource/views.py:190
#, python-format
msgid " resource name %(resource_name_old)s to %(resource_name)s"
msgstr " resource name %(resource_name_old)s to %(resource_name)s"

#: biz_module/base/resource/views.py:265
msgid "update resource success"
msgstr "更新资源成功"

#: biz_module/base/resource/views.py:277
#, python-format
msgid "Delete resource %(resource_name)s"
msgstr "删除资源 %(resource_name)s"

#: biz_module/base/resource/views.py:280
msgid "delete resource success"
msgstr "删除资源成功"

#: biz_module/base/resource/views.py:294
#, python-format
msgid "Assign roles %(roles)s to %(resource_name)s"
msgstr "指定角色 %(roles)s 到 %(resource_name)s"

#: biz_module/base/resource/views.py:298
msgid "assign roles success"
msgstr "指定角色成功"

#: biz_module/base/resource/views.py:306
msgid "resource_id"
msgstr "resource_id"

#: biz_module/base/resource/views.py:306
msgid "resource_name"
msgstr "resource_name"

#: biz_module/base/resource/views.py:306
msgid "parent_id"
msgstr "parent_id"

#: biz_module/base/resource/views.py:307
msgid "resource_type_top_id"
msgstr "resource_type_top_id"

#: biz_module/base/resource/views.py:307
msgid "resource_type_id"
msgstr "resource_type_id"

#: biz_module/base/resource/views.py:369
#, python-format
msgid "Extend resource type %(resource_type_id)s attribute"
msgstr "拓展资源类型 %(resource_type_id)s 属性"

#: biz_module/base/resource/views.py:430
msgid "extend resource attribute success"
msgstr "拓展资源属性成功"

#: biz_module/base/resource/views.py:447
#, python-format
msgid "Update resource type mapping %(resource_type_id)s attribute %(field_name)s"
msgstr "更新资源类型映射 %(resource_type_id)s 属性 %(field_name)s"

#: biz_module/base/resource/views.py:453
#: biz_module/base/resource/views.py:456
#: biz_module/base/resource/views.py:459
#: biz_module/base/resource/views.py:462
#, python-format
msgid " %(key)s to %(value)s"
msgstr " %(key)s to %(value)s"

#: biz_module/base/resource/views.py:469
msgid "update resource type mapping success"
msgstr "更新资源类型映射成功"

#: biz_module/base/role/models.py:417
msgid "No imported excel file was selected"
msgstr "没有选择导入的 excel 文件"

#: biz_module/base/role/types.py:52 biz_module/base/role/types.py:75
msgid "role name is not legal"
msgstr "角色名不合法"

#: biz_module/base/role/types.py:54 biz_module/base/role/types.py:78
msgid "The role name already exists"
msgstr "角色名已经存在"

#: biz_module/base/role/types.py:70 biz_module/base/role/types.py:91
#: biz_module/base/role/types.py:123 biz_module/base/role/types.py:155
#: biz_module/base/role/types.py:171
msgid "current edit role don't exist"
msgstr "当前编辑的角色不存在"

#: biz_module/base/role/types.py:106
msgid "page name is not legal"
msgstr "页面名称不合法"

#: biz_module/base/role/types.py:109
msgid "The page name already exists"
msgstr "页面名称已经存在"

#: biz_module/base/role/types.py:138
msgid "api name is not legal"
msgstr "api 名称不合法"

#: biz_module/base/role/types.py:141
msgid "The page don't exists"
msgstr "页面不存在"

#: biz_module/base/role/types.py:143
msgid "The api name already exists"
msgstr "api 名称不存在"

#: biz_module/base/role/types.py:203 biz_module/base/role/types.py:212
msgid "Role don't exist"
msgstr "角色不存在"

#: biz_module/base/role/types.py:214
msgid "The role has been assigned to the user and cannot be deleted"
msgstr "角色已经指定给了用户不能删除"

#: biz_module/base/role/types.py:216
msgid "The role has been assigned page permissions and cannot be deleted"
msgstr "角色已经指定了页面权限不能删除"

#: biz_module/base/role/types.py:218
msgid "The role has been assigned resource permissions and cannot be deleted"
msgstr "角色已经被指定了资源权限不能删除"

#: biz_module/base/role/views.py:37
msgid "get role name success"
msgstr "获取角色名成功"

#: biz_module/base/role/views.py:47
msgid "get role info success"
msgstr "获取角色信息成功"

#: biz_module/base/role/views.py:71
msgid "add role success"
msgstr "添加角色"

#: biz_module/base/role/views.py:86
msgid "update role success"
msgstr "更新角色"

#: biz_module/base/role/views.py:96
msgid "update role status  success"
msgstr "更新角色状态成功"

#: biz_module/base/role/views.py:106
msgid "add page permission success"
msgstr "添加页面权限成功"

#: biz_module/base/role/views.py:116
msgid "query page operation permission success"
msgstr "查询页面操作权限成功"

#: biz_module/base/role/views.py:126
msgid "add operation permission success"
msgstr "添加操作权限成功"

#: biz_module/base/role/views.py:136
msgid "query role users success"
msgstr "查询角色用户成功"

#: biz_module/base/role/views.py:146
msgid "update role permission success"
msgstr "更新角色权限成功"

#: biz_module/base/role/views.py:181
msgid "import role info fail"
msgstr "导入角色信息失败"

#: biz_module/base/role/views.py:183
msgid "import role info success"
msgstr "导入角色信息成功"

#: biz_module/base/role/views.py:202
msgid "Get the permission of resource success"
msgstr "获取资源权限成功"

#: biz_module/base/role/views.py:211
msgid "Set the permission of resource success"
msgstr "设置资源权限成功"

#: biz_module/base/role/views.py:218
msgid "Get the permission of page success"
msgstr "获取页面权限成功"

#: biz_module/base/role/views.py:226
msgid "Set the permission of page success"
msgstr "设置页面权限成功"

#: biz_module/base/role/views.py:240
msgid "delete role success"
msgstr "删除角色成功"
