from ourbatis import sql_func
from typing import List, Dict, Optional

from pandas import DataFrame

from biz_module.base.authorization.auth_record.typedef import RecordRegion, AuthRecordRegionDTO
from biz_module.base.authorization.auth_region.typedef import OrgTree, UpsertAuthRegionRequest, RemoveAuthRegionBody
from biz_module.base.authorization.base.tools import auth_param_extend
from biz_module.base.authorization.auth_region.typedef import OrgTree, AuthRegion, OrgEgg


@sql_func
class AuthRegionDao:
    def list_record_auth_region(self, auth_record_ids: List[int]) -> List[RecordRegion]:
        """
        获取权限组的组织权限
        """
        ...

    def save_auth_record_region(self, body: UpsertAuthRegionRequest) -> int:
        """
        保存 auth_region
        :param body:UpsertAuthRegionRequest
        :return:
        :rtype:
        """
        ...

    def list_region_tree_type(self) -> List[OrgTree]:
        """
        获取树类型列表
        """
        ...

    def get_auth_region_tree(self, auth_record_id):
        """
        获取标准组织树
        """
        ...

    def update_region_auth_mapping_up(self, auth_record_id: int):
        """

        :param auth_record_id:
        :type auth_record_id:
        :return:
        :rtype:
        """
        ...

    def update_region_auth_mapping_down(self, auth_record_id: int):
        """

        :param auth_record_id:
        :type auth_record_id:
        :return:
        :rtype:
        """
        ...

    def list_auth_region_tree(self, auth_record_id: int, org_type: int) -> DataFrame:
        """
        获取授权的组织树
        """
        ...

    @sql_func(decorator_func=auth_param_extend)
    def get_authorized_region_list(self, org_type, way_field_value: Optional[str] = None) -> DataFrame:
        """
        获取授权的组织树
        """
        ...

    @sql_func(decorator_func=auth_param_extend)
    def get_authorized_region_list_v2(self, org_type, way_field_value: Optional[str] = None) -> DataFrame:
        """
        获取授权的组织树
        """
        ...

    def get_region_list(self) -> DataFrame:
        ...

    @sql_func(decorator_func=auth_param_extend)
    def list_authorized_region_org_sk(self, region_auth_way: str, operating_unit_sk: Optional[int] = None) -> List[int]:
        ...

    @sql_func(decorator_func=auth_param_extend)
    def list_authorized_region_org_sk_include_store(self, region_auth_way: str, operating_unit_sk: Optional[int] = None) -> List[int]:
        ...

    @sql_func(decorator_func=auth_param_extend)
    def list_authorized_region_org_sk_by_brand_code(
        self, brand_code: str, region_auth_way: str, operating_unit_sk: Optional[int] = None
    ) -> List[str]:
        ...

    @sql_func(decorator_func=auth_param_extend)
    def list_authorized_region_store_sk(
        self, brand_code: list = None, region_auth_way: str = "down", operating_unit_sk: Optional[int] = None
    ) -> List[int]:
        ...

    @sql_func(decorator_func=auth_param_extend)
    def list_authorized_region_warehouse_sk(
        self, brand_code: str = None, region_auth_way: str = "down", operating_unit_sk: Optional[int] = None
    ) -> List[int]:
        ...

    @sql_func(decorator_func=auth_param_extend)
    def list_authorized_region_org_code(self, region_auth_way: str, operating_unit_sk: Optional[int] = None) -> List[str]:
        ...

    @sql_func(decorator_func=auth_param_extend)
    def list_authorized_region_org(self, region_auth_way: str = "down",
                                   operating_unit_sk: Optional[int] = None,
                                   brand_code: str = None) -> List[OrgEgg]:
        """
        Args:
            region_auth_way: 查询组织上层/下层
                up: 上级
                down: 下级
            brand_code: 品牌code
            operating_unit_sk: 业务实体sk
        Returns:
            List[OrgEgg]: 组织信息体列表
                org_sk
                org_code
                org_flag
        """
        ...

    def admin_get_region_tree(self, org_type) -> DataFrame:
        """
        管理员获取组织树
        """
        ...

    def list_auth_record_region_by_org_type(self, auth_record_id, org_type) -> List[AuthRegion]:
        """
        根据auth_record_id org_type获取已授权的region
        """
        ...

    def list_region_by_org_type(self, org_type) -> DataFrame:
        """
        根据org_type获取region
        """
        ...

    def upsert_auth_region(self, body: UpsertAuthRegionRequest) -> int:
        ...

    def list_inherit_region_sk(
        self, org_sk_list: List[int], org_flags: List = None, operating_unit_sk: str = None, way: str = None
    ) -> List[int]:
        """

        :return:
        :rtype:
        """
        ...

    def list_parent_sk(self, org_sk_list: List[int]) -> List[int]:
        """

        :return:
        :rtype:
        """
        ...

    def remove_record_auth_region(self, body: RemoveAuthRegionBody) -> int:
        """
        删除组织层级授权记录
        @param body:RemoveAuthRegionBody
        @return:
        """
        ...

    def list_region_info_by_sks(self, org_sk_list: List) -> DataFrame:
        """

        :param org_sk_list:
        :type org_sk_list:
        :return:
        :rtype:
        """
        ...

    def list_region_by_sks_org_order(self, org_sk_list: List) -> DataFrame:
        """
        :param org_sk_list:
        :type org_sk_list:
        :return:
        :rtype:
        """
        ...

    def get_auth_regions_by_auth_record_id(self, auth_record_id: int) -> List[AuthRegion]:
        """
        通过auth_record_id获取组织权限
        @param auth_record_id:
        @return:
        """
        ...

    def get_auth_record_down_region_from_view(self, auth_record_id: int, org_sks, operating_unit_sks) -> List[AuthRegion]:
        """
        通过auth_record_id获取up中存在于down的组织
        @param auth_record_id:
        @param org_sks
        @param operating_unit_sks
        @return:
        """
        ...

    def list_org_and_store_children(self, org_sks: List[int]) -> List[int]:
        ...