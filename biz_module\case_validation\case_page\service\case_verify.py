import datetime
import threading
import time
from copy import deepcopy
from decimal import Decimal
from functools import partial
from typing import List
from json import dumps
import requests
from fastboot.utils.inner_message import MessageParam, send_inner_message
from linezone_commonserver.services.auth.org_service import CommonAuthOrgService
from linezone_commonserver.services.auth.typedef import UserAuthOrgReq
from openpyxl.styles import Border, Side, PatternFill, Font
from pydantic import BaseModel
from flask_login import current_user
from biz import BaseService, config
from biz.api_exception import ParameterException
from biz.helpers import TableColumnOpMixin
from biz_module.base.authorization.auth_region.constants import RegionQueryWayEnum, OrgFlagEnum
from biz_module.case_validation.case_page.dao import BizVerifyDao
from biz_module.case_validation.case_page.tools import ListToExcel585
from biz_module.case_validation.case_page.typedef import BizVerifyScreen, BizVerifyTreeOrg, \
    BizVerifyTreeSort, BizVerifyRunCases, BizVerifyPageRequest, BizVerifySingleCase, \
    BizVerifyTreeSubSubResponse, BizVerifyTreeDetailSub, BizVerifyTreeDetail, BizVerifyOverviewReq, \
    BizVerifyResultCheckReq, BizVerifyExportAllRequest, BizVerifyCaseInfo
from biz_module.common.page_filter.service.page_filter_param import PageFilterParamService
from biz_module.common.page_filter.typedef import FilterParam
from biz_module.replenish_allot.base.constants import EXPORT_CENTER_BIZ_VERIFY_API
from biz_module.unity_service.service.context import auth_region_service


def get_all_stock_by_user(func):
    def f(self, params: BaseModel = None):  # 兼容出问题就用kwargs
        req = UserAuthOrgReq(user_id=current_user.user_id)
        res = self.common_auth_org_service.list_user_authorized_operating_unit_orgs(req)
        params.org_sk = [i.org_sk for i in res]

        return func(self, params)
    return f


def get_common_filter_sql(func):
    def f(self, params: BaseModel = None):  # 兼容出问题就用kwargs
        # 处理筛选器
        if isinstance(params, FilterParam):
            params.filters_str, params.filter_items, params.no_alias_filter_str, params.no_alias_filter_items = \
                self.filter_srv.generate_common_filter_sql(params)
        else:
            raise Exception('参数未继承FilterParam类！')

        return func(self, params)
    return f


class ScreenMapping:
    management = [
        {'filter_code': 'J0040', 'group_code': 'management', 'is_enable': 1},
        {'filter_code': 'J0060', 'group_code': 'management', 'is_enable': 1},
        {'filter_code': 'J0070', 'group_code': 'management', 'is_enable': 1}
    ]

    store = [
        {'filter_code': 'd0040', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0050', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0060', 'group_code': 'store', 'is_enable': 1},
        {'filter_code': 'd0065', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0070', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0080', 'group_code': 'store', 'is_enable': 1},
        {'filter_code': 'd0090', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0100', 'group_code': 'store', 'is_enable': 1},
        {'filter_code': 'd0110', 'group_code': 'store', 'is_enable': 1},
        {'filter_code': 'd0120', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0140', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0150', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0160', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0170', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd0180', 'group_code': 'store', 'is_enable': 0},
        {'filter_code': 'd9900', 'group_code': 'store', 'is_enable': 1}
    ]

    skc = [
        {'filter_code': 'k0010', 'group_code': 'skc', 'is_enable': 1},
        {'filter_code': 'k0020', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0030', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0045', 'group_code': 'skc', 'is_enable': 1},
        {'filter_code': 'k0050', 'group_code': 'skc', 'is_enable': 1},
        {'filter_code': 'k0060', 'group_code': 'skc', 'is_enable': 1},
        {'filter_code': 'k0070', 'group_code': 'skc', 'is_enable': 1},
        {'filter_code': 'k0080', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0047', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0090', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0100', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0110', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0120', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0130', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0140', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k0150', 'group_code': 'skc', 'is_enable': 0},
        {'filter_code': 'k9900', 'group_code': 'skc', 'is_enable': 1}
    ]


class BizVerify(BaseService):
    dao: BizVerifyDao = BizVerifyDao()
    common_auth_org_service = CommonAuthOrgService()
    filter_srv = PageFilterParamService()
    base_headers = [
        {'en_name': 'day_date', 'cn_name': '决策日期', 'type': 'date', 'data_type': 'date'},
        {'en_name': 'unit_org_name', 'cn_name': '业务实体', 'type': 'string', 'data_type': 'string'},
        {'en_name': 'exec_org_name', 'cn_name': '执行组织', 'type': 'string', 'data_type': 'string'},
        {'en_name': 'biz_action_template_name', 'cn_name': '模板名称', 'type': 'string', 'data_type': 'string'}
    ]
    headers_dimension_mapping = {
        'skc': [
            {'en_name': 'brand_name', 'cn_name': '货品品牌', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'year_season', 'cn_name': '商品年季', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'skc_code', 'cn_name': 'SKC编码', "is_can_hide": "0", 'type': 'string'}
        ],
        'org': [
            {'en_name': 's_r_flag', 'cn_name': '发出/接收', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'org_code', 'cn_name': '店仓编码', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'org_name', 'cn_name': '店仓名称', "is_can_hide": "0", 'type': 'string'}
        ],
        'skc-org': [
            {'en_name': 's_r_flag', 'cn_name': '发出/接收', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'org_code', 'cn_name': '店仓编码', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'org_name', 'cn_name': '店仓名称', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'brand_name', 'cn_name': '货品品牌', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'year_season', 'cn_name': '商品年季', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'skc_code', 'cn_name': 'SKC编码', "is_can_hide": "0", 'type': 'string'}
        ],
        'sku-org': [
            {'en_name': 's_r_flag', 'cn_name': '发出/接收', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'org_code', 'cn_name': '店仓编码', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'org_name', 'cn_name': '店仓名称', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'brand_name', 'cn_name': '货品品牌', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'year_season', 'cn_name': '商品年季', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'skc_code', 'cn_name': 'SKC编码', "is_can_hide": "0", 'type': 'string'},
            {'en_name': 'size_name', 'cn_name': '尺码', "is_can_hide": "0", 'type': 'string'}
        ]
    }
    temp = [
        {'cn_name': '检查结果', 'en_name': 'item', 'data_type': 'string'},
        {'cn_name': '出现次数', 'en_name': 'exc_num', 'data_type': 'int', 'width': 150, 'lock': 'right'},
        {'cn_name': '出现比例', 'en_name': 'exc_percent', 'data_type': 'string', 'width': 150, 'lock': 'right'},
    ]
    statistics_page = {
        'check_send_side': {'id': 1, 'headers': temp, 'title': '发出方', 'data': []},
        'check_receive_side_rep': {'id': 2, 'headers': temp, 'title': '补货接收方', 'data': []},
        'check_receive_side_trans': {'id': 3, 'headers': temp, 'title': '调拨接收方', 'data': []}
    }
    check_result_headers = [
        {'cn_name': '验证项', 'en_name': 'case_name', 'data_type': 'string', 'align': 'left'},
        {'cn_name': '模版名称', 'en_name': 'biz_action_template_name', 'data_type': 'string', 'align': 'left'},
        {'cn_name': '尺码', 'en_name': 'size_name', 'data_type': 'string', 'align': 'left'},
        {'cn_name': '发出方检查结果', 'en_name': 'check_send_side', 'data_type': 'string', 'align': 'left'},
        {'cn_name': '补货接收方检查结果', 'en_name': 'check_receive_side_rep', 'data_type': 'string', 'align': 'left'},
        {'cn_name': '调拨接收方检查结果', 'en_name': 'check_receive_side_trans', 'data_type': 'string', 'align': 'left'},
    ]
    overview_headers = [
        {'cn_name': '数据维度', 'en_name': 'dimension', 'data_type': 'string', 'align': 'left'},
        {'cn_name': '验证项', 'en_name': 'case_name', 'data_type': 'string', 'align': 'left'},
        {'cn_name': '是否通过', 'en_name': 'pass_flag', 'data_type': 'string', 'align': 'left'},
        {'cn_name': '异常数', 'en_name': 'exception_num', 'data_type': 'int', 'align': 'right'},
    ]

    @staticmethod
    def _build_conf(page_name: str, *args: List[dict]):
        page_conf = []
        for i in args:
            page_conf += i

        return [BizVerifyScreen(**i, seq=no + 1, page_name=page_name) for no, i in enumerate(page_conf)]

    def biz_verify_screen(self, case_id: List[int], dimension: str = None, write_flag: bool = True) -> (bool, str):
        """
        case_id     验证项id
        dimension   维度(org, skc, skc-org, sku-org)
        write_flag  是否是新增，是则新增，否则删除
        更新是先删再插，建议加锁
        """
        case_id_list = [f'biz_verify_{i}' for i in case_id]
        try:
            for case_id in case_id_list:
                if write_flag:  # 新增
                    if dimension == 'org':
                        args = ScreenMapping.management, ScreenMapping.store
                    elif dimension == 'skc':
                        args = ScreenMapping.management, ScreenMapping.skc
                    else:
                        args = ScreenMapping.management, ScreenMapping.store, ScreenMapping.skc

                    params = self._build_conf(case_id, *args)
                    self.dao.verify_case_add_screen(params)
                else:
                    self.dao.verify_case_del_screen(case_id)
        except Exception:
            return False, '添加筛选器失败'
        return True, '添加筛选器成功'

    @get_all_stock_by_user
    def biz_verify_tree(self, params: BizVerifyTreeOrg):
        """获取验证组树"""
        if not params.tree_org_sk:  # 为空就查全部
            params.tree_org_sk = auth_region_service.list_authorized_region_org_sk()
        if not params.tree_org_sk:
            return {"tree": []}

        params.public_org_sk = auth_region_service.get_inherit_org_by_sk(
            params.tree_org_sk, way_enum=RegionQueryWayEnum.up, org_flags=[OrgFlagEnum.unit])
        res = self.dao.get_biz_verify_tree(params)
        detail = BizVerifyTreeDetail(org_sk=params.org_sk, case=[
            BizVerifyTreeDetailSub(case_id=i[0], run_id=i[1])
            for i in {(c.case_id, c.max_run_id) for g in res for c in g.sub if c.max_run_id > 0}
        ])
        mapping = self.dao.get_biz_verify_tree_detail(detail) if detail else []
        mapping = {i.case_id: i.exc for i in mapping}
        for g in res:
            g_exc_num = 0
            g_all_exc_num = 0
            for c in g.sub:
                if not mapping.get(c.case_id):
                    continue
                case = mapping[c.case_id]
                c.exc_num = case.exc_num
                c.all_exc_num = case.all_exc_num
                g_exc_num += case.exc_num
                g_all_exc_num += case.all_exc_num
            g.exc_num = g_exc_num
            g.all_exc_num = g_all_exc_num

        # 默认按异常数排序
        return self.tree_sort(BizVerifyTreeSort(sort_flag=params.sort_flag, tree=res))

    @staticmethod
    def tree_sort(tree: BizVerifyTreeSort):
        """树排序"""
        sort_flag = tree.sort_flag
        tree = tree.tree

        lam_g = (lambda i: (0 - i.exc_num, i.priority, i.group_name.encode('gbk'))) \
            if sort_flag == 'exc_num' \
            else (lambda i: (i.priority, 0 - i.exc_num, i.group_name.encode('gbk')))

        lam_c = (lambda i: (0 - i.exc_num, i.priority, i.case_name.encode('gbk'))) \
            if sort_flag == 'exc_num' \
            else (lambda i: (i.priority, 0 - i.exc_num, i.case_name.encode('gbk')))
        for x in tree:
            x.sub.sort(key=lam_c)
            for y in x.sub:
                y.unique_id = f'{x.verify_id}_{y.verify_id}'
            x.unique_id = x.verify_id
        tree.sort(key=lam_g)
        return {'tree': tree}

    @staticmethod
    def case_hook(user_id: int, run_type: str, status: bool, **kwargs):
        """执行结果回调"""
        msg = f"批次id：{kwargs.get('run_id')}；{run_type} 重算完成！ 失败数量：{len(kwargs.get('fail_user_case', ''))}"
        param = MessageParam(user_id=user_id, message=msg, message_page="automation_test_verification", title='通知')
        send_inner_message(param)

    def run_cases(self, params: BizVerifyRunCases):
        """执行新跑case的方法"""
        from biz_module.case_validation.base.handler import user_case_info_server
        params.day_date = self.dao.get_biz_verify_day_date()
        res = user_case_info_server.execute_user_case_set(
            params, func=partial(self.case_hook, current_user.user_id, params.run_type))
        if res:
            return {'msg': '重算任务已生成，请等待任务完成后再查询最新数据 ！'}
        raise ParameterException('重算失败')

    def _common(self, params: BizVerifyPageRequest):
        """报表和单项导出的公共部分"""
        params.column_filter_sql = TableColumnOpMixin.column_filter_sql(params.column_filters)
        params.column_orders_sql = TableColumnOpMixin.column_orders_sql(params.column_orders)
        case_info = self.dao.get_biz_verify_case_mapping(params)
        if not case_info:
            raise ParameterException(f'无次case {params.case_id}')
        params.case_name = case_info.case_name
        params.dimension = case_info.dimension
        res = self.dao.biz_verify_page(params)
        headers = self.base_headers + self.headers_dimension_mapping[params.dimension]
        else_headers = []
        eval_int = set()
        for no, i in enumerate(case_info.column_mapping.values()):
            # 是'1'就是指标，不是'1'就是维度，先添加维度，再添加指标
            i['cn_name'] = i['mapping']
            i['en_name'] = f'reserve{no + 1}'
            else_headers.append(i) if i['is_can_hide'] == '1' else headers.append(i)

            i['data_type'] = i.get('type')
            if i['type'] == 'number':
                eval_int.add(i['en_name'])
        for i in eval_int:
            for j in res:
                setattr(j, i, Decimal(getattr(j, i)))

        headers.extend(else_headers)
        return headers, res

    @get_common_filter_sql
    @get_all_stock_by_user
    def screen(self, params: BizVerifyPageRequest):
        """表格筛选"""
        case_info = self.dao.get_biz_verify_case_mapping(params)
        if not case_info:
            raise ParameterException(f'无次case {params.case_id}')
        params.dimension = case_info.dimension
        res = self.dao.biz_verify_page(params) or []
        res = sorted(res, key=lambda i: i.encode('gbk') if isinstance(i, str) else i)
        return {'data': [{'label': i, 'value': i} for i in res]}

    @get_common_filter_sql
    @get_all_stock_by_user
    def page(self, params: BizVerifyPageRequest):
        """报表"""
        headers, data = self._common(params)
        day_date = self.dao.get_biz_verify_day_date()
        return {'headers': headers,
                'day_date': day_date,
                'data': data}

    @staticmethod
    def _export_hook(obj):
        style = {
            'b': Border(*[Side('thin')] * 4),  # 边框加粗
            'c': PatternFill('solid', fgColor='fafafa'),  # 底色
            'f': Font(bold=True),  # 粗体
        }
        for i in range(len(obj.head_handler.cn_name)):
            obj._ws.cell(1, i + 1).fill = style['c']
            obj._ws.cell(1, i + 1).border = style['b']
            obj._ws.cell(1, i + 1).font = style['f']

    @get_common_filter_sql
    @get_all_stock_by_user
    def export(self, params: BizVerifyPageRequest):
        """单项导出"""
        params.export = True
        headers, data = self._common(params)
        excel_params = {
            'filename': f'{params.case_name}_{time.strftime("%Y%m%d_%H%M%S")}.xlsx',
            'headers': headers,
            'data': data,
            'exec_list': [
                ListToExcel585.setup,
                ListToExcel585.add_title,
                ListToExcel585.add_size_group,
                ListToExcel585.add_data,
                ListToExcel585.add_base_style,
                partial(self._export_hook),
                ListToExcel585.log_err,
                ListToExcel585.build_file,
                ListToExcel585.build_resp
            ]
        }
        return ListToExcel585(**excel_params).run()

    @get_common_filter_sql
    @get_all_stock_by_user
    def export_all(self, params: BizVerifyExportAllRequest):
        # res = self.dao.biz_verify_export_all(params)
        params.tree_org_sk = auth_region_service.list_authorized_region_org_sk()
        params.public_org_sk = auth_region_service.get_inherit_org_by_sk(
            params.tree_org_sk, way_enum=RegionQueryWayEnum.up, org_flags=[OrgFlagEnum.unit])
        export_params = {
            'tenant_id': config.TENANT_ID,
            'db_host': config.DB_HOST,
            'db_port': config.DB_PORT,
            'db_name': config.DB_NAME,
            'task_creator_id': current_user.user_id,
            'task_params':
                dumps({
                    'case_id': params.case_id,
                    'skc_filter_sql': params.no_alias_filter_items['no_alias_dim_skc_filter'],
                    'org_filter_sql': params.no_alias_filter_items['no_alias_dim_org_integration_filter'],
                    'temp_filter_sql': params.filter_items['gto_biz_action_template_filter'],
                    # 'case_id': params.case_id,
                    'org_sk': params.org_sk,
                    'tree_org_sk': params.tree_org_sk,
                    'public_org_sk': params.public_org_sk,
                    # **params.no_alias_filter_items,
                    # **params.filter_items
                })
            }
        try:
            res = requests.post(EXPORT_CENTER_BIZ_VERIFY_API, json=export_params).json()
        except:
            return {'msg': '全部导出任务创建失败'}
        if res['success'] is True:
            return {'msg': '全部导出任务已创建，请到任务中心查看'}
        return {'msg': '全部导出任务创建失败'}

    def case_info(self, params: BizVerifySingleCase):
        """验证项信息"""
        info = self.dao.biz_verify_case_info(params.case_id)
        return {'case_info': info}

    def _t_func(self, page, i, params, lock: threading.Lock):
        res = self.dao.biz_verify_statistics(i, params)
        lock.acquire()
        total = sum([j.exc_num for j in res], 0)
        for j in res:
            j.exc_percent = f'{round(j.exc_num * 100 / total, 1)}%'
            page[i]['data'].append(j.dict())
        lock.release()

    @get_common_filter_sql
    @get_all_stock_by_user
    def statistics(self, params: BizVerifyPageRequest):
        """检查结果统计"""
        page = deepcopy(self.statistics_page)
        lock = threading.Lock()
        t_pool = [threading.Thread(target=self._t_func,
                                   args=(page, i, params, lock)) for i in page]
        [i.start() for i in t_pool]
        [i.join() for i in t_pool]

        return {'data': page}

    @get_common_filter_sql
    @get_all_stock_by_user
    def overview(self, params: BizVerifyOverviewReq):
        """
        业务验证概览
        """
        params.tree_org_sk = auth_region_service.list_authorized_region_org_sk()
        params.public_org_sk = auth_region_service.get_inherit_org_by_sk(
            params.tree_org_sk, way_enum=RegionQueryWayEnum.up, org_flags=[OrgFlagEnum.unit])
        data = self.dao.biz_verify_overview(params)
        return {"data": data,
                "headers": self.overview_headers}

    @get_common_filter_sql
    @get_all_stock_by_user
    def result_check(self, params: BizVerifyResultCheckReq):
        """
        业务验证结果检查
        """
        params.tree_org_sk = auth_region_service.list_authorized_region_org_sk()
        params.public_org_sk = auth_region_service.get_inherit_org_by_sk(
            params.tree_org_sk, way_enum=RegionQueryWayEnum.up, org_flags=[OrgFlagEnum.unit])
        # 处理列筛选和排序

        data = self.dao.biz_verify_result_check(params)
        day_date = self.dao.get_biz_verify_day_date()
        return {"data": data,
                "day_date": day_date,
                "headers": self.check_result_headers}

    @get_common_filter_sql
    @get_all_stock_by_user
    def overview_column(self, params: BizVerifyOverviewReq):
        """
        业务验证概览
        """
        params.tree_org_sk = auth_region_service.list_authorized_region_org_sk()
        params.public_org_sk = auth_region_service.get_inherit_org_by_sk(
            params.tree_org_sk, way_enum=RegionQueryWayEnum.up, org_flags=[OrgFlagEnum.unit])

        data = self.dao.biz_verify_overview_column(params)
        return {"data": data}

    @get_common_filter_sql
    @get_all_stock_by_user
    def result_check_column(self, params: BizVerifyResultCheckReq):
        """
        业务验证结果检查
        """
        params.tree_org_sk = auth_region_service.list_authorized_region_org_sk()
        params.public_org_sk = auth_region_service.get_inherit_org_by_sk(
            params.tree_org_sk, way_enum=RegionQueryWayEnum.up, org_flags=[OrgFlagEnum.unit])

        data = self.dao.biz_verify_result_check_column(params)
        return {"data": data}


biz_verify = BizVerify()
