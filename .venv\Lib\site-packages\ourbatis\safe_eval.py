#!/usr/bin/python

"""Safe user-supplied python expression evaluation."""

import ast
import sys
from typing import <PERSON>ple, Any

version = "0.6"

STRICT_MODE = False


class SafeAST(ast.NodeVisitor):
    """AST-tree walker class."""

    allowed = {}

    def __init__(self, safe_nodes=None, add_nodes=None, not_funcs=None, not_attrs=None):
        """create whitelist of allowed operations."""
        if safe_nodes is not None:
            self.allowed = safe_nodes
        else:
            values = ["Num", "Str"]
            # any expression
            expression = ["Expression"]
            # == ...
            compare = ["Compare", "Eq", "NotEq", "Gt", "GtE", "Lt", "LtE"]
            # variable name
            variables = ["Name", "Load"]
            binop = ["BinOp"]
            arithmetics = ["Add", "Sub"]
            subscript = ["Subscript", "Index"]  # person['name']
            boolop = ["BoolOp", "And", "Or", "<PERSON>ryOp", "Not", "IsNot"]  # True and True
            inop = ["In"]  # "aaa" in i['list']
            ifop = ["IfExp"]  # for if expressions, like: expr1 if expr2 else expr3
            nameconst = ["NameConstant", "Constant"]  # for True and False constants
            funcs = ["Call", "Attribute"]

            self.allowed = (
                expression
                + values
                + compare
                + variables
                + binop
                + arithmetics
                + subscript
                + boolop
                + inop
                + ifop
                + nameconst
                + funcs
            )

        self.not_allowed_funcs = not_funcs or []  # 采用黑名单模式
        self.not_allowed_attrs = not_attrs or []  # 采用黑名单模式

        if add_nodes is not None:
            self.allowed = self.allowed + add_nodes

    def generic_visit(self, node):
        """Check node, raise exception is node is not in whitelist."""

        if type(node).__name__ in self.allowed:

            if isinstance(node, ast.Attribute):
                if node.attr in self.not_allowed_attrs:
                    raise ValueError(f"Attribute {node.attr} is not allowed")

                    # separate check for 'Call'
            if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
                if node.func.id in self.not_allowed_funcs:
                    raise ValueError(
                        f"Call to function {node.func.id}() is not allowed"
                    )
                else:
                    # Call to allowed function. good. No exception
                    pass

            ast.NodeVisitor.generic_visit(self, node)
        else:
            raise ValueError(f"Operation type {type(node).__name__} is not allowed")


def evalidate(expression, safe_nodes=None, add_nodes=None, funcs=None, attrs=None):
    """Validate expression.

    return node if it passes our checks
    or pass exception from SafeAST visit.
    """
    node = ast.parse(expression, "<usercode>", "eval")

    v = SafeAST(safe_nodes, add_nodes, funcs, attrs)
    v.visit(node)
    return node


def safeeval(
    src, context=None, safe_nodes=None, add_nodes=None, funcs=None, attrs=None
) -> Tuple[bool, Any]:
    """C-style simplified wrapper, eval() replacement."""
    try:
        node = evalidate(src, safe_nodes, add_nodes, funcs, attrs)
    except Exception as e:
        return False, f"Validation error: {e.__str__()}"

    try:
        code = compile(node, "<usercode>", "eval")
    except Exception as e:
        return False, f"Compile error: {e.__str__()}"

    try:
        context = context if context else {}
        context_copy = context.copy()
        result = eval(code, context_copy)
    except Exception as e:
        et, ev, erb = sys.exc_info()
        return False, f"Runtime error ({type(e).__name__}): {ev}"

    return True, result


def save_eval(expression, context=None) -> Tuple[bool, Any]:
    context = context if context else {}
    if STRICT_MODE:
        success, result = safeeval(expression, context)
    else:
        try:
            result = eval(
                expression,
                {
                    "__builtins__": {"len": len},
                    "locals": __builtins__["locals"],
                    "globals": __builtins__["globals"],
                },
                context,
            )
        except Exception as e:
            result = f"eval error: {e.__str__()}"
            success = False
        else:
            success = True

    return success, result
