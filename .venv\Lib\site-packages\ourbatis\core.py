import inspect
import time
from contextlib import contextmanager
from datetime import datetime, date
from enum import Enum
from functools import partial
from typing import Any, Callable, Type, Union, Dict, Optional

from lz_baselib.tracing import tracing
from skywalking import Component, Layer
from skywalking.trace.context import get_context
from skywalking.trace.tags import (
    TagDbType,
    TagDbInstance,
    TagDbStatement,
)
from sqlalchemy import text
from sqlalchemy.engine.cursor import CursorResult
from sqlalchemy.future import Connection
from sqlalchemy.sql.elements import TextClause

from ourbatis.const import (
    RUN_CONFIG,
    FUNC_CONFIG,
    EXTEND_KWARGS,
    DEFAULT_NS,
    StatementType,
    UNSAFE_NS,
)
from ourbatis.dependant import Dependant, gen_dependent
from ourbatis.exceptions import (
    NoInitSessionException,
    NotFoundNamespaceError,
    NotFoundSqlIDError,
)
from ourbatis.func_config import CompileConfig, RuntimeConfig
from ourbatis.gobal import Global, ConnectStack
from ourbatis.python_mapper.result_convert import get_call_return
from ourbatis.session import SqlSession, inject_sql_statement, deject_sql_statement
from ourbatis.sql_source_parser.objects import BatisElement
from ourbatis.sql_source_parser.param_convert import get_call_params, flat_params
from ourbatis.sql_source_parser.sql_context import SqlContext
from ourbatis.utils import logger, predicate_cls_method


def sql_execute(
    conn: Connection,
    sql_statement: str,
    func_params: Dict[str, Any],
    dependant: Dependant,
    need_raw: bool,
    sql_id,
    runtime_sql,
) -> Any:

    if tracing.finish_init:
        with get_context().new_exit_span(
            op=f"SQL=>{sql_id}",
            peer=f"{conn.engine.url.host}:{conn.engine.url.port}",
            component=Component.Psycopg,
        ) as span:
            span.layer = Layer.Database
            span.tag(TagDbType("PostgreSQL"))
            span.tag(TagDbInstance(str(conn.engine.url)))
            span.tag(TagDbStatement(runtime_sql))
            result: CursorResult = conn.execute(text(sql_statement), func_params)
    else:
        result: CursorResult = conn.execute(text(sql_statement), func_params)

    if need_raw:
        return result.fetchall() if result.returns_rows else None, result.keys()

    if result.returns_rows:
        result = get_call_return(dependant.result, result)

    return result


def sql_execute_by_connect(session: SqlSession, *args, **kwargs) -> Any:
    if ConnectStack.top:
        result = sql_execute(ConnectStack.top, *args, **kwargs)
    else:
        with session.begin() as conn:
            result = sql_execute(conn, *args, **kwargs)
    return result


def get_element_info(session: SqlSession, call_name: str, ns: str) -> BatisElement:
    element: BatisElement = session.session_sql_mapper.get(ns, {}).get(call_name)
    if element is None:
        logger.error(
            f"have no sql_id '{call_name}' in namespace '{ns}' !check your code"
        )
        raise NotFoundSqlIDError(call_name)
    return element


def get_real_session(session: SqlSession, element: BatisElement) -> SqlSession:
    return element.meta.session if element.meta.session else session


def render_query(statement, dialect=None):
    """
    有空再完善
    """
    if dialect is None:
        dialect = statement.bind.dialect

    class LiteralCompiler(dialect.statement_compiler):
        def visit_bindparam(
            self, bindparam, within_columns_clause=False, literal_binds=False, **kwargs
        ):
            return self.render_literal_value(bindparam.value, bindparam.type)

        def render_array_value(self, val, item_type):
            return self.render_literal_value(val, item_type)

        def render_literal_value(self, value, type_):
            if value is None:
                return "null"

            if isinstance(value, Enum):
                value: Enum = value
                value = value.value

            if isinstance(value, int):
                return f"{value}"
            elif isinstance(value, str):
                return f"'{value}'"
            elif isinstance(value, (datetime, date)):
                return f"'{value}'"
            elif isinstance(value, list):
                return f'[{ ",".join([self.render_array_value(x, type_) for x in value])}])'

            return super(LiteralCompiler, self).render_literal_value(value, type_)

    return LiteralCompiler(dialect, statement).process(statement)


def gen_runtime_sql(
    sql_statement: str, func_params: Dict[str, Any], session: SqlSession
):
    stmt: TextClause = text(sql_statement)
    bind_params = set(stmt._bindparams.keys())
    stmt = stmt.bindparams(
        **{key: value for key, value in func_params.items() if key in bind_params}
    )

    try:
        runtime_sql = render_query(stmt, session.engine.dialect)
    except Exception as error:
        logger.warning(
            f"** render runtime sql failed=>\nsql={sql_statement}\nparams={func_params}\nerror={error}"
        )
        runtime_sql = sql_statement

    return runtime_sql


UNITS_MAP = [(14400, "天"), (3600, "小时"), (60, "分钟"), (1, "秒")]


def human_time(use_time: float):
    result = []
    for unit_map in UNITS_MAP:
        time_unit, desc = unit_map
        unit = int(use_time / time_unit)
        if unit > 0:
            result.append(f"{unit}{desc}")
        use_time = use_time % time_unit
    return "".join(result)


def sql_call(call: Callable, *args, **kwargs) -> Any:
    run_config: RuntimeConfig = kwargs.pop(RUN_CONFIG, RuntimeConfig())
    session: SqlSession = (
        Global.session if run_config.run_session is None else run_config.run_session
    )
    if not session:
        logger.error("have not init a sql session!")
        raise NoInitSessionException

    extend_kwargs = kwargs.pop(EXTEND_KWARGS, {})
    func_config: CompileConfig = kwargs.pop(FUNC_CONFIG)
    call_name = call.__name__ if func_config.sql_id is None else func_config.sql_id
    if run_config.direct_func_params:
        func_params: dict = flat_params(run_config.direct_func_params)
    else:
        func_params: dict = get_call_params(call, *args, **kwargs)
    func_params.update(extend_kwargs)  # 针对装饰 sql_func 装饰器的后门
    kwargs.update(func_params)  # if/for语法 需要基于入参做逻辑处理

    element = get_element_info(session, call_name, func_config.namespace)
    session = get_real_session(session, element)
    ns_sql_mapper = session.session_sql_mapper.get(func_config.namespace, None)
    if not ns_sql_mapper:
        logger.error(
            f"cannot find namespace {func_config.namespace}. only find namespace {list(session.session_sql_mapper.keys())} "
        )
        raise NotFoundNamespaceError

    no_annotate = (
        run_config.no_annotate
        if run_config.no_annotate
        else (func_config.no_annotate if func_config.no_annotate else False)
    )
    context = SqlContext(
        input_params=kwargs,
        sql_mapper=ns_sql_mapper,
        no_annotate=no_annotate,
    )

    sql_statement = element.parser.get_sql_statement(
        ns_sql_mapper, child_id=call_name, context=context
    )
    if run_config.append_sql:
        sql_statement = f"{sql_statement} {run_config.append_sql}"

    if func_config.concurrent_protect:
        sql_statement = f"set optimizer = off;\n{sql_statement};\nset optimizer = on;"
    func_params.update(context.inner_gen_params)  # 内部 foreach/bind也会生成一些变量
    logger.info(f"generate sql param ==>\n {func_params}")
    runtime_sql = gen_runtime_sql(sql_statement, func_params, session)
    logger.info(f"generate runtime sql==>\n{runtime_sql}")

    if run_config.only_sql_snippet:
        return sql_statement

    start_time = time.time()
    result = sql_execute_by_connect(
        session,
        sql_statement,
        func_params,
        call.__dependant__,
        func_config.raw,
        sql_id=call_name,
        runtime_sql=runtime_sql,
    )
    use_time = time.time() - start_time
    if use_time > session.slow_sql_threshold:
        logger.warning(
            f"slow sql warning!!! use time {human_time(use_time)}. runtime_sql => {runtime_sql}"
        )
        # todo 发送告警信息
    return result


def gen_real_call(call: Callable, func_config: CompileConfig):
    call.__dependant__ = gen_dependent(call)
    real_call = partial(sql_call, call, func_config=func_config)
    if func_config.decorator_func:
        real_call = func_config.decorator_func(real_call)
        real_call.__sql_func__ = True

    return real_call


def gen_real_class(klass: Type, func_config: CompileConfig):
    methods = inspect.getmembers(klass, predicate=predicate_cls_method)
    attr_dict = dict(klass.__dict__)
    for name, method in methods:
        attr_dict[name] = gen_real_call(method, func_config)

    klass = type(klass.__name__, klass.__bases__, attr_dict)
    return klass


def sql_func(
    call: Union[Callable, Type, None] = None,
    *,
    raw: bool = False,  # 是否获取原始输出(CursorResult.fetchall(), CursorResult.keys())
    sql_id: Optional[str] = None,  # 该sql_id代替默认的函数名
    decorator_func: Optional[Callable] = None,
    namespace: str = DEFAULT_NS,  # 命名空间
    no_annotate: Optional[bool] = None,  # 是否需要注释信息
    concurrent_protect: bool = False,
    **kwargs,
) -> Union[Callable, Type]:
    if not call:
        return partial(
            sql_func,
            raw=raw,
            sql_id=sql_id,
            decorator_func=decorator_func,
            namespace=namespace,
            no_annotate=no_annotate,
            concurrent_protect=concurrent_protect,
            **kwargs,
        )

    compile_config = CompileConfig(
        raw=raw,
        sql_id=sql_id,
        decorator_func=decorator_func,
        namespace=namespace,
        no_annotate=no_annotate,
        concurrent_protect=concurrent_protect,
    )

    if inspect.isclass(call):
        return gen_real_class(call, compile_config)
    else:
        return gen_real_call(call, compile_config)


def run_sql_by_id(
    sql_id: str,
    namespace: str = DEFAULT_NS,
    result_sig: Optional[Any] = None,
    only_sql_snippet=False,
    append_sql: Optional[str] = None,
    run_session: Optional[SqlSession] = None,
    no_annotate: Optional[bool] = None,
    need_raw=True,
    **kwargs,
):
    def dump():
        pass

    if result_sig:
        dump.__annotations__["return"] = result_sig
        need_raw = False

    run_config = RuntimeConfig(
        run_session=run_session,
        only_sql_snippet=only_sql_snippet,
        append_sql=append_sql,
        no_annotate=no_annotate,
        direct_func_params=kwargs,
    )
    result = sql_func(dump, sql_id=sql_id, namespace=namespace, raw=need_raw)(
        run_config=run_config
    )
    return result


@contextmanager
def template_run_sql(
    sql_id: str,
    statement: str,
    statement_type: StatementType = StatementType.ST_XML,
    session: Optional[SqlSession] = None,
    result_sig: Optional[Any] = None,
    **kwargs,
):
    inject_sql_statement(
        statement, statement_type, namespace=UNSAFE_NS, session=session
    )
    try:
        result = run_sql_by_id(
            sql_id,
            namespace=UNSAFE_NS,
            result_sig=result_sig,
            run_session=session,
            **kwargs,
        )
        yield result
    finally:
        deject_sql_statement(
            sql_id, statement_type, namespace=UNSAFE_NS, session=session
        )


sql_class = sql_func
