from datetime import datetime
from typing import Optional, List, Dict, Union, Any
from typing_extensions import TypedDict

from biz.utils.converters import converter_factory
from biz.utils.tools import PropertyBaseModel
from .constants import FeatureSubtypeEnum
from biz_module.common.troubleshooting.core.curd import CrudBean

from pydantic import BaseModel, Field

from .enums import DataTypeEnum, OperatePermissionEnum


class FeatureExpression(BaseModel):
    feature_expression: str


class FeatureListByFilterReq(BaseModel):
    """
    获取特征列表通过筛选条件
    """
    feature_name: Optional[str]  # 特征名
    feature_type: Optional[str]  # 特征类型
    data_type: Optional[str]  # 特征数据类型
    page_no: Optional[int]
    page_size: Optional[int]
    operate_permission: Optional[str]


history_data_keep_day_converter = converter_factory(lambda cls, x: x if x else 30)


class FeatureConfigEgg(BaseModel):
    """
    特征配置体
    """
    id: Optional[int]
    biz_view_id: Optional[int]
    biz_view_dimension_id: Optional[int]
    biz_view_filter_id: Optional[int]
    feature_value_id: Optional[int]
    feature_code: Optional[str]
    feature_name: Optional[str]
    feature_type: Optional[str]
    feature_dim: Optional[list]
    feature_subtype: Optional[Union[str, FeatureSubtypeEnum]]
    feature_category_id: Optional[int]
    calculation_type: Optional[str]
    data_type: Optional[str]
    is_custom: Optional[int]
    is_auto_sql: Optional[int]
    is_value_multiple: Optional[int]
    is_select: Optional[int] = 1
    fill_value: Optional[str]
    calculation_cycle: Optional[str] = 'daily'
    storage_type: Optional[str] = 'separated'
    sql_edit: Optional[str]
    belong_org_sk: Optional[int]
    public_org_sk: Optional[int]
    is_active: Optional[int] = 1
    is_deleted: Optional[int] = 0
    creator: Optional[int]
    modifier: Optional[int]
    create_time: Optional[datetime]
    modify_time: Optional[datetime]
    remark: Optional[str]
    total_rows: Optional[int]
    data_type_extension: Optional[Union[Dict, str]]
    history_data_keep_day: Optional[Union[int, str]] = 30
    convert_1 = history_data_keep_day_converter("history_data_keep_day")
    operate_permission: Optional[int] = OperatePermissionEnum.EMPTY.value

    @property
    def data_type_enum(self):
        if not self.data_type_extension:
            return DataTypeEnum(self.data_type)
        return DataTypeEnum(self.data_type_extension["data_type_enum"])


class FeatureTypeReq(BaseModel):
    """
    特征类型
    """
    feature_type: Optional[str] = 'tag'
    feature_config_id: Optional[int]


class FeatureValueByConfigIdReq(BaseModel):
    """
    通过config ID获取特征值
    """
    feature_config_id: int


class FeatureValueByConfigIdTypeReq(BaseModel):
    """
    通过特征类型和id查询特征值
    """
    feature_config_id: int
    feature_subtype: Optional[str]


class FeatureFilterByIdsReq(BaseModel):
    """
    通过特征值id获取特征过滤器
    """
    inst_filter_ids: List[int]


class FeatureValueEgg(BaseModel):
    """
    特征值体
    """
    id: Optional[int]
    feature_config_id: Optional[int]
    feature_value: Optional[str]
    priority: Optional[str] = -1
    filter_instance_id: Optional[Union[int, None]]
    hit_sql: Optional[str] = ''
    source_feature: Optional[Union[List[str], None]]
    template_id: Optional[int] = None
    template_params: Optional[Any] = None
    creator: Optional[int]
    modifier: Optional[int]
    operate_permission: Optional[int] = OperatePermissionEnum.EMPTY.value
    is_runable: Optional[int] = 1


class FeatureFilterInstanceEgg(BaseModel):
    """
    特征过滤器体
    """
    id: Optional[Union[int, None]]
    condition_set: Optional[Union[dict, str]]
    hit_condition_sql: Optional[str] = ''
    is_deleted: Optional[int]
    creator: Optional[int]
    modifier: Optional[int]


class FeatureValueSaveReq(BaseModel):
    """
    特征保存请求参数
    """
    feature_config_id: Optional[int]
    feature_values: Optional[list]


class FeatureValueClearReq(BaseModel):
    """
    特征值清理
    """
    feature_config_id: Optional[list]
    feature_value_ids: Optional[list]


class FeatureFilterClearReq(BaseModel):
    """
    特征筛选条件清理
    """
    feature_filter_ids: Optional[list] = [-1]


class FeatureConfigDeleteReq(BaseModel):
    """
    删除特征配置请求参数
    """
    feature_config_ids: list
    modifier: Optional[int]


class MetaValueEditor(BaseModel):
    type: str = "element"
    field: str
    title: Optional[str]
    options: Optional[list]
    reg_rule: Optional[str]
    element_id: str


class MetaAppOperator(TypedDict, total=False):
    label: str
    value: str


class MetaApplicableOperator(BaseModel):
    type: str = "operators"
    field: Optional[str] = ""
    default: Optional[MetaAppOperator]
    options: List[MetaAppOperator]
    element_id: str


class MetaInputParameterEditor(BaseModel):
    type: Optional[str] = ""
    field: Optional[str] = ""
    options: Optional[list] = []
    element_id: Optional[str] = ""


class BaseFeatureFilterEgg(BaseModel):
    feature_config_id: int
    input_parameter_editor: str
    applicable_operator: str
    value_editor: str
    creator_id: int
    modifier_id: int
    is_deleted: int = 0


class LabelValueEgg(BaseModel):
    label: Optional[str]
    value: Optional[str]


class FeatureCodeReq(BaseModel):
    feature_code: str


class FeatureInfoEgg(BaseModel):
    feature_dim: Union[str, list]
    feature_name: str
    feature_code: str
    feature_type: Optional[str]
    data_type: Optional[Union[str, None]]


class FeatureTypeDimReq(BaseModel):
    feature_type: str
    feature_dim: List[str]


class FeatureTypeCodeReq(BaseModel):
    feature_type: str
    feature_code: str


class FeatureConfigById(BaseModel):
    feature_config_id: int


class FeatureValueLabelValue(BaseModel):
    label: str
    value: str


class QueryViewsReq(CrudBean):
    """
    视图列表
    """


class DownloadTemplateReq(CrudBean):
    """
    下载模板
    """
    code: List[str]
    feature_codes: Optional[List[str]]


class QueryFeatureConfigByTableFieldCodeReq(CrudBean):
    code: List[str]


class FeatureConfigInstance(CrudBean):
    id: int
    biz_view_id: Optional[int]
    feature_code: str
    feature_name: str
    feature_subtype: str
    feature_category_id: Optional[int]


class ImportDataReq(CrudBean):
    """
    导入数据
    """
    code: Union[List[str], str]
    deleteNull: int = 1
    operating_unit_sk: Optional[int]
    feature_codes: Union[List[str], str]


class ExportDataReq(CrudBean):
    """
    导出数据
    """


class QueryHumanTagReq(CrudBean):
    """
    查询特征
    """
    page_no: int = 1
    page_size: int = 50
    dim_codes: List[str]
    feature_codes: List[str]
    feature_value_filters: Optional[Dict[str, List]] = None  # {"tag$001":["是"]}
    operating_unit_sk: int


class ListHumanTagReq(BaseModel):
    operating_unit_sk: int
    page_size: Optional[int] = 100
    offset: Optional[int]
    auth_orgs_sql_expression: Optional[str] = ""
    feature_related_expression: Optional[str] = ""
    extract_expression: Optional[str] = ""
    with_clause_name: Optional[str]
    on_expression: Optional[str]
    need_auth_flag: bool = False
    feature_codes: Optional[List[str]]


class HumanTagFeatureListResponse(BaseModel):
    feature_code_object: Dict
    feature_value_object: Dict


class FeatureValuesInstance(CrudBean):
    feature_code: str
    feature_name: str
    values: List[str]


class TableFieldCodeInstance(CrudBean):
    table_field_code: List[str]


class QueryMetaDataTableFieldReq(CrudBean):
    code: Optional[List[str]]


class ColumnConfigInstance(CrudBean):
    table_name: str  # 字段所属表名
    belong_table_code: str  # 字段所属表code
    name: str  # 字段英文名
    title: str  # 字段中文名
    is_dynamic: int  # 动态字段，都是以组织为sk
    customer_code: Optional[str]


class MetaDataTableField(CrudBean):
    code: str
    name: str  # 他表字段名
    title: str  # 中文
    belong_table_code: str
    belong_table_name: Optional[str]  # 他表表名
    is_dynamic: int
    data_type: Optional[str]
    customer_code_zh: Optional[str]  # excel展示列名
    customer_code: Optional[str]


class DynamicQueryDimDataReq(CrudBean):
    columns: Optional[List[str]]
    table_name: str
    is_dynamic: Optional[int] = 0
    org_hierarchy_filter: Optional[str]
    org_hierarchy_type_code: Optional[str]
    code_field_name: Optional[str]
    other_required_field_name: Optional[str]
    code_field_values: Optional[List[Any]]
    code_value_expression: Optional[str]


class DynamicQueryFeatureData(CrudBean):
    feature_code: str


class InsertHumanTagReq(CrudBean):
    feature_object: dict = {}
    feature_code_object: dict = {}
    feature_code_json: Optional[str]
    feature_json: Optional[str]
    feature_value: str
    feature_code: str
    operating_unit_sk: Optional[str]


class HumanTagInstance(CrudBean):
    feature_object: Optional[dict] = {}
    feature_code_object: Optional[dict] = {}
    feature_value: Optional[str]
    # feature_code: str
    feature_value_object: Optional[dict] = {}
    feature_values: Optional[list]
    feature_codes: Optional[list]
    feature_code: Optional[str]
    id: Optional[int]


class HumanTagTableTitleResponse(BaseModel):
    code: str
    name: str
    column_type: str = "dim"
    # 是否能过滤
    can_filter: bool = False


class BatchInsertReq(BaseModel):
    operating_unit_sk: int
    feature_object_json_build_expression: str
    feature_code_object_json_build_expression: str
    temp_table_name: str


class FeatureValueReq(BaseModel):
    feature_value: Optional[str]
    condition_set: Optional[Union[dict, str]]
    value_filter_id: Optional[int] = Field(None, alias="feature_filter_id")
    template_id: Optional[int]
    template_params: Optional[Dict]
    sql_edit: Optional[str]
    feature_value_id: Optional[int] = None
    priority: Optional[int] = None


class CreateFeatureReq(BaseModel):
    config_body: FeatureConfigEgg
    value_body: List[FeatureValueReq]


class UpdateFeatureReq(BaseModel):
    config_body: FeatureConfigEgg
    value_body: List[FeatureValueReq]


class UpsertData(BaseModel):
    should_insert_values: Optional[List[FeatureValueEgg]]
    should_update_values: Optional[List[FeatureValueEgg]]
    value_changed_ids: Optional[List[int]]
    # should_deleted_ids: Optional[List[int]]


class DataTypeExtension(PropertyBaseModel):
    data_type_enum: DataTypeEnum

    @property
    def basic_data_type(self):
        return self.data_type_enum.get_basic()

    @property
    def extension(self):
        return self.data_type_enum.get_extension()

    def jsonify_extension_info(self):
        if self.extension is None:
            return None
        return self.json(include={'data_type_enum', 'basic_data_type', 'extension'})
