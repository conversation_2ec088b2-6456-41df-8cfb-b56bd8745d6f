# coding:utf-8

import logging
import os
import time
import random
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont

LOG = logging.getLogger(__name__)


class Captcha(object):

    def __init__(self, session):
        """
        初始化,设置各种属性
        """
        self.session = session
        self.session_key = '_pecan_captcha_key'
        self.captcha_expires_time = '_pecan_captcha_expires_time'

        # 验证码图片尺寸
        self.img_width = 90
        self.img_height = 30

    def _get_font_size(self, code):
        """
        将图片高度的80%作为字体大小
        """
        s1 = int(self.img_height * 0.8)
        s2 = int(self.img_width / len(code))
        return int(min((s1, s2)) + max((s1, s2)) * 0.05)

    def _set_answer(self, answer):
        """
        设置答案和过期时间
        """
        self.session[self.session_key] = str(answer)
        self.session[self.captcha_expires_time] = \
            time.time() + 60

    def _make_code(self):
        """
        生成随机数或随机字符串
        """
        string = random.sample('abcdefghkmnpqrstuvwxyzABCDE'
                               'FGHGKMNOPQRSTUVWXYZ23456789', 4)
        self._set_answer("".join(string))
        return string

    def _make_color(self):
        """
        生成随机颜色
        :return:
        """
        return (random.randint(0, 255), random.randint(10, 255), random.randint(64, 255))

    def display(self):
        """
        生成验证码图片
        """
        background = (random.randrange(200, 255),
                      random.randrange(200, 255),
                      random.randrange(200, 255))
        # code_color = (random.randrange(0, 50),
        #               random.randrange(0, 50),
        #               random.randrange(0, 50),
        #               255)

        font_path = os.path.join(os.path.normpath(os.path.dirname(__file__)),
                                 'timesbi.ttf')

        image = Image.new('RGB', (self.img_width, self.img_height), background)
        # image = image.filter(ImageFilter.EDGE_ENHANCE_MORE)
        code = self._make_code()
        font_size = self._get_font_size(code)
        draw = ImageDraw.Draw(image)

        # 写干扰点
        for i in range(40):
            draw.point([random.randint(0, self.img_width), random.randint(0, self.img_height)], fill=self._make_color())

        # 写干扰圆圈
        for i in range(40):
            draw.point([random.randint(0, self.img_width), random.randint(0, self.img_height)], fill=self._make_color())
            x = random.randint(0, self.img_width)
            y = random.randint(0, self.img_height)
            draw.arc((x, y, x + 4, y + 4), 0, 90, fill=self._make_color())

        # 画干扰线
        for i in range(5):
            x1 = random.randint(0, self.img_width)
            y1 = random.randint(0, self.img_height)
            x2 = random.randint(0, self.img_width)
            y2 = random.randint(0, self.img_height)

            draw.line((x1, y1, x2, y2), fill=self._make_color())

        # x是第一个字母的x坐标
        x = random.randrange(int(font_size * 0.3), int(font_size * 0.5))

        for i in code:
            # 字符y坐标
            y = random.randrange(1, 7)
            # 随机字符大小
            font = ImageFont.truetype(font_path.replace('\\', '/'),
                                      font_size + random.randrange(-3, 7))
            draw.text((x, y), i, font=font, fill=self._make_color())
            # 随机化字符之间的距离 字符粘连可以降低识别率
            x += font_size * random.randrange(6, 8) / 10

        buf = BytesIO()
        image.save(buf, 'gif')

        self.session[self.session_key] = "".join(code)
        # self.session.save()
        return buf.getvalue()

    def check(self, code):
        """
        检查用户输入的验证码是否正确
        """
        _code = self.session.get(self.session_key) or ''
        if not _code:
            LOG.error("************captcha is none***************")
            return False
        # expires_time = \
        #     self.pecan_request.session.get(self.captcha_expires_time) or 0
        # 注意 如果验证之后不清除之前的验证码的话 可能会造成重复验证的现象
        del self.session[self.session_key]
        del self.session[self.captcha_expires_time]
        # if _code.lower() == str(code).lower() and time.time() < expires_time:
        if _code.lower() == code.lower():
            return True
        else:
            return False
