from typing import List

from fastboot.utils.nest import egg
from ourbatis import sql_func

from .typedef import (
    PageConfigReq,
    FeatureConfigEgg,
    FeatureConfigResp,
    PageConfigSaveReq, PageConfigIdReq, ScriptFeatureInfoResult, ScriptPageConfigFormatUpdateDao,
    ScriptPageConfigAddDao,
)


@egg
@sql_func(namespace="add_reduction_order_page_config")
class PageComponentConfigDao(object):
    def query_qrs_page_config(self, params: PageConfigReq) -> dict:
        pass

    def get_page_config_by_id(self, page_id) -> dict:
        pass

    def get_page_config_basic_by_id(self, page_id) -> PageConfigReq:
        pass

    def add_user_page_config_mapping(self, params: PageConfigIdReq) -> int:
        pass

    def update_user_page_config_mapping(self, params: PageConfigSaveReq) -> int:
        pass

    def get_user_page_config_mapping(self, params: PageConfigReq) -> int:
        pass

    def update_page_config_format(self, update_info: ScriptPageConfigFormatUpdateDao):

        ...

    def get_page_config_by_module_name_and_page_name(self, module_name: str, page_name: str) -> List[ScriptPageConfigAddDao]:

        ...

    def get_page_id_by_page_code(self, page_code) -> int:
        ...

@egg
@sql_func(namespace="add_reduction_order_feature_config")
class AddOrderFeatureConfigDao(object):
    def qrs_feature_code_list(self) -> List[FeatureConfigResp]:
        pass

    def qrs_feature_code_exists(self, query_info: FeatureConfigEgg) -> int:
        pass

    def qrs_insert_feature_to_table(self, query_info: FeatureConfigEgg):
        pass

    def qrs_skc_org_feature_realtime(self, p_name, day_date, target_table, schema):
        pass

    def query_feature_info_by_feature_codes(self, feature_codes: List[str]) -> List[ScriptFeatureInfoResult]:
        pass