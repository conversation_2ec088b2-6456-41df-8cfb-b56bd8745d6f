import functools
import inspect
import logging
from typing import Callable, Any, Dict, Type, Optional, Union

from pydantic import BaseConfig
from pydantic.class_validators import Validator
from pydantic.fields import Model<PERSON>ield, FieldInfo, UndefinedType
from pydantic.typing import ForwardRef, evaluate_forwardref

from ourbatis.const import REMOVE_PARAMS
from ourbatis.exceptions import OurBatisError

logger = logging.getLogger("ourbatis")


def get_name(endpoint: Callable) -> str:
    if inspect.isfunction(endpoint) or inspect.isclass(endpoint):
        return endpoint.__name__
    return endpoint.__class__.__name__


def get_typed_annotation(param: inspect.Parameter, globalns: Dict[str, Any]) -> Any:
    annotation = param.annotation
    if isinstance(annotation, str):
        annotation = ForwardRef(annotation)
        annotation = evaluate_forwardref(annotation, globalns, globalns)
    return annotation


def get_typed_signature(
    call: Callable[..., Any]
) -> [inspect.Signature, inspect.Signature]:
    signature = inspect.signature(call)
    globalns = getattr(call, "__globals__", {})
    typed_params = [
        inspect.Parameter(
            name=param.name,
            kind=param.kind,
            default=param.default,
            annotation=get_typed_annotation(param, globalns),
        )
        for param in signature.parameters.values()
        if param.name not in REMOVE_PARAMS
    ]
    typed_signature = inspect.Signature(typed_params)
    return typed_signature, signature.return_annotation


def create_response_field(
    name: str,
    type_: Type[Any],
    class_validators: Optional[Dict[str, Validator]] = None,
    default: Optional[Any] = None,
    required: Union[bool, UndefinedType] = False,
    model_config: Type[BaseConfig] = BaseConfig,
    field_info: Optional[FieldInfo] = None,
    alias: Optional[str] = None,
) -> ModelField:
    """
    Create a new response field. Raises if type_ is invalid.
    """
    class_validators = class_validators or {}
    field_info = field_info or FieldInfo(None)
    model_config.arbitrary_types_allowed = True
    response_field = functools.partial(
        ModelField,
        name=name,
        type_=type_,
        class_validators=class_validators,
        default=default,
        required=required,
        model_config=model_config,
        alias=alias,
    )

    try:
        return response_field(field_info=field_info)
    except RuntimeError:
        raise OurBatisError(
            f"Invalid args for response field! Hint: check that {type_} is a valid pydantic field type"
        )


def predicate_cls_method(check_value: Any):
    if inspect.isfunction(check_value):
        check_value: Callable = check_value
        if getattr(check_value, "__sql_func__", False):
            return False
        if not check_value.__name__.startswith("_"):
            return True
    return False
