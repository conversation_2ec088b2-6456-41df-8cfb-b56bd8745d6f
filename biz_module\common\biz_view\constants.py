from enum import Enum, IntEnum

CODE_DELIMITER = "$"
OPERATING_UNIT_SK_FIELD_NAME = "operating_unit_sk"


class BizViewObjectTypeEnum(Enum):
    biz_object = "业务对象"
    feature = "特征"


class FilterAttributeCodeTypeEnum(IntEnum):
    biz_object_table_field = 1  # 业务对象物理表字段code        1::table_field_code
    feature_meta_table_field = 2  # 特征上物理表字段code 格式为 2::feature_code::table_field_code
    view_feature_only = 3  # 包含在业务视图内的特征本身属性 此时CODE为特征CODE 格式为   3::feature_code
    associated_feature_only = 4  # 通过维度关联的特征   此时CODE为特征CODE 格式为   4::feature_code


class CombineObjectJoinTypeEnum(str, Enum):
    """
    组成对象关联关系枚举类型
    """

    cross_join = "cross join"
    inner_join = "inner join"
    left_join = "left join"
    right_join = "right join"
    # 程序内部使用，不允许对外
    none_join = "None"

    @classmethod
    def front_allowed_types(cls):
        return [cls.cross_join, cls.inner_join, cls.left_join, cls.right_join]

    @classmethod
    def all_allowed_types(cls):
        cls.front_allowed_types().append(cls.none_join)
