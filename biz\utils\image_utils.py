from typing import Optional

from fastboot.utils.config import ConfigMeta
from fastboot.utils.nest import egg


@egg
class ImagesConfig:
    OBS_SKC_IMAGE_URL: str = ConfigMeta(name="obs.skc_image_url")
    OBS_SKC_IMAGE_SUFFIX: Optional[str] = ConfigMeta(name="obs.skc_image_suffix", default=".jpg")
    APP_ID: str = ConfigMeta(name="app_id")


def get_images_name(skc_code):
    # 租户中心那边有约定不允许
    if str(ImagesConfig.APP_ID).__contains__("_"):
        tenant_code = str(ImagesConfig.APP_ID).split("_")[0]
        skc_image_url = ImagesConfig.OBS_SKC_IMAGE_URL.format(tenant_code)
    else:
        skc_image_url = ImagesConfig.OBS_SKC_IMAGE_URL.format(ImagesConfig.APP_ID)
    image_name = "{}{}{}".format(skc_image_url, skc_code, ImagesConfig.OBS_SKC_IMAGE_SUFFIX)
    return image_name

