import pandas as pd
from typing import List, Callable, Dict
from jsonschema import Draft7Validator
from collections import OrderedDict
from ..core import constants
import numpy as np


class ImportConfig(OrderedDict):
    """
    导入配置
    """

    def __new__(cls, schema: Dict, config: Dict):
        v = Draft7Validator(schema=schema)
        errors = [err.message for err in v.iter_errors(config)]
        if errors:
            raise ValueError(errors)
        return config


class ExportConfig(OrderedDict):
    """
    导出配置
    """

    def __new__(cls, schema: Dict, config: Dict):
        v = Draft7Validator(schema=schema)
        errors = [err.message for err in v.iter_errors(config)]
        if errors:
            raise ValueError(errors)
        return config


class DataItem:
    """
    自定义DataFrame数据类型
    """

    def __init__(
        self,
        data,
        config: dict,
        errors: List = None,
        callback: Callable = None,
        options: Dict = None,
    ):
        self.data = data
        self.config = config
        self.errors = errors
        self.callback = callback
        self.options = options


class ErrorDataFrame:
    def __init__(self, columns: pd.Index, *args, **kwargs):
        columns = columns.insert(0, "irow")
        self.df = pd.DataFrame(columns=columns)

    def update_error_dataframe(self, irow: int, columns: List[str], error):
        irow += constants.ROW_GAP
        row = {col: [error] for col in columns}
        row["irow"] = irow

        if self.df.loc[self.df["irow"] == irow].empty:
            self.df = self.df.append({"irow": irow}, ignore_index=True)

        for col in columns:
            if col not in self.df.columns:
                self.df[col] = np.nan

            if self.df.loc[self.df["irow"] == irow, col].any():
                try:
                    old_data = self.df.loc[self.df["irow"] == irow, col]
                    index = old_data.index.values[0]
                    self.df.loc[self.df["irow"] == irow, col] = f"{old_data[index]}、{error}"
                except KeyError as e:
                    print(e)
                    pass
            else:
                self.df.loc[self.df["irow"] == irow, col] = error

