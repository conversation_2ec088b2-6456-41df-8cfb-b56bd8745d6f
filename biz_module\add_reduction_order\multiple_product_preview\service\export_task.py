﻿import io
from typing import Tuple

import pandas as pd

from lz_config_component.database import get_db
from lz_config_component.service import get_excel_components, get_excel_ori_components, process_with_page_config

from biz_module.add_reduction_order.multiple_product_preview.typedef import MultipleProductPreviewListRequest
from biz_module.base.component_excel.service.excel_comp import excel_service
from biz_module.replenish_allot.base.handler import query_auth_org_code_list

from biz import config


def get_excel_comp(task_params):
    page_id = task_params.get('page_id')
    db_session = get_db(config.SQLALCHEMY_DATABASE_URI)
    excel_comp = get_excel_components(db_session, page_id)
    return excel_comp


def get_excel_ori_comp(task_params):
    page_id = task_params.get('page_id')
    db_session = get_db(config.SQLALCHEMY_DATABASE_URI)
    excel_comp = get_excel_ori_components(db_session, page_id)
    return excel_comp




def sync_multiple_product_preview_template_task(tenant_id, day_date, input_file, task_params, **kwargs) -> Tuple:
    """

    """

    excel_comp = get_excel_ori_comp(task_params)
    excel_conf = excel_service.build_excel_obj(excel_comp)
    df_list = [pd.DataFrame()]
    file_output = io.BytesIO()

    filename = excel_service.generate_excel(file_output, df_list, excel_conf)
    return file_output, filename


def sync_multiple_product_preview_export_task(task_params, **kwargs) -> Tuple:
    """


    """
    excel_comp = get_excel_comp(task_params)
    excel_conf = excel_service.build_excel_obj(excel_comp)
    file_output = io.BytesIO()
    # 1. 查询待插入数据，
    order_by = excel_conf.sheets[0].sort_columns
    if order_by:
        task_params['order_by'] = 'order by ' + ','.join([f" {i.get('field')} {i.get('ascending')} " for i in order_by])
    params =  MultipleProductPreviewListRequest(**task_params)
    # 权限控制
    from biz_module.add_reduction_order.multiple_product_preview.service.multiple_product_preview import \
        multiple_product_preview_service
    data = multiple_product_preview_service.query_export_data(params)
    df = pd.DataFrame([i for i in data])
    db_session = get_db(config.SQLALCHEMY_DATABASE_URI)
    df = process_with_page_config(db_session, task_params.get('page_id'), df)
    df = df.fillna('')
    df_list = [df]
    filename = excel_service.generate_excel(file_output, df_list, excel_conf, template=False)
    return file_output, filename
