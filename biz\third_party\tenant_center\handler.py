#!/usr/bin/env python
# encoding: utf-8

import logging
from collections import OrderedDict

import requests

from biz.api_exception import ServerError

from biz.settings import Config
from biz_module.base.auth.types import UpdateSuperAdminPwdRequest

Logger = logging.getLogger(__name__)


class TenantCenterHandler:
    """
    租户中心处理类
    """

    @classmethod
    def list_tenant_module_pages(cls, tenant_code: str, virtual_code: str):
        """
        根据租户编码和虚拟环境编码获取租户已购买的模块页面列表
        :param tenant_code: 租户编码
        :param virtual_code: 虚拟环境编码
        :return:
        """

        api_path = "api/md-master/v5/tenant-center/tenant-module-page/outer/list"
        method_desc = "根据租户编码和虚拟环境编码获取租户已购买的模块页面列表"
        params = dict(tenant_code=tenant_code, virtual_code=virtual_code)
        result = TenantCenterHandler.__call_tenant_center_api(api_path, method_desc, params)
        items = result.get("data").get("items")

        return items

    @classmethod
    def list_purchased_modules(cls, tenant_code: str, virtual_code: str):
        """
        根据租户编码和虚拟环境编码获取租户已购买的模块列表
        :param tenant_code: 租户编码
        :param virtual_code: 虚拟环境编码
        :return:
        """

        api_path = "api/md-master/v5/tenant-center/tenant-module/purchased-module/outer/list"
        method_desc = "根据租户编码和虚拟环境编码获取租户已购买的模块列表"
        params = dict(tenant_code=tenant_code, virtual_code=virtual_code)
        result = TenantCenterHandler.__call_tenant_center_api(api_path, method_desc, params)
        items = result.get("data").get("items")
        return items

    @classmethod
    def update_super_admin_pwd(cls, item: UpdateSuperAdminPwdRequest):
        api_path = "api/md-master/v5/tenant-center/tenant-virtual/outer/update-tenant-pwd"
        method_desc = "修改租户虚拟环境租户密码"
        params = item.dict()
        result = TenantCenterHandler.__call_tenant_center_api(api_path, method_desc, params)
        return result

    @classmethod
    def __call_tenant_center_api(cls, api_path, method_desc, params):
        """
        调用租户中心的api（只支持put和post）
        :param params:
        :return:
        """

        result = OrderedDict()
        center_name = "租户中心"
        msg_header = "++++++ tenant_center::__call_tenant_center_api【租户中心】 "
        try:
            center_domain = "http://%s:%s/" % (Config.TENANT_CENTER_HOST,
                                               Config.TENANT_CENTER_PORT)
            url = center_domain + str(api_path)
            Logger.info(msg_header + "start,url:{url},params:{params}".format(url=url, params=params))
            resp = requests.post(url=url, json=params)
        except Exception as e:
            error_msg = "调用" + center_name + "服务" + method_desc + "异常，请求参数：{params}"
            Logger.error(msg_header + error_msg.format(params=params))
            Logger.error(e)
            raise ServerError()

        if 200 != resp.status_code:
            error_msg = "调用" + center_name + "服务" + method_desc + "返回状态异常，请求参数：{params}, 状态status_code为：{status_code} "
            Logger.error(msg_header + error_msg.format(params=params, status_code=resp.status_code))
            raise ServerError()

        resp_json = resp.json()
        if not resp_json.get("success", False):
            error_msg = "调用" + center_name + "服务" + method_desc + "异常，请求参数：{params}, code为：{code}, msg为：{msg} "
            Logger.error(
                msg_header + error_msg.format(params=params, code=resp_json.get("code"), msg=resp_json.get("msg")))
            raise ServerError()

        result["data"] = resp_json["data"]
        Logger.info(msg_header + " end!")
        return result
