from typing import List, Dict, Optional, Union, Any
from pydantic import BaseModel


class GroupBy(BaseModel):
    column: str  # dim_skc.skc_sk
    data_type: str  # int


class ObjectDimension(BaseModel):
    object_code: str
    # attribute_code: Optional[str]
    dimension_code: Optional[str]
    # 别名
    dimension_alias_code: Optional[str]


class BizViewDimensionEntity(BaseModel):
    dimension_id: Optional[int]
    dimension_code: Optional[str]
    biz_view_id: Optional[int]
    biz_object_attribute_code: Optional[List[str]]
    table_field_code: Optional[List[str]]
    meta_data_dimension_code: Optional[List[str]]
    group_by: Optional[List[GroupBy]]
    raw_dimension: Optional[List[ObjectDimension]]
    creator: Optional[int]
    modifier: Optional[int]


class DmFeatureEntity(BaseModel):
    feature_code: Optional[str]
    feature_name: Optional[str]
    select_sql: Optional[str]


class JoinRelation(BaseModel):
    on: Optional[Union[str, List[str]]] = ""
    object_code: str


class ObjectRelation(BaseModel):
    join: str
    join_relation: List[JoinRelation]


class TableRelation(BaseModel):
    on: Optional[Union[str, List[str]]]
    table_name: Optional[str]
    alias_name: Optional[str]


class TableJoinRelation(BaseModel):
    join: Optional[str]
    table_relation: Optional[List[TableRelation]] = []


class CombineObject(BaseModel):
    object_code: str
    source_type: str


class BizViewEntity(BaseModel):
    id: Optional[int]
    view_code: Optional[str]
    alias_name: Optional[str]
    biz_view_combine: Optional[List[CombineObject]]
    join_relation: Optional[List[ObjectRelation]]
    relation_sql: Optional[str]
    main_object_code: Optional[str]
    creator: Optional[int]
    modifier: Optional[int]
    is_builtin: Optional[int] = 0


class BizViewAndDimensionRequest(BaseModel):
    alias_name: str
    objects: List[CombineObject]
    object_relations: List[ObjectRelation]
    dimensions: List[ObjectDimension]


class BizViewDimensionDropListResponse(BaseModel):
    dimension_id: Optional[int]
    biz_view_id: Optional[int]
    dimension_title: Optional[str]


class BizViewCombineDropListResponse(BaseModel):
    biz_view_id: Optional[int]
    biz_view_name: Optional[str]
    biz_view_dimensions: Optional[List[BizViewDimensionDropListResponse]] = []


class MetaDataIntegration(BaseModel):
    meta_data_dimension_codes: List[str]
    biz_object_attribute_codes: List[str]
    table_field_codes: List[str]
    group_bys: List[GroupBy]


class FilterItemResponse(BaseModel):
    code: str
    expression: List
    title: str


class BizViewFilter(BaseModel):
    id: Optional[int]
    biz_view_id: Optional[int]
    filter_instance_id: Optional[int]
    default_condition_sql: Optional[str]
    source_type: Optional[int]
    creator: Optional[int]
    modifier: Optional[int]
