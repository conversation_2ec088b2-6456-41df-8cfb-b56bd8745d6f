import json
import logging
import re
from typing import Optional, List

from fastboot.utils.ourbatis_utils import replace_sql_snippet_runtime
from flask_login import current_user
from linezone_commonserver.services.biz_view.typedef import CommonBizViewDimension, CommonBizView
from linezone_commonserver.services.feature.constants import FEATURE_VALUE_FIELD_NAME
from linezone_commonserver.services.feature.tools import make_field_alias
from linezone_commonserver.services.feature.typedef import FeatureEntity, ListDependenciesReq
from linezone_commonserver.services.filter.service import FeatureFilterParser
from linezone_commonserver.services.feature.service import FeatureService as CommonFeatureService
from pydantic import BaseModel

from biz.api_exception import ParameterException
from biz_module.common.expression.expression import parse_expression
from biz_module.common.feature_template.service.feature_template_service import FeatureTemplateService
from biz_module.common.feature_template.typedef import FeatureTemplateContext, PreviewFeatureTemplateParam
from biz_module.common.feature_workbench.constants import ACTION_UPDATE
from biz_module.common.feature_workbench.enums import FeatureSubtypeEnum, FeatureTypeEnum, CalculationTypeEnum
from biz_module.common.feature_workbench.typedef import (
    FeatureValueReq,
    FeatureFilterInstanceEgg,
    FeatureValueEgg,
)
from biz_module.common.feature_workbench.utils.feature_tamplate import (
    TemplateCombineObject,
    get_template_by_combine_object,
    TemplateRunSqlParamReq,
    get_template_run_sql,
)
from config.config_manager import Settings

from biz_module.common.feature_workbench.dao import FeatureDao

logger = logging.getLogger(__name__)


class SubtypeBuildReq(BaseModel):
    feature_code: str
    biz_view_dimension_id: int
    feature_type_enum: FeatureTypeEnum
    feature_subtype_enum: FeatureSubtypeEnum
    calculation_type_enum: CalculationTypeEnum
    biz_view: CommonBizView
    biz_view_dimension: CommonBizViewDimension
    biz_view_object_codes: List[str]
    validator: object
    req_feature_entity: FeatureEntity
    action_flag: str


class FeatureValueSubtype:
    __subtype_obj = {}
    default_hit_sql = ""
    default_source_feature = None
    default_filter_id = None
    default_template_id = None
    default_template_params = None
    feature_dao = FeatureDao()

    def __init__(self):
        self.feature_code_pattern = re.compile(rf"tenant_{Settings.TENANT_CODE}_feature\s*\.\s*(?P<feature_code>[a-zA-Z0-9_$]+)")
        self._subtype_build_req = None

    def gen_source_feature(self, hit_sql: str) -> Optional[List[str]]:
        source_feature = set()
        not_find_feature_code = set()
        all_feature_codes = self.feature_dao.get_all_feature_codes()
        for feature_code_match in self.feature_code_pattern.finditer(hit_sql):
            feature_code = feature_code_match.groupdict().get("feature_code", "not_exist_feature")
            if feature_code in all_feature_codes:
                source_feature.add(feature_code)
            else:
                not_find_feature_code.add(feature_code)

        if not_find_feature_code:
            raise ParameterException(f"特征{','.join(not_find_feature_code)} 不存在. 检查sql片段")
        return list(source_feature) if source_feature else None  # 无依赖时为None,统一source_feature格式

    @classmethod
    def register_subtype(cls, subtype: FeatureSubtypeEnum):
        def wrapper(cls_obj):
            if not issubclass(cls_obj, cls):
                raise TypeError
            cls.__subtype_obj[subtype] = cls_obj()
            return cls_obj

        return wrapper

    @classmethod
    def get_feature_subtype(cls, req: SubtypeBuildReq):
        if not req:
            raise ValueError

        if req.feature_subtype_enum not in cls.__subtype_obj:
            raise ParameterException("找不到指定的subtype处理器")
        sub_type_obj = cls.__subtype_obj[req.feature_subtype_enum]
        sub_type_obj._subtype_build_req = req
        return sub_type_obj


@FeatureValueSubtype.register_subtype(FeatureSubtypeEnum.RULE_AUTO)
class _RuleAutoValueType(FeatureValueSubtype):
    _feature_dao = FeatureDao()

    def __call__(self, value_reg: FeatureValueReq):
        # 特征筛选器
        parser = FeatureFilterParser(self._subtype_build_req.req_feature_entity, value_reg.condition_set).build()
        hit_sql = self._generate_hit_sql(parser, value_reg)
        source_feature = parser.source_feature_codes if parser.source_feature_codes else None

        filter_inst = FeatureFilterInstanceEgg(
            condition_set=json.dumps(value_reg.condition_set),
            hit_condition_sql=parser.hit_condition_sql,
            creator=current_user.user_id,
            modifier=current_user.user_id,
        )
        if value_reg.value_filter_id:
            filter_inst.id = value_reg.value_filter_id
            self._feature_dao.update_feature_filter(filter_inst)
            filter_id = value_reg.value_filter_id
        else:
            filter_obj = self._feature_dao.save_feature_filter(filter_inst)
            filter_id = filter_obj.id

        value_egg = FeatureValueEgg(
            id=value_reg.feature_value_id,
            feature_value=value_reg.feature_value,
            hit_sql=hit_sql,
            priority=value_reg.priority,  # 对优先级不处理
            filter_instance_id=filter_id,
            source_feature=source_feature,
            template_id=self.default_template_id,
            template_params=self.default_template_params,
        )
        return value_egg

    def _parse_feature_value_expression(self, feature_value):
        """解析feature value表达式"""
        if self._subtype_build_req.feature_type_enum != FeatureTypeEnum.KPI:
            return f" '{feature_value}' as feature_value", None

        value_fields = []
        index = 1

        def value_expression(feature_name):
            nonlocal index
            name_feature_dict = self._subtype_build_req.validator.validate_value_expression(feature_name)
            alias = f"value_{index}"
            feature = name_feature_dict.get(feature_name)
            expression_item = make_field_alias(feature.feature_code, FEATURE_VALUE_FIELD_NAME, alias, feature.data_type)
            value_fields.append(expression_item)
            index += 1
            return alias

        expr_result = parse_expression(feature_value, value_expression)

        if expr_result.error_messages:
            logger.error(",".join(expr_result.error_messages))
            raise ParameterException("非法表达式解析失败， 请检查")

        return f"({expr_result.expression}) as feature_value", ",".join(value_fields)

    def _parse_view_dimension(self):
        """解析汇聚粒度"""
        wrapped_field_expressions, outer_field_aliases = [], []
        for dimension in self._subtype_build_req.biz_view_dimension.raw_dimension:
            field_alias = dimension.dimension_alias_code if dimension.dimension_alias_code else dimension.dimension_code
            outer_field_aliases.append(field_alias)

            if dimension.object_code in self._subtype_build_req.biz_view_object_codes:
                code_object_mapping = self._subtype_build_req.validator.validate_biz_object_dimension(dimension)
                biz_object = code_object_mapping.get(dimension.object_code)
                field_exp = make_field_alias(biz_object.include_table[0].alias, dimension.dimension_code, field_alias)
                wrapped_field_expressions.append(field_exp)
            else:
                self._subtype_build_req.validator.validate_feature_dimension(dimension)
                field_exp = make_field_alias(dimension.object_code, dimension.dimension_code, field_alias)
                wrapped_field_expressions.append(field_exp)

        return ",".join(wrapped_field_expressions), ",".join(outer_field_aliases)

    def _generate_hit_sql(self, parser, value_reg: FeatureValueReq):
        template_req = [
            TemplateCombineObject(object_code=_obj.object_code, source_type=_obj.source_type)
            for _obj in self._subtype_build_req.biz_view.biz_view_combine
        ]
        template_info = get_template_by_combine_object(template_req)

        value_expression, value_expression_field = self._parse_feature_value_expression(value_reg.feature_value)
        group_by_with_prefix_expression, group_by_without_prefix_expression = self._parse_view_dimension()
        run_sql_param = TemplateRunSqlParamReq(
            feature_value_expression=value_expression,
            value_expression_field=value_expression_field,
            view_sql=self._subtype_build_req.biz_view.relation_sql,
            group_by_with_prefix_expression=group_by_with_prefix_expression,
            group_by_without_prefix_expression=group_by_without_prefix_expression,
            hit_condition_sql=parser.hit_condition_sql,
            outer_feature_code=self._subtype_build_req.feature_code,
            view_condition_sql=parser.biz_view_condition_sql,
            product_table_name=template_info.product_table_name,
            hit_condition_sql_field_with_prefix=parser.hit_condition_sql_field_with_prefix,
        )
        hit_sql = get_template_run_sql(template_info.template_id, run_sql_param)
        return hit_sql


@FeatureValueSubtype.register_subtype(FeatureSubtypeEnum.HUMAN_TAG)
class _HumanTagValueType(FeatureValueSubtype):
    def __call__(self, value_reg: FeatureValueReq):
        value_egg = FeatureValueEgg(
            id=value_reg.feature_value_id,
            feature_value=value_reg.feature_value,
            hit_sql=self.default_hit_sql,
            priority=value_reg.priority,  # 对优先级不处理
            filter_instance_id=self.default_filter_id,
            source_feature=self.default_source_feature,
            template_id=self.default_template_id,
            template_params=self.default_template_params,
        )
        return value_egg


@FeatureValueSubtype.register_subtype(FeatureSubtypeEnum.FEATURE_TEMPLATE)
class _FeatureTemplateValueType(FeatureValueSubtype):
    feature_template_service = FeatureTemplateService()

    def __call__(self, value_reg: FeatureValueReq):
        if not value_reg.template_id:
            raise ParameterException("参数错误，缺少模版ID")
        if not value_reg.template_params:
            raise ParameterException("参数错误，缺少组装模版所需参数")

        _context = FeatureTemplateContext(view_dim_id=self._subtype_build_req.biz_view_dimension_id)
        preview_param = PreviewFeatureTemplateParam(
            template_id=value_reg.template_id, template_params=value_reg.template_params, context=_context
        )
        parsed_sql = self.feature_template_service.preview_feature_template(preview_param)
        if parsed_sql.preview_error:
            raise ParameterException(f"模版解析失败, {parsed_sql.preview_error}")
        hit_sql = replace_sql_snippet_runtime(parsed_sql.preview_result)

        template_id = value_reg.template_id
        template_params = json.dumps(value_reg.template_params) if value_reg.template_params else None

        value_egg = FeatureValueEgg(
            id=value_reg.feature_value_id,
            feature_value=value_reg.feature_value,
            hit_sql=hit_sql,
            priority=value_reg.priority,  # 对优先级不处理
            filter_instance_id=self.default_filter_id,
            source_feature=self.default_source_feature,
            template_id=template_id,
            template_params=template_params,
        )
        return value_egg


@FeatureValueSubtype.register_subtype(FeatureSubtypeEnum.SQL_APP)
class _SqlAppValueType(FeatureValueSubtype):
    common_feature_service = CommonFeatureService()

    def __call__(self, value_reg: FeatureValueReq):
        self._subtype_build_req.validator.validate_sql_text(value_reg.sql_edit)
        hit_sql = value_reg.sql_edit

        if self._subtype_build_req.calculation_type_enum is CalculationTypeEnum.MID:  # 保存模型中, 调用数仓脚本需要, 后续考虑扩展子类
            self.mid_hit_sql = hit_sql

        source_feature_code = self._filter_source_feature(self.gen_source_feature(hit_sql))
        if self._subtype_build_req.action_flag == ACTION_UPDATE:
            self._subtype_build_req.validator.validate_circle_dependency(source_feature_code)

        value_egg = FeatureValueEgg(
            id=value_reg.feature_value_id,
            feature_value=value_reg.feature_value,
            hit_sql=hit_sql,
            priority=value_reg.priority,  # 对优先级不处理
            filter_instance_id=self.default_filter_id,
            source_feature=source_feature_code,
            template_id=self.default_template_id,
            template_params=self.default_template_params,
        )
        return value_egg

    def _filter_source_feature(self, source_feature):
        if not source_feature or self._subtype_build_req.feature_code not in source_feature:
            return source_feature

        source_feature.remove(self._subtype_build_req.feature_code)  # 依赖自身, 排除边
        if len(source_feature) == 0:
            source_feature = None
        return source_feature
