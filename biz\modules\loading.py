# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" Modules (also called addons) management.

"""

import logging
import threading

import biz.modules.graph

from biz import config
from biz.modules.module import initialize_sys_path, load_openerp_module, loaded as loaded_modules, opj

_logger = logging.getLogger(__name__)


def synchronized(func):
    func.__lock__ = threading.Lock()

    def lock_func(*args, **kwargs):
        with func.__lock__:
            return func(*args, **kwargs)

    return lock_func


def load_module_graph(graph, skip_modules=None, update=False):
    module_count = len(graph)
    skip_modules = skip_modules if skip_modules else loaded_modules
    _logger.info('loading %d modules...', module_count - len(skip_modules))

    for index, package in enumerate(graph, 1):
        module_name = package.name
        if skip_modules and module_name in skip_modules:
            continue

        _logger.info('loading module %s (%d/%d)', module_name, index, module_count)

        load_openerp_module(package.name, package.path, reload=update)


class AddIns(object):
    _init = False
    per_page = {}  # 保存每个页面的page_info
    table_depends = []  # 保存每个manifest的table_depends
    graph = None

    def init_app(self, app, db):

        if self._init:
            return

        self.app = app
        self.db = db

        self.graph = biz.modules.graph.Graph()
        self.load_modules()
        self._init_bp(app)
        # TODO 从模块备份中更新loaded_module
        self._init = True

    def load_modules(self):

        initialize_sys_path()

        marked_modules = []
        custom_marked_modules = []
        table_depends = []

        # 加载基础模块
        # base_path = biz.modules.get_module_path('base')
        # marked, table_depends = self.get_module_pages(mod_path=base_path)

        # graph.add_modules(marked)
        # loaded, processed = load_module_graph(graph)

        # 获取biz_module/custom_module下所有模块路径
        for i in biz.modules.get_modules():
            mod_path = biz.modules.get_module_path(i)

            if not mod_path:
                continue

            info = biz.modules.load_information_from_description_file(i)

            if not info:
                continue
            if config['custom_p'] in mod_path and info['is_enable'] and info['is_custom']:
                #   获取自定义模块
                marked, table_depends = self.get_module_pages(mod_path=mod_path)
                table_depends.extend(info.get('table_depends', []))
                custom_marked_modules.extend(marked)
            elif info['is_enable']:
                marked, table_depends = self.get_module_pages(mod_path=mod_path)
                table_depends.extend(info.get('table_depends', []))
                marked_modules.extend(marked)

            self.table_depends.extend(table_depends)

        # 首先加载非自定义模块
        self.graph.add_modules(marked_modules)
        load_module_graph(self.graph)
        # 加载自定义模块
        self.load_custom_modules(custom_marked_modules)

    def get_module_pages(self, mod_path, check_page=None):
        """
            获取一级模块下的模块（页面层级）
            Example:
                `-- biz_module
                    |-- replenish_allot
                    |   |-- decision_monitors   ←--
        :param mod_path:
        :param check_page: 默认为None，不校验
        :return:
        """
        marked_modules = []
        table_depends = []
        for page_path, mod_name in biz.modules.get_pages(mod_path):

            if not page_path:
                continue

            info = biz.modules.load_information_from_description_file(None, page_path)

            if not info:
                continue
            if info['is_enable']:
                dependencies = info.get('table_depends', [])
                table_depends.extend(dependencies)
                if check_page is None or mod_name in check_page:
                    marked_modules.append((page_path, mod_name))
                else:
                    pass

                page_info = info.get('page_info', {})
                if page_info:
                    if isinstance(page_info, dict):
                        page_info['extra1'] = page_info.pop('page_shot_route', '')
                        self.per_page[page_info.get('page_route')] = page_info
                    elif isinstance(page_info, (list, tuple)):
                        for _p in page_info:
                            _p['extra1'] = _p.pop('page_shot_route', '')
                            self.per_page[_p.get('page_route')] = _p
        return marked_modules, table_depends

    def load_custom_modules(self, module_list):
        """加载custom_module（is_custom是True的）下的模块."""

        # processed_modules = []
        if module_list:
            self.graph.add_modules(module_list)
            load_module_graph(self.graph)

    def load_new_modules(self, new_modules, update=False):
        """
        运行过程中动态导入模块
        :param new_modules: dict in list like：
                    module_code
                    parent_code
                    module_type
                    module_name_cn
                    module_name_en
                    module_version
        :param update: 是否更新旧模块，默认导入新模块

        :return:
        """
        initialize_sys_path()
        parent_paths = {}
        marked_modules = []

        error_mod_config = []
        error_not_exist = []
        error_exist_same = []
        backup_list = []
        need_register_modules = set()
        base_imported = set()

        for mod in new_modules:
            parent_name_en = mod["parent_name_en"]
            module_name_en = mod["module_name_en"]
            full_module_name = "%s.%s" % (parent_name_en, module_name_en)
            mod["full_name"] = full_module_name

            # 检查是否已加载相同版本的该模块
            if full_module_name in loaded_modules and \
                    mod["module_version"] == loaded_modules[full_module_name]["version"]:
                error_exist_same.append(module_name_en)
                continue

            # TODO !!!
            if update:
                loaded_modules.pop(full_module_name, None)

            # 以下获取父级目录
            if parent_name_en in ("", None):
                continue
            if parent_name_en not in parent_paths:
                parent_path = biz.modules.get_module_path(parent_name_en)
                parent_paths[parent_name_en] = parent_path
            else:
                parent_path = parent_paths[parent_name_en]

            if not parent_path:
                error_not_exist.append(module_name_en)
                continue

            info = biz.modules.load_information_from_description_file(module_name_en,
                                                                      mod_path=opj(parent_path, module_name_en))
            if info and info['is_enable'] and info["version"] == mod["module_version"] \
                    and info["module_code"] == mod["module_code"]:
                marked, table_depends = self.get_module_pages(mod_path=parent_path,
                                                              check_page=[full_module_name])

                if len(marked):
                    # 以上是获取非base页面模块
                    # 以下是为了获取base模块，因为模块列表里他们不传base
                    # Shit!!!
                    if parent_name_en in base_imported or "{}.base".format(parent_name_en) in loaded_modules:
                        pass
                        # 此判断逻辑决定两点：
                        # 1. 对于实例来说，该父模块第一次被添加时，那么loaded_modules必然没有父模块的base，此时需要base_imported（对于本次请求）记录
                        # 2. 对于本次请求来说，该父模块在此之前就已经被添加，那么loaded_modules必然有父模块的base，此时base_imported为次要判断条件

                    else:
                        bases, _ = self.get_module_pages(mod_path=parent_path,
                                                         check_page=["{}.base".format(parent_name_en)])
                        if len(bases):
                            marked_modules.extend(bases)
                            need_register_modules.add('base')
                        base_imported.add(parent_name_en)

                    marked_modules.extend(marked)
                else:
                    error_not_exist.append(module_name_en)

            else:
                error_mod_config.append(module_name_en)
            need_register_modules.add(module_name_en)

        if len(marked_modules):
            self.graph.add_modules(marked_modules)
            load_module_graph(self.graph, update=update)
            self._init_bp(self.app, init=False, need_register_modules=need_register_modules)

            # 更新已加载模块信息
            for mod_ in new_modules:
                if mod_["full_name"] in loaded_modules:
                    loaded_modules[mod_["full_name"]].update(mod_)
                    backup_list.append(mod_["full_name"])

        return error_mod_config, error_not_exist, error_exist_same, backup_list

    @staticmethod
    def _init_bp(app, init=True, need_register_modules=None):
        """
        注册蓝图
        :param app:
        :return:
        """
        # [app.register_blueprint(bp) for bp in biz.http.controllers_per_blurprint.values()]
        # 👆 this looks like slow
        if init:
            list(map(lambda bp: app.register_blueprint(bp), sum(biz.http.controllers_per_blueprint.values(), [])))
        if need_register_modules:
            _bp = [biz.http.controllers_per_blueprint.get(mod, []) for mod in need_register_modules]
            list(map(lambda bp: app.register_blueprint(bp), sum(_bp, [])))

    @synchronized
    def __new__(cls, *args, **kwargs):
        if not hasattr(cls, "_instance"):
            cls._instance = super(AddIns, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    def post_init_page_hook(self, info):
        """
        v5.0.0版本将路由迁移至pg_code，故不需要进行hook
        """
        return self.app
