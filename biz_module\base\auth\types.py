import re

import wsme.types as wtypes
import wsme
from flask_login import current_user
from flask_babel import lazy_gettext as _
from pydantic import BaseModel

from biz import config
from .models import User
from biz.api_exception import InvalidPassword, UserExists, NoLegalPassword, \
    NewNoLegalPassword, DifferentPassword, ParameterException, NotFound, UserUsed, \
    EmailExists, PhoneExists, InsufficientPermissionsError
# from biz.biz_module.base.public_key.models import KeyPair
from biz.types import Success, listtype, jsontype
from biz.utils import rsa, check_pd
# from biz.constants import PublicKeyC
from .constants import DEFAULT_PWD


class LoginInfo(wtypes.Base):
    """登录传入参数校验"""
    username = wsme.wsattr(wtypes.text, mandatory=True)
    password = wsme.wsattr(wtypes.text, mandatory=True)
    captcha = wsme.wsattr(wtypes.text, mandatory=False)

    def validate(self):

        # disable captcha
        # check_result = captcha.Captcha(session).check(self.captcha)
        # if not check_result:
        #     raise CaptchaError()

        # 获取私钥
        # key_pairs = KeyPair.query.filter_by(id=PublicKeyC.GLOBAL_KEY_PAIR_ID).all()
        # private_key = key_pairs[0].private_key
        private_key = config.RSA_PRIVATE_KEY
        # # 解密用户名和密码
        self.password = rsa.decrypt(private_key, self.password)
        self.username = rsa.decrypt(private_key, self.username)
        # self.account_key = rsa.encrypt(config.RSA_PUBLIC_KEY, current_user.username)
        # 判断用户是否存在
        user = User.query.filter_by(username=self.username).first()

        if not user:
            raise NotFound(msg=str(_("Invalid username or password")))
        # 校验密码
        if not user.check_password(self.password):
            print('Invalid password')
            raise InvalidPassword()
        return self


class LoginPlaintextInfo(LoginInfo):

    def validate(self):

        # disable captcha
        # check_result = captcha.Captcha(session).check(self.captcha)
        # if not check_result:
        #     raise CaptchaError()

        # 判断用户是否存在
        user = User.query.filter_by(username=self.username).first()

        if not user:
            raise NotFound(msg=str(_("Invalid username or password")))
        # 校验密码
        if not user.check_password(self.password):
            print('Invalid password')
            raise InvalidPassword()
        return self


class RegisterInfo(wtypes.Base):
    """注册传入参数校验"""
    username = wsme.wsattr(wtypes.text, mandatory=True)
    password = wsme.wsattr(wtypes.text, mandatory=True)
    repeat_password = wsme.wsattr(wtypes.text, mandatory=True)
    email = wsme.wsattr(wtypes.text, mandatory=True)
    phone = wsme.wsattr(wtypes.text)

    def validate(self):
        # key_pairs = KeyPair.query.filter_by(id=PublicKeyC.GLOBAL_KEY_PAIR_ID).all()
        # private_key = key_pairs[0].private_key
        private_key = config.RSA_PRIVATE_KEY

        self.username = rsa.decrypt(private_key, self.username)
        self.password = rsa.decrypt(private_key, self.password)
        self.repeat_password = rsa.decrypt(private_key, self.repeat_password)
        self.email = rsa.decrypt(private_key, self.email)
        self.phone = rsa.decrypt(private_key, self.phone)

        if self.password != self.repeat_password:
            raise DifferentPassword()
        user = User.query.filter_by(username=self.username).first()
        if user:
            raise UserExists()
        legal_password = check_pd.CheckPD(self.password).query_password()
        if not legal_password:
            raise NoLegalPassword()
        if not re.match(r"[\S]*@[\S]*\.[\S]*", self.email):
            raise ParameterException(msg="请输入正确邮箱")
        if self.phone and not re.match(r"^[1][3456789][0-9]{9}$", self.phone):
            raise ParameterException(msg=str(_('The phone is not legal.')))

        return self


class AddUserInfo(wtypes.Base):
    """新增用户传入参数校验"""
    username = wsme.wsattr(wtypes.text, mandatory=True)
    name = wsme.wsattr(wtypes.text, mandatory=True)
    email = wsme.wsattr(wtypes.text, mandatory=False)
    phone = wsme.wsattr(wtypes.text, mandatory=False)
    password = wsme.wsattr(wtypes.text, default="")
    remark = wsme.wsattr(wtypes.text, default="")
    roles = wsme.wsattr(listtype, default=[])
    resources = listtype
    manager_resources = listtype

    def validate(self):
        if self.password:
            # 检查密码是否符合规范
            legal_password = check_pd.CheckPD(self.password).query_password()
            if not legal_password:
                print('The password is not legal.')
                raise NoLegalPassword()
        else:
            self.password = DEFAULT_PWD

        # 校验用户名
        if self.username is None or len(self.username) > 64:
            raise ParameterException(msg=(_("username is not legal")))
        # 校验姓名
        if self.name is None or len(self.name) > 64:
            raise ParameterException(msg=(_("name is not legal")))

        user = User.query.filter_by(username=self.username, is_delete=False).first()
        # 判断用户名是否已存在数据库
        if user:
            raise UserExists()

        # 邮箱校验
        if self.email and not re.match(r"[\S]*@[\S]*\.[\S]*", self.email):
            raise ParameterException(msg="请输入正确邮箱")
        if self.email:
            user = User.query.filter_by(email=self.email, is_delete=False).first()
            if user:
                raise EmailExists()

        # 手机号校验
        if self.phone and not re.match(r"^[1][3456789][0-9]{9}$", self.phone):
            raise ParameterException(msg=str(_('The phone is not legal.')))
        if self.phone:
            user = User.query.filter_by(phone=self.phone, is_delete=False).first()
            if user:
                raise PhoneExists()

        return self


class UpdateUserInfo(wtypes.Base):
    """更新用户传入参数校验"""
    user_id = wsme.wsattr(int, mandatory=True)
    # username = wsme.wsattr(wtypes.text, mandatory=True)
    name = wsme.wsattr(wtypes.text, mandatory=True)
    email = wsme.wsattr(wtypes.text, mandatory=False)
    phone = wsme.wsattr(wtypes.text, mandatory=False)
    remark = wsme.wsattr(wtypes.text, mandatory=False)
    is_active = wsme.wsattr(bool, mandatory=False)
    roles = wsme.wsattr(listtype, mandatory=False)
    resources = listtype
    manager_resources = listtype

    def validate(self):
        # key_pairs = KeyPair.query.filter_by(id=PublicKeyC.GLOBAL_KEY_PAIR_ID).all()
        # private_key = key_pairs[0].private_key
        # self.username = rsa.decrypt(private_key, self.username)
        # self.email = rsa.decrypt(private_key, self.email)
        # self.phone = rsa.decrypt(private_key, self.phone)

        # 判断用户是否存在
        is_user = User.query.filter_by(user_id=self.user_id).first()
        if not is_user:
            raise NotFound(msg=str(_("current user don't exist")))
        # 如果修改用户名， 就要校验用户名是否规范和是否已经存在
        # if is_user.username != self.username:
        #     if self.username is None or len(self.username) > 64:
        #         raise ParameterException(msg=(_("username is not legal")))
        #
        #     if User.query.filter_by(username=self.username).first():
        #         raise UserExists()
        # 检验姓名
        if self.name is None or len(self.name) > 64:
            raise ParameterException(msg=(_("name is not legal")))
        # 校验邮箱
        if self.email and not re.match(r"[\S]*@[\S]*\.[\S]*", self.email):
            raise ParameterException(msg="请输入正确邮箱")
        if self.email:
            user = User.query.filter_by(email=self.email, is_delete=False).first()
            if user and user.user_id != self.user_id:
                raise EmailExists()
        # 校验手机号
        if self.phone and not re.match(r"^[1][3456789][0-9]{9}$", self.phone):
            print('The email is not legal.')
            raise ParameterException(msg=str(_('The phone is not legal.')))
        if self.phone:
            user = User.query.filter_by(phone=self.phone, is_delete=False).first()
            if user and user.user_id != self.user_id:
                raise PhoneExists()

        return self


class ChangePasswordInfo(wtypes.Base):
    """修改密码传入参数校验"""
    old_password = wsme.wsattr(wtypes.text, mandatory=True)
    new_password = wsme.wsattr(wtypes.text, mandatory=True)
    new_repeat_password = wsme.wsattr(wtypes.text, mandatory=True)

    def validate(self):
        # 获取私钥
        # key_pairs = KeyPair.query.filter_by(id=PublicKeyC.GLOBAL_KEY_PAIR_ID).all()
        # private_key = key_pairs[0].private_key
        # self.username = rsa.decrypt(private_key, self.username)
        private_key = config.RSA_PRIVATE_KEY

        self.old_pwd = self.old_password
        self.new_pwd = self.new_password

        # 解码密码
        self.old_password = rsa.decrypt(private_key, self.old_password)
        self.new_password = rsa.decrypt(private_key, self.new_password)
        self.new_repeat_password = rsa.decrypt(private_key, self.new_repeat_password)
        self.account_key = rsa.encrypt(config.RSA_PUBLIC_KEY, current_user.username)
        # 校验密码
        if not current_user.check_password(self.old_password):
            raise InvalidPassword()
        if self.new_password != self.new_repeat_password:
            print('different password')
            raise DifferentPassword()

        legal_password = check_pd.CheckPD(self.new_password).query_password()
        if not legal_password:
            print('The new password is not legal.')
            raise NewNoLegalPassword()
        return self


class ResetPasswordInfo(wtypes.Base):
    """重置密码传入参数校验"""
    user_id = wsme.wsattr(int, mandatory=True)

    def validate(self):
        # 判断用户是否存在
        user = User.query.filter_by(user_id=self.user_id).first()
        if not user:
            raise NotFound(msg=str(_("current user don't exist")))

        return self


class ForbiddenUserInfo(wtypes.Base):
    """禁用用户传入参数校验"""
    user_id = wsme.wsattr(int, mandatory=True)
    is_active = wsme.wsattr(bool, mandatory=True)

    def validate(self):
        # 判断用户是否存在
        user = User.query.filter_by(user_id=self.user_id).first()
        if not user:
            raise NotFound(msg=str(_("current user don't exist")))

        return self


class DeleteUserInfo(wtypes.Base):
    """删除用户传入参数校验"""
    user_id = wsme.wsattr(int, mandatory=True)

    def validate(self):
        # 判断用户是否存在
        user = User.query.filter_by(user_id=self.user_id).first()
        if not user:
            raise NotFound(msg=str(_("current user don't exist")))
        if user.is_admin:
            raise InsufficientPermissionsError()
        return self


class SendEmailInfo(wtypes.Base):
    """忘记密码发送邮箱传入参数校验"""
    username = wsme.wsattr(wtypes.text, mandatory=True)

    def validate(self):
        # 判断用户是否存在
        user = User.query.filter_by(username=self.username).first()
        if not user:
            raise NotFound(msg=str(_("current user don't exist")))

        return self


class ForgetPdChangePdInfo(wtypes.Base):
    """忘记密码修改密码传入参数校验"""
    token = wsme.wsattr(wtypes.text, mandatory=True)
    password = wsme.wsattr(wtypes.text, mandatory=True)
    repeat_password = wsme.wsattr(wtypes.text, mandatory=True)

    def validate(self):
        # 获取公钥
        # key_pairs = KeyPair.query.filter_by(id=PublicKeyC.GLOBAL_KEY_PAIR_ID).all()
        # private_key = key_pairs[0].private_key
        private_key = config.RSA_PRIVATE_KEY

        # 解密密码
        self.password = rsa.decrypt(private_key, self.password)
        self.repeat_password = rsa.decrypt(private_key, self.repeat_password)

        # 校验密码
        if self.password != self.repeat_password:
            print('different password')
            raise DifferentPassword()
        legal_password = check_pd.CheckPD(self.password).query_password()
        if not legal_password:
            print('The password is not legal.')
            raise NoLegalPassword()
        return self


class GetUserName(wtypes.Base):
    """模糊查询获取用户名和姓名传入参数校验"""
    query_level_value = wsme.wsattr(wtypes.text, mandatory=True)
    is_active = wsme.wsattr(bool)

    def validate(self):
        # key_pairs = KeyPair.query.filter_by(id=PublicKeyC.GLOBAL_KEY_PAIR_ID).all()
        # private_key = key_pairs[0].private_key
        # self.password = rsa.decrypt(private_key, self.password)
        # self.repeat_password = rsa.decrypt(private_key, self.repeat_password)

        return self


class GetUserInfo(wtypes.Base):
    """查询获取列表信息传入参数校验"""
    username_list = wsme.wsattr(listtype, mandatory=False)
    page = wsme.wsattr(int)
    page_size = wsme.wsattr(int)

    def validate(self):
        # key_pairs = KeyPair.query.filter_by(id=PublicKeyC.GLOBAL_KEY_PAIR_ID).all()
        # private_key = key_pairs[0].private_key
        # self.password = rsa.decrypt(private_key, self.password)
        # self.repeat_password = rsa.decrypt(private_key, self.repeat_password)
        if not self.page:
            self.page = 1
        if not self.page_size:
            self.page_size = 200

        return self


class QueryUserInfo(wtypes.Base):
    filter_str = wsme.wsattr(wtypes.text, default="")
    is_active = wtypes.wsattr(bool)


class QueryByID(wtypes.Base):
    user_id = wsme.wsattr(int, mandatory=True)


class UserResultInfo(wtypes.Base):
    """自定义查询返回参数校验"""
    username = wsme.wsattr(wtypes.text)
    name = wsme.wsattr(wtypes.text)
    email = wsme.wsattr(wtypes.text)
    phone = wsme.wsattr(wtypes.text)


class GetUserSuccess(Success):
    """用户模块查询返回结果"""
    content = wsme.wsattr(jsontype)


class QueryUserResult(Success):
    """自定义查询返回结果"""
    content = wsme.wsattr([UserResultInfo], mandatory=False)


class LoginSuccess(GetUserSuccess):
    """登录返回结果"""
    # csrf_token = wsme.wsattr(wtypes.text, mandatory=True)


class UpdateSuperAdminPwdRequest(BaseModel):
    """
    修改超级管理员请求类
    """

    tenant_code: str
    virtual_code: str
    tenant_account: str
    new_tenant_pwd: str
    new_confirm_tenant_pwd: str
    old_tenant_pwd: str
