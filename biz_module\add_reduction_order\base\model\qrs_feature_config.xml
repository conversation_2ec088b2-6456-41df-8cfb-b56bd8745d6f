<?xml version="1.0"?>
<mapper namespace="add_reduction_order_feature_config">

     <select id="qrs_feature_code_exists">
        select 1 from biz.qrs_feature_config
        where feature_code=#{feature_code} and target_table = #{target_table}
    </select>

    <select id="qrs_feature_code_list">
        select feature_code, feature_name
        from biz.qrs_feature_config
        where status = 0
    </select>

    <insert id="qrs_insert_feature_to_table">
        with query as (
            select 1 from biz.qrs_feature_config
            where feature_code=#{feature_code} and target_table = #{target_table}
        )
        insert into biz.qrs_feature_config(
            feature_code,
            feature_name,
            feature_dim,
            feature_type,
            status,
            from_table,
            target_table,
            targe_history_table,
            create_time,
            modify_time)
        select
            #{feature_code},
            #{feature_name},
            string_to_array(#{feature_dim}, ','),
            #{feature_type},
            #{status},
            #{from_table},
            #{target_table},
            #{targe_history_table},
            now(),
            now()
        where not exists( select 1 from query )
        returning id;
    </insert>

    <select id="qrs_skc_org_feature_realtime">
        select biz.${p_name}(#{day_date}, #{target_table}, #{schema} , 'update', 'qrs_feature_config')
    </select>

    <select id="query_feature_info_by_feature_codes">
        SELECT a.id, a.feature_code, a.feature_code || ';;' || coalesce(b.feature_value, 'null') feature_name_tag, b.id feature_value_id
        FROM biz.base_feature_config AS a
        INNER JOIN biz.base_feature_config_value AS b
        ON a.id = b.feature_config_id
        WHERE a.is_deleted = 0 AND b.is_deleted = 0
        AND a.feature_code IN ${feature_codes}
    </select>

</mapper>