#!/usr/bin/env python
# _*_ coding: utf-8 _*_

"""
@author: yang<PERSON><PERSON><PERSON>
@license: Copyright 2017-2020, LineZoneData.
@contact: yang<PERSON><PERSON><PERSON>@linezonedata.com
@software: pycharm
@time: 2019/11/8 10:33
@desc: 邮件服务
"""

import threading
from pathlib import Path
from typing import Dict, List

import flask
from flask_mail import Message, Mail
from jinja2 import Environment, FileSystemLoader


def send_mail(app: flask.Flask, msg: Message):
    """
    发送邮件
    :param app:
    :param msg:
    :return:
    """
    mail = Mail(app)
    with app.app_context():
        mail.send(msg)


def send_async(app: flask.Flask, msg: Message):
    """
    异步发送邮件
    :param app: flask.Flask
    :param msg: flask_mail.Message, 邮件内容
    :return:
    """
    # send_email(msg)
    thr = threading.Thread(target=send_mail, args=[app, msg])  # 创建线程
    thr.start()
    return thr


class AppBaseMessage(Message):
    def __init__(self, sender_name: str = None, *args, **kwargs):
        sender_name = sender_name or ''
        if "sender" not in kwargs:
            # sender_name 为邮件发件人显示名称
            # 例如: ("览众数据", "<EMAIL>")
            #       收件时收件人显示为 biz<<EMAIL>>
            kwargs["sender"] = (sender_name, flask.current_app.config["MAIL_USERNAME"])
        super().__init__(*args, **kwargs)


class MailSender:
    """基于模板发送邮件"""
    def __init__(self, app: flask.Flask, template_file: str, sender_name: str = None, *args, **kwargs):
        """
        初始化
        :param app: flask.Flask实例
        :param template_path: 指定模板文件的路径
        :param sender_name: 发件人显示名称
        :param args:
        :param kwargs:
        """
        self.app = app
        template_path = Path(__file__).resolve().parent / 'templates' / template_file
        env = Environment(loader=FileSystemLoader(str(template_path.parent)))
        self.t = env.get_template(template_path.name)
        # 用于AppBaseMessage对象初始化的参数
        self.sender_name = sender_name
        self._args = args
        self._kwargs = kwargs

    def render(self, params: Dict = None) -> str:
        """
        渲染模板
        :param params: 模板渲染参数
        :return:
        """
        html = self.t.render(**params)
        return html

    def send_template_mail(self, subject: str,
                           content_params: Dict,
                           recipients: List[str],
                           cc: List[str] = None,
                           async_mail: bool = True):
        """
        发送模板邮件
        :param subject: str, 邮件主题
        :param content_params: Dict, 模板渲染参数
        :param recipients: List[str], 收件人邮箱列表
        :param cc: List[str], 抄送人邮箱列表
        :param async_mail: bool, 是否异步发送邮件
        :return:
        """
        # 渲染邮件内容模板
        html = self.render(content_params)

        msg = AppBaseMessage(subject=subject,
                             recipients=recipients,
                             cc=cc,
                             html=html,
                             *self._args,
                             **self._kwargs)
        if async_mail:
            send_async(self.app, msg)
        else:
            send_mail(self.app, msg)
        return

    def send_plain_mail(self, subject: str,
                        body: str,
                        recipients: List[str],
                        cc: List[str] = None,
                        async_mail: bool = True):
        """
        发送普通邮件
        :param subject: str, 邮件主题
        :param body: str, 邮件正文
        :param recipients: List[str], 收件人邮箱列表
        :param cc: List[str], 抄送人邮箱列表
        :param async_mail: bool, 是否异步发送邮件
        :return:
        """
        msg = AppBaseMessage(subject=subject,
                             recipients=recipients,
                             cc=cc,
                             body=body,
                             *self._args,
                             **self._kwargs)
        if async_mail:
            send_async(self.app, msg)
        else:
            send_mail(self.app, msg)
        return


class VerificationCodeMailSender(MailSender):
    def __init__(self, app: flask.Flask, sender_name="", *args, **kwargs):
        """
        初始化
        :param app: flask.Flask类实例
        :param template_path: 指定模板文件的路径
        :param sender_name: 发件人显示名称
        :param args:
        :param kwargs:
        """
        # 邮件模板地址
        template_file = str(Path(__file__).resolve().parent / 'templates' / 'verification_code_mail.html')
        super().__init__(app, template_file, sender_name, *args, **kwargs)

    def send(self, operation_name: str, verif_code: str, recipient: str, subject="验证码"):
        """
        发送验证码邮件
        :param operation_name: str, 操作名称，如 "重置密码"
        :param verif_code: str, 验证码
        :param recipient: str, 收件人邮箱
        :param subject: str, 邮件主题
        :return:
        """
        content_params = {
            "operation_name": operation_name,
            "verif_code": verif_code,
        }
        self.send_template_mail(subject=subject,
                                content_params=content_params,
                                recipients=[recipient])


class VerificationCodeMailSender(MailSender):

    def __init__(self, app: flask.Flask, sender_name="", *args, **kwargs):
        """
        初始化
        :param app: flask.Flask类实例
        :param template_path: 指定模板文件的路径
        :param sender_name: 发件人显示名称
        :param args:
        :param kwargs:
        """
        # 邮件模板地址
        template_file = str(Path(__file__).resolve().parent / 'templates' / 'forgot_pwd_mail.html')
        super().__init__(app, template_file, sender_name, *args, **kwargs)

    def send(self, operation_name: str, verif_code: str, recipient: str, subject="验证码"):
        """
        发送验证码邮件
        :param operation_name: str, 操作名称，如 "重置密码"
        :param verif_code: str, 验证码
        :param recipient: str, 收件人邮箱
        :param subject: str, 邮件主题
        :return:
        """
        content_params = {
            "operation_name": operation_name,
            "verif_code": verif_code,
        }
        self.send_template_mail(subject=subject,
                                content_params=content_params,
                                recipients=[recipient])


# if __name__ == "__main__":
#     import os
#     root_path = Path(__file__).resolve().parents[3]
#     os.chdir(str(root_path))
#
#     from manage import app
#
#     with app.app_context():
#         sender = VerificationCodeMailSender(app=app, sender_name="LZ")
#         sender.send("重置密码", "abcdefg1234", "<EMAIL>", subject="验证码")
