from.import encoder,decoder
__version__='0.10.1'
_spec_='0.5.0'
load=decoder.load
loads=decoder.loads
TomlDecoder=decoder.TomlDecoder
TomlDecodeError=decoder.TomlDecodeError
TomlPreserveCommentDecoder=decoder.TomlPreserveCommentDecoder
dump=encoder.dump
dumps=encoder.dumps
TomlEncoder=encoder.TomlEncoder
TomlArraySeparatorEncoder=encoder.TomlArraySeparatorEncoder
TomlPreserveInlineDictEncoder=encoder.TomlPreserveInlineDictEncoder
TomlNumpyEncoder=encoder.TomlNumpyEncoder
TomlPreserveCommentEncoder=encoder.TomlPreserveCommentEncoder
TomlPathlibEncoder=encoder.TomlPathlibEncoder