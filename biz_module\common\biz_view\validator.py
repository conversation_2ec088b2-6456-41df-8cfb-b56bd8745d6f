"""
业务视图校验
"""
import logging

from fastboot.utils.nest import egg
from linezone_commonserver.services.feature.constants import FEATURE_VALUE_FIELD_NAME
from linezone_commonserver.services.feature.service import FeatureService
from linezone_commonserver.services.meta_data.service import MetaDataService

from biz.api_exception import ParameterException
from biz_module.common.biz_view.constants import CombineObjectJoinTypeEnum, BizViewObjectTypeEnum
from biz_module.common.biz_view.typedef import BizViewAndDimensionRequest
from biz_module.common.feature_workbench.utils.feature_tamplate import (
    HitTemplateFlag,
    TemplateCombineObject,
    get_template_by_combine_object,
)

logger = logging.getLogger(__name__)


class CombineObjectAndRelationValidator:
    """
    业务视图组层对象校验类
    """

    def __call__(self, construct_body: BizViewAndDimensionRequest):
        object_count = len(construct_body.objects)
        object_relation_count = len(construct_body.object_relations)
        if object_count == 0:
            logger.warning(f"请求参数objects为空，request params:{construct_body}")
            raise ParameterException("请配置组合对象")

        if object_count == 1 and object_relation_count != 0:
            logger.warning(f"无需配置组合关系object_count == 1 and relation_count != 0 request params:{construct_body}")
            raise ParameterException("无需配置组合关系")

        combine_object_codes = {x.object_code for x in construct_body.objects}
        for index, obj_relation in enumerate(construct_body.object_relations, 1):
            allowed_join_types = CombineObjectJoinTypeEnum.front_allowed_types()
            if obj_relation.join not in allowed_join_types:
                raise ParameterException(f"参数错误， 第{index}组 join 非法, 未知的关联关系")

            if len(obj_relation.join_relation) == 0:
                raise ParameterException(f"参数错误， 第{index}组 join_relation 非法")
            if len(obj_relation.join_relation) != 2:
                logger.warning(f"{obj_relation.join_relation}未成对出现")
                raise ParameterException(f"参数错误，第{index}组 join_relation 需要成对设置关联关系")

            join_relation_set = {join_relation.object_code for join_relation in obj_relation.join_relation}
            if join_relation_set.difference(combine_object_codes):
                logger.warning(
                    f"组合对象关系错误，请选择组合上属性"
                    f" 差异：{join_relation_set.difference(combine_object_codes)} construct_body:{construct_body}"
                )
                raise ParameterException(f"参数错误，第{index}组 join_relation 非法，请选择objects的属性")


class CombineObjectSourceValidator:
    """
    业务对象来源校验
    """

    def __call__(self, construct_body: BizViewAndDimensionRequest):
        objects = construct_body.objects
        for combine_object in objects:
            if combine_object.source_type not in BizViewObjectTypeEnum.__members__.keys():
                raise ParameterException("只支持特征/业务对象的组装")


class DimensionRequestParamValidator:
    """
    维度校验
    """

    logger = logging.getLogger(__name__)

    def __call__(self, construct_body: BizViewAndDimensionRequest):
        if len(construct_body.dimensions) == 0:
            raise ParameterException("维度不能为空")

        combine_object_codes = [x.object_code for x in construct_body.objects]
        for dimension in construct_body.dimensions:
            if dimension.object_code not in combine_object_codes:
                logger.warning(f"维度参数错误，请选择组合上属性construct_body:{construct_body}")
                raise ParameterException("维度参数错误，请选择组合上属性")


@egg
class CombineObjectExistenceValidator:
    """
    组成对象存在性校验
    """

    meta_data_service: MetaDataService
    common_feature_service: FeatureService

    def __call__(self, construct_body: BizViewAndDimensionRequest):
        biz_object_code_set = set()
        feature_code_set = set()
        for obj in construct_body.objects:
            if obj.source_type == BizViewObjectTypeEnum.biz_object.name:
                biz_object_code_set.add(obj.object_code)
            if obj.source_type == BizViewObjectTypeEnum.feature.name:
                feature_code_set.add(obj.object_code)

        if biz_object_code_set:
            biz_objects = self.meta_data_service.list_biz_object_by_codes(list(biz_object_code_set))
            if not biz_objects or len(biz_objects) != len(biz_object_code_set):
                logger.warning(f"部分业务对象code不存在  biz_object_code_set:{biz_object_code_set}")
                raise ParameterException(f"参数错误，部分业务对象不存在，请检查")

            for biz_object in biz_objects:
                if biz_object.belong_module_code != "feature":
                    logger.warning(f"组装的业务对象不属于特征feature模块  request biz_object:{biz_object}")
                    raise ParameterException(f"请创建或者选择属于特征模块的业务对象，{biz_object.code}不属于特征模块")

        if feature_code_set:
            features = self.common_feature_service.list_by_feature_codes(list(feature_code_set))
            if not features or len(features) != len(feature_code_set):
                logger.warning(f"组装的特征code不存在  request feature_codes:{feature_code_set}")
                raise ParameterException("参数错误，部分特征不存在，请检查")


@egg
class ObjectAttributeExistenceValidator:
    """
    组成对象join条件校验
    """

    meta_data_service: MetaDataService

    def __call__(self, construct_body: BizViewAndDimensionRequest):
        biz_object_codes = []
        feature_codes = []
        for obj in construct_body.objects:
            if obj.source_type == BizViewObjectTypeEnum.biz_object.name:
                biz_object_codes.append(obj.object_code)
            if obj.source_type == BizViewObjectTypeEnum.feature.name:
                feature_codes.append(obj.object_code)

        dict_code_attr_set = {}
        for index, object_relation in enumerate(construct_body.object_relations, 1):
            for join_relation in object_relation.join_relation:
                if not isinstance(join_relation.on, (str, list)):
                    raise ParameterException("on条件只支持字符串/数组")
                if object_relation.join != CombineObjectJoinTypeEnum.cross_join and not join_relation.on:
                    raise ParameterException(f"参数错误, 第{index}组 join_relation中的 on 不能为空")

                if join_relation.on:
                    attrs = [join_relation.on] if isinstance(join_relation.on, str) else join_relation.on
                    dict_code_attr_set.setdefault(join_relation.object_code, set()).update(attrs)

        for k, attr_set in dict_code_attr_set.items():
            if "operating_unit_sk" in attr_set:
                attr_set.remove("operating_unit_sk")  # operating_unit_sk跳过，暂不校验
            if len(attr_set) == 0:
                continue
            if k in biz_object_codes:
                fields = self.meta_data_service.list_table_field_by_biz_object_code(k, list(attr_set))
            else:
                fields = self.meta_data_service.list_table_field_by_feature_code(k, list(attr_set))
            if len(attr_set) != len(fields):
                fields = {field.table_field_name for field in fields}
                illegal_field_str = ",".join(attr_set.difference(fields))
                raise ParameterException(f"请求参数错误，{k}的on:{illegal_field_str}非法")


@egg
class DimensionValidator:
    """
    汇聚维度校验
    """

    meta_data_service: MetaDataService
    common_feature_service: FeatureService

    def __call__(self, construct_body: BizViewAndDimensionRequest):
        biz_object_codes = [
            biz_object.object_code
            for biz_object in construct_body.objects
            if biz_object.source_type == BizViewObjectTypeEnum.biz_object.name
        ]

        feature_codes = [
            feature.object_code
            for feature in construct_body.objects
            if feature.source_type == BizViewObjectTypeEnum.feature.name
        ]

        operating_unit_sks = [
            dimension.dimension_code
            for dimension in construct_body.dimensions
            if dimension.dimension_code == "operating_unit_sk"
        ]

        template_req = [
            TemplateCombineObject(object_code=_obj.object_code, source_type=_obj.source_type) for _obj in construct_body.objects
        ]
        feature_template = get_template_by_combine_object(template_req)

        if feature_template.hit_template_flag in (HitTemplateFlag.org_template, HitTemplateFlag.product_only_template):

            if operating_unit_sks:
                raise ParameterException(f"包含{HitTemplateFlag.get_title(feature_template.hit_template_flag)}无需指定业务实体")

        if feature_template.hit_template_flag == HitTemplateFlag.without_org_and_product_template:
            if not operating_unit_sks:
                raise ParameterException(f"{HitTemplateFlag.get_title(feature_template.hit_template_flag)}视图需要指定业务实体")
            if len(operating_unit_sks) > 1:
                raise ParameterException("汇聚维度中只能指定一个业务实体")

        for dim in construct_body.dimensions:
            dim.dimension_code = dim.dimension_code.strip()
            if dim.dimension_code == "operating_unit_sk":
                continue
            combine_object = None
            if dim.object_code in biz_object_codes:
                combine_object = self.meta_data_service.get_meta_data_dimension_field_attribute(
                    dim.object_code, dim.dimension_code
                )
            if dim.object_code in feature_codes:
                combine_object = self.meta_data_service.get_dimension_field_attribute_by_feature_code(
                    dim.object_code, dim.dimension_code
                )
            if combine_object is None:
                raise ParameterException(f"{dim.object_code}中无维度{dim.dimension_code}<br/>请确认维度是否正确或者在元数据表定义")
