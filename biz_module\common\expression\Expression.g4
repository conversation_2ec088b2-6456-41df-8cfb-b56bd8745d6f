// antlr4.bat -Dlanguage=Python3 Expression.g4
grammar Expression;
statement : (expr | branch) ';';

condition_body: expr ':' expr;

base_if: IF condition_body;

option_if: ELIF condition_body;

default_if: ELSE ':' expr;

branch: base_if (option_if)* default_if? ;

expr    :   Func '(' expr (',' expr)*? ')'      # Func
        |   '-' expr                # Neg
        |   expr '*' expr           # Mul
        |   expr '/' expr           # Div
        |   expr '+' expr           # Add
        |   expr '-' expr           # Sub
        |   expr '=' expr           # Eql
        |   expr ('!='| '<>') expr  # NotEql
        |   expr '>' expr           # Gt
        |   expr '<' expr           # Lt
        |   expr '>=' expr          # Gte
        |   expr '<=' expr          # Lte
        |   expr AND expr           # And
        |   expr OR expr            # Or
        |   NOT expr                # Not
        |   expr 'in' '(' expr (',' expr)* ')'  # In
        |   ID                      # Id
        |   NULL                    # Null
        |   STR                     # Str
        |   INT                     # Int
        |   FLOAT                   # Float
        |   '(' expr ')'            # Parens
   ;

Func: [cC][oO][aA][lL][eE][sS][cC][eE]|[aA][vV][gG] | [mM][iI][nN] | [mM][aA][xX] | [sS][uU][mM] | [cC][oO][uU][nN][tT] | 'nullif' | 'NULLIF' ;
IF   : [iI][fF] ;
ELIF : [eE][lL][iI][fF];
ELSE : [eE][lL][sS][eE];
AND : [Aa][Nn][Dd];
NOT : [Nn][Oo][Tt];
OR : [Oo][Rr];
NULL : [nN][uU][lL][lL];
ID  :   LETTER (LETTER | [0-9])* ;
STR : '\'' ( ('\'\'') | ('\\'+ ~'\\') | ~('\'' | '\\') )* '\'' ;

fragment
LETTER : [A-Za-z\u0080-\uFFFF_]+;
INT :   [0-9]+ ;
FLOAT:  [0-9]*'.'[0-9]+ ;

WS  :   [ \t\n\r]+ -> skip ;