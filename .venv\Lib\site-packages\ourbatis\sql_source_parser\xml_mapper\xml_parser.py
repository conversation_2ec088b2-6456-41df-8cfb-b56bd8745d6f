import logging
import re
from typing import Optional

import sqlparse
from lxml import etree as et
from lxml.etree import Element

from ourbatis.const import DEFAULT_NS
from ourbatis.exceptions import NotFoundSqlIDError
from ourbatis.sql_source_parser.base_parser import BaseParser
from ourbatis.sql_source_parser.objects import (
    SessionSQLMapper,
    SQLMapperMeta,
    BatisElement,
    SQLMapper,
)
from ourbatis.sql_source_parser.sql_context import SqlContext
from ourbatis.sql_source_parser.xml_mapper.convert import (
    QUERY_TYPES,
    convert_cdata,
    convert_children,
)

logger = logging.getLogger("xml parser")
CDATA_REGEX = re.compile(r"(<!\[CDATA\[)([\s\S]*?)(]]>)")
ANNOTATION_REGEX = re.compile(r"<!--.*?-->", re.DOTALL)


class XMLParser(BaseParser):
    SOURCE_SUFFIX = (".xml",)

    @staticmethod
    def strip_annotation(raw_text):
        raw_text = ANNOTATION_REGEX.sub("", raw_text)
        return raw_text

    @staticmethod
    def replace_cdata(raw_text):
        """
        Replace CDATA String
        :param raw_text:
        :return:
        """
        match = CDATA_REGEX.search(raw_text)
        if match:
            cdata_text = match.group(2)
            cdata_text = convert_cdata(cdata_text, reverse=True)
            raw_text = raw_text.replace(match.group(), cdata_text)
        return raw_text

    @classmethod
    def gen_mapper(cls, statement: str, meta: SQLMapperMeta):
        session_sql_mapper: SessionSQLMapper = {}

        raw_text = cls.strip_annotation(statement)
        raw_text = cls.replace_cdata(raw_text)
        root = et.fromstring(raw_text)
        namespace = meta.namespace = root.attrib.get("namespace", DEFAULT_NS)
        session_sql_mapper.setdefault(namespace, {})
        for child in root:
            child: Element = child
            if child.tag in QUERY_TYPES:
                child_id = child.attrib.get("id")
                session_sql_mapper[namespace][child_id] = BatisElement(
                    meta=meta.copy(), parser=cls, element=child
                )
        return session_sql_mapper

    @classmethod
    def create_mapper_by_statement(
        cls, statement: str, namespace=DEFAULT_NS
    ) -> SessionSQLMapper:
        statement = f"<mapper namespace='{namespace}'>\n{statement}\n</mapper>"
        meta = SQLMapperMeta(file_name="<string>")
        session_sql_mapper = cls.gen_mapper(statement, meta)
        return session_sql_mapper

    @classmethod
    def create_mapper(cls, source_file) -> SessionSQLMapper:
        """
        Parse XML files
        Get mybatis mapper
        :return:
        """
        meta = SQLMapperMeta(file_name=source_file)
        with open(source_file, "r", encoding="utf-8") as f:
            xml_raw_text = f.read()
        logger.info(f"handler xml file '{meta.file_name}' now")
        session_sql_mapper = cls.gen_mapper(xml_raw_text, meta)
        return session_sql_mapper

    @staticmethod
    def get_sql_statement(
        sql_mapper: SQLMapper, child_id: str, context: SqlContext
    ) -> str:
        """
        Get SQL Statement By child_id
        Formatting of SQL Statements
        :return:
        """
        context.input_params = (
            context.input_params
            if context.input_params
            else {"reindent": True, "strip_comments": True}
        )
        # get sql
        statement = ""
        child = sql_mapper.get(child_id)
        if child is None:
            logger.error(f"have no sql_id '{child_id}'!check your code")
            raise NotFoundSqlIDError(child_id)

        statement += convert_children(sql_mapper, child, context)
        # The child element has children
        for next_child in child:
            statement += convert_children(sql_mapper, next_child, context)
        return sqlparse.format(statement, **context.input_params)
