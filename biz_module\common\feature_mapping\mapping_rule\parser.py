"""
规则解析
"""
import logging
from functools import cached_property
from typing import List, Union, Dict
from linezone_commonserver.utils.tools import SqlSnippetHelper, SqlSnippetReq, object_to_object_list
from biz_module.common.feature_mapping.dao import FeatureMappingRuleDao
from biz_module.common.feature_mapping.feature_source_descriptor import (
    FeatureSourceDescriptor,
    meta_dimension_descriptor,
    category_name_descriptor,
    feature_code_descriptor,
    feature_id_descriptor,
    meta_dimension_is_deleted_descriptor,
    category_is_deleted_descriptor,
    feature_calculation_type_descriptor,
    feature_feature_type_descriptor,
    feature_subtype_descriptor,
    feature_data_type_descriptor,
    feature_is_custom_descriptor,
    feature_is_deleted_descriptor,
    feature_category_id_descriptor,
    feature_view_dimension_descriptor,
)
from biz_module.common.feature_mapping.mapping_rule.condition_item import (
    create_eq_type_condition,
)
from biz_module.common.feature_mapping.mapping_rule.typedef import (
    GroupRuleDefinition,
    ExpressionGlue,
    RuleDefinition,
    IsDeletedEnum,
)
from biz_module.common.feature_mapping.typedef import SourceDependentValuesReq

logger = logging.getLogger(__name__)
_EMPTY_CONDITION = ""


def is_rule_definition(rule):
    return isinstance(rule, RuleDefinition)


class ParserManager:
    _parsers = {}

    def __init__(self, group_rule_definition: GroupRuleDefinition):

        self._group_rule_definition = group_rule_definition

    @cached_property
    def condition_snippet(self) -> str:
        if not self._group_rule_definition:
            return _EMPTY_CONDITION

        return self._parse_group_rule_definition(self._group_rule_definition)

    def _parse_group_rule_definition(self, group_rule_definition: GroupRuleDefinition) -> str:
        """
        解析组规则

        Parameters
        ----------
        group_rule_definition

        Returns
        -------
        Notes
        -------
        同一层级对象类型一致

        """
        conditions = []
        for rule in group_rule_definition.rules:
            condition = None
            if is_rule_definition(rule):
                condition = self._parse_rule_definition(rule)
            elif isinstance(rule, GroupRuleDefinition):
                condition = self._parse_group_rule_definition(rule)

            if condition:
                conditions.append(condition)

        return self._to_snippet(group_rule_definition.relation, conditions)

    def _parse_rule_definition(self, rule_definition: RuleDefinition):
        parser = self.get_parser(rule_definition.descriptor)
        return parser(rule_definition)

    @classmethod
    def register_parser(cls, descriptor: Union[FeatureSourceDescriptor, List[FeatureSourceDescriptor]]):
        def wrapper(parser_cls):
            if isinstance(descriptor, FeatureSourceDescriptor):
                cls._parsers[id(descriptor)] = parser_cls()
            else:
                for d in descriptor:
                    cls._parsers[id(d)] = parser_cls()
            return parser_cls

        return wrapper

    @classmethod
    def get_parser(cls, descriptor: FeatureSourceDescriptor):
        if not descriptor:
            raise TypeError("empty descriptor")

        parser = cls._parsers.get(id(descriptor), None)
        if not parser:
            raise TypeError("illegal descriptor")

        return parser

    @staticmethod
    def _to_snippet(relation: ExpressionGlue, conditions: List[str]):
        if not conditions or not relation:
            return _EMPTY_CONDITION

        condition_str = f" {relation.value} ".join(conditions)
        if len(conditions) > 1:
            condition_str = f"({condition_str})"
        return condition_str


class BaseParser:
    _descriptor_condition_items: Dict[int, List[RuleDefinition]] = None
    rule_dao = FeatureMappingRuleDao()

    def load_dependent_values(self, descriptor: FeatureSourceDescriptor, condition_sql: str):
        condition_sql = self._combine_siblings(descriptor, condition_sql)
        req_param = SourceDependentValuesReq(
            source_table=descriptor.tenant_table_name, search_column=descriptor.target_column, condition_sql=condition_sql
        )
        values = self.rule_dao.load_source_dependent_values(req_param)
        return values

    @staticmethod
    def _check_rule_data(rule_definition: RuleDefinition):
        if not rule_definition.rule_data:
            raise ValueError("empty rule data!")
        # todo data_type_check

    @staticmethod
    def _rule_data_to_list(rule_definition: RuleDefinition):
        rule_definition.rule_data = object_to_object_list(rule_definition.rule_data)

    def _combine_siblings(self, descriptor: FeatureSourceDescriptor, condition_sql: str):
        """
        组合descriptor siblings

        本版本仅支持Operator.AND
        Parameters
        ----------
        descriptor
        condition_sql

        Returns
        -------

        """
        if descriptor.siblings is None or self._descriptor_condition_items is None:
            return condition_sql

        sibling_conditions = []
        for sibling in descriptor.siblings:
            siblings = self._descriptor_condition_items.get(id(sibling), None)
            if siblings is None:
                continue
            sibling_conditions.extend(siblings)

        if not sibling_conditions:
            return condition_sql

        build_in_condition = ExpressionGlue.AND.value.join(
            [condition_item.get_expression() for condition_item in sibling_conditions]
        )
        return f"{condition_sql} {ExpressionGlue.AND.value} {build_in_condition}"


@ParserManager.register_parser(meta_dimension_descriptor)
class DimensionParser(BaseParser):
    """
    维度规则

    通过元数据维度关联特征，不会进行级联关联
    一个rule_data数组代表一个维度组合，完全匹配的特征才会被映射进来
    元数据维度->业务视图维度->特征
    """

    rule_dao: FeatureMappingRuleDao
    _descriptor_condition_items = {
        id(meta_dimension_is_deleted_descriptor): [
            create_eq_type_condition(meta_dimension_is_deleted_descriptor, IsDeletedEnum.NOT_DELETED.value)
        ]
    }

    def __call__(self, rule_definition: RuleDefinition):
        # 根据维度条件筛选维度ID
        dimension_ids = self.load_dependent_values(rule_definition.descriptor, rule_definition.get_expression())
        if not dimension_ids:
            return _EMPTY_CONDITION

        # 根据维度ID生成最终条件
        last_descriptor = rule_definition.descriptor.next_descriptor
        condition_req2 = SqlSnippetReq(
            key=last_descriptor.condition_column,
            data_values=dimension_ids,
            data_type=last_descriptor.condition_value_data_type,
        )
        return SqlSnippetHelper.build_in_snippet(condition_req2)


@ParserManager.register_parser(category_name_descriptor)
class CategoryNameParser(BaseParser):
    _descriptor_condition_items = {
        id(category_is_deleted_descriptor): [
            create_eq_type_condition(category_is_deleted_descriptor, IsDeletedEnum.NOT_DELETED.value)
        ]
    }

    def __call__(self, rule_definition: RuleDefinition):
        # 根据类别Name获取类别ID
        category_name_condition_sql = rule_definition.get_expression()
        category_ids = self.load_dependent_values(rule_definition.descriptor, category_name_condition_sql)
        if not category_ids:
            return _EMPTY_CONDITION

        # 根据类别ID生成最终条件
        last_descriptor = rule_definition.descriptor.next_descriptor
        condition_req2 = SqlSnippetReq(
            key=last_descriptor.condition_column,
            data_values=category_ids,
            data_type=last_descriptor.condition_value_data_type,
        )
        return SqlSnippetHelper.build_in_snippet(condition_req2)


@ParserManager.register_parser(
    [
        feature_id_descriptor,
        feature_code_descriptor,
        feature_calculation_type_descriptor,
        feature_feature_type_descriptor,
        feature_subtype_descriptor,
        feature_data_type_descriptor,
        feature_is_custom_descriptor,
        feature_is_deleted_descriptor,
        feature_category_id_descriptor,
        feature_view_dimension_descriptor,
    ]
)
class FeatureAttributeParser(BaseParser):
    def __call__(self, rule_definition: RuleDefinition):
        self._check_rule_data(rule_definition)
        return rule_definition.get_expression()
