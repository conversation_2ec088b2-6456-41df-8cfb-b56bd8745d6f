import enum


class IsIncludeGroupBuyEnum(enum.Enum):
    YES = '含团购'
    NO = '不含团购'

    @classmethod
    def return_value_label_list(cls) -> list:
        return [{"label": i, "value": i} for i in [cls.YES.value, cls.NO.value]]


class LineChartKpiEnum(enum.Enum):
    AVG_STORE_WEEK_SALE = 'avg_store_week_sale'

    SALE_QTY = 'sale_qty'

    SALE_AMT = 'sale_amt'

    WEEK_SALE_OUT = 'week_sale_out'

    TOTAL_SALE_OUT = 'total_sale_out'

    DISTRIBUTE_STORE_QTY = 'distribute_store_qty'

    DISCOUNT = 'discount'

    TEMPERATURE = 'temperature'

    @classmethod
    def get_line_chart_info_by_kpi_key(cls, kpi_key, is_current_quarter, has_skc):
        mapping = {
            1: {
                0:{
            cls.AVG_STORE_WEEK_SALE.value: {
                "xAxisData": [[], []],
                "seriesData": [[], []],
                "legendData": [{"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                "typeData": ['line', 'line'],
            },
            cls.SALE_QTY.value: {
                "xAxisData": [[], []],
                "seriesData": [[], []],
                "legendData": [{"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际/模拟', "color": "#0e0e0f", "key": "kpi"}],
                "typeData": ['line', 'line'],
            },
            cls.SALE_AMT.value: {
                "xAxisData": [[], []],
                "seriesData": [[], [], []],
                "legendData": [{"name": "目标", "color": "#218000", "key": "target_kpi"}, {"name": '参照', "color": "#FF0000", "key": "refer_kpi"},
                               {"name": '实际/模拟', "color": "#0e0e0f", "key": "kpi"}],
                "typeData": ['line', 'line', 'line'],
            },
            cls.WEEK_SALE_OUT.value: {
                "xAxisData": [[], []],
                "seriesData": [[], []],
                "legendData": [{"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际/模拟', "color": "#0e0e0f", "key": "kpi"}],
                "typeData": ['line', 'line'],
            },
            cls.TOTAL_SALE_OUT.value: {
                "xAxisData": [[], []],
                "seriesData": [[], [], []],
                "legendData": [{"name": "目标", "color": "#218000", "key": "target_kpi"}, {"name": '参照', "color": "#FF0000", "key": "refer_kpi"},
                               {"name": '实际/模拟', "color": "#0e0e0f", "key": "kpi"}],
                "typeData": ['line', 'line', 'line'],
            },
            cls.DISTRIBUTE_STORE_QTY.value: {
                "xAxisData": [[], []],
                "seriesData": [[]],
                "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                "typeData": ['line'],
            },
            cls.DISCOUNT.value: {
                "xAxisData": [[], []],
                "seriesData": [[], []],
                "legendData": [{"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                "typeData": ['line', 'line'],
            },
            cls.TEMPERATURE.value: {
                "xAxisData": [[], []],
                "seriesData": [[], []],
                "legendData": [{"name": '最高', "color": "#8b0100", "key": "max_kpi"}, {"name": '最低', "color": "#218000", "key": "min_kpi"}],
                "typeData": ['line', 'line'],
            },
        },
                1: {
                cls.AVG_STORE_WEEK_SALE.value: {
                    "xAxisData": [[], []],
                    "seriesData": [[], []],
                    "legendData": [{"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                    "typeData": ['line', 'line'],
                },
                cls.SALE_QTY.value: {
                    "xAxisData": [[], []],
                    "seriesData": [[], [], []],
                    "legendData": [{"name": "目标", "color": "#218000", "key": "target_kpi"}, {"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                    "typeData": ['line', 'line', 'line'],
                },
                cls.SALE_AMT.value: {
                    "xAxisData": [[], []],
                    "seriesData": [[], [], []],
                    "legendData": [{"name": "目标", "color": "#218000", "key": "target_kpi"}, {"name": '参照', "color": "#FF0000", "key": "refer_kpi"},
                                   {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                    "typeData": ['line', 'line', 'line'],
                },
                cls.WEEK_SALE_OUT.value: {
                    "xAxisData": [[], []],
                    "seriesData": [[], []],
                    "legendData": [{"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                    "typeData": ['line', 'line'],
                },
                cls.TOTAL_SALE_OUT.value: {
                    "xAxisData": [[], []],
                    "seriesData": [[], [], []],
                    "legendData": [{"name": "目标", "color": "#218000", "key": "target_kpi"}, {"name": '参照', "color": "#FF0000", "key": "refer_kpi"},
                                   {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                    "typeData": ['line', 'line', 'line'],
                },
                cls.DISTRIBUTE_STORE_QTY.value: {
                    "xAxisData": [[], []],
                    "seriesData": [[], []],
                    "legendData": [{"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                    "typeData": ['line', 'line'],
                },
                cls.DISCOUNT.value: {
                    "xAxisData": [[], []],
                    "seriesData": [[], []],
                    "legendData": [{"name": '参照', "color": "#FF0000", "key": "refer_kpi"}, {"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                    "typeData": ['line', 'line'],
                },
                cls.TEMPERATURE.value: {
                    "xAxisData": [[], []],
                    "seriesData": [[], []],
                    "legendData": [{"name": '最高', "color": "#8b0100", "key": "max_kpi"},
                                   {"name": '最低', "color": "#218000", "key": "min_kpi"}],
                    "typeData": ['line', 'line'],
                },
            }
        },
            0: {
                0: {
                    cls.AVG_STORE_WEEK_SALE.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[]],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                        "typeData": ['line'],
                    },
                    cls.SALE_QTY.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"},
                                       {"name": '还原', "color": "#808080", "key": "restore_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                    cls.SALE_AMT.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"},
                                       {"name": '还原', "color": "#808080", "key": "restore_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                    cls.WEEK_SALE_OUT.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"},
                                       {"name": '还原', "color": "#808080", "key": "restore_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                    cls.TOTAL_SALE_OUT.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"},
                                       {"name": '还原', "color": "#808080", "key": "restore_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                    cls.DISTRIBUTE_STORE_QTY.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[]],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                        "typeData": ['line'],
                    },
                    cls.DISCOUNT.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[]],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                        "typeData": ['line'],
                    },
                    cls.TEMPERATURE.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '最高', "color": "#8b0100", "key": "max_kpi"},
                                       {"name": '最低', "color": "#218000", "key": "min_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                },
                1: {
                    cls.AVG_STORE_WEEK_SALE.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[]],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                        "typeData": ['line'],
                    },
                    cls.SALE_QTY.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"},
                                       {"name": '还原', "color": "#808080", "key": "restore_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                    cls.SALE_AMT.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"},
                                       {"name": '还原', "color": "#808080", "key": "restore_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                    cls.WEEK_SALE_OUT.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"},
                                       {"name": '还原', "color": "#808080", "key": "restore_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                    cls.TOTAL_SALE_OUT.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"},
                                       {"name": '还原', "color": "#808080", "key": "restore_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                    cls.DISTRIBUTE_STORE_QTY.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[]],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                        "typeData": ['line'],
                    },
                    cls.DISCOUNT.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[]],
                        "legendData": [{"name": '实际', "color": "#0e0e0f", "key": "kpi"}],
                        "typeData": ['line'],
                    },
                    cls.TEMPERATURE.value: {
                        "xAxisData": [[], []],
                        "seriesData": [[], []],
                        "legendData": [{"name": '最高', "color": "#8b0100", "key": "max_kpi"},
                                       {"name": '最低', "color": "#218000", "key": "min_kpi"}],
                        "typeData": ['line', 'line'],
                    },
                }
            }
        }
        return mapping.get(is_current_quarter, {}).get(has_skc, {}).get(kpi_key, {})


class LifCycleColorEnum(enum.Enum):

    INTRODUCTION_PERIOD = '导入期（铺货期）'

    GROWTH_PERIOD = '成长期'

    MATURITY_PERIOD = '成熟期'

    DECLINE_PERIOD = '衰退期'

    @classmethod
    def get_color(cls, stage: str):
        """
        获取生命周期颜色
        :param stage:
        :return:
        """
        mapping = {
           cls.INTRODUCTION_PERIOD.value: '#eefcfb',
           cls.GROWTH_PERIOD.value: '#edf7e9',
           cls.MATURITY_PERIOD.value: '#fee8ea',
           cls.DECLINE_PERIOD.value: '#fef7f2',
        }
        return mapping.get(stage)


ReferSkcListTitle = [
    {"name": "当前款", "display": True, "code": "current_quarter_skc_code", "value_type": "string", "formatter": ""},
    {"name": "参照款", "display": True, "code": "refer_skc_code", "value_type": "string", "formatter": ""},
    {"name": "图片", "display": True, "code": "image_url", "value_type": "string", "formatter": ""},
    {"name": "参照款优先级", "display": True, "code": "priority", "value_type": "number", "formatter": ""},
    {"name": "发布季", "display": True, "code": "product_year_quarter", "value_type": "string", "formatter": ""},
    {"name": "上市日期", "display": True, "code": "real_listing_date", "value_type": "datetime", "formatter": "YYYY-MM-DD"},
    {"name": "款来源", "display": True, "code": "source", "value_type": "string", "formatter": ""},
    {"name": "性别", "display": True, "code": "gender", "value_type": "string", "formatter": ""},
    {"name": "大类", "display": True, "code": "big_class", "value_type": "string", "formatter": ""},
    {"name": "品类", "display": True, "code": "product_range", "value_type": "string", "formatter": ""},
    {"name": "PS中类", "display": True, "code": "ps_mid_class", "value_type": "string", "formatter": ""},
    {"name": "销售中小类", "display": True, "code": "sale_mid_tiny_class", "value_type": "string", "formatter": ""},
    {"name": "款名称", "display": True, "code": "skc_name", "value_type": "string", "formatter": ""},
    {"name": "吊牌价", "display": True, "code": "target_price", "value_type": "number", "formatter": "0,000"},
    {"name": "颜色", "display": True, "code": "color_name", "value_type": "string", "formatter": ""},
    {"name": "发货量", "display": True, "code": "total_shipment_qty", "value_type": "number", "formatter": "0,000"},
    {"name": "订货量", "display": True, "code": "order_qty", "value_type": "number", "formatter": "0,000"},
    {"name": "周销量", "display": True, "code": "week_sale_qty","children": [], "fold": False},
    {"name": "累计销量", "display": True, "code": "total_sale_qty", "value_type": "number", "formatter": "0,000"},
    {"name": "周售罄", "display": True, "code": "week_sale_out","children": [], "fold": False},
    {"name": "累计售罄", "display": True, "code": "total_sale_rate", "value_type": "number", "formatter": "0.0%"},
    {"name": "周折扣", "display": True, "code": "week_discount","children": [], "fold": False},
    {"name": "累计折扣", "display": True, "code": "discount", "value_type": "number", "formatter": "0.0%"},
]


ReferSkcReferSkcListTitle = [
    {"name": "当前款", "display": True, "code": "current_quarter_skc_code", "value_type": "string", "formatter": ""},
    {"name": "参照款", "display": True, "code": "refer_skc_code", "value_type": "string", "formatter": ""},
    {"name": "图片", "display": True, "code": "image_url", "value_type": "string", "formatter": ""},
    {"name": "参照款优先级", "display": True, "code": "priority", "value_type": "number", "formatter": ""},
    {"name": "发布季", "display": True, "code": "product_year_quarter", "value_type": "string", "formatter": ""},
    {"name": "上市日期", "display": True, "code": "real_listing_date", "value_type": "datetime", "formatter": "YYYY-MM-DD"},
    {"name": "款来源", "display": True, "code": "source", "value_type": "string", "formatter": ""},
    {"name": "性别", "display": True, "code": "gender", "value_type": "string", "formatter": ""},
    {"name": "大类", "display": True, "code": "big_class", "value_type": "string", "formatter": ""},
    {"name": "品类", "display": True, "code": "product_range", "value_type": "string", "formatter": ""},
    {"name": "PS中类", "display": True, "code": "ps_mid_class", "value_type": "string", "formatter": ""},
    {"name": "销售中小类", "display": True, "code": "sale_mid_tiny_class", "value_type": "string", "formatter": ""},
    {"name": "款名称", "display": True, "code": "skc_name", "value_type": "string", "formatter": ""},
    {"name": "吊牌价", "display": True, "code": "target_price", "value_type": "number", "formatter": "0,000"},
    {"name": "颜色", "display": True, "code": "color_name", "value_type": "string", "formatter": ""},
    {"name": "发货量", "display": True, "code": "total_shipment_qty", "value_type": "number", "formatter": "0,000"},
    {"name": "订货量", "display": True, "code": "order_qty", "value_type": "number", "formatter": "0,000"},
    {"name": "当前到货量", "display": True, "code": "total_arrive_qty", "value_type": "number", "formatter": "0,000"},
    {"name": "累计销量", "display": True, "code": "total_sale_qty", "value_type": "number", "formatter": "0,000"},
    {"name": "累计售罄", "display": True, "code": "total_sale_rate", "value_type": "number", "formatter": "0.0%"},
    {"name": "累计折扣", "display": True, "code": "discount", "value_type": "number", "formatter": "0.0%"},
]


