import enum


class AddReductionOrderStatusEnum(enum.IntEnum):

    INIT = 1
    CONFIRM = 2

    @classmethod
    def get_cn_status(cls, status: int) -> str:
        d = {
            cls.INIT.value: '待确认',
            cls.CONFIRM.value: '已确认',
        }
        return d.get(status)

    @classmethod
    def return_status_list(cls) -> list:
        return [{"label": cls.get_cn_status(i), "value": i} for i in [cls.INIT.value, cls.CONFIRM.value]]


class AddReductionOrderDecisionSuggestEnum(enum.Enum):

    # 保持现状
    KEEP = "保持现状"
    # 待定
    UNDETERMINED = "待定"
    # 翻单
    ADD_ORDER = "加单"
    # 不翻单
    NOT_ADD_ORDER = "不加单"
    # 用不翻单
    NOT_ADD_ORDER_FOREVER = "永久不加单"
    # 减单
    REDUCTION = "减单"
    # 不减单
    NOT_REDUCTION = "不减单"

    @classmethod
    def get_decision_suggest_list(cls) -> str:
        d = [cls.KEEP.value, cls.UNDETERMINED.value,  cls.ADD_ORDER.value,
             cls.NOT_ADD_ORDER.value, cls.NOT_ADD_ORDER_FOREVER.value,
             cls.REDUCTION.value, cls.NOT_REDUCTION.value]
        return [{"label": i, "value": i} for i in d]



    @classmethod
    def get_all_decision_suggest(cls) -> list:
        return {cls.KEEP.value, cls.UNDETERMINED.value, cls.ADD_ORDER.value, cls.NOT_ADD_ORDER.value, cls.NOT_ADD_ORDER_FOREVER.value, cls.REDUCTION.value, cls.NOT_REDUCTION.value}


class StageEnum(enum.Enum):

    HAS_MARKED_WEEK = '已上市周'

    LT = 'LT'

    ST = 'ST'

    @classmethod
    def get_color_by_stage(cls, stage: str):

        mapping = {
            cls.HAS_MARKED_WEEK.value: '#eefcfb',
            cls.LT.value: '#edf7e9',
            cls.ST.value: '#fee8ea',
        }
        return mapping.get(stage, '#fee8ea')