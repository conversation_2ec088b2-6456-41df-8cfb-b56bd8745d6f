<?xml version="1.0"?>
<mapper namespace="life_cycle_aggregation_dimension">
    
    <select id="query_aggregation_dimension_org_filter">
        select
            <if test="filter_key != 'org_sk'">
                ${filter_key} as label, ${filter_key} as value
            </if>
            <if  test="filter_key == 'org_sk'">
                org_name as label, org_sk as value
            </if>
        from biz.qrs_life_cycle_base_kpi
        where ${filter_key} is not null
        <if test="not org_sk_list">
            and 1 != 1
        </if>
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="large_region">
            and large_region = #{large_region}
        </if>
        <if test="filter_key == 'org_sk'">
            group by org_sk, org_name
        </if>
        <if test="filter_key != 'org_sk'">
            group by ${filter_key}
        </if>
        order by ${filter_key} desc
    </select>

    <select id="query_aggregation_dimension_filter">
        select
            <if test="filter_key == 'skc_sk'">
                skc_code as label, skc_sk as value
            </if>
            <if test="filter_key == 'product_year_quarter'">
                ${filter_key} as label, ${filter_key} as value
                , case when b.quarter is null then 0 else 1 end as is_current_quarter
            </if>
            <if test="filter_key == 'real_listing_date'">
                to_char(put_on_date_correct, 'yyyy-mm-dd') as label, to_char(put_on_date_correct, 'yyyy-mm-dd') as value
            </if>
            <if test="filter_key not in ('skc_sk', 'product_year_quarter', 'real_listing_date')">
                ${filter_key} as label, ${filter_key} as value
            </if>
        from biz.qrs_life_cycle_base_kpi as a
        <if test="filter_key == 'product_year_quarter'">
            left join biz.qrs_current_quarter_config as b on a.product_year_quarter = EXTRACT(YEAR FROM CURRENT_DATE) || b.quarter
            and current_date between  (EXTRACT(YEAR FROM CURRENT_DATE) || '-' || b.begin_date)::DATE and (EXTRACT(YEAR FROM CURRENT_DATE) || '-' || b.end_date)::DATE
        </if>
        where ${filter_key} is not null
        <if test="not is_admin">
            and a.product_range in (select product_range from sys.user_product where user_id = #{user_id})
            and a.big_class in (select big_class from sys.user_product where user_id = #{user_id})
        </if>
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="not channel_type">
            and channel_type is null
        </if>
        <if test="large_region">
            and large_region = #{large_region}
        </if>
        <if test="not large_region">
            and large_region is null
        </if>
        <if test="is_include_group_buy">
            and is_include_group_buy = #{is_include_group_buy}
        </if>
        <if test="org_sk">
            and org_sk = #{org_sk}
        </if>
        <if test="big_class">
            and big_class = #{big_class}
        </if>
        <if test="product_range">
            and product_range = #{product_range}
        </if>
        <if test="product_year_quarter">
            and product_year_quarter = #{product_year_quarter}
        </if>
        <if test="product_belong_name">
            and product_belong_name = #{product_belong_name}
        </if>
        <if test="ps_mid_class">
            and ps_mid_class = #{ps_mid_class}
        </if>
        <if test="sale_mid_tiny_class">
            and sale_mid_tiny_class = #{sale_mid_tiny_class}
        </if>
        <if test="gender">
            and gender = #{gender}
        </if>
        <if test="real_listing_date">
            and put_on_date_correct = #{real_listing_date}
        </if>
        <if test="not real_listing_date and filter_key == 'skc_sk'">
            and put_on_date_correct is null
        </if>
        <if test="product_name">
            and product_name = #{product_name}
        </if>
        <if test="filter_key == 'skc_sk'">
            group by skc_code, skc_sk
        </if>
        <if test="filter_key == 'product_year_quarter'">
            group by product_year_quarter, quarter
        </if>
        <if test="filter_key == 'real_listing_date'">
            group by put_on_date_correct
        </if>
        <if test="filter_key not in ('skc_sk', 'product_year_quarter', 'real_listing_date')">
            group by ${filter_key}
        </if>
        <if test="filter_key == 'product_range'">
            order by case when product_range = '跑步' then 1 when product_range = '健身' then 2 when product_range = '运动生活' then 3 else 4 end
        </if>
        <if test="filter_key != 'product_range'">
            order by label desc
        </if>

    </select>

    <select id="query_aggregation_dimension_base_kpi">
       SELECT
            product_year_quarter -- 发布季
            , gender -- 性别
            , color_name -- 颜色
            , target_price -- 吊牌价
            , big_class -- 大类
            , ps_mid_class -- PS中类
            , sale_mid_tiny_class -- 销售中类小类
            , product_name -- 款名称
            , lt -- lt
            , life_cycle -- 生命周期
            , to_char(put_on_date, 'yyyy-mm-dd') as put_on_date -- 上市日期
            , to_char(real_listing_date, 'yyyy-mm-dd') as real_listing_date -- 实际上市日期
            , order_qty -- 订货量
            , total_shipment_qty -- 发货量
            --, has_add_qty -- 已加单量
            , not_in_stock_qty -- 未到货量
            , to_char(last_arrival_date, 'yyyy-mm-dd') last_arrival_date -- 最近到货日期
            , has_stock_store_qty distributed_store_qty -- 铺店数
            , max_has_stock_store_qty max_distributed_store_qty -- 铺店数
            , has_stock_store_qty::numeric / nullif(total_store_qty, 0) distributed_rate -- 铺货率
            , max_has_stock_store_qty::numeric / nullif(total_store_qty, 0) max_distributed_rate -- 铺货率
            , remaining_stock_qty -- 剩余库存
            , warehouse_stock_qty -- 仓库库存
            , store_stock_qty_include_on_road -- 门店库存（含在途）
            , salable_weeks_num::numeric / nullif(salable_weeks_den, 0) saleable_weeks -- 可销周数
            , store_stock_qty_include_on_road::numeric / nullif(has_stock_store_qty,0) avg_store_stock -- 店均库存
            --, break_size_store_qty::numeric / nullif(has_stock_store_qty, 0)  store_size_break_rate -- 断码率
            , to_char(first_sale_date, 'yyyy-mm-dd') as first_sale_date -- 首销日期
            , total_sale_qty -- 累计销量
            , total_sale_qty_without_group_buy -- 无团购累销
            , has_sale_store_qty -- 有销门店数
            , last_first_week_sale_qty::numeric / nullif(has_sale_store_qty, 0) avg_store_week_sale -- 店均周销
            , last_four_weeks_sale_qty -- 近4周总销量
            , last_first_week_sale_qty -- 近第1周销量
            , last_second_week_sale_qty -- 近第2周销量
            , last_third_week_sale_qty -- 近第3周销量
            , total_sale_qty::numeric / nullif(total_shipment_qty, 0) as total_sale_rate-- 累计售罄
            , last_first_week_sale_qty::numeric / nullif(total_shipment_qty, 0) as last_first_week_sale_rate-- 近一周售罄
            , last_second_week_sale_qty::numeric / nullif(total_shipment_qty, 0) as last_second_week_sale_rate-- 近一周售罄
            , last_third_week_sale_qty::numeric / nullif(total_shipment_qty, 0) as last_third_week_sale_rate-- 近一周售罄
            , total_sale_amt::numeric / nullif(total_sale_tag_amt, 0) total_discount -- 累计则扣
            , last_first_week_sale_amt::numeric / nullif(last_first_week_sale_tag_amt, 0) last_first_week_discount -- 累计则扣
            , last_second_week_sale_amt::numeric / nullif(last_second_week_sale_tag_amt, 0) last_second_week_discount -- 累计则扣
            , last_third_week_sale_amt::numeric / nullif(last_third_week_sale_tag_amt, 0) last_third_week_discount -- 累计则扣
        FROM biz.qrs_life_cycle_base_kpi
        WHERE 1 = 1
        <if test="channel_type">
            AND channel_type = #{channel_type}
        </if>
        <if test="not channel_type">
            AND channel_type is null
        </if>
        <if test="large_region">
            AND large_region = #{large_region}
        </if>
        <if test="not large_region">
            AND large_region is null
        </if>
        <if test="org_sk">
            AND org_sk = #{org_sk}
        </if>
        <if test="not org_sk">
            AND org_sk is null
        </if>
        AND is_include_group_buy = #{is_include_group_buy}
        AND big_class  = #{big_class}
        AND product_range = #{product_range}
        AND product_year_quarter = #{product_year_quarter}
        AND gender = #{gender}
        <if test="real_listing_date">
            AND put_on_date_correct = #{real_listing_date}
        </if>
        <if test="not real_listing_date">
            AND put_on_date_correct is null
        </if>
        <if test="ps_mid_class">
            AND ps_mid_class = #{ps_mid_class}
        </if>
        <if test="not ps_mid_class">
            AND ps_mid_class is null
        </if>
        <if test="sale_mid_tiny_class">
            AND sale_mid_tiny_class = #{sale_mid_tiny_class}
        </if>
        <if test="not sale_mid_tiny_class">
            AND sale_mid_tiny_class is null
        </if>
        <if test="product_name">
            AND product_name = #{product_name}
        </if>
        <if test="not product_name">
            AND product_name is null
        </if>
        <if test="skc_sk">
            AND skc_sk = #{skc_sk}
        </if>
        <if test="not skc_sk">
            AND skc_sk is null
        </if>

    </select>

    <select id="query_aggregation_dimension_line_chart">
        SELECT
        ROW_NUMBER() OVER (ORDER BY natural_year, natural_time) AS listing_time
        ,natural_time, natural_year, stage
        <if test="is_current_quarter">
            <if test="kpi_key=='avg_store_week_sale'">
                , round(sale_qty::numeric / nullif(has_sale_store_qty, 0), 1) as kpi
                , round(refer_sale_qty::numeric / nullif(refer_has_sale_store_qty, 0), 1) as refer_kpi
            </if>
            <if test="kpi_key=='sale_qty'">
                , sale_qty as kpi
                , refer_sale_qty as refer_kpi
                , target_sale_qty as target_kpi
            </if>
            <if test="kpi_key=='sale_amt'">
                , sale_amt::int as kpi
                , refer_sale_amt::int as refer_kpi
                , target_sale_amt::int as target_kpi
            </if>
            <if test="kpi_key=='week_sale_out'">
                , round(sale_qty * 100.0 / nullif(shipment_qty, 0), 1) as kpi
                , round(refer_sale_qty * 100.0 / nullif(refer_shipment_qty, 0), 1) as refer_kpi
            </if>
            <if test="kpi_key=='total_sale_out'">
                , round(total_sale_qty * 100.0 / nullif(shipment_qty, 0), 1) as kpi
                , round(refer_total_sale_qty * 100.0 / nullif(refer_shipment_qty, 0), 1) as refer_kpi
                , round(target_total_sale_qty * 100.0 / nullif(shipment_qty, 0), 1) as target_kpi
            </if>
            <if test="kpi_key=='distribute_store_qty'">
                , has_stock_store_qty as kpi
                , refer_has_stock_store_qty as refer_kpi
            </if>
            <if test="kpi_key=='discount'">
                , round(sale_amt * 100.0 / nullif(sale_tag_amt, 0), 1) as kpi
                , round(refer_sale_amt * 100.0 / nullif(refer_sale_tag_amt, 0), 1) as refer_kpi
            </if>
            <if test="kpi_key=='temperature'">
                , round(max_temperature, 1) as max_kpi
                , round(min_temperature, 1) as min_kpi
            </if>
        </if>
        <if test=" not is_current_quarter">
            <if test="kpi_key=='avg_store_week_sale'">
                , round(sale_qty::numeric / nullif(has_sale_store_qty, 0), 1) as kpi
            </if>
            <if test="kpi_key=='sale_qty'">
                , sale_qty as kpi
                , restore_sale_qty as restore_kpi
                , target_sale_qty as target_kpi
            </if>
            <if test="kpi_key=='sale_amt'">
                , sale_amt::int as kpi
                , restore_sale_amt::int as restore_kpi
                , target_sale_amt::int as target_kpi
            </if>
            <if test="kpi_key=='week_sale_out'">
                , round(sale_qty * 100.0 / nullif(shipment_qty, 0), 1) as kpi
                , round(restore_sale_qty * 100.0 / nullif(shipment_qty, 0), 1) as restore_kpi
            </if>
            <if test="kpi_key=='total_sale_out'">
                , round(total_sale_qty * 100.0 / nullif(shipment_qty, 0), 1) as kpi
                , round(restore_total_sale_qty * 100.0 / nullif(shipment_qty, 0), 1) as restore_kpi
                , round(target_total_sale_qty * 100.0 / nullif(shipment_qty, 0), 1) as target_kpi
            </if>
            <if test="kpi_key=='distribute_store_qty'">
                , has_stock_store_qty as kpi
            </if>
            <if test="kpi_key=='discount'">
                , round(sale_amt * 100.0 / nullif(sale_tag_amt, 0), 1) as kpi
            </if>
            <if test="kpi_key=='temperature'">
                , round(max_temperature, 1) as max_kpi
                , round(min_temperature, 1) as min_kpi
            </if>
        </if>
        FROM biz.qrs_life_cycle_week_kpi
        where type = #{dimension}
        <if test="channel_type">
            AND channel_type = #{channel_type}
        </if>
        <if test="not channel_type">
            AND channel_type is null
        </if>
        <if test="large_region">
            AND large_region = #{large_region}
        </if>
        <if test="not large_region">
            AND large_region is null
        </if>
        <if test="org_sk">
            AND org_sk = #{org_sk}
        </if>
        <if test="not org_sk">
            AND org_sk is null
        </if>
        AND is_include_group_buy = #{is_include_group_buy}
        AND big_class  = #{big_class}
        AND product_range = #{product_range}
        AND product_year_quarter = #{product_year_quarter}
        AND gender = #{gender}
        <if test="real_listing_date">
            AND put_on_date_correct = #{real_listing_date}
        </if>
        <if test="not real_listing_date">
            AND put_on_date_correct is null
        </if>
        <if test="ps_mid_class">
            AND ps_mid_class = #{ps_mid_class}
        </if>
        <if test="not ps_mid_class">
            AND ps_mid_class is null
        </if>
        <if test="sale_mid_tiny_class">
            AND sale_mid_tiny_class = #{sale_mid_tiny_class}
        </if>
        <if test="not sale_mid_tiny_class">
            AND sale_mid_tiny_class is null
        </if>
        <if test="product_name">
            AND product_name = #{product_name}
        </if>
        <if test="not product_name">
            AND product_name is null
        </if>
        <if test="skc_sk">
            AND skc_sk = #{skc_sk}
        </if>
        <if test="not skc_sk">
            AND skc_sk is null
        </if>
        order by listing_time
    </select>

    <select id="query_aggregation_dimension_list">
        select
            ROW_NUMBER() OVER (ORDER BY natural_year, natural_time) AS listing_time, natural_time, natural_year, stage
            ,round(sale_qty::numeric / nullif(has_sale_store_qty, 0), 3) as avg_store_week_sale -- 店均周销
            ,round(refer_sale_qty::numeric / nullif(refer_has_sale_store_qty, 0), 3) as refer_avg_store_week_sale -- 店均周销
            ,sale_qty -- 周销
            ,target_sale_qty -- 周销
            ,refer_sale_qty -- 周销
            ,restore_sale_qty -- 周销
            ,sale_amt::int -- 流水
            ,target_sale_amt::int -- 流水
            ,refer_sale_amt::int -- 流水
            ,restore_sale_amt::int -- 流水
            ,round(sale_qty::numeric / nullif(shipment_qty, 0), 3) as week_sale_out -- 单周售罄
            ,round(refer_sale_qty::numeric / nullif(refer_shipment_qty, 0), 3) as refer_week_sale_out -- 单周售罄
            ,round(restore_sale_qty::numeric / nullif(shipment_qty, 0), 3) as restore_week_sale_out -- 单周售罄
            ,round(total_sale_qty::numeric / nullif(shipment_qty, 0), 3) as total_sale_out -- 累计售罄
            ,round(target_total_sale_qty::numeric / nullif(shipment_qty, 0), 3) as target_total_sale_out -- 累计售罄
            ,round(refer_total_sale_qty::numeric / nullif(refer_shipment_qty, 0), 3) as refer_total_sale_out -- 累计售罄
            ,round(restore_total_sale_qty::numeric / nullif(shipment_qty, 0), 3) as restore_total_sale_out -- 累计售罄
            ,has_stock_store_qty -- 铺货门店数
            ,refer_has_stock_store_qty -- 铺货门店数
            ,round(sale_amt::numeric / nullif(sale_tag_amt, 0), 3) as discount -- 折扣
            ,round(refer_sale_amt::numeric / nullif(refer_sale_tag_amt, 0), 3) as refer_discount -- 折扣
            ,round(max_temperature, 1) max_temperature -- 最高温度
            ,round(min_temperature, 1) min_temperature -- 最低温度
        FROM biz.qrs_life_cycle_week_kpi
        where type = #{dimension}
        <if test="channel_type">
            AND channel_type = #{channel_type}
        </if>
        <if test="not channel_type">
            AND channel_type is null
        </if>
        <if test="large_region">
            AND large_region = #{large_region}
        </if>
        <if test="not large_region">
            AND large_region is null
        </if>
        <if test="org_sk">
            AND org_sk = #{org_sk}
        </if>
        <if test="not org_sk">
            AND org_sk is null
        </if>
        AND is_include_group_buy = #{is_include_group_buy}
        AND big_class  = #{big_class}
        AND product_range = #{product_range}
        AND product_year_quarter = #{product_year_quarter}
        AND gender = #{gender}
       <if test="real_listing_date">
            AND put_on_date_correct = #{real_listing_date}
        </if>
        <if test="not real_listing_date">
            AND put_on_date_correct is null
        </if>
        <if test="ps_mid_class">
            AND ps_mid_class = #{ps_mid_class}
        </if>
        <if test="not ps_mid_class">
            AND ps_mid_class is null
        </if>
        <if test="sale_mid_tiny_class">
            AND sale_mid_tiny_class = #{sale_mid_tiny_class}
        </if>
        <if test="not sale_mid_tiny_class">
            AND sale_mid_tiny_class is null
        </if>
        <if test="product_name">
            AND product_name = #{product_name}
        </if>
        <if test="not product_name">
            AND product_name is null
        </if>
        <if test="skc_sk">
            AND skc_sk = #{skc_sk}
        </if>
        <if test="not skc_sk">
            AND skc_sk is null
        </if>
        order by listing_time
    </select>

    <select id="query_aggregation_dimension_skc_list">
        select lf.skc_code, lf.big_class, lf.product_range, lf.product_year_quarter, lf.gender
            , lf.ps_mid_class, lf.sale_mid_tiny_class, lf.product_name
            , real_listing_date
            , lf.target_price
            , lf.color_name
            , order_qty
            , total_shipment_qty
            , total_sale_qty
            , total_sale_qty::numeric / nullif(total_shipment_qty, 0) as sale_out
            , total_sale_amt::numeric / nullif(total_sale_tag_amt, 0) as discount
            , count(1) over() as total_count
            , picture_url as image_url
        from biz.qrs_life_cycle_base_kpi as lf
        left join dm.dim_skc as b on lf.skc_sk = b.skc_sk
        WHERE is_include_group_buy = #{is_include_group_buy}
        <if test="channel_type">
            AND channel_type = #{channel_type}
        </if>
        <if test="not channel_type">
            AND channel_type is null
        </if>
        <if test="large_region">
            AND large_region = #{large_region}
        </if>
        <if test="not large_region">
            AND large_region is null
        </if>
        <if test="org_sk">
            AND org_sk = #{org_sk}
        </if>
        <if test="not org_sk">
            AND org_sk is null
        </if>
        AND lf.big_class  = #{big_class}
        AND lf.product_range = #{product_range}
        AND lf.product_year_quarter = #{product_year_quarter}
        AND lf.gender = #{gender}
        <if test="real_listing_date">
            and lf.put_on_date_correct = #{real_listing_date}
        </if>
        <if test="not real_listing_date">
            and lf.put_on_date_correct is null
        </if>
        <if test="ps_mid_class">
            AND lf.ps_mid_class = #{ps_mid_class}
        </if>
        <if test="not ps_mid_class">
            AND lf.ps_mid_class is null
        </if>
        <if test="sale_mid_tiny_class">
            AND lf.sale_mid_tiny_class = #{sale_mid_tiny_class}
        </if>
        <if test="not sale_mid_tiny_class">
            AND lf.sale_mid_tiny_class is null
        </if>
        <if test="product_name">
            AND lf.product_name = #{product_name}
        </if>
        <if test="not product_name">
            AND lf.product_name is null
        </if>
        AND lf.skc_sk is not null
        <if test="column_orders_str">
            order by ${column_orders_str}
        </if>
        <if test="page_size">
            LIMIT ${page_size}
            OFFSET ${page_offset}
        </if>
    </select>

    <select id="query_aggregation_dimension_refer_skc_list">
       WITH current_quarter_skc AS (
            SELECT channel_type, large_region_sk as org_sk, skc_sk as current_quarter_skc_sk, skc_code as current_quarter_skc_code, skc_code, skc_code as refer_skc_code, 0::int as priority, product_year_quarter, real_listing_date
                , '当前款'::text as source
                , gender, big_class, product_range, ps_mid_class, sale_mid_tiny_class, product_name, target_price
                , color_name, total_shipment_qty, order_qty, total_sale_qty
                , total_sale_qty::numeric / nullif(total_shipment_qty, 0) as total_sale_rate
                , total_sale_amt::numeric / nullif(total_sale_tag_amt, 0) as discount
            FROM biz.qrs_life_cycle_base_kpi
            WHERE channel_type = '直营' AND large_region ='正价店' and org_sk is null and is_include_group_buy = '含团购'
            and skc_sk = #{skc_sk}
        )
        , refer_skc AS (
            SELECT
                c.channel_type, c.large_region_sk as org_sk, b.current_quarter_skc_sk, b.current_quarter_skc_code, b.skc_code, c.skc_code as refer_skc_code, a.priority, c.product_year_quarter, c.real_listing_date
                , a.source
                , c.gender, c.big_class, c.product_range, c.ps_mid_class, c.sale_mid_tiny_class, c.product_name, c.target_price
                , c.color_name, c.total_shipment_qty, c.order_qty, c.total_sale_qty
                , c.total_sale_qty::numeric / nullif(c.total_shipment_qty, 0) as total_sale_rate
                , c.total_sale_amt::numeric / nullif(c.total_sale_tag_amt, 0) as discount
            FROM biz.qrs_refer_skc_item as a
            INNER JOIN current_quarter_skc as b ON a.skc_sk = b.current_quarter_skc_sk AND a.channel_type = b.channel_type AND a.org_sk = b.org_sk
            INNER JOIN biz.qrs_life_cycle_base_kpi AS c ON a.refer_skc_sk = c.skc_sk AND a.channel_type = c.channel_type AND a.org_sk = c.large_region_sk and c.org_sk is null and c.is_include_group_buy = '含团购'
            WHERE a.is_deleted = 0
        )
        ,base_data AS (
            SELECT * FROM  current_quarter_skc
            UNION ALL
            SELECT * FROM refer_skc
        )
        SELECT a.*, b.picture_url as image_url
        FROM base_data as a
        left join dm.dim_skc as b on a.refer_skc_code = b.skc_code
        ORDER BY a.skc_code, priority
    </select>

</mapper>