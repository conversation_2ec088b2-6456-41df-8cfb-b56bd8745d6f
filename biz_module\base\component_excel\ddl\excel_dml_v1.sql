
DELETE FROM tenant_testno2_biz.base_excel_book_conf where id = 1;
INSERT INTO tenant_testno2_biz.base_excel_book_conf
(id, conf_code, conf_name, conf_alias, conf_type, module_code, user_id, file_name, fmt, other, creator, modifier, is_deleted, create_time, modify_time)
VALUES (1, 'order_detail', '区域订单导出', '快反采购订单', 'sys', 'qrs_order', 1, '快反采购订单', '', null, 1, 1, 0, now(), now());

DELETE FROM tenant_testno2_biz.base_excel_sheet_conf WHERE id = 1;
INSERT INTO tenant_testno2_biz.base_excel_sheet_conf (id, book_id, sheet_name, alias_name, freeze_col, freeze_row, contain_size,
                                       query_type, func_code, idx, display, other, creator, modifier, fmt, create_time)
VALUES (1, 1, '订单详情', '订单详情', 'tag_price', '', 1, 'method', 'query_all_order_detail', '1', 1, '{ "row_height": 24, "header_height": 24, "orders": [{ "type": "asc", "cn_name": "尺码组", "en_name": "size_group_code", "has_size_group": false },{ "type": "asc", "cn_name": "中类", "en_name": "mid_class", "has_size_group": false }, { "type": "desc", "cn_name": "快反下单量", "en_name": "qty", "has_size_group": true }, { "type": "asc", "cn_name": "货号", "en_name": "skc_code", "has_size_group": false } ] }', 1, 1, null, now());

DELETE FROM tenant_testno2_biz.base_excel_columns_conf where sheet_id = 1;
INSERT INTO tenant_testno2_biz.base_excel_columns_conf (sheet_id, source, col_code, col_name, col_alias, idx, value_type, value_fmt, editable, display, is_size, width, size_width, size_fold, is_hide, is_import, header_fmt, row_fmt, other, creator, modifier, is_feature, is_deleted)
VALUES

        (1, 'dim_skc', 'img_url', '图片链接', '图片', 1, 'image', '', 0, 1, 0, 8, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'item_no', '款号', '款号', 2, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'skc_code', '货号', '货号', 3, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'gender', '性别', '性别', 4, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'mid_class', '中类', '中类', 5, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'tag_price', '吊牌价', '吊牌价', 6, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'feature', 'kpi_proj_channel_qrs_put_on_date', '快反到店日期', '快反到店日期', 7, 'datetime', 'yyyy-mm-dd', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'pre_delivery_date', '预估交期', '预估交期', 8, 'datetime', 'yyyy-mm-dd', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'tag_proj_qrs_type', '快反类型', '快反类型', 9, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'dim_skc', 'skc_name', '品名', '品名', 10, 'text', '', 0, 0, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'product_year', '年份', '年份', 11, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'product_quarter', '季节', '季节', 12, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'product_range', '系列', '系列', 13, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'big_class', '大类', '大类', 14, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'tiny_class', '小类', '小类', 15, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'band', '波段', '波段', 16, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'long_or_short', '长短', '长短', 17, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'thin_or_thick', '厚薄', '厚薄', 18, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'knitting_tatting', '针梭织', '针梭织', 19, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'fabric', '面料', '面料', 20, 'text', '', 0, 0, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'feature', 'kpi_proj_channel_order_qty', 'sku_渠道累计订货量', '累计订货量', 21, 'number', '', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'kpi_proj_channel_cut_order_qty', 'sku_渠道切单量', '切单量', 22, 'number', '', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'kpi_proj_channel_no_arrival_order_qty', 'sku_渠道未到量', '未到货量', 23, 'number', '', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'kpi_proj_channel_arrive_qty', '到货量', '到货量', 24, 'number', '', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'kpi_proj_channel_total_sale_qty', '销售量', '销售量', 25, 'number', '', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'kpi_proj_channel_sale_out_rate', '售罄率', '售罄率', 26, 'number', '0%', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'kpi_proj_channel_standard_sale_out_rate', '标准售罄', '标准售罄', 27, 'number', '0%', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'feature', 'kpi_proj_channel_sale_out_deviation', '售罄差值', '售罄差值', 28, 'number', '0%', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),

        (1, 'biz', 'match_code', '配码', '配码', 31, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'biz', 'match_qty', '配码量', '箱数', 32, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'dim_skc', 'size_group_code', '尺码组', '尺码组', 33, 'text', '', 0, 1, 0, 16, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'biz', 'qty', '快反订量', '快反订量', 34, 'number', '#,##0', 0, 1, 1, 12, 8, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'biz', 'pre_warning', '预警信息', '预警信息', 45, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'biz', 'opinions', '审批意见', '审批意见', 46, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),

        (1, 'feature', 'decision_flag', '圈款来源', '圈款来源', 47, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (1, 'biz', 'status', '行状态 ', '行状态 ', 48, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (1, 'biz', 'remark', '备注', '备注', 49, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0)
;

DELETE FROM tenant_testno2_biz.base_excel_book_conf where id = 3;
INSERT INTO tenant_testno2_biz.base_excel_book_conf
    (id, conf_code, conf_name, conf_alias, conf_type, module_code, user_id, file_name, fmt, other, creator, modifier, create_time)
VALUES
    (3, 'affiliate_order_detail', '加盟订单导出', '快反采购订单', 'sys', 'qrs_order', 1, '快反采购订单', '', null, 1, 1, now());


DELETE FROM tenant_testno2_biz.base_excel_sheet_conf WHERE id = 3;
INSERT INTO tenant_testno2_biz.base_excel_sheet_conf
    (id, book_id, sheet_name, alias_name, freeze_col, freeze_row, contain_size, query_type, func_code, idx, display, other, creator, modifier, fmt, create_time)
VALUES
    (3, 3, '订单详情', '订单详情', 'tag_price', '', 1, '', '', '1', 1, '{ "row_height": 24,"header_height": 24, "orders": [{ "type": "asc", "cn_name": "尺码组", "en_name": "size_group_code", "has_size_group": false },{ "type": "asc", "cn_name": "中类", "en_name": "mid_class", "has_size_group": false }, { "type": "desc", "cn_name": "快反下单量", "en_name": "qty", "has_size_group": true }, { "type": "asc", "cn_name": "货号", "en_name": "skc_code", "has_size_group": false } ] }', 1, 1, null, now());


DELETE FROM tenant_testno2_biz.base_excel_columns_conf WHERE sheet_id = 3;
INSERT INTO tenant_testno2_biz.base_excel_columns_conf
    (sheet_id, source,col_code, col_name, col_alias, idx, value_type, value_fmt, editable, display, is_size, width, size_width, size_fold, is_hide, is_import, header_fmt, row_fmt, other, creator, modifier, is_feature, is_deleted)
VALUES

        (3, 'dim_skc', 'img_url', '图片链接', '图片', 1, 'image', '', 0, 1, 0, 8, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'item_no', '款号', '款号', 2, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'skc_code', '货号', '货号', 3, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'gender', '性别', '性别', 4, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'mid_class', '中类', '中类', 5, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'tag_price', '吊牌价', '吊牌价', 6, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'feature', 'kpi_proj_channel_qrs_put_on_date', '快反到店日期', '快反到店日期', 7, 'datetime', 'yyyy-mm-dd', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'feature', 'pre_delivery_date', '预估交期', '预估交期', 8, 'datetime', 'yyyy-mm-dd', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'feature', 'tag_proj_qrs_type', '快反类型', '快反类型', 9, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'dim_skc', 'skc_name', '品名', '品名', 10, 'text', '', 0, 0, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'product_year', '年份', '年份', 11, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'product_quarter', '季节', '季节', 12, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'product_range', '系列', '系列', 13, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'big_class', '大类', '大类', 14, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'tiny_class', '小类', '小类', 15, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'band', '波段', '波段', 16, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'long_or_short', '长短', '长短', 17, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'thin_or_thick', '厚薄', '厚薄', 18, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'knitting_tatting', '针梭织', '针梭织', 19, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'fabric', '面料', '面料', 20, 'text', '', 0, 0, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'feature', 'kpi_proj_franchisee_arrive_qty', '到货量', '到货量', 21, 'number', '', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'feature', 'kpi_proj_franchisee_total_sale_qty', '销售量', '销售量', 22, 'number', '', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'feature', 'kpi_proj_franchisee_sale_out_rate', '售罄率', '售罄率', 23, 'number', '0%', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'feature', 'kpi_proj_franchisee_standard_sale_out_rate', '标准售罄', '标准售罄', 24, 'number', '0%', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'feature', 'kpi_proj_franchisee_sale_out_deviation', '售罄差值', '售罄差值', 25, 'number', '0%', 0, 1, 0, 10, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'biz', 'match_code', '配码', '配码', 31, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'biz', 'match_qty', '配码量', '箱数', 32, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'dim_skc', 'size_group_code', '尺码组', '尺码组', 33, 'text', '', 0, 1, 0, 16, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'biz', 'qty', '快反订量', '快反订量', 34, 'number', '#,##0', 0, 1, 1, 12, 8, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (3, 'feature', 'decision_flag', '圈款来源', '圈款来源', 35, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (3, 'biz', 'remark', '备注', '备注', 36, 'text', '', 0, 1, 0, 20, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0)
;

DELETE FROM tenant_testno2_biz.base_excel_book_conf where id = 6;
INSERT INTO tenant_testno2_biz.base_excel_book_conf
    (id, conf_code, conf_name, conf_alias, conf_type, module_code, user_id, file_name, fmt, other, creator, modifier, create_time)
values
    (6, 'multi_order_detail', '批量订单导出', '批量订单详情导出', 'sys', 'qrs_order', 1, '快反采购订单-批量', '', null, 1, 1, now());



DELETE FROM tenant_testno2_biz.base_excel_sheet_conf WHERE  id = 6 AND book_id = 6;
INSERT INTO tenant_testno2_biz.base_excel_sheet_conf
    (id, book_id, sheet_name, alias_name, freeze_col, freeze_row, contain_size, query_type, func_code, idx, display, other, creator, modifier, fmt, create_time)
VALUES
    (6, 6, '订单详情', '订单详情', 'skc_code', '', 1, '', '', '1', 1, '{ "row_height": 24, "header_height": 24, "orders": [ { "type": "asc", "cn_name": "订单组织", "en_name": "org_sk", "has_size_group": false }, { "type": "asc", "cn_name": "大类", "en_name": "product_line", "has_size_group": false }, { "type": "asc", "cn_name": "中类", "en_name": "mid_class", "has_size_group": false }, { "type": "asc", "cn_name": "快反类型", "en_name": "tag_proj_qrs_type", "has_size_group": false }, { "type": "desc", "cn_name": "快反订量", "en_name": "qty_sum", "has_size_group": false }, { "type": "asc", "cn_name": "货号", "en_name": "skc_code", "has_size_group": false } ] }', 1, 1, null, now());




DELETE FROM tenant_testno2_biz.base_excel_columns_conf WHERE sheet_id = 6;
INSERT INTO tenant_testno2_biz.base_excel_columns_conf
    (sheet_id, source, col_code, col_name, col_alias, idx, value_type, value_fmt, editable, display, is_size, width, size_width, size_fold, is_hide, is_import, header_fmt, row_fmt, other, creator, modifier, is_feature, is_deleted)
VALUES
        (6, 'biz', 'parent_org_sk', '订单组织sk', '订单组织sk', 3, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'parent_org_name', '分司', '分司', 4, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'org_sk', '订单组织sk', '订单组织sk', 5, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'org_name', '订单组织', '订单组织', 6, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),

        (6, 'dim_skc', 'item_no', '款号', '款号', 7, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'skc_code', '货号', '货号', 8, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'skc_name', '品名', '品名', 9, 'text', '', 0, 0, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (6, 'dim_skc', 'big_class', '大类', '大类', 14, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'mid_class', '中类', '中类', 15, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'tag_price', '吊牌价', '吊牌价', 16, 'text', '', 0, 1, 0, 12, null, 0, 1, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'feature', 'kpi_proj_channel_qrs_put_on_date', '快反到店日期', '快反到店日期', 17, 'datetime', 'yyyy-mm-dd', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (6, 'feature', 'pre_delivery_date', '预估交期', '预估交期', 18, 'datetime', 'yyyy-mm-dd', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'feature', 'tag_proj_qrs_type', '快反类型', '快反类型', 19, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'gender', '性别', '性别', 20, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'product_year', '年份', '年份', 30, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'product_quarter', '季节', '季节', 31, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'product_range', '系列', '系列', 32, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'tiny_class', '小类', '小类', 33, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'band', '波段', '波段', 34, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'price_band', '价位带', '价位带', 36, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'long_or_short', '长短', '长短', 37, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'thin_or_thick', '厚薄', '厚薄', 38, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'knitting_tatting', '针梭织', '针梭织', 39, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'fabric', '面料', '面料', 50, 'text', '', 0, 0, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'match_code', '配码', '配码', 51, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'match_qty', '配码量', '箱数', 52, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),

        (6, 'feature', 'model_suggest_qty', '建议订量', '建议订量', 60, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'model_suggest_amt', '建议金额', '建议金额', 61, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'qty_sum', '快反订量', '快反订量', 62, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'qty_sum_amt', '快反金额', '快反金额', 63, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'pre_warning', '预警信息', '预警信息', 65, 'text', '', 0, 1, 0, 30, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'opinions', '审批意见', '审批意见', 66, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'dim_skc', 'size_group_code', '尺码组', '尺码组', 67, 'text', '', 0, 1, 0, 16, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'qty', '快反订量（尺码）', '快反订量（尺码）', 68, 'number', '#,##0', 0, 1, 1, 12, 8, 1, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),

        (6, 'biz', 'remark', '备注', '备注', 80, 'text', '', 0, 1, 0, 20, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'feature', 'decision_flag', '圈款来源', '圈款来源', 81, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (6, 'biz', 'status', '行状态 ', '行状态 ', 82, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'order_status', '订单状态', '订单状态 ', 83, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (6, 'biz', 'window_period_content', '窗口期', '窗口期', 84, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0)
;



DELETE FROM tenant_testno2_biz.base_excel_book_conf where id = 21;
INSERT INTO tenant_testno2_biz.base_excel_book_conf
    (id, conf_code, conf_name, conf_alias, conf_type, module_code, user_id, file_name, fmt, other, creator, modifier, create_time)
values
    (21, 'multi_affiliate_detail', '批量订单导出', '批量订单详情导出', 'sys', 'qrs_order', 1, '快反加盟订单-批量', '', null, 1, 1, now());



DELETE FROM tenant_testno2_biz.base_excel_sheet_conf WHERE  id = 21 AND book_id = 21;
INSERT INTO tenant_testno2_biz.base_excel_sheet_conf
    (id, book_id, sheet_name, alias_name, freeze_col, freeze_row, contain_size, query_type, func_code, idx, display, other, creator, modifier, fmt, create_time)
VALUES
    (21, 21, '订单详情', '订单详情', 'skc_code', '', 1, '', '', '1', 1, '{ "row_height": 24, "header_height": 24, "orders": [ { "type": "asc", "cn_name": "订单组织", "en_name": "org_sk", "has_size_group": false }, { "type": "asc", "cn_name": "大类", "en_name": "product_line", "has_size_group": false }, { "type": "asc", "cn_name": "中类", "en_name": "mid_class", "has_size_group": false }, { "type": "asc", "cn_name": "快反类型", "en_name": "tag_proj_qrs_type", "has_size_group": false }, { "type": "desc", "cn_name": "快反订量", "en_name": "qty_sum", "has_size_group": false }, { "type": "asc", "cn_name": "货号", "en_name": "skc_code", "has_size_group": false } ] }', 1, 1, null, now());


DELETE FROM tenant_testno2_biz.base_excel_columns_conf WHERE sheet_id = 21;
INSERT INTO tenant_testno2_biz.base_excel_columns_conf
    (sheet_id, source, col_code, col_name, col_alias, idx, value_type, value_fmt, editable, display, is_size, width, size_width, size_fold, is_hide, is_import, header_fmt, row_fmt, other, creator, modifier, is_feature, is_deleted)
VALUES
        (21, 'biz', 'parent_org_sk', '订单组织sk', '订单组织sk', 3, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'parent_org_name', '分司', '分司', 4, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'org_sk', '订单组织sk', '订单组织sk', 5, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'org_name', '订单组织', '订单组织', 6, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),

        (21, 'dim_skc', 'item_no', '款号', '款号', 7, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'skc_code', '货号', '货号', 8, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'skc_name', '品名', '品名', 9, 'text', '', 0, 0, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'big_class', '大类', '大类', 14, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'mid_class', '中类', '中类', 15, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'tag_price', '吊牌价', '吊牌价', 16, 'text', '', 0, 1, 0, 12, null, 0, 1, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'feature', 'kpi_proj_franchisee_qrs_put_on_date', '快反到店日期', '快反到店日期', 17, 'datetime', 'yyyy-mm-dd', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (21, 'feature', 'pre_delivery_date', '预估交期', '预估交期', 18, 'datetime', 'yyyy-mm-dd', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (21, 'feature', 'tag_proj_qrs_type', '快反类型', '快反类型', 19, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (21, 'dim_skc', 'gender', '性别', '性别', 20, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'product_year', '年份', '年份', 30, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'product_quarter', '季节', '季节', 31, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'product_range', '系列', '系列', 32, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'tiny_class', '小类', '小类', 33, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'band', '波段', '波段', 34, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'price_band', '价位带', '价位带', 36, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'long_or_short', '长短', '长短', 37, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'thin_or_thick', '厚薄', '厚薄', 38, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'knitting_tatting', '针梭织', '针梭织', 39, 'text', '', 0, 0, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'fabric', '面料', '面料', 50, 'text', '', 0, 0, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'match_code', '配码', '配码', 51, 'text', '', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'match_qty', '配码量', '箱数', 52, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),

        (21, 'feature', 'model_suggest_qty', '建议订量', '建议订量', 60, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (21, 'biz', 'model_suggest_amt', '建议金额', '建议金额', 61, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'qty_sum', '快反订量', '快反订量', 62, 'number', '#,##0', 0, 1, 0, 12, null, 1, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'qty_sum_amt', '快反金额', '快反金额', 63, 'number', '#,##0', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'dim_skc', 'size_group_code', '尺码组', '尺码组', 67, 'text', '', 0, 1, 0, 16, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'qty', '快反订量（尺码）', '快反订量（尺码）', 68, 'number', '#,##0', 0, 1, 1, 12, 8, 1, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),

        (21, 'biz', 'remark', '备注', '备注', 80, 'text', '', 0, 1, 0, 20, null, 0, 0, 1, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'feature', 'decision_flag', '圈款来源', '圈款来源', 81, 'text', '', 0, 1, 0, 12, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 1, 0),
        (21, 'biz', 'status', '行状态 ', '行状态 ', 82, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'order_status', '订单状态', '订单状态 ', 83, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0),
        (21, 'biz', 'window_period_content', '窗口期', '窗口期', 84, 'text', '', 0, 1, 0, 20, null, 0, 0, 0, '{}', '{}', '{}', 1, 1, 0, 0)
;
