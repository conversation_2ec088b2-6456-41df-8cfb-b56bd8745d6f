from biz.common.lz_db import models
from biz.common.lz_db import db


class APILogModel(models.Base):
    __tablename__ = 'api_log'

    id = db.Column(db.String(length=64), nullable=False, primary_key=True)
    ip = db.Column(db.String(length=64), nullable=True)
    ip_type = db.Column(db.String(length=64), nullable=True)
    username = db.Column(db.String(length=64), nullable=True)
    path = db.Column(db.Text(), nullable=True)
    params = db.Column(db.Text(), nullable=True)
    time_start = db.Column(db.TIMESTAMP(), nullable=True)
    time_end = db.Column(db.TIMESTAMP(), nullable=True)
    return_mimetype = db.Column(db.String(length=64), nullable=True)
    is_json = db.Column(db.String(length=1), nullable=True)
    return_message = db.Column(db.Text(), nullable=True)
    code_return = db.Column(db.String(length=64), nullable=True)
    except_code = db.Column(db.String(length=3), nullable=True)
    except_args = db.Column(db.Text(), nullable=True)
