#! /usr/bin/python


class CheckPD(object):

    def __init__(self, password):
        self.password = password

    def len(self):
        if len(self.password) >= 8:
            return True
        else:
            print("The password should be at least 8 characters.")
            return False

    def symbol(self):
        match = self.password.isalnum()
        if match:
            return True
        else:
            print("The passwords should not include special characters.")
            return False

    def query_password(self):
        check = self.len() and self.symbol()
        if check:
            print("The password is legal.")
            return True
        else:
            print("The password is not legal.")
            return False


if __name__ == '__main__':
    pass
    # pd = CheckPD('1234567@@')
    # pd.check_password()
