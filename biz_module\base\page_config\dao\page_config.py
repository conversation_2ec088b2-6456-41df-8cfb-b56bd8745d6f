from ourbatis import sql_func
from biz_module.base.authorization.base.tools import auth_param_extend
from biz_module.base.page_config.typedef import page_config as tf_conf
from typing import List


@sql_func(decorator_func=auth_param_extend)
class PageConfigDao:
    """
    流通指令配置
    """
    def get_page_conf_by_id(self, params: tf_conf.GetConfigByIDReq) \
            -> tf_conf.ConfigEgg:
        ...

    def get_page_conf_list(self, params: tf_conf.GetConfigListReq) \
            -> List[tf_conf.ConfigEgg]:
        ...

    def get_ra_decision_dim_values(self):
        ...

    def save_config(self, params: tf_conf.SaveReq) -> int:
        ...

    def save_for_config(self, params: tf_conf.SaveForReq) -> int:
        ...

    def delete_conf(self, params: tf_conf.SaveReq):
        ...

    def check_sys_conf(self, params: tf_conf.CheckSysConf) -> tf_conf.ConfigEgg:
        ...

    def check_rep_name(self, params: tf_conf.SaveForReq) -> int:
        ...

    def get_feature_name_by_code(self, feature_code: str) -> str:
        ...

    def get_feature_config_template(self, params: tf_conf.FeatureToConfigReq) -> dict:
        ...
