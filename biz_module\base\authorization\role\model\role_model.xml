<?xml version="1.0"?>
<mapper namespace="default">
    <!-- 查询 -->
    <select id="list_role_with_page">
            with role_list as (
                select role_id, role_name, is_active, remark
                from sys.role
                where is_deleted = 0
                <if test="is_active is not None">
                    and is_active is #{is_active}
                </if>
                <if test="query_like">
                    and (role_name like #{query_like} or remark like #{query_like})
                </if>
            ) ,role_users as (
                select
                   rl.role_id,
                   rl.role_name,
                   rl.is_active,
                   coalesce(rl.remark, '') as remark,
                   case when count(ur.user_id) = 0 then False else True end as is_used
            from role_list rl
                     left join sys.user_role ur on ur.role_id = rl.role_id
            group by rl.role_id, role_name, is_active, remark
                )
            select
                   row_number() over (order by role_id desc) as row_id,
                   role_id,
                   role_name,
                   is_active,
                   remark,
                   is_used
            from role_users
            limit #{page_size} offset #{offset}
    </select>
    <!--获取当前所有启用的角色 不分页 -->
    <select id="list_role">
        select role_id, role_name, is_active
        from sys.role
        where is_deleted = 0
        and is_active is True
        order by role_id desc
    </select>

    <!-- 获取总记录数 -->
    <select id="role_total_row">
        select count(*) from sys.role
        where is_deleted = 0
         <if test="is_active is not None">
             and is_active is #{is_active}
         </if>
        <if test="query_like">
            and (role_name like #{query_like} or remark like #{query_like})
        </if>
    </select>

    <!-- 根据id查询角色是否存在 -->
    <select id="role_find_id_exists">
        select exists(select 1 from sys.role where role_id=#{role_id} and is_deleted = 0 )
    </select>

    <!-- 更新 -->
    <update id="update_role_info">
        update sys.role
        <set>
            <if test="role_name">role_name=#{role_name},</if>
            <if test="remark is not None">remark=#{remark},</if>
            modify_time=now()
        </set>
        where role_id = #{role_id}
    </update>

    <update id="update_role_is_active_status">
        update sys.role
        set is_active = #{is_active},
        modify_time=now()
        where role_id = #{role_id}
    </update>

    <!-- 软删除 -->
    <update id="remove_role">
        update sys.role
        set is_deleted = 1,
        modify_time = now()
        where role_id = #{role_id}
    </update>
    <!-- 根据role_id获取用户 -->
    <select id="find_role_users">
         select u.user_id
            from sys.user_role ur
            inner join sys.user u
            on ur.user_id = u.user_id
            and u.is_delete is False
        where role_id = #{role_id}
    </select>

    <!-- 根据user_id获取角色 -->
    <select id="find_user_role">
         select ur.role_id
            from sys.user_role ur
            inner join sys.role r
            on ur.role_id = r.role_id
            where user_id = #{user_id}
            and r.is_deleted = 0
            and r.is_active is True
            order by r.role_id asc
    </select>

    <select id="find_user_role_with_page_permission">
        select pp.role_id
        from sys.user_role ur
        inner join sys.role_page_permission pp
        on ur.role_id = pp.role_id and ur.user_id  = #{user_id}
        group by pp.role_id
        order by  pp.role_id
    </select>

    <select id="find_role_with_page_permission">
        select distinct page_id
        from sys.role_page_permission
        where role_id = #{role_id}
    </select>

    <!-- 根据role_id和user_id获取关系 -->
    <select id="find_role_user_exists">
        select exists(
            select 1 from sys.user_role where role_id = #{role_id} and user_id=#{user_id})
    </select>

    <!-- 插入 -->
    <insert id="save_role">
        insert into sys.role(role_name,remark,is_active,create_time)
        values(#{role_name},#{remark},#{is_active},now()) RETURNING role_id;
    </insert>

    <!-- 根据role_name查询用户 -->
    <select id="find_by_role_name">
        select role_id from sys.role
        where role_name=#{role_name} and is_deleted = 0
        <if test="except_role_id"> and role_id != #{except_role_id}</if>
        limit 1;
    </select>
    <select id="find_by_role_id">
        select role_id, role_name from sys.role
        where role_id= #{role_id}
        and is_deleted = 0
        and is_active is True
        limit 1
    </select>

    <select id="find_by_role_ids">
        select role_id, role_name from sys.role
        where role_id in ${role_ids}
        and is_deleted = 0
        and is_active is True
    </select>

    <!-- 删除用户角色关系 -->
    <delete id="remove_role_users">
        delete from sys.user_role
        where role_id = #{role_id}
        <if test="user_ids">
            and user_id in ${user_ids}
        </if>
        RETURNING  user_id;
    </delete>

        <!-- 插入用户角色关系 暂不支持批量插入-->
    <insert id="insert_role_user">
        insert into sys.user_role(user_id,role_id) values(#{user_id},#{role_id})RETURNING *;
    </insert>

    <!-- 获取用户功能权限-->
    <select id="list_role_action">
        with action_list as (
            select  id,
                    action_code,
                    action_name,
                    parent_action_code
            from biz.base_auth_action
            where is_deleted = 0
        ),auth_action as (
             select  id,
                    role_id,
                    action_id,
                    2 as operation_type
            from biz.base_auth_action_mapping
            where is_deleted = 0
            and role_id = #{role_id}
        )
        select al.id,
        al.action_code,
        al.action_name,
        al.parent_action_code,
        coalesce(auth.operation_type,0) as operation_type
        from action_list al
        left join auth_action auth
        on al.id = auth.action_id
        order by convert_to(action_name,'GBK');
    </select>
    <!-- UPSERT插入用户功能权限
    <insert id="save_role_biz_action">
        with upsert as (
            update biz.base_auth_action_mapping
            set modify_time = now()
            where is_deleted = 0
            and role_id = #{role_id}
            and action_id = #{action_id}
            returning *
        )
        insert into biz.base_auth_action_mapping(role_id,action_id,create_time,creator_id)
        select #{role_id},#{action_id},now(),#{creator_id}
        where not exists( select 1 from upsert where role_id = #{role_id} and action_id = #{action_id})
    </insert>
    -->

    <insert id="save_role_biz_action">
        INSERT INTO biz.base_auth_action_mapping(
            role_id,
            action_id,
            create_time,
            creator_id)
        VALUES(
            #{role_id},
            #{action_id},
            now(),
            #{creator_id}
            )returning id;
    </insert>

    <select id="is_role_take_by_auth_record">
         select exists(select 1 from biz.base_auth_record where role_id=#{role_id} and is_deleted = 0 )
    </select>

    <update id="remove_role_biz_action">
        update biz.base_auth_action_mapping
        set is_deleted = 1,
        modify_time = now(),
        modifier_id = #{modifier_id}
        where role_id = #{role_id}
    </update>

    <select id="find_action_by_ids">
        select  id,
                action_code,
                action_name,
                parent_action_code
        from biz.base_auth_action
        where is_deleted = 0
        and id in ${action_ids}
    </select>
    <delete id="remove_role_user">
        delete from sys.user_role where role_id = #{role_id} and user_id = #{user_id}
    </delete>
</mapper>
