import json
import logging
from datetime import datetime
from functools import wraps
from pprint import pprint
import threading
import traceback

from flask import current_app, g, request
from flask.signals import request_started, request_finished
from flask_login import current_user
from oslo_utils import uuidutils

from biz.utils.client_ip import get_client_ip
from biz.common.lz_db import db
from biz.common.lz_api_log.models import APILogModel

log = logging.getLogger()


def _get_username():
    if current_user.is_authenticated:
        return current_user.username
    else:
        return ''


def _parse_config(upper_k, upper_d, lower_k, lower_d, e_type, default, v_range=None):
    v = upper_d.get(upper_k, None)
    if v is None:
        v = lower_d.get(lower_k, default)
        if v is not None and not isinstance(v, e_type):
            raise RuntimeError(
                '\nThe type of config variable is WRONG !\n'
                'Type expected: {}\n'
                'Config variable = {} [type: {}]\n'
                ''.format(e_type, v, type(v))
            )
    if v_range is not None and v not in v_range:
        raise RuntimeError(
            '\nThe config variable is not support !\n'
            'Value expected: {}\n'
            'Config variable = {}\n'
            ''.format(v_range, v)
        )
    return v


def _performance(f):
    @wraps(f)
    def wrapper(self, *args, **kwargs):
        path = request.path
        if path in self.skip_paths:
            return
        else:
            try:
                rv = f(self, *args, **kwargs)
            except Exception as e:
                if self.error_level == 'ignore':
                    pass
                elif self.error_level == 'log':
                    log.error(e)
                elif self.error_level == 'raise':
                    raise e
            else:
                return rv

    return wrapper


class APILog(object):
    """
    TODO: doc
    """

    trigger = None

    skip_paths = None

    error_level = None
    error_levels = ('ignore', 'log', 'raise')

    store_level = None
    store_levels = ('print', 'log', 'database')

    store_method = None
    store_methods = ('thread', 'publish')

    g_key = None

    top_conf = None

    def __init__(self, app=None, top_conf=None):
        self.app = app
        self.top_conf = top_conf if top_conf else {}
        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        # used to be grabbed by sqlalchemy
        a = APILogModel(id=1)
        del a

        self.trigger = _parse_config(
            upper_k='trigger',
            upper_d=self.top_conf,
            lower_k='LZ_API_LOG_TRIGGER',
            lower_d=app.config,
            e_type=bool,
            default=False
        )
        if not self.trigger:
            return

        self.g_key = uuidutils.generate_uuid()

        self.skip_paths = _parse_config(
            upper_k='skip_paths',
            upper_d=self.top_conf,
            lower_k='LZ_API_LOG_SKIP_PATHS',
            lower_d=app.config,
            e_type=list,
            default=[]
        )
        self.error_level = _parse_config(
            upper_k='error_level',
            upper_d=self.top_conf,
            lower_k='LZ_API_LOG_ERROR_LEVEL',
            lower_d=app.config,
            e_type=str,
            default='log',
            v_range=self.error_levels
        )
        self.store_level = _parse_config(
            upper_k='store_level',
            upper_d=self.top_conf,
            lower_k='LZ_API_LOG_STORE_LEVEL',
            lower_d=app.config,
            e_type=str,
            default='print',
            v_range=self.store_levels
        )
        self.store_method = _parse_config(
            upper_k='store_method',
            upper_d=self.top_conf,
            lower_k='LZ_API_LOG_STORE_METHOD',
            lower_d=app.config,
            e_type=str,
            default='thread',
            v_range=self.store_methods
        )

        request_started.connect(self._request_started, app)
        request_finished.connect(self._request_finished, app)
        self._decorate_error_handler(app)

    @_performance
    def _request_started(self, sender, **extra):
        payload = self._get_payload()

        payload['ip'], payload['ip_type'] = get_client_ip()
        payload['username'] = _get_username()
        payload['path'] = request.path

        params = request.values.to_dict()
        payload['params'] = json.dumps(params) if params else ''

        payload['time_start'] = datetime.now()

    @_performance
    def _request_finished(self, sender, response, **extra):
        payload = self._get_payload()
        payload['time_end'] = datetime.now()
        payload['return_mimetype'] = response.mimetype

        if not response.is_json:
            payload['is_json'] = 'N'
            payload['return_message'] = ''
            payload['code_return'] = ''
        else:
            payload['is_json'] = 'Y'

            _json = response.get_json()
            if _json:
                payload['return_message'] = _json.get('msg', '')
                payload['code_return'] = hex(_json.get('code', 0))

        self._handle_data(payload, mod=self.store_level)

    @_performance
    def _extra_error_function(self, e):
        payload = self._get_payload()
        payload['except_code'] = str(getattr(e, 'code', ''))
        tmp = getattr(e, 'args', '')
        args = []
        for arg in tmp:
            if arg is not None:
                args.append(arg)
        payload['except_args'] = '; \n'.join(args)

    def _get_payload(self):
        if not hasattr(g, self.g_key):
            payload = dict()
            payload['id'] = uuidutils.generate_uuid()
            setattr(g, self.g_key, payload)
        return getattr(g, self.g_key)

    def _handle_data(self, payload, mod=None):
        handler = getattr(self, '_{}'.format(mod))
        handler(payload)

    @staticmethod
    def _print(payload):
        pprint(payload)

    @staticmethod
    def _log(payload):
        log.info(payload)

    def _database(self, payload):
        self._handle_data(payload, mod=self.store_method)

    def _thread(self, payload):
        app = current_app._get_current_object()
        t = threading.Thread(target=self._model_add_commit, args=[app, payload])
        t.start()

    @staticmethod
    def _model_add_commit(app, payload):
        log.info(
            'API Log [id: {}] is inserting to database with ``thread`` method'
            ''.format(payload['id'])
        )
        try:
            with app.app_context():
                api_log = APILogModel(**payload)
                db.session.add(api_log)
                db.session.commit()
        except Exception:
            log.error(traceback.format_exc())
        else:
            log.info(
                'API Log [id: {}] is inserted successfully'
                ''.format(payload['id'])
            )

    @staticmethod
    def _publish(payload):
        # TODO: future feature
        pass

    def _decorate_error_handler(self, app):
        exc_class, code = app._get_exc_class_and_code(Exception)
        handlers = app.error_handler_spec \
            .setdefault(None, {}) \
            .setdefault(code, {})
        f = handlers.get(exc_class, None)
        if f is None:
            raise RuntimeError(
                '\nError happens ! Make sure it is as followings:\n'
                '   app = Flask(__name__)\n'
                '   api_log = APILog()\n'
                '   app.errorhandler(Exception)(your_handler) # Necessary !\n'
                '   api_log.init_app(app)                     # The order matters!\n'
            )
        handlers[exc_class] = self._error_handler_wrapper(f)

    def _error_handler_wrapper(self, f):
        @wraps(f)
        def wrapper(e, *args, **kwargs):
            self._extra_error_function(e)
            rv = f(e, *args, **kwargs)
            return rv

        return wrapper
