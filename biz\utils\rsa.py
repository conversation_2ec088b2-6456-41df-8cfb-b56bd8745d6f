from Crypto.Cipher import PKCS1_v1_5 as Cipher_pkcs1_v1_5
from Crypto.PublicKey import RSA

import base64


def get_key_pair():
    # random_generator = Random.new().read
    rsa = RSA.generate(1024)

    private_key = str(rsa.exportKey(), "utf8")
    public_key = str(rsa.publickey().exportKey(), "utf8")
    return private_key, public_key


def encrypt(public_key, message, encoding="utf8"):
    message_b = bytes(message, encoding=encoding)
    rsakey = RSA.importKey(public_key)
    cipher = Cipher_pkcs1_v1_5.new(rsakey)
    cipher_text = base64.b64encode(cipher.encrypt(message_b))
    cipher_text = str(cipher_text, encoding=encoding)
    return cipher_text


def decrypt(private_key, encrypt_text, encoding="utf8"):
    # random_generator = Random.new().read
    rsakey = RSA.importKey(private_key)
    cipher = Cipher_pkcs1_v1_5.new(rsakey)
    encrypt_bin = bytes(encrypt_text, encoding=encoding)
    text_b = cipher.decrypt(base64.b64decode(encrypt_bin), None)
    text = str(text_b, encoding=encoding)
    return text
