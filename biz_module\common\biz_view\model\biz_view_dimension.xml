<?xml version="1.0"?>
<mapper namespace="biz_view_dimension">
    <!-- 查询 -->
    <select id="list_by_biz_view_id">
        with dimension_info as (
            select dimension_id,
            biz_view_id,
            unnest(table_field_code) as table_field_code
            from biz.base_biz_view_dimension
            where biz_view_id = #{biz_view_id}
            and is_deleted = 0
        )
        select dimension_id, title as dimension_title, biz_view_id
        from dimension_info va
        inner join biz.base_meta_data_table_field tf
        on va.table_field_code = tf.code
    </select>

    <select id="find_biz_view_dimension_by_code">
        select
        dimension_id,
        dimension_code,
        meta_data_dimension_code,
        biz_object_attribute_code,
        table_field_code,
        group_by
        from biz.base_biz_view_dimension
        where dimension_code = #{dimension_code}
        and biz_view_id = #{biz_view_id}
        and is_deleted = 0
    </select>

    <insert id="save_dimension">
        INSERT INTO
            biz.base_biz_view_dimension (
            dimension_code,
            biz_view_id,
            meta_data_dimension_code,
            biz_object_attribute_code,
            table_field_code,
            group_by,
            raw_dimension,
            create_time,
            creator
            )
        VALUES (
            #{dimension_code},
            #{biz_view_id},
            #{meta_data_dimension_code},
            #{biz_object_attribute_code},
            #{table_field_code},
            #{group_by},
            #{raw_dimension},
            now(),
            #{creator}
        )
        RETURNING dimension_id;
    </insert>

    <update id="update_dimension">
       UPDATE biz.base_biz_view_dimension
        SET
        biz_object_attribute_code = #{biz_object_attribute_code},
        table_field_code = #{table_field_code},
        meta_data_dimension_code = #{meta_data_dimension_code},
        group_by = #{group_by},
        raw_dimension = #{raw_dimension},
        modifier = #{modifier},
        modify_time = now()
        where dimension_id = #{dimension_id}
    </update>

    <select id="find_view_dimension_by_id">
        select * from biz.base_biz_view_dimension
        where dimension_id = ${dimension_id}
            and is_deleted = 0
    </select>
</mapper>
