# Generated from biz_module\common\expression\Expression.g4 by ANTLR 4.9.3
# encoding: utf-8
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
	from typing import TextIO
else:
	from typing.io import TextIO


def serializedATN():
    with <PERSON><PERSON>() as buf:
        buf.write("\3\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964\3 ")
        buf.write("\u0081\4\2\t\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7\t\7")
        buf.write("\4\b\t\b\3\2\3\2\5\2\23\n\2\3\2\3\2\3\3\3\3\3\3\3\3\3")
        buf.write("\4\3\4\3\4\3\5\3\5\3\5\3\6\3\6\3\6\3\6\3\7\3\7\7\7\'\n")
        buf.write("\7\f\7\16\7*\13\7\3\7\5\7-\n\7\3\b\3\b\3\b\3\b\3\b\3\b")
        buf.write("\7\b\65\n\b\f\b\16\b8\13\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b")
        buf.write("\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\5\bI\n\b\3\b\3\b\3\b")
        buf.write("\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3")
        buf.write("\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b")
        buf.write("\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\3\b\7\bu")
        buf.write("\n\b\f\b\16\bx\13\b\3\b\3\b\7\b|\n\b\f\b\16\b\177\13\b")
        buf.write("\3\b\3\66\3\16\t\2\4\6\b\n\f\16\2\3\3\2\r\16\2\u0093\2")
        buf.write("\22\3\2\2\2\4\26\3\2\2\2\6\32\3\2\2\2\b\35\3\2\2\2\n ")
        buf.write("\3\2\2\2\f$\3\2\2\2\16H\3\2\2\2\20\23\5\16\b\2\21\23\5")
        buf.write("\f\7\2\22\20\3\2\2\2\22\21\3\2\2\2\23\24\3\2\2\2\24\25")
        buf.write("\7\3\2\2\25\3\3\2\2\2\26\27\5\16\b\2\27\30\7\4\2\2\30")
        buf.write("\31\5\16\b\2\31\5\3\2\2\2\32\33\7\25\2\2\33\34\5\4\3\2")
        buf.write("\34\7\3\2\2\2\35\36\7\26\2\2\36\37\5\4\3\2\37\t\3\2\2")
        buf.write("\2 !\7\27\2\2!\"\7\4\2\2\"#\5\16\b\2#\13\3\2\2\2$(\5\6")
        buf.write("\4\2%\'\5\b\5\2&%\3\2\2\2\'*\3\2\2\2(&\3\2\2\2()\3\2\2")
        buf.write("\2),\3\2\2\2*(\3\2\2\2+-\5\n\6\2,+\3\2\2\2,-\3\2\2\2-")
        buf.write("\r\3\2\2\2./\b\b\1\2/\60\7\24\2\2\60\61\7\5\2\2\61\66")
        buf.write("\5\16\b\2\62\63\7\6\2\2\63\65\5\16\b\2\64\62\3\2\2\2\65")
        buf.write("8\3\2\2\2\66\67\3\2\2\2\66\64\3\2\2\2\679\3\2\2\28\66")
        buf.write("\3\2\2\29:\7\7\2\2:I\3\2\2\2;<\7\b\2\2<I\5\16\b\27=>\7")
        buf.write("\31\2\2>I\5\16\b\n?I\7\34\2\2@I\7\33\2\2AI\7\35\2\2BI")
        buf.write("\7\36\2\2CI\7\37\2\2DE\7\5\2\2EF\5\16\b\2FG\7\7\2\2GI")
        buf.write("\3\2\2\2H.\3\2\2\2H;\3\2\2\2H=\3\2\2\2H?\3\2\2\2H@\3\2")
        buf.write("\2\2HA\3\2\2\2HB\3\2\2\2HC\3\2\2\2HD\3\2\2\2I}\3\2\2\2")
        buf.write("JK\f\26\2\2KL\7\t\2\2L|\5\16\b\27MN\f\25\2\2NO\7\n\2\2")
        buf.write("O|\5\16\b\26PQ\f\24\2\2QR\7\13\2\2R|\5\16\b\25ST\f\23")
        buf.write("\2\2TU\7\b\2\2U|\5\16\b\24VW\f\22\2\2WX\7\f\2\2X|\5\16")
        buf.write("\b\23YZ\f\21\2\2Z[\t\2\2\2[|\5\16\b\22\\]\f\20\2\2]^\7")
        buf.write("\17\2\2^|\5\16\b\21_`\f\17\2\2`a\7\20\2\2a|\5\16\b\20")
        buf.write("bc\f\16\2\2cd\7\21\2\2d|\5\16\b\17ef\f\r\2\2fg\7\22\2")
        buf.write("\2g|\5\16\b\16hi\f\f\2\2ij\7\30\2\2j|\5\16\b\rkl\f\13")
        buf.write("\2\2lm\7\32\2\2m|\5\16\b\fno\f\t\2\2op\7\23\2\2pq\7\5")
        buf.write("\2\2qv\5\16\b\2rs\7\6\2\2su\5\16\b\2tr\3\2\2\2ux\3\2\2")
        buf.write("\2vt\3\2\2\2vw\3\2\2\2wy\3\2\2\2xv\3\2\2\2yz\7\7\2\2z")
        buf.write("|\3\2\2\2{J\3\2\2\2{M\3\2\2\2{P\3\2\2\2{S\3\2\2\2{V\3")
        buf.write("\2\2\2{Y\3\2\2\2{\\\3\2\2\2{_\3\2\2\2{b\3\2\2\2{e\3\2")
        buf.write("\2\2{h\3\2\2\2{k\3\2\2\2{n\3\2\2\2|\177\3\2\2\2}{\3\2")
        buf.write("\2\2}~\3\2\2\2~\17\3\2\2\2\177}\3\2\2\2\n\22(,\66Hv{}")
        return buf.getvalue()


class ExpressionParser ( Parser ):

    grammarFileName = "Expression.g4"

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    sharedContextCache = PredictionContextCache()

    literalNames = [ "<INVALID>", "';'", "':'", "'('", "','", "')'", "'-'", 
                     "'*'", "'/'", "'+'", "'='", "'!='", "'<>'", "'>'", 
                     "'<'", "'>='", "'<='", "'in'" ]

    symbolicNames = [ "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "<INVALID>", "<INVALID>", 
                      "<INVALID>", "<INVALID>", "Func", "IF", "ELIF", "ELSE", 
                      "AND", "NOT", "OR", "NULL", "ID", "STR", "INT", "FLOAT", 
                      "WS" ]

    RULE_statement = 0
    RULE_condition_body = 1
    RULE_base_if = 2
    RULE_option_if = 3
    RULE_default_if = 4
    RULE_branch = 5
    RULE_expr = 6

    ruleNames =  [ "statement", "condition_body", "base_if", "option_if", 
                   "default_if", "branch", "expr" ]

    EOF = Token.EOF
    T__0=1
    T__1=2
    T__2=3
    T__3=4
    T__4=5
    T__5=6
    T__6=7
    T__7=8
    T__8=9
    T__9=10
    T__10=11
    T__11=12
    T__12=13
    T__13=14
    T__14=15
    T__15=16
    T__16=17
    Func=18
    IF=19
    ELIF=20
    ELSE=21
    AND=22
    NOT=23
    OR=24
    NULL=25
    ID=26
    STR=27
    INT=28
    FLOAT=29
    WS=30

    def __init__(self, input:TokenStream, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.9.3")
        self._interp = ParserATNSimulator(self, self.atn, self.decisionsToDFA, self.sharedContextCache)
        self._predicates = None




    class StatementContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expr(self):
            return self.getTypedRuleContext(ExpressionParser.ExprContext,0)


        def branch(self):
            return self.getTypedRuleContext(ExpressionParser.BranchContext,0)


        def getRuleIndex(self):
            return ExpressionParser.RULE_statement

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterStatement" ):
                listener.enterStatement(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitStatement" ):
                listener.exitStatement(self)




    def statement(self):

        localctx = ExpressionParser.StatementContext(self, self._ctx, self.state)
        self.enterRule(localctx, 0, self.RULE_statement)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 16
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [ExpressionParser.T__2, ExpressionParser.T__5, ExpressionParser.Func, ExpressionParser.NOT, ExpressionParser.NULL, ExpressionParser.ID, ExpressionParser.STR, ExpressionParser.INT, ExpressionParser.FLOAT]:
                self.state = 14
                self.expr(0)
                pass
            elif token in [ExpressionParser.IF]:
                self.state = 15
                self.branch()
                pass
            else:
                raise NoViableAltException(self)

            self.state = 18
            self.match(ExpressionParser.T__0)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Condition_bodyContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def getRuleIndex(self):
            return ExpressionParser.RULE_condition_body

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterCondition_body" ):
                listener.enterCondition_body(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitCondition_body" ):
                listener.exitCondition_body(self)




    def condition_body(self):

        localctx = ExpressionParser.Condition_bodyContext(self, self._ctx, self.state)
        self.enterRule(localctx, 2, self.RULE_condition_body)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 20
            self.expr(0)
            self.state = 21
            self.match(ExpressionParser.T__1)
            self.state = 22
            self.expr(0)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Base_ifContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def IF(self):
            return self.getToken(ExpressionParser.IF, 0)

        def condition_body(self):
            return self.getTypedRuleContext(ExpressionParser.Condition_bodyContext,0)


        def getRuleIndex(self):
            return ExpressionParser.RULE_base_if

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterBase_if" ):
                listener.enterBase_if(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitBase_if" ):
                listener.exitBase_if(self)




    def base_if(self):

        localctx = ExpressionParser.Base_ifContext(self, self._ctx, self.state)
        self.enterRule(localctx, 4, self.RULE_base_if)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 24
            self.match(ExpressionParser.IF)
            self.state = 25
            self.condition_body()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Option_ifContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ELIF(self):
            return self.getToken(ExpressionParser.ELIF, 0)

        def condition_body(self):
            return self.getTypedRuleContext(ExpressionParser.Condition_bodyContext,0)


        def getRuleIndex(self):
            return ExpressionParser.RULE_option_if

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterOption_if" ):
                listener.enterOption_if(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitOption_if" ):
                listener.exitOption_if(self)




    def option_if(self):

        localctx = ExpressionParser.Option_ifContext(self, self._ctx, self.state)
        self.enterRule(localctx, 6, self.RULE_option_if)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 27
            self.match(ExpressionParser.ELIF)
            self.state = 28
            self.condition_body()
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class Default_ifContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def ELSE(self):
            return self.getToken(ExpressionParser.ELSE, 0)

        def expr(self):
            return self.getTypedRuleContext(ExpressionParser.ExprContext,0)


        def getRuleIndex(self):
            return ExpressionParser.RULE_default_if

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterDefault_if" ):
                listener.enterDefault_if(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitDefault_if" ):
                listener.exitDefault_if(self)




    def default_if(self):

        localctx = ExpressionParser.Default_ifContext(self, self._ctx, self.state)
        self.enterRule(localctx, 8, self.RULE_default_if)
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 30
            self.match(ExpressionParser.ELSE)
            self.state = 31
            self.match(ExpressionParser.T__1)
            self.state = 32
            self.expr(0)
        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class BranchContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser

        def base_if(self):
            return self.getTypedRuleContext(ExpressionParser.Base_ifContext,0)


        def option_if(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.Option_ifContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.Option_ifContext,i)


        def default_if(self):
            return self.getTypedRuleContext(ExpressionParser.Default_ifContext,0)


        def getRuleIndex(self):
            return ExpressionParser.RULE_branch

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterBranch" ):
                listener.enterBranch(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitBranch" ):
                listener.exitBranch(self)




    def branch(self):

        localctx = ExpressionParser.BranchContext(self, self._ctx, self.state)
        self.enterRule(localctx, 10, self.RULE_branch)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 34
            self.base_if()
            self.state = 38
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            while _la==ExpressionParser.ELIF:
                self.state = 35
                self.option_if()
                self.state = 40
                self._errHandler.sync(self)
                _la = self._input.LA(1)

            self.state = 42
            self._errHandler.sync(self)
            _la = self._input.LA(1)
            if _la==ExpressionParser.ELSE:
                self.state = 41
                self.default_if()


        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.exitRule()
        return localctx


    class ExprContext(ParserRuleContext):
        __slots__ = 'parser'

        def __init__(self, parser, parent:ParserRuleContext=None, invokingState:int=-1):
            super().__init__(parent, invokingState)
            self.parser = parser


        def getRuleIndex(self):
            return ExpressionParser.RULE_expr

     
        def copyFrom(self, ctx:ParserRuleContext):
            super().copyFrom(ctx)


    class AddContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterAdd" ):
                listener.enterAdd(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitAdd" ):
                listener.exitAdd(self)


    class SubContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterSub" ):
                listener.enterSub(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitSub" ):
                listener.exitSub(self)


    class EqlContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterEql" ):
                listener.enterEql(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitEql" ):
                listener.exitEql(self)


    class NullContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def NULL(self):
            return self.getToken(ExpressionParser.NULL, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterNull" ):
                listener.enterNull(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitNull" ):
                listener.exitNull(self)


    class OrContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)

        def OR(self):
            return self.getToken(ExpressionParser.OR, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterOr" ):
                listener.enterOr(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitOr" ):
                listener.exitOr(self)


    class FuncContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def Func(self):
            return self.getToken(ExpressionParser.Func, 0)
        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFunc" ):
                listener.enterFunc(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFunc" ):
                listener.exitFunc(self)


    class MulContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterMul" ):
                listener.enterMul(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitMul" ):
                listener.exitMul(self)


    class InContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterIn" ):
                listener.enterIn(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitIn" ):
                listener.exitIn(self)


    class ParensContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self):
            return self.getTypedRuleContext(ExpressionParser.ExprContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterParens" ):
                listener.enterParens(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitParens" ):
                listener.exitParens(self)


    class NotEqlContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterNotEql" ):
                listener.enterNotEql(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitNotEql" ):
                listener.exitNotEql(self)


    class LtContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterLt" ):
                listener.enterLt(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitLt" ):
                listener.exitLt(self)


    class GtContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterGt" ):
                listener.enterGt(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitGt" ):
                listener.exitGt(self)


    class IntContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def INT(self):
            return self.getToken(ExpressionParser.INT, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterInt" ):
                listener.enterInt(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitInt" ):
                listener.exitInt(self)


    class StrContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def STR(self):
            return self.getToken(ExpressionParser.STR, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterStr" ):
                listener.enterStr(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitStr" ):
                listener.exitStr(self)


    class DivContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterDiv" ):
                listener.enterDiv(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitDiv" ):
                listener.exitDiv(self)


    class NegContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self):
            return self.getTypedRuleContext(ExpressionParser.ExprContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterNeg" ):
                listener.enterNeg(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitNeg" ):
                listener.exitNeg(self)


    class FloatContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def FLOAT(self):
            return self.getToken(ExpressionParser.FLOAT, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterFloat" ):
                listener.enterFloat(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitFloat" ):
                listener.exitFloat(self)


    class NotContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def NOT(self):
            return self.getToken(ExpressionParser.NOT, 0)
        def expr(self):
            return self.getTypedRuleContext(ExpressionParser.ExprContext,0)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterNot" ):
                listener.enterNot(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitNot" ):
                listener.exitNot(self)


    class AndContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)

        def AND(self):
            return self.getToken(ExpressionParser.AND, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterAnd" ):
                listener.enterAnd(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitAnd" ):
                listener.exitAnd(self)


    class GteContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterGte" ):
                listener.enterGte(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitGte" ):
                listener.exitGte(self)


    class IdContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def ID(self):
            return self.getToken(ExpressionParser.ID, 0)

        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterId" ):
                listener.enterId(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitId" ):
                listener.exitId(self)


    class LteContext(ExprContext):

        def __init__(self, parser, ctx:ParserRuleContext): # actually a ExpressionParser.ExprContext
            super().__init__(parser)
            self.copyFrom(ctx)

        def expr(self, i:int=None):
            if i is None:
                return self.getTypedRuleContexts(ExpressionParser.ExprContext)
            else:
                return self.getTypedRuleContext(ExpressionParser.ExprContext,i)


        def enterRule(self, listener:ParseTreeListener):
            if hasattr( listener, "enterLte" ):
                listener.enterLte(self)

        def exitRule(self, listener:ParseTreeListener):
            if hasattr( listener, "exitLte" ):
                listener.exitLte(self)



    def expr(self, _p:int=0):
        _parentctx = self._ctx
        _parentState = self.state
        localctx = ExpressionParser.ExprContext(self, self._ctx, _parentState)
        _prevctx = localctx
        _startState = 12
        self.enterRecursionRule(localctx, 12, self.RULE_expr, _p)
        self._la = 0 # Token type
        try:
            self.enterOuterAlt(localctx, 1)
            self.state = 70
            self._errHandler.sync(self)
            token = self._input.LA(1)
            if token in [ExpressionParser.Func]:
                localctx = ExpressionParser.FuncContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx

                self.state = 45
                self.match(ExpressionParser.Func)
                self.state = 46
                self.match(ExpressionParser.T__2)
                self.state = 47
                self.expr(0)
                self.state = 52
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,3,self._ctx)
                while _alt!=1 and _alt!=ATN.INVALID_ALT_NUMBER:
                    if _alt==1+1:
                        self.state = 48
                        self.match(ExpressionParser.T__3)
                        self.state = 49
                        self.expr(0) 
                    self.state = 54
                    self._errHandler.sync(self)
                    _alt = self._interp.adaptivePredict(self._input,3,self._ctx)

                self.state = 55
                self.match(ExpressionParser.T__4)
                pass
            elif token in [ExpressionParser.T__5]:
                localctx = ExpressionParser.NegContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 57
                self.match(ExpressionParser.T__5)
                self.state = 58
                self.expr(21)
                pass
            elif token in [ExpressionParser.NOT]:
                localctx = ExpressionParser.NotContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 59
                self.match(ExpressionParser.NOT)
                self.state = 60
                self.expr(8)
                pass
            elif token in [ExpressionParser.ID]:
                localctx = ExpressionParser.IdContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 61
                self.match(ExpressionParser.ID)
                pass
            elif token in [ExpressionParser.NULL]:
                localctx = ExpressionParser.NullContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 62
                self.match(ExpressionParser.NULL)
                pass
            elif token in [ExpressionParser.STR]:
                localctx = ExpressionParser.StrContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 63
                self.match(ExpressionParser.STR)
                pass
            elif token in [ExpressionParser.INT]:
                localctx = ExpressionParser.IntContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 64
                self.match(ExpressionParser.INT)
                pass
            elif token in [ExpressionParser.FLOAT]:
                localctx = ExpressionParser.FloatContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 65
                self.match(ExpressionParser.FLOAT)
                pass
            elif token in [ExpressionParser.T__2]:
                localctx = ExpressionParser.ParensContext(self, localctx)
                self._ctx = localctx
                _prevctx = localctx
                self.state = 66
                self.match(ExpressionParser.T__2)
                self.state = 67
                self.expr(0)
                self.state = 68
                self.match(ExpressionParser.T__4)
                pass
            else:
                raise NoViableAltException(self)

            self._ctx.stop = self._input.LT(-1)
            self.state = 123
            self._errHandler.sync(self)
            _alt = self._interp.adaptivePredict(self._input,7,self._ctx)
            while _alt!=2 and _alt!=ATN.INVALID_ALT_NUMBER:
                if _alt==1:
                    if self._parseListeners is not None:
                        self.triggerExitRuleEvent()
                    _prevctx = localctx
                    self.state = 121
                    self._errHandler.sync(self)
                    la_ = self._interp.adaptivePredict(self._input,6,self._ctx)
                    if la_ == 1:
                        localctx = ExpressionParser.MulContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 72
                        if not self.precpred(self._ctx, 20):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 20)")
                        self.state = 73
                        self.match(ExpressionParser.T__6)
                        self.state = 74
                        self.expr(21)
                        pass

                    elif la_ == 2:
                        localctx = ExpressionParser.DivContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 75
                        if not self.precpred(self._ctx, 19):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 19)")
                        self.state = 76
                        self.match(ExpressionParser.T__7)
                        self.state = 77
                        self.expr(20)
                        pass

                    elif la_ == 3:
                        localctx = ExpressionParser.AddContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 78
                        if not self.precpred(self._ctx, 18):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 18)")
                        self.state = 79
                        self.match(ExpressionParser.T__8)
                        self.state = 80
                        self.expr(19)
                        pass

                    elif la_ == 4:
                        localctx = ExpressionParser.SubContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 81
                        if not self.precpred(self._ctx, 17):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 17)")
                        self.state = 82
                        self.match(ExpressionParser.T__5)
                        self.state = 83
                        self.expr(18)
                        pass

                    elif la_ == 5:
                        localctx = ExpressionParser.EqlContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 84
                        if not self.precpred(self._ctx, 16):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 16)")
                        self.state = 85
                        self.match(ExpressionParser.T__9)
                        self.state = 86
                        self.expr(17)
                        pass

                    elif la_ == 6:
                        localctx = ExpressionParser.NotEqlContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 87
                        if not self.precpred(self._ctx, 15):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 15)")
                        self.state = 88
                        _la = self._input.LA(1)
                        if not(_la==ExpressionParser.T__10 or _la==ExpressionParser.T__11):
                            self._errHandler.recoverInline(self)
                        else:
                            self._errHandler.reportMatch(self)
                            self.consume()
                        self.state = 89
                        self.expr(16)
                        pass

                    elif la_ == 7:
                        localctx = ExpressionParser.GtContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 90
                        if not self.precpred(self._ctx, 14):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 14)")
                        self.state = 91
                        self.match(ExpressionParser.T__12)
                        self.state = 92
                        self.expr(15)
                        pass

                    elif la_ == 8:
                        localctx = ExpressionParser.LtContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 93
                        if not self.precpred(self._ctx, 13):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 13)")
                        self.state = 94
                        self.match(ExpressionParser.T__13)
                        self.state = 95
                        self.expr(14)
                        pass

                    elif la_ == 9:
                        localctx = ExpressionParser.GteContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 96
                        if not self.precpred(self._ctx, 12):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 12)")
                        self.state = 97
                        self.match(ExpressionParser.T__14)
                        self.state = 98
                        self.expr(13)
                        pass

                    elif la_ == 10:
                        localctx = ExpressionParser.LteContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 99
                        if not self.precpred(self._ctx, 11):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 11)")
                        self.state = 100
                        self.match(ExpressionParser.T__15)
                        self.state = 101
                        self.expr(12)
                        pass

                    elif la_ == 11:
                        localctx = ExpressionParser.AndContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 102
                        if not self.precpred(self._ctx, 10):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 10)")
                        self.state = 103
                        self.match(ExpressionParser.AND)
                        self.state = 104
                        self.expr(11)
                        pass

                    elif la_ == 12:
                        localctx = ExpressionParser.OrContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 105
                        if not self.precpred(self._ctx, 9):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 9)")
                        self.state = 106
                        self.match(ExpressionParser.OR)
                        self.state = 107
                        self.expr(10)
                        pass

                    elif la_ == 13:
                        localctx = ExpressionParser.InContext(self, ExpressionParser.ExprContext(self, _parentctx, _parentState))
                        self.pushNewRecursionContext(localctx, _startState, self.RULE_expr)
                        self.state = 108
                        if not self.precpred(self._ctx, 7):
                            from antlr4.error.Errors import FailedPredicateException
                            raise FailedPredicateException(self, "self.precpred(self._ctx, 7)")
                        self.state = 109
                        self.match(ExpressionParser.T__16)
                        self.state = 110
                        self.match(ExpressionParser.T__2)
                        self.state = 111
                        self.expr(0)
                        self.state = 116
                        self._errHandler.sync(self)
                        _la = self._input.LA(1)
                        while _la==ExpressionParser.T__3:
                            self.state = 112
                            self.match(ExpressionParser.T__3)
                            self.state = 113
                            self.expr(0)
                            self.state = 118
                            self._errHandler.sync(self)
                            _la = self._input.LA(1)

                        self.state = 119
                        self.match(ExpressionParser.T__4)
                        pass

             
                self.state = 125
                self._errHandler.sync(self)
                _alt = self._interp.adaptivePredict(self._input,7,self._ctx)

        except RecognitionException as re:
            localctx.exception = re
            self._errHandler.reportError(self, re)
            self._errHandler.recover(self, re)
        finally:
            self.unrollRecursionContexts(_parentctx)
        return localctx



    def sempred(self, localctx:RuleContext, ruleIndex:int, predIndex:int):
        if self._predicates == None:
            self._predicates = dict()
        self._predicates[6] = self.expr_sempred
        pred = self._predicates.get(ruleIndex, None)
        if pred is None:
            raise Exception("No predicate with index:" + str(ruleIndex))
        else:
            return pred(localctx, predIndex)

    def expr_sempred(self, localctx:ExprContext, predIndex:int):
            if predIndex == 0:
                return self.precpred(self._ctx, 20)
         

            if predIndex == 1:
                return self.precpred(self._ctx, 19)
         

            if predIndex == 2:
                return self.precpred(self._ctx, 18)
         

            if predIndex == 3:
                return self.precpred(self._ctx, 17)
         

            if predIndex == 4:
                return self.precpred(self._ctx, 16)
         

            if predIndex == 5:
                return self.precpred(self._ctx, 15)
         

            if predIndex == 6:
                return self.precpred(self._ctx, 14)
         

            if predIndex == 7:
                return self.precpred(self._ctx, 13)
         

            if predIndex == 8:
                return self.precpred(self._ctx, 12)
         

            if predIndex == 9:
                return self.precpred(self._ctx, 11)
         

            if predIndex == 10:
                return self.precpred(self._ctx, 10)
         

            if predIndex == 11:
                return self.precpred(self._ctx, 9)
         

            if predIndex == 12:
                return self.precpred(self._ctx, 7)
         




