#!/usr/bin/env python
# _*_ coding: utf-8 _*_

"""
@author: yang<PERSON><PERSON><PERSON>
@license: Copyright 2017-2020, LineZoneData.
@contact: yang<PERSON><PERSON><PERSON>@linezonedata.com
@software: pycharm
@time: 2020/4/2 11:20
@desc:
"""
import re

import wsme
from wsme import types as wtypes
from flask_babel import lazy_gettext as _

from biz.api_exception import ParameterException
from biz.types import BaseResult, jsontype, listtype


class PageConfigResult(BaseResult):
    code = wsme.wsattr(int, default=0)
    msg = wsme.wsattr(wtypes.text, default=str(_('success')))
    content = jsontype


class PageConfigBody(wtypes.Base):
    """
    页面配置列表过滤参数
    """
    set_types = wsme.wsattr(listtype, mandatory=False)
    # module_names = wsme.wsattr(listtype, mandatory=False)
    page_names = wsme.wsattr(listtype, mandatory=False)
    page_no = wsme.wsattr(int, mandatory=False)
    page_size = wsme.wsattr(int, mandatory=False)


class ConfigContentBody(wtypes.Base):
    """新增修改页面配置"""
    # module_name = wsme.wsattr(wtypes.text)
    page_name = wsme.wsattr(wtypes.text)
    remark = wsme.wsattr(wtypes.text)
    config_name = wsme.wsattr(wtypes.text)

    def validate(self):
        config_name_validate_msg = '20字符以内，支持输入中/英文、数字、中/英文括号、中/下划线'
        if len(self.config_name) > 20 or len(self.config_name) < 0:
            raise ParameterException(msg=config_name_validate_msg)
        if not bool(re.match('^[A-Za-z0-9-_（）()\u4e00-\u9fa5]+$', self.config_name)):
            raise ParameterException(msg=config_name_validate_msg)

        return self


class DeleteConfigContentBody(wtypes.Base):
    """删除页面配置"""
    id = wsme.wsattr(int, mandatory=True)


class UpdateConfigContentBody(wtypes.Base):
    """修改页面配置"""
    id = wsme.wsattr(int, mandatory=True)
    # module_name = wsme.wsattr(wtypes.text)
    page_name = wsme.wsattr(wtypes.text)
    content = wsme.wsattr(jsontype)
    remark = wsme.wsattr(wtypes.text)
    config_name = wsme.wsattr(wtypes.text)

    def validate(self):
        config_name_validate_msg = '20字符以内，支持输入中/英文、数字、中/英文括号、中/下划线'
        if len(self.config_name) > 20 or len(self.config_name) < 0:
            raise ParameterException(msg=config_name_validate_msg)
        if not bool(re.match('^[A-Za-z0-9-_（）()\u4e00-\u9fa5]+$', self.config_name)):
            raise ParameterException(msg=config_name_validate_msg)

        return self

