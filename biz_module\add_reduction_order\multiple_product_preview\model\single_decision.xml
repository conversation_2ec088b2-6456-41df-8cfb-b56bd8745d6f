<?xml version="1.0"?>
<mapper namespace="add_reduction_order_single_decision">


    <select id="query_single_decision_org_filter">
        select
            <if test="filter_key == 'channel_type'">
                channel_type as label, channel_type as value
            </if>
            <if  test="filter_key == 'org_sk'">
                org_name as label, org_sk as value
            </if>
        from biz.qrs_multiple_product_overview
        where ${filter_key} is not null
        <if test="day_date">
            and day_date = #{day_date}
        </if>
        <if test="org_sk_list">
            and org_sk in ${org_sk_list}
        </if>
        <if test="not org_sk_list">
            and 1 != 1
        </if>
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="filter_key == 'org_sk'">
            group by org_sk, org_name
        </if>
        <if test="filter_key != 'org_sk'">
            group by ${filter_key}
        </if>
        order by ${filter_key} desc
    </select>

    <select id="query_single_decision_filter">
        select
            <if test="filter_key == 'skc_sk'">
                skc_code as label, skc_sk as value
            </if>
            <if test="filter_key == 'day_date'">
                to_char(day_date, 'yyyy-mm-dd') as label, to_char(day_date, 'yyyy-mm-dd') as value
            </if>
            <if test="filter_key not in ('skc_sk', 'day_date')">
                ${filter_key} as label, ${filter_key} as value
            </if>
        from biz.qrs_multiple_product_overview
        where ${filter_key} is not null
        <if test="day_date">
            and day_date = #{day_date}
        </if>
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="org_sk">
            and org_sk = #{org_sk}
        </if>
        <if test="not org_sk and filter_key !='day_date'">
            and 1 != 1
        </if>
        <if test="big_class">
            and big_class in ${big_class}
        </if>
        <if test="product_range">
            and product_range in ${product_range}
        </if>
        <if test="product_year_quarter">
            and product_year_quarter in ${product_year_quarter}
        </if>
        <if test="product_belong_name">
            and product_belong_name in ${product_belong_name}
        </if>
        <if test="ps_mid_class">
            and ps_mid_class in ${ps_mid_class}
        </if>
        <if test="sale_mid_tiny_class">
            and sale_mid_tiny_class in ${sale_mid_tiny_class}
        </if>
        <if test="gender">
            and gender in ${gender}
        </if>
        <if test="product_name">
            and product_name in ${product_name}
        </if>
        <if test="filter_key == 'skc_sk'">
            group by skc_code, skc_sk
        </if>
        <if test="filter_key != 'skc_sk'">
            group by ${filter_key}
        </if>
        order by ${filter_key} desc
    </select>


    <select id="query_single_decision_detail_kpi">
        WITH base_org AS (
            SELECT a.org_sk, a.org_name, b.org_name as select_org_name
            FROM dm.dim_org_integration as a
            cross join dm.dim_org_integration as b
            WHERE array_length(string_to_array(a.org_long_code, ':'), 1) &lt;=3 AND a.org_flag =3
            AND CAST(#{org_sk} as text) = any(string_to_array(a.org_long_sk, ':')) and b.org_sk = #{org_sk}
        )
        SELECT
            max(product_year_quarter) as product_year_quarter -- 发布季
            ,max(gender) as gender -- 性别
            ,max(color_name) as color_name -- 颜色
            ,max(target_price) as target_price -- 吊牌价
            ,max(big_class) as big_class -- 大类
            ,max(ps_mid_class) as ps_mid_class -- PS中类
            ,max(sale_mid_tiny_class) as sale_mid_tiny_class -- 销售中类小类
            ,max(product_name) as product_name -- 款名称
            ,max(lt) as lt -- lt
            ,max(life_cycle) as life_cycle -- 生命周期
            , to_char(min(real_listing_date), 'yyyy-mm-dd') as real_listing_date -- 实际上市日期
            , sum(order_qty) as order_qty -- 订货量
            , sum(total_shipment_qty) as total_shipment_qty -- 发货量
            , sum(has_add_qty) as has_add_qty -- 已加单量
            , sum(not_in_stock_qty) not_in_stock_qty -- 未到货量
            , to_char(min(last_arrival_date), 'yyyy-mm-dd') last_arrival_date -- 最近到货日期
            , sum(has_stock_store_qty) distributed_store_qty -- 铺店数
            , sum(has_stock_store_qty)::numeric / nullif(sum(total_store_qty),0) distributed_rate -- 铺货率
            , sum(remaining_stock_qty) remaining_stock_qty -- 剩余库存
            , sum(warehouse_stock_qty) warehouse_stock_qty -- 仓库库存
            , sum(store_stock_qty_include_on_road) store_stock_qty_include_on_road -- 门店库存（含在途）
            , sum(salable_weeks_num)::numeric / nullif(sum(salable_weeks_den), 0) saleable_weeks -- 可销周数
            , sum(store_stock_qty_include_on_road)::numeric / nullif(sum(has_stock_store_qty),0) avg_store_stock -- 店均库存
            , sum(break_size_store_qty)::numeric / nullif(sum(has_stock_store_qty), 0)  store_size_break_rate -- 断码率
            , to_char(min(first_sale_date), 'yyyy-mm-dd') as first_sale_date -- 首销日期
            , sum(total_sale_qty) as total_sale_qty -- 累计销量
            , sum(total_sale_qty_without_group_buy) as total_sale_qty_without_group_buy -- 无团购累销
            , sum(has_sale_store_qty) has_sale_store_qty -- 有销门店数
            , sum(last_first_week_sale_qty)::numeric / nullif(sum(has_sale_store_qty), 0) avg_store_week_sale -- 店均周销
            , sum(last_four_weeks_sale_qty) last_four_weeks_sale_qty -- 近4周总销量
            , sum(last_first_week_sale_qty) last_first_week_sale_qty -- 近第1周销量
            , sum(last_second_week_sale_qty) last_second_week_sale_qty -- 近第2周销量
            , sum(last_third_week_sale_qty) last_third_week_sale_qty -- 近第3周销量
            , sum(total_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as total_sale_rate-- 累计售罄
            , sum(last_first_week_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_first_week_sale_rate-- 近一周售罄
            , sum(last_second_week_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_second_week_sale_rate-- 近一周售罄
            , sum(last_third_week_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_third_week_sale_rate-- 近一周售罄
            , sum(total_sale_amt)::numeric / nullif(sum(total_sale_tag_amt), 0) total_discount -- 累计则扣
            , sum(last_first_week_sale_amt)::numeric / nullif(sum(last_first_week_sale_tag_amt), 0) last_first_week_discount -- 累计则扣
            , sum(last_second_week_sale_amt)::numeric / nullif(sum(last_second_week_sale_tag_amt), 0) last_second_week_discount -- 累计则扣
            , sum(last_third_week_sale_amt)::numeric / nullif(sum(last_third_week_sale_tag_amt), 0) last_third_week_discount -- 累计则扣
        FROM biz.qrs_single_decision_kpi as a
        inner join base_org as b on a.org_sk = b.org_sk
        WHERE a.day_date = #{day_date}
        AND a.skc_sk = #{skc_sk}
        GROUP BY select_org_name
    </select>

    <select id="query_single_decision_detail_info">
        WITH forecast_data as (
            select human_decision_qty, model_decision_qty, sale_out_target, sale_out_date, lt, st, skc_sk, org_sk || '@@' ||  skc_sk ||  '@@' || day_date as uuid
            from  biz.qrs_multiple_product_overview as a
            where a.day_date = #{day_date}
            and a.skc_sk = #{skc_sk}
            and a.org_sk = #{org_sk}
        )
        , base_org AS (
            SELECT a.org_sk, a.org_name, b.org_name as select_org_name
            FROM dm.dim_org_integration as a
            cross join dm.dim_org_integration as b
            WHERE array_length(string_to_array(a.org_long_code, ':'), 1) &lt;=3 AND a.org_flag =3
            AND CAST(#{org_sk} as text) = any(string_to_array(a.org_long_sk, ':')) and b.org_sk = #{org_sk}
        )
        , detail_data AS (
            SELECT
                skc_sk
                , select_org_name
                ,sum(last_two_weeks_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_two_weeks_sale_rate-- 上上周售罄
                ,sum(last_week_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as last_week_sale_rate-- 上周售罄
                ,sum(last_two_weeks_sale_qty_without_group_buy)::numeric / nullif(sum(total_shipment_qty), 0) as last_two_weeks_without_group_buy_sale_rate-- 剔除团购上上周售罄
                ,sum(last_week_sale_qty_without_group_buy)::numeric / nullif(sum(total_shipment_qty), 0) as last_week_without_group_buy_sale_rate-- 剔除团购上周售罄
                ,sum(total_sale_qty)::numeric / nullif(sum(total_shipment_qty), 0) as total_sale_rate-- 累计售罄
                ,sum(same_listing_month_sale_qty)::numeric / nullif(sum(same_listing_month_arrival_qty), 0) as same_listing_month_sale_rate-- 同上市月份平均售罄
                ,case
                    when max(select_org_name) = '正价店' and  COALESCE(sum(lt_sale_qty), 0) -  COALESCE(sum(stock_qty), 0) >= 0 then '有断货风险'
                    when max(select_org_name) = '正价店' and  COALESCE(sum(lt_sale_qty), 0) -  COALESCE(sum(stock_qty), 0) &lt; 0 then '无断货风险'
                    else null
                end as lt_sale_risk -- lt断货风险
                , min(real_listing_date) as real_listing_date -- 实际上市日期
                , min(first_sale_date) as first_sale_date -- 首销日期
                , sum(order_qty) as order_qty -- 订货量
                , sum(total_shipment_qty) as total_shipment_qty -- 累计发货量
                , sum(total_sale_qty) as total_sale_qty -- 累计销量
                , sum(total_sale_qty_without_group_buy) as total_sale_qty_without_group_buy -- 无团购累销
                , sum(last_two_weeks_sale_qty) as last_two_weeks_sale_qty -- 上上周销量
                , sum(last_week_sale_qty) as last_week_sale_qty -- 上周销量
                , sum(total_sale_amt)::numeric / nullif(sum(total_sale_tag_amt), 0) total_discount -- 累计则扣
                , sum(last_two_weeks_sale_amt)::numeric / nullif(sum(last_two_weeks_sale_tag_amt), 0) last_two_weeks_sale_discount -- 上上周折扣
                , sum(last_week_sale_amt)::numeric / nullif(sum(last_week_sale_tag_amt), 0) last_week_sale_discount -- 上周折扣
                , sum(stock_qty) stock_qty -- 店仓剩余库存
                , sum(not_in_stock_qty) not_in_stock_qty -- 未到货量
                , sum(salable_weeks_num)::numeric / nullif(sum(salable_weeks_den), 0) salable_weeks -- 可销周数
                , sum(has_sale_store_qty) has_sale_store_qty -- 有销门店数
                , sum(last_week_sale_qty)::numeric / nullif(sum(has_sale_store_qty),0)::numeric avg_store_week_sale -- 店均周销
                , sum(st_forecast_sale_qty) st_forecast_sale_qty -- ST预计销量
                , sum(life_cycle_stock_demand) life_cycle_stock_demand -- 生命周期内库存需求
                , case when select_org_name in ('直营', '正价店')then max(refer_skc_sale_rate) else null end refer_skc_sale_rate -- 参照款上市至决策日销量占比
                , case when select_org_name in ('直营', '正价店') then max(refer_skc_lt_sale_rate) else null end refer_skc_lt_sale_rate -- 参照款LT销量占比
                , case when select_org_name in ('直营', '正价店') then max(refer_skc_st_sale_rate) else null end refer_skc_st_sale_rate -- 参照款ST销量占比
            FROM biz.qrs_multiple_product_overview_kpi as a
            inner join base_org as b on a.org_sk = b.org_sk
            WHERE a.day_date = #{day_date}
            AND a.skc_sk = #{skc_sk}
            GROUP BY select_org_name, skc_sk
        )
        select a.human_decision_qty, a.model_decision_qty, a.sale_out_target, a.sale_out_date, a.lt, a.st, a.uuid, b.*, b.select_org_name as channel_type
        from forecast_data as a
        inner join detail_data as b on a.skc_sk = b.skc_sk
    </select>

    <update id="update_single_decision_detail_info">
        update biz.qrs_multiple_product_overview
        set human_decision_qty = #{human_decision_qty},
            modifier_id = #{user_id},
            modify_time = now(),
            status = 1
        where day_date = #{day_date}
        and skc_sk = #{skc_sk}
        and org_sk = #{org_sk}
    </update>
    <select id="query_refer_skc_info">
        select
            listing_week, natural_week, sale_qty, refer_skc_sale_qty, natural_year, week_stage
        from biz.qrs_skc_week_sale_info
        where day_date = #{day_date}
        and  skc_sk = #{skc_sk}
        and  org_sk = #{org_sk}
        order by natural_year, natural_week
    </select>
</mapper>