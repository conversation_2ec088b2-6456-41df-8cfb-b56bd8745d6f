#!usr/bin/env python
# -*-coding:utf-8-*-
"""
author:chen<PERSON><PERSON><PERSON>

"""
import json
from typing import Optional

from fastboot.utils.config import ConfigMeta
from fastboot.utils.nest import egg

from biz.common.lz_db import DBUtil
from biz.settings import config
from .constants import INSIGHT_PAGES_SQL
CONSTANT = config['schema_constant']

@egg
class CustomPageConfig:
    TENANT_INSTANCE_CUSTOM_PAGES: Optional[str] = ConfigMeta(name="tenant_instance.custom.pages", default="")


def get_tenant_purchased_pages():
    """
    获取租户已购买的菜单

    """

    switch_call_tenant_center = config.SWITCH_CALL_TENANT_CENTER
    if switch_call_tenant_center:
        from biz.third_party.tenant_center.handler import TenantCenterHandler
        tenant_purchased_pages = TenantCenterHandler.list_tenant_module_pages(config.TENANT_CODE, config.VIRTUAL_CODE)
    else:
        # 从配置文件中获取，便于本地化部署
        tenant_purchased_pages = json.loads(config.TENANT_INSTANCE_PURCHASED_MODULE_PAGES)
    return tenant_purchased_pages


def get_tenant_purchased_modules():
    """
    获取租户已购买的模块
    """
    switch_call_tenant_center = config.SWITCH_CALL_TENANT_CENTER
    if switch_call_tenant_center:
        from biz.third_party.tenant_center.handler import TenantCenterHandler
        tenant_purchased_modules = TenantCenterHandler.list_purchased_modules(config.TENANT_CODE, config.VIRTUAL_CODE)
    else:
        # 从配置文件中获取，便于本地化部署
        tenant_purchased_modules = json.loads(config.TENANT_INSTANCE_PURCHASED_MODULES)
    return tenant_purchased_modules


def is_tenant_purchased_insight_module():
    """
    检查用户是否购买了洞察模块
    """
    # 硬编码
    insight_module_code = "8000"
    tenant_purchased_modules = get_tenant_purchased_modules()
    return insight_module_code in [p.get("module_code") for p in tenant_purchased_modules]


def add_all_insight_module_pages_into_purchased_pages(page_list):
    """
    讲洞察模块页面加入到传入的页面列表中。
    """
    # 查询洞察及洞察下的所有子菜单(多级)
    user_list = DBUtil.fetch_data_sql(INSIGHT_PAGES_SQL, {})
    for page in user_list:
        page_list.append({
            "page_id": page.page_id,
            "page_name": page.page_name,
            "page_name_en": page.page_name_en,
        })


def filter_tenant_purchased_pages(pages):
    """
    从传入的模块菜单列表(pages)中过滤出租户已购买的模块页面列表

    :param pages: 模块页面列表
    :return:    过滤后的模块页面列表
    """
    # 查询已购买的模块页面
    tenant_purchased_pages = get_tenant_purchased_pages()
    # 新增用户自定义页面
    tenant_instance_custom_pages = CustomPageConfig.TENANT_INSTANCE_CUSTOM_PAGES
    if tenant_instance_custom_pages:
        tenant_instance_custom_pages = tenant_instance_custom_pages.replace("\"", "")
    if tenant_instance_custom_pages:
        # 项目自定义页面
        add_all_custom_module_pages_into_purchased_pages(tenant_purchased_pages, tenant_instance_custom_pages)

    # 检查租户是否购买了洞察模块，如果购买了洞察模块，则将所有洞察模块页面加进tenant_purchased_pages
    # if is_tenant_purchased_insight_module():
        # 将所有洞察模块页面加进tenant_purchased_pages
        # add_all_insight_module_pages_into_purchased_pages(tenant_purchased_pages)

    # 过滤模块页面
    pages_ids = [str(p.get("page_id")) for p in tenant_purchased_pages]
    result = pages[pages['page_id'].isin(pages_ids)]
    return result


def add_all_custom_module_pages_into_purchased_pages(page_list, tenant_instance_custom_pages):
    """
    讲洞察模块页面加入到传入的页面列表中。
    """
    # 查询洞察及洞察下的所有子菜单(多级)
    tenant_instance_custom_page_list = tenant_instance_custom_pages.split(",")
    if tenant_instance_custom_page_list and len(tenant_instance_custom_page_list) == 1:
        page_id_condition = "page_id = '{}'".format(tenant_instance_custom_page_list[0])
    else:
        page_id_condition = "page_id in ({})".format(tenant_instance_custom_pages)
    params = {
        "page_id_condition": page_id_condition
    }
    custom_pages_sql = '''-- 获取项目自定义相关页面
    WITH RECURSIVE pages AS (
       SELECT
          page_id, page_name, extra1 as page_name_en
       FROM
          {system_schema_name}.page_permission
       WHERE {page_id_condition}
       UNION
          SELECT
             pp.page_id,
             pp.page_name,
             pp.extra1 as page_name_en
          FROM
             {system_schema_name}.page_permission pp
          INNER JOIN pages p2 ON p2.page_id = pp.parent_id
    ) SELECT * FROM pages;
    '''.format(**CONSTANT, **params)
    user_list = DBUtil.fetch_data_sql(custom_pages_sql, {})
    for page in user_list:
        page_list.append({
            "page_id": page.page_id,
            "page_name": page.page_name,
            "page_name_en": page.page_name_en,
        })
