@startuml
'https://plantuml.com/sequence-diagram

autonumber

actor actor

actor -> bizService

participant 队列服务 as asyncService

box "业务部分" #lightgrey

participant 业务服务 as bizService


end box

box "Excel服务" #Seashell

participant "ExcelService" as excelService

bizService -> fileService: 上传文件备份

bizService -> bizService: 确认业务类型

bizService -> excelService: 调用导入服务

excelService -> excelService: 查询配置数据
excelService -> excelService: 解析文件,数据提取
excelService -> excelService: 基础校验

alt #Honeydew 存在错误

excelService -> bizService : 返回错误
else #palegreen

excelService -> bizService: 返回DataFrame / json 数据

end

bizService -> bizService: 数据业务逻辑校验
bizService -> bizService: 数据持久化

end box


participant 文件服务 as fileService



alt #Honeydew 同步
 bizService -> actor: 返回结果
else #palegreen
 bizService -> asyncService: 添加任务记录
 bizService -> asyncService: 查询任务记录
 bizService -> fileService: 请求文件
 fileService -> bizService: 返回文件流
 asyncService -> asyncService: 更新任务步骤

 actor -> asyncService: 任务列表查询任务执行结果

end alt



@enduml