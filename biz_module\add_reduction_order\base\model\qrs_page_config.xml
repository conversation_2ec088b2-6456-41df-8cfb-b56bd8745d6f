<?xml version="1.0"?>
<mapper namespace="add_reduction_order_page_config">

    <select id="query_qrs_page_config">
        with conf_tenant as (
            select * from sys.page_config
            where config_type='tenant'
                and module_name=#{module_name}
                and page_name=#{page_name}
                and is_deleted=0
        ), conf_product as (
            select * from sys.page_config
            where config_type='product'
                and module_name=#{module_name}
                and page_name=#{page_name}
                and is_deleted=0
        ), conf_common as (
            -- 获取普通用户配置时如果有租户配置则能看到租户级别的配置，否则只能看到产品配置
            select * from sys.page_config
            where creator = ${user_id}
                and module_name=#{module_name}
                and page_name=#{page_name}
                and config_type='common'
                and is_deleted=0
            union all
            select * from conf_tenant
            union all
            select * from conf_product where not exists (select 1 from conf_tenant)
        ), conf as(
            select * from sys.page_config where 1 = 2 and is_deleted=0

            <if test="is_common">
                union all
                -- 普通用户配置
                select * from conf_common
            </if>

            <if test="is_tenant">
                -- 租户级别配置
                union all
                select * from conf_tenant
            </if>

            <if test="is_product">
                -- 产品级别配置
                union all
                select * from conf_product
            </if>
        )
        select content from conf
        order by set_type asc, modify_time desc limit 1
    </select>

    <select id="get_page_config_by_id">
        select content from sys.page_config
        where page_id= #{page_id}
        and is_deleted = 0
    </select>

    <select id="get_page_config_basic_by_id">
        select page_name, module_name, page_id from sys.page_config
        where page_id= #{page_id}
        and is_deleted = 0
    </select>

<!--    <select id="user_page_config_mapping">-->
<!--        select content from sys.user_page_config-->
<!--        where page_id= #{page_id}-->
<!--        and is_deleted = 0-->
<!--    </select>-->

    <insert id="add_user_page_config_mapping">
        delete from sys.user_page_config_mapping
        where page_name=#{page_name} and module_name=#{module_name} and user_id=#{user_id};
        insert into sys.user_page_config_mapping(
            page_id,
            page_name,
            module_name,
            user_id,
            creator_id,
            modifier_id,
            create_time,
            modify_time,
            is_deleted)
        values (
            #{page_id},
            #{page_name},
            #{module_name},
            #{user_id},
            #{user_id},
            #{user_id},
            now(),
            now(),
            0)
        returning id;
    </insert>

    <select id="get_user_page_config_mapping">
        select m.page_id from sys.user_page_config_mapping m
        inner join sys.page_config p on m.page_id = p.page_id
        where m.user_id= #{user_id}
        and m.page_name = #{page_name}
        and m.module_name = #{module_name}
        and m.is_deleted = 0
        and p.is_deleted = 0
        order by m.modify_time desc limit 1
    </select>

    <update id="update_page_config_format">
        UPDATE sys.page_config
        SET
           content = #{content}
        WHERE page_id = #{page_id}
    </update>

    <select id="get_page_config_by_module_name_and_page_name">
        SELECT page_id, content
        FROM sys.page_config WHERE module_name = #{module_name} and page_name = #{page_name}
    </select>


    <select id="get_page_id_by_page_code">
        SELECT page_id
        FROM biz.config_page WHERE page_code = #{page_code} and is_deleted = false
        limit 1
    </select>
</mapper>