from abc import ABC<PERSON><PERSON>, abstractmethod
from collections import deque
from typing import Callable, Optional, List

from antlr4 import InputStream, CommonTokenStream, ParseTreeWalker
from antlr4.error.ErrorListener import <PERSON>rror<PERSON><PERSON>ener
from pydantic import BaseModel

from biz_module.common.expression.ExpressionLexer import Expression<PERSON>exer
from biz_module.common.expression.ExpressionListener import ExpressionListener
from biz_module.common.expression.ExpressionParser import ExpressionParser


class BaseExpressionListener(ExpressionListener, metaclass=ABCMeta):

    @abstractmethod
    def get_result(self):
        pass


class ErrorRecord:
    def __init__(self):
        self.error_messages = []

    def add_error_message(self, column: int, message: str):
        self.error_messages.append(f"column:{column}. msg:{message}")

    def get_error_messages(self):
        return self.error_messages


class CommonErrorListener(ErrorListener):

    def __init__(self, error_record: ErrorRecord):
        self.error_record = error_record

    def syntaxError(self, recognizer, _, line, column, msg, e):
        self.error_record.add_error_message(column, msg)


class CommonListener(BaseExpressionListener):

    def __init__(self, error_record: ErrorRecord, id_change_func: Callable):
        self.stack = deque()
        self.error_record = error_record
        self.id_change_func = id_change_func
        self.index = 0
        self.in_exp_flag = "__in__"
        self.param_flag = "__param__"

    def append_op_stack(self, op: str):
        right_value = self.stack.pop()
        left_value = self.stack.pop()
        self.stack.append(f"{left_value} {op} {right_value}")

    def append_condition_body(self):
        then_data = self.stack.pop()
        when_data = self.stack.pop()
        pre_body = self.stack.pop()
        self.stack.append(f"{pre_body}{'    '*self.index}WHEN {when_data} THEN {then_data}\n")

    def enterBranch(self, ctx: ExpressionParser.BranchContext):
        self.index += 1
        self.stack.append('CASE\n')

    def exitBase_if(self, ctx: ExpressionParser.Base_ifContext):
        self.append_condition_body()

    def exitOption_if(self, ctx: ExpressionParser.Option_ifContext):
        self.append_condition_body()

    def exitDefault_if(self, ctx: ExpressionParser.Default_ifContext):
        then_data = self.stack.pop()
        pre_body = self.stack.pop()
        self.stack.append(f"{pre_body}{'    '*self.index}ELSE {then_data}\n")

    def exitBranch(self, ctx: ExpressionParser.BranchContext):
        self.index -= 1
        branch_body = self.stack.pop()
        self.stack.append(f'{branch_body}END')

    def enterFunc(self, ctx: ExpressionParser.FuncContext):
        self.stack.append(self.param_flag)

    def exitFunc(self, ctx: ExpressionParser.FuncContext):
        param_values = deque()
        while True:
            value = self.stack.pop()
            if value != self.param_flag:
                param_values.appendleft(value)
            else:
                break
        func_name = ctx.Func().getText()
        self.stack.append(f"{func_name}({', '.join(param_values)})")

    def exitId(self, ctx: ExpressionParser.IdContext):
        id_text = ctx.ID().getText()
        try:
            id_text = self.id_change_func(id_text)
        except Exception as error:
            self.error_record.add_error_message(ctx.start.column, str(error))

        self.stack.append(id_text)

    def exitNeg(self, ctx: ExpressionParser.NegContext):
        value = self.stack.pop()
        self.stack.append(f"-{value}")

    def exitDiv(self, ctx: ExpressionParser.DivContext):
        self.append_op_stack("/")

    def exitMul(self, ctx: ExpressionParser.MulContext):
        self.append_op_stack('*')

    def exitAdd(self, ctx: ExpressionParser.AddContext):
        self.append_op_stack('+')

    def exitSub(self, ctx: ExpressionParser.SubContext):
        self.append_op_stack('-')

    def exitEql(self, ctx: ExpressionParser.EqlContext):
        self.append_op_stack('=')

    def exitNotEql(self, ctx: ExpressionParser.NotEqlContext):
        self.append_op_stack('!=')

    def exitGt(self, ctx: ExpressionParser.GtContext):
        self.append_op_stack('>')

    def exitLt(self, ctx: ExpressionParser.LtContext):
        self.append_op_stack('<')

    def exitGte(self, ctx: ExpressionParser.GteContext):
        self.append_op_stack('>=')

    def exitLte(self, ctx: ExpressionParser.LteContext):
        self.append_op_stack('<=')

    def exitAnd(self, ctx: ExpressionParser.AndContext):
        self.append_op_stack('and')

    def exitOr(self, ctx: ExpressionParser.OrContext):
        self.append_op_stack('or')

    def exitNot(self, ctx: ExpressionParser.NotContext):
        value = self.stack.pop()
        self.stack.append(f"not {value}")

    def enterIn(self, ctx: ExpressionParser.InContext):
        self.stack.append(self.in_exp_flag)

    def exitIn(self, ctx: ExpressionParser.InContext):
        in_values = deque()
        while True:
            value = self.stack.pop()
            if value != self.in_exp_flag:
                in_values.appendleft(value)
            else:
                break
        left_exp = in_values.popleft()
        self.stack.append(f"{left_exp} in ({', '.join(in_values)})")

    def exitParens(self, ctx: ExpressionParser.ParensContext):
        paren_value = self.stack.pop()
        self.stack.append(f'({paren_value})')

    def exitFloat(self, ctx: ExpressionParser.FloatContext):
        float_value = ctx.FLOAT().getText()
        self.stack.append(float_value)

    def exitInt(self, ctx: ExpressionParser.IntContext):
        int_value = ctx.INT().getText()
        self.stack.append(int_value)

    def exitNull(self, ctx: ExpressionParser.NullContext):
        self.stack.append('null')

    def exitStr(self, ctx: ExpressionParser.StrContext):
        str_value = ctx.STR().getText()
        self.stack.append(str_value)

    def get_result(self):
        result = self.stack.pop()
        return result


class ExpressionResult(BaseModel):
    expression: Optional[str]
    error_messages: List[str]


def dump_id_change_func(id_name: str) -> str:
    return id_name


def parse_expression(expression: str, id_change_func: Callable[[str], str] = dump_id_change_func) -> ExpressionResult:
    input_stream = InputStream(f"{expression};")
    lexer = ExpressionLexer(input_stream)
    stream = CommonTokenStream(lexer)
    parser = ExpressionParser(stream)
    error_record = ErrorRecord()
    error_listener = CommonErrorListener(error_record)
    parser.removeErrorListeners()
    parser.addErrorListener(error_listener)
    tree = parser.statement()
    listener = CommonListener(error_record, id_change_func)
    walker = ParseTreeWalker()
    walker.walk(listener, tree)
    result = ExpressionResult(
        expression=listener.get_result(),
        error_messages=error_record.get_error_messages()
    )
    return result
