# -*- coding: UTF-8 -*-
import re
from typing import Dict, List

from ourbatis.exceptions import SQLParamMiss, SQLParamTypeError
from ourbatis.sql_source_parser.sql_context import SqlContext
from pydantic import BaseModel

PARAM_PATTERNS = {
    "#": re.compile(r"#{\s*[a-zA-Z_][0-9a-zA-Z_.]*\s*}"),
    "$": re.compile(r"\${\s*[a-zA-Z_][0-9a-zA-Z_.]*\s*}"),
}

META_PATTERNS = {
    "@": re.compile(r"@{.*?}", re.DOTALL),  # @操作符可以操作xml元素
}


class Param(BaseModel):
    full_name: str = None
    name: str = None
    mock_value: str = None


def get_param_attr(param, attr):
    try:
        param_attr = getattr(param, attr)
    except AttributeError:
        param_attr = param[attr]

    return param_attr


def covert_param_name(param_name: str, context: SqlContext) -> str:
    result = param_name
    if param_name.find(".") < 0:
        return result
    param_real_name, attr = param_name.split(".")
    result = f"{param_real_name}_{attr}"
    if param_real_name in context.foreach_temp_values:
        for_temp_value = context.foreach_temp_values[param_real_name]
        for_temp_value.attr_names.append(attr)

    input_param = context.params[param_real_name]
    context.inner_gen_params.update({result: get_param_attr(input_param, attr)})

    return result


def gen_match_params(pattern: re.Pattern, convert_string: str):
    tmp_params = []
    match = pattern.findall(convert_string)
    tmp_params += sorted(set(match), key=match.index)
    yield from tmp_params


def get_meta_id(param_str: str) -> str:
    if "(" not in param_str and ")" not in param_str:
        return param_str.strip()

    return param_str.split("(")[0].strip()


def covert_literal_parameters(
    convert_string: str, param: Param, context: SqlContext
) -> str:
    raw_data = context.params.get(param.name)
    if raw_data is None:
        raise SQLParamMiss(param.name)
    if not isinstance(raw_data, (str, int, float, tuple, list, set)):
        raise SQLParamTypeError(param.name, str, type(raw_data))
    if isinstance(raw_data, (list, set)):
        raw_data = tuple(raw_data)
    if isinstance(raw_data, tuple) and len(raw_data) == 1:
        raw_data = str(raw_data).replace(",", "")
    return convert_string.replace(param.full_name, str(raw_data))


def get_params(convert_string: str, context: SqlContext) -> Dict[str, List[Param]]:
    """
    Get SQL Params
    example: #{age,javaType=int,jdbcType=NUMERIC,typeHandler=MyTypeHandler}
    change: '#','$'
    :return:
    """

    params = {"#": [], "$": [], "@": []}
    for change, pattern in PARAM_PATTERNS.items():
        for param in gen_match_params(pattern, convert_string):
            param_dict = dict()
            param_dict["full_name"] = param
            param = param.replace(change + "{", "").replace("}", "")
            param = param.strip()
            name = param.split(",")[0]
            param_dict["name"] = covert_param_name(name, context)
            param_dict["mock_value"] = f":{param_dict['name']}"
            params[change].append(Param(**param_dict))

    for change, pattern in META_PATTERNS.items():
        for param in gen_match_params(pattern, convert_string):
            param_dict = dict()
            param_dict["full_name"] = param
            param = param.replace("\n", "")
            param = param.replace(change + "{", "").replace("}", "")
            param = param.strip()
            param_dict["name"] = get_meta_id(param)
            param_dict["mock_value"] = param
            params[change].append(Param(**param_dict))

    return params


def basic_convert_parameters(
    convert_string: str, context: SqlContext = None
) -> (str, Dict[str, List[Param]]):
    params = get_params(convert_string, context)
    for param in params["#"]:
        convert_string = convert_string.replace(param.full_name, param.mock_value)
    for param in params["$"]:
        convert_string = covert_literal_parameters(convert_string, param, context)
    return convert_string, params
