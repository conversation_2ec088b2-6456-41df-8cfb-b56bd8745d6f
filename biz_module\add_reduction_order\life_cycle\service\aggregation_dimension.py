import json
import logging
from datetime import datetime
from typing import List

from fastboot.utils.nest import egg
from flask_login import current_user
from pydantic.utils import defaultdict

from biz import BaseService
from biz.api_exception import ParameterException
from biz.utils.image_utils import get_images_name
from ..constants import IsIncludeGroupBuyEnum, LineChartKpiEnum, LifCycleColorEnum
from ..dao import AggregationDimensionDao
from biz_module.add_reduction_order.multiple_product_preview.service.page_config import PageComponentConfigService
from biz_module.add_reduction_order.life_cycle import typedef as td


logger = logging.getLogger(__name__)


@egg
class AggregationDimensionService(BaseService):

    dao: AggregationDimensionDao
    page_config_service: PageComponentConfigService

    org_filter = ["channel_type", "large_region", "org_sk"]
    skc_filter = ["big_class","product_range","product_year_quarter","product_belong_name"
        ,"ps_mid_class","sale_mid_tiny_class","product_name","gender", "real_listing_date", "skc_sk"]

    @classmethod
    def column_order_sql_build(cls, column_orders: List[dict], filed_table_alis_dict):
        order_sqls = []
        error_msg = []
        for column_order in column_orders:
            field_column = filed_table_alis_dict.get(column_order["size_code"])
            if field_column.get("is_calculate"):
                error_msg.append(f'指标:{field_column["cn_name"]}正在计算中，不支持排序')
                continue
            table_alias = field_column.get("table_alias")
            value_type = field_column.get("value_type")
            column_order["value_type"] = value_type
            data_type = field_column.get("data_type")
            column_order["data_type"] = data_type

            if table_alias:
                alias_name = table_alias + "."

            # if column_order["order"].upper() == "DESC":
            #     order_type = " nulls last"
            # else:
            #     order_type = " nulls first"

            order_type = " nulls last"
            column_order["order_type"] = order_type

            column_order["alias_name"] = alias_name
            if data_type in ("numeric", "int"):
                order_sql = (
                    "{alias_name}{size_code}::{data_type} {order} {order_type}"
                )
            elif data_type == "text":
                order_sql = (
                    "convert_to({alias_name}{size_code}, 'GBK') {order} {order_type}"
                )
            else:
                order_sql = "{alias_name}{size_code} {order} {order_type}"
            order_sqls.append(order_sql.format(**column_order))
        if error_msg:
            raise ParameterException("; ".join(error_msg))
        return ",".join(order_sqls)

    @staticmethod
    def add_picture(item: dict):
        """添加图片"""
        if item.get("skc_code"):
            item["image_url"] = get_images_name(item["skc_code"])
        else:
            item["image_url"] = get_images_name(item["_skc_code"])

    def query_aggregation_dimension_filter(self, query_info: td.AggregationDimensionFilterRequest):

        filter_key = query_info.filter_key
        data = []
        # 如果组织筛选 则需要过滤时间
        if filter_key in self.org_filter:
            index = self.org_filter.index(filter_key)
            for i in self.org_filter[index+1:]:
                setattr(query_info, i, None)
            from biz_module.base.authorization.auth_region.service import auth_region_service
            org_sk_list = auth_region_service.list_authorized_region_org_sk()
            query_info.org_sk_list = org_sk_list
            data = self.dao.query_aggregation_dimension_org_filter(query_info)

        elif filter_key in self.skc_filter:
            index = self.skc_filter.index(filter_key)
            # 将index更大的值置为None
            for i in self.skc_filter[index+1:]:
                setattr(query_info, i, None)
            query_info.is_admin = current_user.is_admin
            query_info.user_id = current_user.user_id
            data = self.dao.query_aggregation_dimension_filter(query_info)
        elif filter_key == 'is_include_group_buy':
            data = IsIncludeGroupBuyEnum.return_value_label_list()

        return data

    @classmethod
    def check_filter(cls, query_info: td.AggregationDimensionFilterRequest):
        error = []
        is_include_group_buy = query_info.is_include_group_buy
        if not is_include_group_buy:
            error.append("请选择是否包含团购")
        big_class = query_info.big_class
        if not big_class:
            error.append("请选择大类")
        product_range = query_info.product_range
        if not product_range:
            error.append("请选择品类")
        product_year_r_quarter = query_info.product_year_quarter
        if not product_year_r_quarter:
            error.append("请选择销售年季")
        gender = query_info.gender
        if not gender:
            error.append("请选择性别")
        real_listing_date = query_info.real_listing_date
        # if not real_listing_date:
        #     error.append("请选择上市日期")
        ps_mid_class = query_info.ps_mid_class
        sale_mid_tiny_class = query_info.sale_mid_tiny_class
        product_name = query_info.product_name
        if big_class == '服装' and (not ps_mid_class or not sale_mid_tiny_class):
            error.append("服装必须筛选PS中类/销售中小类")
        elif big_class == '鞋' and not product_name:
            error.append("鞋必须选择款名称")

        if error:
            raise ParameterException("; ".join(error))

    def query_aggregation_dimension_base_kpi(self, query_info: td.AggregationDimensionBaseKpiRequest):

        self.check_filter(query_info)
        # 当季
        if query_info.is_current_quarter:
            if query_info.skc_sk:
                page_name = "base_kpi_current_quarter"
            else:
                page_name = "base_kpi_current_quarter_skc_list"
        else:
            # 历史单款
            if query_info.skc_sk:
                page_name="base_kpi_history_quarter"
            else:
                page_name="base_kpi_history_quarter_skc_list"
        req = td.PageConfigReq(
            module_name="life_cycle",
            page_name=page_name,
            user_id=current_user.user_id,
        )
        (
            header_dict_list,
            table_alis_filed_dict,
            order_by,
            filed_table_alis_dict,
        ) = self.page_config_service.parse_page_config_field(req)
        day_date_set = {
            j.get("code")
            for i in header_dict_list
            for j in i.get("options", {})
            if j.get("data_type") == "timestamp"
        }

        data = self.dao.query_aggregation_dimension_base_kpi(query_info)

        for header_dict in header_dict_list:
            option_list = header_dict.get("options")
            for option_dict in option_list:
                if option_dict["code"] in day_date_set:
                    if data.get(option_dict.get("code")):
                        option_dict["value"] = data.get(
                            option_dict.get("code")
                        ).strftime("%Y-%m-%d")
                else:
                    option_dict["value"] = data.get(option_dict.get("code")) if data else None

        return {"data_list": header_dict_list}

    def query_aggregation_dimension_line_chart(self, query_info: td.AggregationDimensionLineChartRequest):

        self.check_filter(query_info)
        kpi_key = query_info.kpi_key
        dimension = query_info.dimension
        is_current_quarter = query_info.is_current_quarter
        has_skc = 1 if query_info.skc_sk else 0
        line_chart_info = LineChartKpiEnum.get_line_chart_info_by_kpi_key(kpi_key, is_current_quarter, has_skc)
        if not line_chart_info:
            raise ParameterException("请选择正确的指标")

        xAxisData = line_chart_info.get("xAxisData")
        seriesData = line_chart_info.get("seriesData")
        legendData = line_chart_info.get("legendData")
        typeData = line_chart_info.get("typeData")
        data = self.dao.query_aggregation_dimension_line_chart(query_info)
        if not data:
            return {"xAxisData": xAxisData, "seriesData": seriesData, "legendData": legendData, "typeData": typeData, 'backGround': []}
        pre_stage = None
        listing_time = None
        stage_range = defaultdict(dict)
        today = datetime.today()
        iso_year = today.year
        iso_week = today.isocalendar().week
        for i in data:
            listing_time = i.get('listing_time')
            natural_time = i.get('natural_time')
            natural_year = i.get('natural_year')
            if dimension == 'week':
                xAxisData[0].append(f'{listing_time}W')
                xAxisData[1].append(f'{natural_time}W')
            elif dimension == 'month':
                xAxisData[0].append(f'{listing_time}M')
                xAxisData[1].append(f'{natural_time}M')
            for index, value in enumerate(legendData):
                key:str = value.get('key')
                if key.startswith('target_') or (key in ('kpi', 'max_kpi', 'min_kpi')and (iso_year < natural_year or (iso_year == natural_year and iso_week <= natural_time))):
                    seriesData[index].append(i.get(key))
                else:
                    seriesData[index].append(i.get(key) or 0)
            # 处理生命周期
            stage = i.get('stage')
            if pre_stage is None:
                stage_range[stage].update({"begin": listing_time})
            else:
                if pre_stage != stage:
                    stage_range[pre_stage].update({"end": listing_time})
                    stage_range[stage].update({"begin": listing_time})
            pre_stage = stage
        stage_range[pre_stage].update({"end": listing_time})
        backGround = []
        for key, value in stage_range.items():
            backGround.append({
                "name": key,
                "begin": value.get("begin") - 1,
                "end": value.get("end") - 1,
                "color": LifCycleColorEnum.get_color(key)
            })

        return {"xAxisData": xAxisData,
                "seriesData": seriesData,
                "legendData": legendData,
                "typeData": typeData,
                "backGround": backGround}

    def query_aggregation_dimension_list(self, query_info: td.AggregationDimensionListRequest):

        self.check_filter(query_info)
        dimension = query_info.dimension
        dimension = 'W' if dimension == 'week' else 'M'
        is_current_quarter = query_info.is_current_quarter
        data = self.dao.query_aggregation_dimension_list(query_info)

        #  处理数据
        title = [
            # {"name": "", "display": True, "code": "real_listing_date",
            #         "children": [
            #             {"name": "生命周期", "display": True, "code": "kpi_name"},
            #             {"name": "所属阶段", "display": True, "code": "kpi_dim"},
            #         ]
            #         }
            {"code": "kpi_name", "name": "指标", "display": True,},
            {"code": "kpi_dim", "name": "类型"},

        ]
        if is_current_quarter:
            if not query_info.skc_sk:
                result = [
                    {"kpi_name": "店均周销", "kpi_dim": "参照", "kpi_key": 'refer_avg_store_week_sale', "formatter": "0.0"},
                    {"kpi_name": "店均周销", "kpi_dim": "实际", "kpi_key": "avg_store_week_sale", "formatter": "0.0"},
                    {"kpi_name": "周销量", "kpi_dim": "参照", "kpi_key": "refer_sale_qty", "formatter": "0,000"},
                    {"kpi_name": "周销量", "kpi_dim": "实际/模拟", "kpi_key": "sale_qty", "formatter": "0,000"},
                    {"kpi_name": "周流水", "kpi_dim": "参照", "kpi_key": "refer_sale_amt", "formatter": "0,000"},
                    {"kpi_name": "周流水", "kpi_dim": "实际/模拟", "kpi_key": "sale_amt", "formatter": "0,000"},
                    {"kpi_name": "周售罄", "kpi_dim": "参照", "kpi_key": "refer_week_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "周售罄", "kpi_dim": "实际/模拟", "kpi_key": "week_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "累计售罄", "kpi_dim": "目标", "kpi_key": "target_total_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "累计售罄", "kpi_dim": "参照", "kpi_key": "refer_total_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "累计售罄", "kpi_dim": "实际/模拟", "kpi_key": "total_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "铺货门店数", "kpi_dim": "实际", "kpi_key": "has_stock_store_qty", "formatter": "0,000"},
                    {"kpi_name": "折扣", "kpi_dim": "参照", "kpi_key": "refer_discount", "formatter": "0.0%"},
                    {"kpi_name": "折扣", "kpi_dim": "实际", "kpi_key": "discount", "formatter": "0.0%"},
                    {"kpi_name": "温度", "kpi_dim": "最高", "kpi_key": "max_temperature"},
                    {"kpi_name": "温度", "kpi_dim": "最低", "kpi_key": "min_temperature"},
                ]
            else:
                result = [
                    {"kpi_name": "店均周销", "kpi_dim": "参照", "kpi_key": 'refer_avg_store_week_sale',"formatter": "0.0"},
                    {"kpi_name": "店均周销", "kpi_dim": "实际", "kpi_key": "avg_store_week_sale", "formatter": "0.0"},
                    {"kpi_name": "周销量", "kpi_dim": "目标", "kpi_key": "target_sale_qty", "formatter": "0,000"},
                    {"kpi_name": "周销量", "kpi_dim": "参照", "kpi_key": "refer_sale_qty", "formatter": "0,000"},
                    {"kpi_name": "周销量", "kpi_dim": "实际", "kpi_key": "sale_qty", "formatter": "0,000"},
                    {"kpi_name": "周流水", "kpi_dim": "目标", "kpi_key": "target_sale_amt", "formatter": "0,000"},
                    {"kpi_name": "周流水", "kpi_dim": "参照", "kpi_key": "refer_sale_amt", "formatter": "0,000"},
                    {"kpi_name": "周流水", "kpi_dim": "实际", "kpi_key": "sale_amt", "formatter": "0,000"},
                    {"kpi_name": "周售罄", "kpi_dim": "参照", "kpi_key": "refer_week_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "周售罄", "kpi_dim": "实际", "kpi_key": "week_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "累计售罄", "kpi_dim": "目标", "kpi_key": "target_total_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "累计售罄", "kpi_dim": "参照", "kpi_key": "refer_total_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "累计售罄", "kpi_dim": "实际", "kpi_key": "total_sale_out", "formatter": "0.0%"},
                    {"kpi_name": "铺货门店数", "kpi_dim": "参照", "kpi_key": "refer_has_stock_store_qty", "formatter": "0,000"},
                    {"kpi_name": "铺货门店数", "kpi_dim": "实际", "kpi_key": "has_stock_store_qty", "formatter": "0,000"},
                    {"kpi_name": "折扣", "kpi_dim": "参照", "kpi_key": "refer_discount", "formatter": "0.0%"},
                    {"kpi_name": "折扣", "kpi_dim": "实际", "kpi_key": "discount", "formatter": "0.0%"},
                    {"kpi_name": "温度", "kpi_dim": "最高", "kpi_key": "max_temperature"},
                    {"kpi_name": "温度", "kpi_dim": "最低", "kpi_key": "min_temperature"},
                ]
        else:
            result = [
                {"kpi_name": "店均周销", "kpi_dim": "实际", "kpi_key": "avg_store_week_sale", "formatter": "0.0"},
                {"kpi_name": "销量", "kpi_dim": "实际", "kpi_key": "sale_qty", "formatter": "0,000"},
                {"kpi_name": "销量", "kpi_dim": "还原", "kpi_key": "restore_sale_qty", "formatter": "0,000"},
                {"kpi_name": "周流水", "kpi_dim": "实际", "kpi_key": "sale_amt", "formatter": "0,000"},
                {"kpi_name": "周流水", "kpi_dim": "还原", "kpi_key": "restore_sale_amt", "formatter": "0,000"},
                {"kpi_name": "累计售罄", "kpi_dim": "实际", "kpi_key": "total_sale_out", "formatter": "0.0%"},
                {"kpi_name": "累计售罄", "kpi_dim": "还原", "kpi_key": "restore_total_sale_out", "formatter": "0.0%"},
                {"kpi_name": "周售罄", "kpi_dim": "实际", "kpi_key": "week_sale_out", "formatter": "0.0%"},
                {"kpi_name": "周售罄", "kpi_dim": "还原", "kpi_key": "restore_week_sale_out", "formatter": "0.0%"},
                {"kpi_name": "折扣", "kpi_dim": "实际", "kpi_key": "discount", "formatter": "0.0%"},
                {"kpi_name": "铺货门店数", "kpi_dim": "实际", "kpi_key": "has_stock_store_qty", "formatter": "0,000"},
                {"kpi_name": "温度", "kpi_dim": "最高", "kpi_key": "max_temperature"},
                {"kpi_name": "温度", "kpi_dim": "最低", "kpi_key": "min_temperature"},
            ]
        today = datetime.today()
        iso_year = today.year
        iso_week = today.isocalendar().week
        for i in data:
            listing_time = i.get('listing_time')
            stage = i.get('stage')
            natural_time = i.get('natural_time')
            natural_year = i.get('natural_year')
            title.append({
                "name": f'{listing_time}{dimension}',"display": True, "code": f'{listing_time}{dimension}',
                "children": [
                    {"name": stage, "display": True, "code":  f'{listing_time}{dimension}', "align":"right"},
                ]
            })

            for j in result:
                kpi_key  = j.get("kpi_key")
                if kpi_key.startswith('target_'):
                    j[f'{listing_time}{dimension}'] = i.get(kpi_key)
                elif kpi_key.startswith('refer_'):
                    j[f'{listing_time}{dimension}'] = i.get(kpi_key) or 0
                else:
                    if iso_year < natural_year or (iso_year == natural_year and iso_week <= natural_time):
                        j[f'{listing_time}{dimension}'] = i.get(kpi_key)
                    else:
                        j[f'{listing_time}{dimension}'] = i.get(kpi_key) or 0

        return {"title": title, "data": result}

    def query_aggregation_dimension_skc_list(self, query_info: td.AggregationDimensionFilterRequest):
        if query_info.skc_sk:
            raise ParameterException("skc必须为空")
        self.check_filter(query_info)
        req = td.PageConfigReq(
            module_name="life_cycle",
            page_name="skc_list",
            user_id=current_user.user_id,
        )
        (
            header_dict_list,
            table_alis_filed_dict,
            order_by,
            filed_table_alis_dict,
        ) = self.page_config_service.parse_page_config_field(req)
        day_date_set = {
            i.get("code") for i in header_dict_list if i.get("data_type") == "timestamp"
        }
        page_size = query_info.page_size
        page_no = query_info.page_no
        query_info.page_offset = (page_no - 1) * page_size

        column_orders = query_info.sort_filter
        column_orders_str = ""
        if column_orders and column_orders != [{}]:
            column_orders_str = self.column_order_sql_build(
                column_orders, filed_table_alis_dict
            )
        query_info.column_orders_str = (
            column_orders_str if column_orders_str else order_by[0]
        )
        data = self.dao.query_aggregation_dimension_skc_list(query_info)

        total_count = data[0].get('total_count') if data else 0
        for item in data:
            # self.add_picture(item)
            for i in day_date_set:
                if item.get(i):
                    item[i] = item[i].strftime("%Y-%m-%d")
        return {"data_list": data, "header_list": header_dict_list, "page_size": page_size, "page_no": page_no,
                "total_count": total_count}

    def query_aggregation_dimension_refer_skc_list(self, query_info: td.AggregationDimensionFilterRequest):
        if not query_info.skc_sk:
            raise ParameterException("未选择skc")
        self.check_filter(query_info)
        req = td.PageConfigReq(
            module_name="life_cycle",
            page_name="refer_skc",
            user_id=current_user.user_id,
        )
        (
            header_dict_list,
            table_alis_filed_dict,
            order_by,
            filed_table_alis_dict,
        ) = self.page_config_service.parse_page_config_field(req)
        day_date_set = {
            i.get("code") for i in header_dict_list if i.get("data_type") == "timestamp"
        }
        page_size = query_info.page_size
        page_no = query_info.page_no
        query_info.page_offset = (page_no - 1) * page_size

        column_orders = query_info.sort_filter
        column_orders_str = ""
        if column_orders and column_orders != [{}]:
            column_orders_str = self.column_order_sql_build(
                column_orders, filed_table_alis_dict
            )
        query_info.column_orders_str = (
            column_orders_str if column_orders_str else order_by[0]
        )
        data = self.dao.query_aggregation_dimension_refer_skc_list(query_info)

        total_count = data[0].get('total_count') if data else 0
        for item in data:
            # self.add_picture(item)
            for i in day_date_set:
                if item.get(i):
                    item[i] = item[i].strftime("%Y-%m-%d")
        return {"data_list": data, "header_list": header_dict_list, "page_size": page_size, "page_no": page_no,
                "total_count": total_count}




aggregation_dimension_service = AggregationDimensionService()

