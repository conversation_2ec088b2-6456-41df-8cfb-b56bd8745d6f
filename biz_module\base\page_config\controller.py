from flask import request

from biz import http
from flask_login import current_user

from biz.api_exception import ParameterException
from biz.typedef import Result, AnyResult
from biz_module.base.page_config.service.page_config import page_config_service as conf_srv
from biz_module.base.page_config.typedef import page_config as tf_conf
from biz_module.base.page_config.constants import ROLE_PAGE_ID
from biz_module.base.authorization.role.service import role_service


def page_permission_rw_checker(page_id: int, mode: str='w'):
    if not page_id:
        return
    err_msgs = {"w": "无页面可写权限", "r": "无页面可读权限"}
    err_msg = err_msgs.get(mode)
    operation_type = 2 if mode == "w" else 1
    auth_type = role_service.get_current_user_page_permission(page_id)
    if auth_type < operation_type:
        raise ParameterException(err_msg)


class PageConfigController(http.Controller):

    @http.route('/api/v5.2.0/page-config/get-config', methods=['POST'])
    def page_config_by_id(self):
        """
        配置页面配置项列表
        """
        req = tf_conf.GetConfigByIDReq(**request.json)
        result = conf_srv.get_config(req)
        return Result(data=result).dict()

    @http.route('/api/v5.2.0/page-config/list', methods=['POST'])
    def page_config_list(self):
        """
        配置页面配置项列表
        """
        params = tf_conf.GetConfigListReq(**request.json)
        params.user_id = current_user.user_id
        result = conf_srv.get_config_list(params)
        return Result(data=result).dict()

    @http.route('/api/v5.2.0/page-config/dim-values', methods=['POST'])
    def page_config_dim_values(self):
        """
        配置页面配置筛选框 默认值可选列表
        warning: !!modify mapping must todo: synchronize with get_filter_dict_handler PAGE_CONFIG_MAP
        :return:
        """
        req = tf_conf.GetDimValueReq(**request.json)
        result = conf_srv.get_dim_values(req)
        res = {
            'data': result,
            # 有些下拉框的取值完全相同，通过 mapping 将它们映射到数据库的一份值上 todo: synchronize with get_filter_dict_handler
            'mapping': {
                'model_allot_out_up_org_code': 'manager_org',
                'model_allot_in_up_org_code': 'manager_org',
                'human_allot_out_up_org_code': 'manager_org',
                'human_allot_in_up_org_code': 'manager_org',
                'model_allot_out_org_sk': 'org',
                'human_allot_out_org_sk': 'org',
                'model_allot_in_org_sk': 'org',
                'human_allot_in_org_sk': 'org',
                'out_sales_level_code': 'sales_level_code',
                'in_sales_level_code': 'sales_level_code',
                'reserved7': 'store_manager',
                'reserved8': 'store_manager'
            }
        }
        return Result(data=res).dict()

    @http.route('/api/v5.2.0/page-config/save', methods=['POST'])
    def save(self):
        """
        修改
        :return:
            content: id
        """
        req = tf_conf.SaveReq(**request.json)
        page_permission_rw_checker(req.role_page_id)
        result = conf_srv.save(req)
        return Result(data=result).dict()

    @http.route('/api/v5.2.0/page-config/save_for', methods=['POST'])
    def save_for(self):
        """
        修改
        :return:
            content: id
        """
        req = tf_conf.SaveForReq(**request.json)
        page_permission_rw_checker(req.role_page_id)
        result = conf_srv.save_for(req)
        return Result(data=result).dict()

    @http.route('/api/v5.5.0/page-config/delete', methods=['POST'])
    def delete_conf(self):
        """
        删除
        :return:
            content: id
        """
        req = tf_conf.SaveReq(**request.json)
        page_permission_rw_checker(req.role_page_id)
        result = conf_srv.delete_conf(req)
        return AnyResult(data=result).dict()

    @http.route('/api/v5.8.0/page-config/feature/list', methods=['POST'])
    def list_feature_by_dimension_codes(self):
        """
        根据维度编码获取相应特征
        """
        req = tf_conf.ListFeatureReq(**request.json)
        result = conf_srv.list_feature_by_dimension_codes(req)
        return AnyResult(data=result).dict()

    @http.route('/api/v5.8.0/page-config/feature-to-config', methods=['POST'])
    def feature_to_page_config(self):
        """
        根据维度编码获取相应特征
        """
        req = tf_conf.FeatureToConfigReq(**request.json)
        result = conf_srv.feature_to_page_config(req)
        return AnyResult(data=result).dict()
