<?xml version="1.0"?>
<mapper namespace="default">
    <select id="get_page_conf_by_id">
        select * from sys.page_config
        where page_id = ${page_id}
    </select>

    <select id="get_page_conf_list">
        with conf_tenant as (
            select * from sys.page_config
            where config_type='tenant' 
                and module_name=#{module_name}
                and page_name=#{page_name}
                and is_deleted=0
        ), conf_product as (
            select * from sys.page_config
            where config_type='product' 
                and module_name=#{module_name}
                and page_name=#{page_name}
                and is_deleted=0
        ), conf_common as (
            -- 获取普通用户配置时如果有租户配置则能看到租户级别的配置，否则只能看到产品配置
            select * from sys.page_config
            where creator = ${user_id}
                and module_name=#{module_name}
                and page_name=#{page_name}
                and config_type='common'
                and is_deleted=0
            union all
            select * from conf_tenant
            union all
            select * from conf_product where not exists (select 1 from conf_tenant)
        ), conf as(
            select * from sys.page_config where 1 = 2 and is_deleted=0

            <if test="is_common">
                union all
                -- 普通用户配置
                select * from conf_common 
            </if>
                
            <if test="is_tenant">
                -- 租户级别配置
                union all
                select * from conf_tenant
            </if>

            <if test="is_product">
                -- 产品级别配置
                union all
                select * from conf_product
            </if>
        )
        select * from conf
        order by set_type asc, modify_time desc
    </select>

    <select id="check_rep_name">
        <if test="type_code == 0">
            with conf as(
                -- 产品级别配置
                select * from sys.page_config as pc
                where pc.creator isnull
                    and module_name=#{module_name}
                    and page_name=#{page_name}
                    and is_deleted=0

                union all
                -- 租户级别配置
                select c.* from sys.page_config as c
                inner join sys.user as u on c.creator = u.user_id
                where u.type_code = 1
                    and is_deleted=0

                union all
                -- 用户级别配置
                select * from sys.page_config
                where creator = ${creator}
                    and module_name=#{module_name}
                    and page_name=#{page_name}
                    and is_deleted=0
            )
            select count(1) from conf where config_name = #{config_name}
        </if>

        <if test="type_code == 1">
            select count(1) from sys.page_config
            where config_name = #{config_name}
                and module_name=#{module_name}
                and page_name=#{page_name}
                and is_deleted=0
        </if>
    </select>

    <select id="get_ra_decision_dim_values">
        SELECT brand_code, field_name, option_name, option_value, serial_number, 'ra_decision' AS page_name
        FROM biz.rst_ra_filter_dict rrfd
        order by brand_code asc, field_name asc, serial_number asc
    </select>

    <update id="save_config">
        update sys.page_config set
            content = #{content},
            modify_time = now()
        where page_id = ${page_id}
        RETURNING page_id;
    </update>

    <insert id="save_for_config">
        insert into sys.page_config (
            module_name, 
            page_name, 
            content, 
            create_time,
            modify_time, 
            creator, 
            modifier,
            config_name, 
            remark, 
            status, 
            computed_status,
            set_type,
            config_type
        )
        values (
            #{module_name}, 
            #{page_name}, 
            #{content}, 
            now(),
            now(), 
            ${creator},
            ${modifier},
            #{config_name}, 
            #{remark}, 
            1, 
            0, 
            #{set_type},
            #{config_type}
        )
        RETURNING page_id;
    </insert>

    <select id="check_sys_conf">
        select * from sys.page_config
        where creator=${creator}
            and set_type = 'sys_default'
            and page_id != ${page_id}
            and module_name = 'replenish_allot'
            and page_name = 'ra_decision'
            and is_deleted = 0
    </select>

    <update id="delete_conf">
        update sys.page_config
        set is_deleted = 1
        where page_id = ${page_id}
<!--            and module_name = 'replenish_allot'-->
<!--            and page_name = 'ra_decision'-->
            and config_type != 'product'
    </update>

    <select id="get_feature_name_by_code">
        select feature_name
        from dm.dim_feature_define
        where feature_code = #{feature_code}
    </select>

    <select id="get_feature_config_template">
        select feature_template
        from sys.page_config
        where module_name = #{module_name}
            and config_type = 'product'
    </select>
</mapper>
