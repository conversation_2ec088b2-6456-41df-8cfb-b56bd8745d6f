#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2022/10/10 15:05
# <AUTHOR> XXX
# @Site : 
# @File : page_table.py
# @Software: PyCharm
import io
import logging
from collections import OrderedDict
from dataclasses import dataclass
from datetime import datetime
from typing import List, Tuple, Dict, Optional

import numpy as np
import openpyxl
import pandas as pd
import requests
import xlsxwriter
from dacite import from_dict
from fastboot.utils.nest import egg
from openpyxl.reader.excel import load_workbook
from pandas import Series
from xlsxwriter.format import Format
from xlsxwriter.utility import xl_rowcol_to_cell
from xlsxwriter.worksheet import Worksheet

from biz import BaseService, config
from biz.api_exception import ParameterException, ExcelException
from biz.utils.image_utils import ImagesConfig, get_images_name
from biz_module.base.component_excel.constants import ExcelSheetLackError, ExcelErrorEnum, ExcelColLackError
from biz_module.base.component_excel.dao import ExcelConfDao
from biz_module.base.component_excel.model.model import ExcelConfig, ExcelColumn, ExcelSheet, ExcelComp, ExcelSheetComp
from biz_module.base.component_excel.service.excel_format import ExcelFmt
from biz_module.base.component_excel.typedef import ExcelColumnReq, ExcelSheetReq, \
    SaveAsExcelSheetConfReq, SaveAsExcelBookConfReq, SaveAsExcelColumnsConfReq, ExcelConfIdentReq, ExcelColumnUpdateReq, \
    ExcelSheetUpdateReq
from biz_module.config_center.base.excel_validator import InvalidTableFormatError

logger = logging.getLogger(__name__)

"""
WARNING: 使用须知
此版本为实现尺码组合并为一行，并逐行写入数据。 部分样式原始配置失效。

"""


def _log_excel_sheet_names(file_object: io.BytesIO):
    """
    文件中的 sheet 页名称
    """
    file_object.seek(0)
    wb = openpyxl.load_workbook(file_object, read_only=True)
    sheet_names = wb.sheetnames
    logger.info("导入文件共{}个sheet页".format(len(sheet_names)))


@dataclass
class FileNameFmt:
    ori_name: str
    fmt: Optional[str] = ''
    suffix: Optional[str] = ''
    unique: Optional[str] = ''

    FILE_NAME_FMT = '{filename}-{unique}{suffix}'
    SUFFIX = '.xlsx'

    def __post_init__(self):
        if self.SUFFIX in self.ori_name:
            self.ori_name = self.ori_name.rstrip(self.SUFFIX)

        if not self.fmt:
            self.fmt = self.FILE_NAME_FMT
        if not self.suffix:
            self.suffix = self.SUFFIX
        if not self.unique:
            #  yymmddhhmm unique 只到分。
            self.unique = datetime.now().strftime('%y%m%d%H%M%S%f')[:-4]

    def __str__(self):
        return self.fmt.format(filename=self.ori_name, unique=self.unique, suffix=self.suffix)

    @property
    def new_name(self):
        return str(self)


@dataclass
class FileNameTypeFmt:
    ori_name: str
    fmt: Optional[str] = ''
    suffix: Optional[str] = ''
    unique: Optional[str] = ''
    type_: Optional[str] = ''
    sub_name: Optional[str] = ''

    FILE_NAME_FMT = '{filename}_{unique}{suffix}'
    SUFFIX = '.xlsx'

    def __post_init__(self):
        if self.SUFFIX in self.ori_name:
            self.ori_name = self.ori_name.rstrip(self.SUFFIX)

        if self.sub_name:
            self.ori_name = f"{self.ori_name}_{self.sub_name}"

        if not self.fmt:
            self.fmt = self.FILE_NAME_FMT
        if not self.suffix:
            self.suffix = self.SUFFIX
        if not self.unique:
            self.unique = datetime.now().strftime('%y%m%d%H%M')

    def __str__(self):
        return self.fmt.format(filename=self.ori_name, unique=self.unique, suffix=self.suffix)

    @property
    def new_name(self):
        return str(self)


@egg
class ExcelExportService(BaseService):
    excel_conf_dao: ExcelConfDao

    def _get_excel_conf(self, conf_req: Dict) -> ExcelComp:
        """查询 excel book 配置

        Args:
            conf_req:
                module_code
                conf_code
                user_id
                conf_alias
                expect_sheets
                conf_code
        Return:
            ExcelBookConfPO
        Raise:
            ParameterException

        """
        excel_sheet_comp = [ExcelSheetComp(**i) for i in conf_req.get('excel_worksheets')]
        book = ExcelComp(
            book_id=conf_req.get('comp_id'),
            book_name=conf_req.get('comp_name'),
            sheets=excel_sheet_comp
        )
        return book

    def _get_columns_by_sheet_id(self, sheet_id) -> List[ExcelColumn]:
        """根据 sheet_id 获取 columns
        """
        columns = self.excel_conf_dao.get_sheet_cols(ExcelColumnReq(sheet_id=sheet_id))
        column_list = [from_dict(data_class=ExcelColumn, data=col.dict()) for col in columns]
        return column_list

    def _get_sheet_list(self, book_id: int) -> List[ExcelSheet]:
        """
        根据 excel book 表格 查询 sheet_id
        Args:
            book_id
        Return:
            List[ExcelSheetConfPO]
        Raise:
            ParameterException
        """
        sheets = self.excel_conf_dao.get_excel_sheets(ExcelSheetReq(book_id=book_id))
        sheet_list = []
        for s in sheets:
            data = s.dict()
            columns = self._get_columns_by_sheet_id(s.id)
            data['columns'] = columns
            sheet = from_dict(data_class=ExcelSheet, data=data)

            sheet_list.append(sheet)

        return sheet_list

    def build_excel_obj(self, config_params: Dict) -> ExcelComp:
        """构建 excel 配置对象。

        """

        book = self._get_excel_conf(config_params)
        return book

    def read_excel_ignore_hidden_columns(self, filename):
        # 使用 openpyxl 加载 Excel 文件
        wb = load_workbook(filename, data_only=True)  # data_only=True 为了只获取数据而不是公式
        ws = wb.active

        # 检查哪些列是隐藏的
        hidden_cols_dict = {col_idx: col_dim for col_idx, col_dim in enumerate(ws.column_dimensions.values()) if
                            col_dim.hidden}

        # 使用 pandas 读取 Excel 文件
        df = pd.read_excel(filename, engine='openpyxl')

        # 删除被隐藏的列
        df.drop(df.columns[hidden_cols_dict.keys()], axis=1, inplace=True)

        return df

    def parse_file_by_conf(self, file: io.BytesIO, book_conf: ExcelComp, ffill_flag=True) -> List[pd.DataFrame]:
        """
        2024.5.31 乐林峰： 增加ffill_flag，原先默认ffill

        """

        errors = OrderedDict()
        df_list = list()

        for sheet_conf in book_conf.sheets:
            sheet_name = sheet_conf.name
            try:
                file.seek(0)
                df = pd.read_excel(file, sheet_name=sheet_name, dtype=str, keep_default_na=True, header=None)
                # df = self.read_excel_ignore_hidden_columns(file)

            except ValueError as e:
                try:
                    logger.exception(e)
                    _log_excel_sheet_names(file)
                except Exception as e:
                    raise InvalidTableFormatError from e
                error = ExcelSheetLackError(file_name=book_conf.book_name, sheet_name=sheet_conf.name)
                errors[ExcelErrorEnum.sheet_name_error] = error
                continue
            except Exception as e:
                logger.exception(msg=e)
                raise InvalidTableFormatError from e

            # 查询首行表头是否有重复值
            filtered_row = df.iloc[0]
            duplicates = filtered_row[filtered_row.duplicated(keep=False)].dropna()
            if not duplicates.empty:
                dup_list = duplicates.drop_duplicates().to_list()
                raise ParameterException(msg=f'表头有重复列{dup_list}')

            df = df.applymap(lambda x: x.strip() if isinstance(x, str) else x)
            # 全空的列
            df.replace(['', None], np.nan, inplace=True)
            df.drop(columns=df.columns[df.isna().all()], inplace=True)
            # 使用 dropna() 方法删除包含空值的行
            df = df.dropna(how='all')
            if df.empty:
                raise ParameterException(msg=f'无有效数据')
            current_cols = df.iloc[0].to_list()
            must_cols = sheet_conf.columns_dict.keys()
            # lack_cols = must_cols - current_cols
            lack_cols = [must_col for must_col in must_cols if must_col not in current_cols]
            if lack_cols:
                error = ExcelColLackError(file_name=book_conf.book_name,
                                          sheet_name=sheet_conf.name, error=lack_cols)
                errors[ExcelErrorEnum.lack_cols] = error
                self.deal_with_err(book_conf.book_name, errors)

            df.iloc[0] = df.iloc[0].replace(sheet_conf.columns_dict)
            if ffill_flag:
                df.iloc[0] = df.iloc[0].replace(r'^\s*$', np.nan, regex=True).ffill()
            else:
                df.iloc[0] = df.iloc[0].replace(r'^\s*$', np.nan, regex=True)
            # 当前不支持多行表头
            cols = pd.MultiIndex.from_frame(df.iloc[0:1].T)
            df.columns = cols
            cols = df.columns
            unique_cols = cols[~cols.duplicated(keep='last')]
            df = df[unique_cols]
            df = df.drop(index=[i for i in range(0, 1)])
            df = df[sheet_conf.columns_dict.values()]
            df = df.fillna('').applymap(str.strip)

            df_list.append(df)
        self.deal_with_err(book_conf.book_name, errors)
        return df_list

    def excel_value_map(self, df, cols):
        for i in cols:
            map_ = i.other.get("excel_value_mapping", {})
            if map_:
                df[i.code] = df[i.code].apply(lambda x: map_.get(x))

    def size_title_df(self):
        return self._size_group_title_df()

    def max_depth(self, _columns):
        if not _columns:
            return 0
        max_depth = 0
        for item in _columns:
            if item.children:
                child_depth = self.max_depth(item.children) + 1
                max_depth = max(max_depth, child_depth)
        return max_depth

    def generate_file_by_conf(self, file: io.BytesIO,
                              data_list: List[pd.DataFrame],
                              excel_conf: ExcelComp,
                              template=True
                              ) -> str:
        """根据配置生成 excel 文件
        Args:
            file: file output
            data_list: len(data) should equal worksheet config of this workbook.
            excel_conf:
        Return:
            str: Excel file_name
        """
        # workbook级
        logger.info(msg=f'开始生成文件')
        wb = xlsxwriter.Workbook(file)
        wb_fmt_dict = self.add_workbook_header_fmt(wb)
        [item_df.fillna('', inplace=True) for item_df in data_list if isinstance(item_df, pd.DataFrame)]

        for idx, sheet_conf in enumerate(excel_conf.sheets):
            if idx >= len(data_list):
                continue
            logger.info(msg=f'开始写入sheet1{sheet_conf.alias}')
            ws = wb.add_worksheet(sheet_conf.alias)
            data_df = data_list[idx]
            columns = sheet_conf.columns
            row_height = sheet_conf.other.get('row_height', 24)
            only_import_col = sheet_conf.other.get('only_import_col')
            if template and only_import_col:
                columns = [col for col in columns if col.template_col]

            seq = 0
            xl_no_dict = {}  # 只存储叶子节点的列位置
            max_header_depth = self.max_depth(columns)

            
            logger.info(msg=f'写入表头')
            scope_fmt_list = []
            
            last_col = None
            for col in columns:
                if not col.display or (not template and col.data_source_only):
                    continue
                cell_fmt, col_fmt = self.get_fmt_of_header(col, wb_fmt_dict)
                if "cell_fmt" in col.header_fmt.keys():
                    cell_fmt = wb.add_format(col.header_fmt.get("cell_fmt"))
                if "col_fmt" in col.header_fmt.keys():
                    col_fmt = wb.add_format(col.header_fmt.get("col_fmt"))
                
                next_seq, depth = self.write_header(
                    col, ws, seq, cell_fmt, col_fmt, 
                    xl_no_dict, scope_fmt_list, last_col, 0, max_header_depth
                )
                max_header_depth = max(max_header_depth, depth)
                seq = next_seq
                last_col = col
            
            # 设置所有表头行的高度
            header_height = sheet_conf.other.get('header_height', 28)
            for row in range(max_header_depth + 1):
                ws.set_row(row, header_height)
            
            # 数据起始行号要考虑表头深度
            row_no = max_header_depth + 1
            if sheet_conf.contain_size:
                row_no += 1
            
            self.freeze_panes(ws, row_no, sheet_conf.freeze_col, xl_no_dict)
            
            # 行数据相关逻辑
            logger.info(msg=f'转换列数据类型')
            for col in columns:
                # 当前仅有 date 类型指定为 datetime 的 yyyy-mm-dd 字段， 简化其日期处理逻辑
                if col.code in data_df.columns and col.value_type == 'list':
                    data_df[col.code] = data_df[col.code].apply(
                        lambda x: '\n'.join([col.value_fmt.format(**i) for i in x]) if isinstance(x, list) else x)

            # write row data
            # 通过尺码组列顺序写入 不再需要 size_name , size_code 列
            unique_columns = [col.code for col in columns if col.unique_key]
            # 兼容空列导致的分组问题
            for uc in unique_columns:
                if uc not in data_df.columns:
                    data_df[uc] = ''

            row_fmt = sheet_conf.other.get('row_fmt', {"border_color": "#BFC4CA", "border": True})
            fmt = wb.add_format(row_fmt)
            fmt.set_border_color(row_fmt.get('border_color', '#BFC4CA'))
            fmt.set_align('center')
            fmt.set_align('vcenter')
            text_wrap = row_fmt.get("text_wrap", False)
            fmt.set_text_wrap(text_wrap)

            logger.info(msg=f'开始写入行数据')

            # 获取所有叶子节点列
            leaf_columns = self.get_leaf_columns(columns)
            cell_fmt_dict = self.add_workbook_col_format(wb, leaf_columns)
            
            # 创建叶子节点列的映射
            leaf_col_mapping = {col.code: col for col in leaf_columns}
            
            for index, row in data_df.iterrows():
                item = row.to_dict()
                row_vals = {}
                
                # 只处理叶子节点列的数据
                for col in leaf_columns:
                    if not col.display or (not template and col.data_source_only):
                        continue
                        
                    cell_data = item.get(col.code)
                    if cell_data is not None:
                        cell_data = col.excel_map_val(cell_data)
                        if col.value_type == 'number':
                            try:
                                cell_data = float(cell_data)
                            except (ValueError, TypeError):
                                pass
                        row_vals[col.code] = cell_data
                
                # 按照xl_no_dict的顺序写入数据
                for col_code, col_idx in sorted(xl_no_dict.items(), key=lambda x: x[1]):
                    if col_code in row_vals:
                        col = leaf_col_mapping.get(col_code)
                        if col:
                            cell_fmt = cell_fmt_dict.get(col.value_fmt, fmt)
                            ws.write(row_no, col_idx, row_vals[col_code], cell_fmt)
                
                ws.set_row(row_no, row_height)

                row_no += 1

            ws.hide_zero()

        logger.info(msg=f'结束写入数据')
        wb.close()
        file.seek(0)
        return excel_conf.book_name if not template else f"{excel_conf.book_name}导入模板"

    def generate_excel(self, file: io.BytesIO, data_list: List[pd.DataFrame], 
                      excel_conf: ExcelConfig, template: bool = True) -> str:
        """生成Excel文件
        
        Args:
            file: io.BytesIO Excel文件对象
            data_list: List[pd.DataFrame] 数据列表
            excel_conf: ExcelConfig Excel配置
            template: bool 是否生成模板
        Return:
            str: 文件名
        """
        if template:
            filename = self.generate_file_by_conf(file, data_list, excel_conf)
        else: 
            filename = self.generate_file_by_conf(file, data_list, excel_conf, template=False)
        file.seek(0)
        file_name = str(FileNameFmt(ori_name=filename))
        return file_name

    @staticmethod
    def write_img(ws, columns, xl_no_dict, skc_code_row_no):
        for col in columns:
            if col.display and col.value_type == 'image':
                xl_col_no = xl_no_dict[col.code]
                for skc_row_no, skc_code in skc_code_row_no.items():
                    url = get_images_name(skc_code)
                    response = None
                    try:
                        if url:
                            response = requests.get(url)
                    except Exception as e:
                        logger.error(msg=f'{url} 路径访问异常')
                        raise ParameterException(msg=f'配置错误 {url} 不是一个可访问路径')
                    if response and response.status_code == 200:
                        image_data = io.BytesIO(response.content)
                        from PIL import Image
                        image_file = Image.open(image_data)
                        x_s = 32
                        y_s = 32
                        out_xy = image_file.resize((x_s, y_s), Image.ANTIALIAS)
                        image_data_byte = io.BytesIO()
                        out_xy.save(image_data_byte, format='png')
                        out_xy.close()
                        options = {
                            'x_offset': 1,
                            'y_offset': 1,
                            # 'x_scale': 0.5,
                            # 'y_scale': 0.5,
                            'url': url,
                            'tip': None,
                            'image_data': image_data_byte,
                            'positioning': None,
                        }
                        # 当 图片为 80 px 行高为 60 y轴偏移 1 视觉居中
                        ws.set_row(skc_row_no, 24)
                        cell_loc = xl_rowcol_to_cell(skc_row_no, xl_col_no)
                        ws.insert_image(cell_loc, f'{col.code}.{ImagesConfig.OBS_SKC_IMAGE_SUFFIX}', options)

    @staticmethod
    def get_fmt_of_header(col, wb_fmt_dict) -> Tuple:
        """
        Doct fmt_name form self.add_workbook_header_fmt result keys.
        """
        fmt_name = 'import_cell_fmt' if col.is_import else 'cell_fmt'
        cell_fmt = wb_fmt_dict.get(fmt_name)
        col_fmt_name = 'hidden_col_fmt' if col.hidden else 'col_fmt'
        col_fmt = wb_fmt_dict.get(col_fmt_name)

        return cell_fmt, col_fmt

    @staticmethod
    def add_workbook_header_fmt(wb) -> Dict:
        grey_color = '#BFC4CA'
        import_cell_fmt = wb.add_format(ExcelFmt.header_fmt_cel_dict)
        import_cell_fmt.set_bg_color(ExcelFmt.RED_BG)
        import_cell_fmt.set_border_color(grey_color)

        cell_fmt = wb.add_format(ExcelFmt.header_fmt_cel_dict)
        cell_fmt.set_border_color(grey_color)

        col_fmt = wb.add_format(ExcelFmt.header_fmt_dict)
        hidden_col_fmt = wb.add_format(ExcelFmt.header_fmt_dict)
        hidden_col_fmt.set_hidden()

        fmt_dict = dict()
        fmt_dict['import_cell_fmt'] = import_cell_fmt
        fmt_dict['cell_fmt'] = cell_fmt
        fmt_dict['col_fmt'] = col_fmt
        fmt_dict['hidden_col_fmt'] = hidden_col_fmt

        return fmt_dict

    @staticmethod
    def add_workbook_col_format(wb, columns: List[ExcelColumn]) -> Dict:
        """"""
        res = dict()
        for col in columns:
            value_fmt = wb.add_format()
            value_fmt.set_num_format(col.value_fmt)
            if col.value_type == 'text':
                value_fmt.set_align('center')
            value_fmt.set_align('vcenter')

            row_fmt = col.row_fmt
            text_wrap = row_fmt.get("text_wrap", False)
            value_fmt.set_text_wrap(text_wrap)
            res[col.value_fmt] = value_fmt
        return res

    @staticmethod
    def freeze_panes(ws, row_no, col_code, xl_no_dict):
        """冻结表头和指定冻结列

        Args:
            ws: WorkSheet
            row_no: row number.
            col_code: the code of column to be frozen .
            xl_no_dict: col number dict
        Return:
        """
        ws.freeze_panes(row_no, xl_no_dict.get(col_code, -1) + 1)

    def trans_size_data_to_list(self, col: ExcelColumn, items: pd.DataFrame, xl_cols: pd.DataFrame, size_title_df):
        """转换尺码列 行数据 为 list
        Docs:
            1. 提取尺码和行数据列

        Args:
            col: ExcelColumn
            items: grouped data by unique columns.
            xl_cols: grouped data by unique columns.
            size_title_df:
        Return:
            List[Union[pd.nan, '']
        Raise:
            Unknown yet.
        """

        vals = items[['sku_sk', col.code]]
        if isinstance(xl_cols, Series):
            xl_cols = xl_cols.to_frame().T
        vals = vals.merge(xl_cols, how='right', on=['sku_sk']).fillna(0)
        vals = vals.merge(size_title_df, how='right', on='size_title_order')
        vals = vals.where(vals.notnull(), None)
        vals = vals.sort_values(by=['size_title_order'])

        qty_list = vals[col.code].to_list()
        return qty_list

    def write_header(self, col: ExcelColumn, ws: Worksheet, first: int,
                     cell_fmt: Format, col_fmt: Format, xl_no_dict: dict,
                     scope_fmt_list=None, last_col=None, current_row=0, max_depth=0) -> Tuple[int, int]:
        """写入表头数据，支持多级表头

        Args:
            col: ExcelColumn 列配置数据
            ws: WorkSheet of WorkBook
            first: first columns to be written
            cell_fmt: 单元格样式
            col_fmt: WorkSheet 列样式
            xl_no_dict: 记录叶子节点列的位置映射
            scope_fmt_list: scope_fmt_list
            last_col: 前一列配置
            current_row: 当前处理的行号(表头深度)
            max_depth: 最大表头深度
        Return:
            Tuple[int, int]: (next_col, max_depth) 下一列序号和最大深度
        """
        last = first
        options = {}
        if last_col and last_col.fold:
            options = {"collapsed": last_col.fold}
        if col.fold:
            options = {"hidden": col.fold, "level": 1}
        if col.hidden:
            options = {"hidden": col.hidden}

        ws.set_column(first_col=first, last_col=last, width=col.width, cell_format=col_fmt, options=options)

        if hasattr(col, 'children') and col.children:
            # 计算当前列占用的总列数
            child_cols = 0
            max_child_depth = current_row + 1
            
            # 先遍历一遍计算总宽度和最大深度
            for child in col.children:
                child_width, child_depth = self.write_header(
                    child, ws, first + child_cols, cell_fmt, col_fmt, 
                    xl_no_dict, scope_fmt_list, last_col, current_row + 1, max_depth
                )
                child_cols += (child_width - (first + child_cols))
                max_child_depth = max(max_child_depth, child_depth)
            
            # 合并当前层级的单元格
            if child_cols > 1:
                ws.merge_range(current_row, first, current_row, first + child_cols - 1, col.alias, cell_fmt)
            else:
                ws.write(current_row, first, col.alias, cell_fmt)
                
            if col.annotation:
                ws.write_comment(current_row, first, col.annotation)
                
            return first + child_cols, max_child_depth
        else:
            # 叶子节点，写入数据并合并到最底层
            if max_depth > current_row:
                ws.merge_range(current_row, first, max_depth, first, col.alias, cell_fmt)
            else:
                ws.write(current_row, first, col.alias, cell_fmt)
                
            if col.annotation:
                ws.write_comment(current_row, first, col.annotation)
                
            # 只在叶子节点记录列位置    
            xl_no_dict[col.code] = first
                
            return first + 1, max(current_row, max_depth)

    @staticmethod
    def deal_with_err(filename='', errors: OrderedDict = None):
        msg = f'{filename} 文件未通过校验: \n\n'
        if errors:
            for i in errors.values():
                msg += f'\n{i}'
            raise ExcelException(msg=msg)

    @staticmethod
    def write_size_group_code(ws, size_title_list: List[str], xl_no_dict, fmt_dict: Dict = None) -> int:
        """写入尺码列的 size_group_code, 同时标记 表头行
        Args:
            ws: WorkSheet
            size_title_list: List[str]
            xl_no_dict: col number dict.
            fmt_dict: format of workbook have.
        Doct:
            import_cell_fmt: from self.add_workbook_header_fmt special key.
            row_no: default begin second line.
        """
        row_no = 1
        col_no = xl_no_dict.get('size_group_code')
        if not col_no:
            return row_no
        fmt = fmt_dict.get('import_cell_fmt')
        for size_name in size_title_list:
            ws.write(row_no, col_no, size_name, fmt)
            row_no += 1
        return row_no

    def save_as_excel_config(self, req: SaveAsExcelBookConfReq) -> int:
        sheet_ids = self.excel_conf_dao.get_sheets_by_book(req.book_id)
        if len(sheet_ids) == 0:
            return req.book_id
        book_seq = f"nextval('tenant_{config.TENANT_CODE}_biz.base_excel_book_conf_id_seq')"
        sheet_seqs = [f"nextval('tenant_{config.TENANT_CODE}_biz.base_excel_sheet_conf_id_seq')"] * len(sheet_ids)
        sheet_seqs = f"array[{','.join(sheet_seqs)}]"
        book_sheet_list = self.excel_conf_dao.generate_ids(book_seq, sheet_seqs)
        req.target_book_id = book_sheet_list[0].get("book_id")
        sheet_list = []
        for index, sheet_id in enumerate(sheet_ids):
            target_sheet_id = book_sheet_list[index].get("sheet_id")
            sheet_params = SaveAsExcelSheetConfReq(
                ori_book_id=req.book_id,
                target_book_id=req.target_book_id,
                category=req.category,
                user_id=req.user_id,
                ori_sheet_id=sheet_id,
                target_sheet_id=target_sheet_id,
            )
            sheet_list.append(sheet_params)
        self.excel_conf_dao.save_as_conf(sheet_list)
        return req.target_book_id

    def update_excel_config(self, sheet_req: ExcelSheetUpdateReq, columns_req: List[ExcelColumnUpdateReq]):
        """"""

        self.update_sheet_config(sheet_req)
        self.batch_update_column_config(columns_req)

    def update_sheet_config(self, req: ExcelSheetUpdateReq):
        self.excel_conf_dao.update_sheet_conf(req)

    def batch_update_column_config(self, req: List[ExcelColumnUpdateReq]):
        self.excel_conf_dao.batch_update_sheet_conf(req)

    def parse_multi_level_file_by_conf(self, file: io.BytesIO, book_conf: ExcelConfig) -> List[pd.DataFrame]:
        """
        多级表头数据解析

        """

        errors = OrderedDict()
        df_list = list()
        for sheet_conf in book_conf.sheets:
            sheet_name = sheet_conf.name
            try:
                file.seek(0)
                df = pd.read_excel(file, sheet_name=sheet_name, dtype=str, keep_default_na=True, header=None)

            except ValueError as e:
                try:
                    _log_excel_sheet_names(file)
                except Exception as e:
                    raise InvalidTableFormatError from e
                error = ExcelSheetLackError(file_name=book_conf.book_name, sheet_name=sheet_conf.name)
                errors[ExcelErrorEnum.sheet_name_error] = error
                continue
            except Exception as e:
                logger.error(msg=e)
                raise InvalidTableFormatError from e
            df = df.applymap(lambda x: x.strip() if isinstance(x, str) else x)
            # 全空的列
            df.replace(['', None], np.nan, inplace=True)
            df.drop(columns=df.columns[df.isna().all()], inplace=True)
            # 使用 dropna() 方法删除包含空值的行
            df = df.dropna(how='all')
            if df.empty:
                raise ParameterException(msg=f'无有效数据')
            current_cols = df.iloc[0].to_list()
            level_cols = df.iloc[1].drop_duplicates().to_list()
            must_cols = sheet_conf.columns_dict.keys()
            multi_level_must_cols = sheet_conf.level_columns_dict.keys()
            lack_cols = must_cols - current_cols
            multi_lack_cols = multi_level_must_cols - level_cols
            lack_cols = set(list(lack_cols) + list(multi_lack_cols))
            if lack_cols:
                error = ExcelColLackError(file_name=book_conf.book_name,
                                          sheet_name=sheet_conf.name, error=lack_cols)
                errors[ExcelErrorEnum.lack_cols] = error
                self.deal_with_err(book_conf.book_name, errors)

            df.iloc[0] = df.iloc[0].replace(sheet_conf.columns_dict)
            df.iloc[1] = df.iloc[1].replace(sheet_conf.level_columns_dict)
            df.iloc[0] = df.iloc[0].replace(r'^\s*$', np.nan, regex=True).ffill()

            cond = df.iloc[1].apply(
                lambda x: x in sheet_conf.level_columns_dict.values()
            ) | df.iloc[0].apply(
                lambda x: x in sheet_conf.columns_dict.values()
            )
            df = df.loc[:, cond]

            cols = pd.MultiIndex.from_frame(df.iloc[0:2].T)
            df.columns = cols
            cols = df.columns
            unique_cols = cols[~cols.duplicated(keep='last')]
            df = df[unique_cols]
            df = df.drop(index=[i for i in range(0, 2)])
            df = df.fillna('').applymap(str.strip)
            df_list.append(df)
        self.deal_with_err(book_conf.book_name, errors)
        return df_list

    def get_leaf_columns(self, columns: List[ExcelColumn]) -> List[ExcelColumn]:
        """获取所有叶子节点列"""
        leaf_columns = []
        for col in columns:
            if hasattr(col, 'children') and col.children:
                leaf_columns.extend(self.get_leaf_columns(col.children))
            else:
                leaf_columns.append(col)
        return leaf_columns

    def get_template_columns(self, columns: List[ExcelColumn]) -> List[ExcelColumn]:
        """获取所有标记为leaf=True的列，不递归展开children
        
        Args:
            columns: List[ExcelColumn] 列配置数据列表
        Return:
            List[ExcelColumn]: 所有leaf=True的列
        """
        return [col for col in columns if getattr(col, 'template_col', False)]

    def write_template_header(self, columns: List[ExcelColumn], ws: Worksheet,
                             cell_fmt: Format, col_fmt: Format) -> int:
        """写入模板表头（平铺结构）
        
        Args:
            columns: List[ExcelColumn] 列配置数据列表
            ws: WorkSheet of WorkBook
            cell_fmt: 单元格样式
            col_fmt: WorkSheet 列样式
        Return:
            int: next_col 最后一列的序号
        """
        # 获取所有leaf=True的列
        leaf_columns = self.get_template_columns(columns)
        
        for idx, col in enumerate(leaf_columns):
            # 设置列样式
            options = {}
            if col.fold:
                options = {"hidden": col.fold, "level": 1}
            if col.hidden:
                options = {"hidden": col.hidden}
            
            ws.set_column(first_col=idx, last_col=idx, width=col.width, 
                         cell_format=col_fmt, options=options)

            # 写入表头
            ws.write(0, idx, col.alias, cell_fmt)
            if col.annotation:
                ws.write_comment(0, idx, col.annotation)
        
        return len(leaf_columns)

    def generate_template(self, file: io.BytesIO, excel_conf: ExcelConfig) -> str:
        """生成Excel导入模板，仅写入表头
        
        Args:
            file: io.BytesIO Excel文件对象
            excel_conf: ExcelConfig Excel配置
        Return:
            str: 文件名
        """
        wb = xlsxwriter.Workbook(file, {'constant_memory': True})
        wb_fmt_dict = self.add_workbook_header_fmt(wb)
        
        for idx, sheet_conf in enumerate(excel_conf.sheets):
            ws = wb.add_worksheet(sheet_conf.name)
            columns = sheet_conf.columns
            
            logger.info(msg=f'写入模板表头')
            
            # 获取默认格式
            cell_fmt, col_fmt = self.get_fmt_of_header(columns[0], wb_fmt_dict)
            
            # 写入表头
            col_count = self.write_template_header(columns, ws, cell_fmt, col_fmt)
            
            # 设置表头行高
            header_height = sheet_conf.other.get('header_height', 28)
            ws.set_row(0, header_height)
            
            # 设置冻结窗格（使用实际列数）
            freeze_col = min(sheet_conf.freeze_col, col_count) if sheet_conf.freeze_col else 0
            ws.freeze_panes(1, freeze_col)
        
        wb.close()
        return f"{excel_conf.book_name}导入模板"


excel_service = ExcelExportService()
