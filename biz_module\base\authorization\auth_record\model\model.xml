<?xml version="1.0"?>
<mapper namespace="default">

    <sql id="auth_filter_and_in_all">
        <if test="not _is_admin">

        and ${region_field} in (
            select
                org_sk
            from biz.base_region_auth_mapping
            where auth_record_id = #{_auth_record_id}
              and way = #{region_auth_way}
              and (#{operating_unit_sk} is null or operating_unit_sk = #{operating_unit_sk})
        )

        </if>
    </sql>

    <sql id="auth_filter_by_belong_open">

        <if test="not _is_admin">
        and (
            ${belong_field} in (
                select
                    org_sk
                from biz.base_region_auth_mapping
                where auth_record_id = #{_auth_record_id}
                  and way = 'down'
                  and (#{operating_unit_sk} is null or operating_unit_sk = #{operating_unit_sk})
            ) or
            ${open_field} in (
                select
                    org_sk
                from biz.base_region_auth_mapping
                where auth_record_id = #{_auth_record_id}
                  and way = 'up'
                  and (#{operating_unit_sk} is null or operating_unit_sk = #{operating_unit_sk})
            )
        )
        </if>
    </sql>

    <sql id="auth_sql_inner_join_snap" >
        <if test="not _is_admin">
        inner join biz.base_region_auth_mapping ramv
            on ramv.org_sk = ${region_field}
            and ramv.operating_unit_sk = ${operating_unit_field}
            where auth_record_id = #{_auth_record_id}
            <if test="way_field_value">
            and way = #{way_field_value}
            </if>
        </if>

    </sql>

    <select id="find_auth_record_by_id">
        select
            id,
            role_id,
            user_id,
            begin_at,
            expire_at,
            is_default,
            is_deleted,
            create_time,
            modify_time,
            creator_id,
            modifier_id
        from biz.base_auth_record
        where is_deleted = 0
        and id = #{auth_record_id}
    </select>

    <select id="find_auth_record_by_user_and_role">
        select id
        from biz.base_auth_record
        where is_deleted = 0
        and user_id = #{user_id}
        and role_id = #{role_id}
    </select>

    <select id="find_auth_record">
        select id
        from biz.base_auth_record
        where is_deleted = 0
    </select>

    <select id="find_auth_record_by_role_users">
        select id
        from biz.base_auth_record
        where is_deleted = 0
        and user_id in ${user_ids}
        and role_id = #{role_id}

    </select>

    <select id="find_auth_record_by_ids">
        select
            id,
            role_id,
            user_id,
            begin_at,
            expire_at,
            is_default,
            is_deleted,
            create_time,
            modify_time,
            creator_id,
            modifier_id
        from biz.base_auth_record
        where is_deleted = 0
        and user_id = #{user_id}
        and id in ${auth_record_ids}
    </select>

    <select id="list_user_auth_record">
        select
        id,
        role_id,
        user_id
        from biz.auth_record
        where is_deleted = 0
        and user_id = #{user_id}
    </select>

    <select id="list_by_user_id">
        <![CDATA[
        select
            id,
            role_id,
            user_id,
            begin_at,
            expire_at,
            is_default,
            is_deleted,
            create_time,
            modify_time,
            creator_id,
            modifier_id
        from biz.base_auth_record
        where is_deleted = 0
        and user_id = #{user_id}
        order by id asc
        ]]>
    </select>

        <select id="find_default_by_user_id">
        <![CDATA[
        select
            id,
            role_id,
            user_id,
            begin_at,
            expire_at,
            is_default,
            is_deleted
        from biz.base_auth_record
        where is_deleted = 0
        and is_default = 1
        and user_id = #{user_id}
        order by id asc
        limit 1
        ]]>
    </select>

    <insert id="save_auth_record">
        INSERT INTO
        biz.base_auth_record (
        role_id,
        user_id,
        begin_at,
        expire_at,
        is_default,
        is_deleted,
        create_time,
        creator_id
        )
        VALUES (
        #{role_id},
        #{user_id},
        now(),
        '9999-12-30'::date,
        #{is_default},
        #{is_deleted},
        now(),
        #{creator_id}
        )
        RETURNING id;
    </insert>

    <insert id="batch_save_auth_record">
        INSERT INTO
        biz.base_auth_record (
        role_id,
        user_id,
        begin_at,
        expire_at,
        is_default,
        is_deleted,
        create_time,
        modify_time,
        creator_id,
        modifier_id
        )
        VALUES (
        #{role_id},
        #{user_id},
        #{begin_at},
        #{expire_at},
        #{is_default},
        #{is_deleted},
        #{create_time},
        #{modify_time},
        #{creator_id},
        #{modifier_id}
        )
        RETURNING *;
    </insert>

    <update id="update_auth_record">
        UPDATE biz.base_auth_record
        SET role_id = #{role_id},
        modifier_id = #{modifier_id},
        modify_time = #{modify_time}
        where id = #{id}
        RETURNING id
    </update>


    <delete id="delete_by_auth_record_id">
         <![CDATA[
         BEGIN;
            UPDATE biz.base_auth_record
            SET
            is_deleted = 1
            where id = #{auth_record_id}
            ;
            UPDATE biz.base_region_auth
            SET
            is_deleted = 1
            where auth_record_id = #{auth_record_id}
            ;UPDATE biz.base_product_tag_auth
            SET
            is_deleted = 1
            where auth_record_id = #{auth_record_id}
            ;
            DELETE FROM biz.base_region_auth_mapping where auth_record_id = #{auth_record_id};
         COMMIT;
         ]]>
    </delete>
    <mix id="refresh_mat_view">
        REFRESH MATERIALIZED VIEW biz.region_auth_mat_view WITH DATA;
    </mix>
    <delete id="delete_by_auth_record_ids">
     <![CDATA[
        UPDATE biz.base_auth_record
        SET
        is_deleted = 1,
        modifier_id = #{modifier_id},
        modify_time = now()
        where id in ${auth_record_id}
        ;
        UPDATE biz.base_region_auth
        SET
        is_deleted = 1,
        modifier_id = #{modifier_id},
        modify_time = now()
        where auth_record_id in ${auth_record_id}
        ;
        UPDATE biz.base_product_tag_auth
        SET
        is_deleted = 1,
        modifier_id = #{modifier_id},
        modify_time = now()
        where auth_record_id in ${auth_record_id}
        ;
        DELETE FROM biz.base_region_auth_mapping where auth_record_id in ${auth_record_id};
     ]]>
    </delete>

    <delete id="delete_user_role_when_role_not_used_by_record">
        delete from sys.user_role ur
        where  user_id = #{user_id} and role_id in ${role_ids}
        and not exists(
            select 1
            from biz.base_auth_record ar
            where ur.user_id = ar.user_id
            and ur.role_id = ar.role_id
            and ar.is_deleted = 0
            <if test="exclude_auth_record_ids">
                and ar.id not in ${exclude_auth_record_ids}
            </if>
            )
    </delete>

    <update id="update_default_auth_record">
        BEGIN;
            update biz.base_auth_record
            set is_default = 0
            where is_deleted = 0
            and user_id = #{user_id};

            update biz.base_auth_record
            set is_default = 1
            where is_deleted = 0
            and user_id = #{user_id}
            and id = #{auth_record_id}
            ;
        COMMIT;
    </update>

    <update id="update_newest_record_default">
        with newest_record as (
            select id from biz.base_auth_record
            where user_id = #{user_id}
            and is_deleted = 0
            order by id asc
            limit 1
        )
        update biz.base_auth_record bar
        set is_default = 1
        from newest_record
        where bar.id = newest_record.id
        returning bar.id;
    </update>
    <!-- 根据用户id获取权限组合-->
    <select id="list_auth_record_combination_by_user_id">
        with user_role_records as (
                    select id,
                           role_id
                    from biz.base_auth_record
                    where user_id = #{user_id}
                    and is_deleted = 0
                ),product_tags as (
                    select auth_record_id,array_agg(product_tag_id) as auth_tags
                    from user_role_records rr
                    inner join biz.base_product_tag_auth ta
                    on ta.auth_record_id = rr.id
                    and ta.is_deleted = 0
                    group by  auth_record_id
                ),auth_regions as (
                    select  auth_record_id,
                            json_agg(json_build_object('org_sk',ra.org_sk,'org_type',ra.org_type,'operating_unit_sk',ra.operating_unit_sk)) as auth_regions
                    from user_role_records rr
                    inner join biz.base_region_auth ra
                    on ra.auth_record_id = rr.id
                    and ra.is_deleted = 0
                    group by  auth_record_id
                )
                    select
                    rr.id as auth_record_id,
                    rr.role_id,
                    auth_regions
                    --auth_tags
                    --'{}' as auth_tags
                from user_role_records rr
                -- inner join  product_tags pt on rr.id = pt.auth_record_id
                inner join  auth_regions ar on rr.id = ar.auth_record_id
    </select>
</mapper>
