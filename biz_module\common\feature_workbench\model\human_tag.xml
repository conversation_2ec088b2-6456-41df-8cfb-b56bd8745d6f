<?xml version="1.0"?>
<mapper namespace="default">
    <select id="query_view_dimension_table_field_code">
        select distinct table_field_code
        from biz.base_feature_config t1
        inner join biz.base_biz_view_dimension t2
        on t2.dimension_id = t1.biz_view_dimension_id
        where t1.feature_subtype = 'human_tag'
        and t1.is_deleted = 0 and t2.is_deleted = 0
        order by table_field_code;
    </select>

    <select id="query_meta_data_table_field">
        -- query_meta_data_table_field
        select code,name,belong_table_code,title, is_dynamic
        from biz.base_meta_data_table_field
        where is_deleted = 0
        <if test="code">
            and code in ${code}
        </if>
    </select>

    <select id="query_feature_config_by_table_field_code">
        -- query_feature_config_by_table_field_code
        select
        t1.id,
        feature_code,
        feature_name,
        feature_subtype,
        feature_category_id
        from biz.base_feature_config t1
        -- inner join biz.base_biz_view t2 on t1.biz_view_id = t2.id
        inner join biz.base_biz_view_dimension t3 on t3.dimension_id = t1.biz_view_dimension_id
        where t1.feature_subtype = 'human_tag'
        and t3.table_field_code =
        <foreach collection="code" item="item" separator="," open="'{" close="}'">
            ${item}
        </foreach>
        and t1.is_deleted = 0
        -- and t2.is_deleted = 0
        and t3.is_deleted = 0
        order by convert_to(feature_name,'GBK');
    </select>

    <select id="query_view_dimension_table_field_code_by_code">
        -- query_view_dimension_table_field_code_by_code
        select
        a.code,
        a.name,
        a.title,
        a.data_type,
        a.belong_table_code,
        b.table_name as belong_table_name,
        is_dynamic
        from biz.base_meta_data_table_field a
        inner join biz.base_meta_data_table b
        on a.belong_table_code = b.code
        and a.code in ${code}
        and a.is_deleted = 0
        and b.is_deleted = 0
    </select>

    <select id="query_dimension_column_config">
        with belong_table_code as (
        select belong_table_code from biz.base_meta_data_table_field where code in ${code}
        ),base_meta_data_table_field_name as (
        select name, belong_table_code, title, is_dynamic from biz.base_meta_data_table_field where code in
        ${code}
        )
        select t1.table_name, t2.belong_table_code, t2.name, t2.title, t2.is_dynamic
        from biz.base_meta_data_table t1 inner join base_meta_data_table_field_name t2 on t1.code =
        t2.belong_table_code
        where code in (select * from belong_table_code);
    </select>

    <select id="dynamic_query_dim_data">
        -- 动态查询粒度范围
        select distinct
        ${code_field_name} as code,
        ${other_required_field_name} as value
        from ${table_name}
        where ${code_field_name} is not null
        and ${code_value_expression}
    </select>
    <select id="dynamic_query_dim_data_bak">
        -- 动态查询粒度范围，返回df
        select
        <foreach collection="columns" item="item" separator="," open="" close="">
            ${item}
        </foreach>
        from ${table_name}
        <if test="is_dynamic">
            where org_hierarchy_code = #{org_hierarchy_filter}
        </if>
        <if test="org_hierarchy_type_code">
            where org_hierarchy_type_code = #{org_hierarchy_type_code}
        </if>
    </select>

    <select id="dynamic_query_feature_data">
        select feature_value
        from biz.base_feature_config_value t1
        inner join biz.base_feature_config t2 on t1.feature_config_id = t2.id
        where t2.feature_code = #{feature_code};
    </select>


    <insert id="batch_insert">
        insert into biz.base_feature_human
        (${columns})
        values
        ${value_expression}
    </insert>

    <update id="import_batch_update">
        update biz.base_feature_human t1
        set feature_value = tmp.feature_value, feature_code_object = tmp.feature_code_json::jsonb,
        operating_unit_sk=tmp.operating_unit_sk::integer
        from (values
        <foreach item="item" collection="req"
                 open="" separator="," close="">
            (#{item.feature_code}, #{item.feature_value}, #{item.feature_json}, #{item.feature_code_json},
            #{item.operating_unit_sk} )
        </foreach>
        ) as tmp (feature_code, feature_value, feature_json, feature_code_json, operating_unit_sk )
        where t1.feature_code = tmp.feature_code
        and t1.feature_object = tmp.feature_json::jsonb
    </update>

    <select id="list_authorized_human_tags">
        with ${with_clause_name} as (
            select
            id
            <if test="need_auth_flag">
                ,${extract_expression}
            </if>
            from biz.base_feature_human
            where operating_unit_sk = #{operating_unit_sk}
            and ${feature_related_expression}
        )
        select
        id
        from ${with_clause_name}
        <if test="need_auth_flag">
            inner join ${auth_orgs_sql_expression}
            on ${on_expression}
        </if>
    </select>

    <select id="delete_human_tags_by_dim_and_feature_code">
        delete
        from biz.base_feature_human
        where operating_unit_sk = #{operating_unit_sk}
        and ${dim_condition_expression}
        and feature_code in ${feature_codes}
    </select>
    <select id="delete_human_tags_by_temp_table">
        delete
        from biz.base_feature_human a
        using ${temp_table_name} ${temp_alias}
        where
        a.operating_unit_sk = #{operating_unit_sk}
        and a.feature_code = ${temp_alias}.feature_code
        and ${on_expression}
    </select>

    <select id="batch_insert_by_temp_table">
        insert into biz.base_feature_human
        (operating_unit_sk, feature_code, feature_value, feature_object, feature_code_object)
        select
        #{operating_unit_sk},
        feature_code,
        feature_value,
        json_build_object(${feature_object_json_build_expression})::jsonb as feature_object,
        json_build_object(${feature_code_object_json_build_expression})::jsonb as feature_code_object
        from ${temp_table_name}
    </select>

    <delete id="batch_delete_by_ids">
        delete
        from biz.base_feature_human
        using (
        values
        <foreach item="item" collection="ids"
                 open="" separator="," close="">
            (#{item})
        </foreach>

        ) temp(should_deleted_id)
        where id=should_deleted_id
    </delete>


    <select id="list_human_tag_by_dynamic_condition">
        with ${with_clause_name} as (
            select
            id,
            feature_code,
            feature_value,
            <if test="need_auth_flag">
            ${extract_expression},
            </if>
            feature_code_object,
            feature_object
            from biz.base_feature_human
            where operating_unit_sk = #{operating_unit_sk}
            and ${feature_related_expression}
        )
        select
        feature_code_object,
        json_object_agg(feature_code, feature_value) as feature_value_object
        from ${with_clause_name}
        <if test="need_auth_flag">
            inner join ${auth_orgs_sql_expression}
            on ${on_expression}
        </if>
        group by feature_object,feature_code_object
        order by feature_code_object
        limit #{page_size} offset #{offset}
    </select>

    <select id="count_human_tag_by_dynamic_condition">
        with ${with_clause_name} as (
            select
            <if test="need_auth_flag">
            ${extract_expression},
            </if>
            feature_object
            from biz.base_feature_human
            where operating_unit_sk = #{operating_unit_sk}
            and ${feature_related_expression}
        )
        select
        count(*) as count
        from ${with_clause_name}
        <if test="need_auth_flag">
            inner join ${auth_orgs_sql_expression}
            on ${on_expression}
        </if>
    </select>

    <select id="query_feature_config_by_human_tag_bak">
        -- query_feature_config_by_human_tag

        with temp as (
        select
        max(id) as id,
        t1.feature_object,
        t1.feature_code_object,
        array(select feature_value
        from biz.base_feature_human as t2
        where t1.feature_object = t2.feature_object order by feature_value) as feature_values,
        array(select feature_code
        from biz.base_feature_human as t2

        where t1.feature_object = t2.feature_object order by feature_value) as feature_codes
        from biz.base_feature_human as t1
        inner join lateral (
        select array_agg(c.key) keys from json_each(t1.feature_object::json) c
        ) b on
        #{dim_codes_filters}
        @&gt; b.keys and #{dim_codes_filters} &lt;@ b.keys
        where
        <if test="operating_unit_sk">
           t1.operating_unit_sk = #{operating_unit_sk}
        </if>
        <if test="not operating_unit_sk">
           1 = 0
        </if>

        <if test="dim_org_filter and operating_unit_sk">
            and ${dim_org_filter}
        </if>
        group by t1.feature_object, t1.feature_code_object
        )select * from temp where 1 = 1
        <if test="feature_value_filters">
            and feature_values &lt;@ #{feature_value_filters}
        </if>
        order by id
        offset (${page_no} * ${page_size} -
        ${page_size}) limit ${page_size};
    </select>

    <select id="export_feature_config_by_human_tag">
        -- export_feature_config_by_human_tag
        select *
        from biz.base_feature_human t1
        inner join lateral (
        select array_agg(t2.key) keys from json_each(t1.feature_object::json) t2
        ) t3 on #{dim_codes_filters} @&gt; t3.keys and #{dim_codes_filters} &lt;@ t3.keys where t1.operating_unit_sk = #{operating_unit_sk}
        <if test="dim_org_filter">
            and ${dim_org_filter}
        </if>
        order by id
    </select>

    <select id="count_feature_config_by_human_tag">
        -- select count('*') from biz.base_feature_human where feature_object ?&amp; array ${dim_codes};

        with temp as (
        select
        max(id) as id,
        t1.feature_object,
        array(select feature_value
        from biz.base_feature_human as t2
        where t1.feature_object = t2.feature_object order by t2.feature_code) as feature_values,
        array(select feature_code
        from biz.base_feature_human as t2
        where t1.feature_object = t2.feature_object
        <if test="dim_org_filter and operating_unit_sk">
            and ${dim_org_filter}
        </if>
        order by t2.feature_code) as feature_codes
        from biz.base_feature_human as t1 where t1.operating_unit_sk = #{operating_unit_sk} group by t1.feature_object
        )select count(*) from temp where 1 = 1
        <if test="feature_value_filters">
            and feature_values &lt;@ #{feature_value_filters}
        </if>


    </select>

    <select id="query_feature_values_by_codes">
        select t1.feature_code, t1.feature_name, array (select feature_value from
        biz.base_feature_config_value as t2 where t2.feature_config_id = t1.id and t2.is_deleted = 0) as values
        from biz.base_feature_config as t1
        where is_deleted = 0
        <if test="codes">
            and feature_code in ${codes};
        </if>

    </select>

    <select id="query_base_feature_human_tag">
        select id, feature_code, feature_value, feature_object from biz.base_feature_human
    </select>

    <select id="exists_human_tag_by_feature_code">
        select count(*)
        from biz.base_feature_human t1
        inner join biz.base_feature_config t2 on t1.feature_code = t2.feature_code where t2.id =
        #{feature_id}
    </select>

    <select id="list_dynamic_authorized_orgs_by_org_sk_expression">
        select ${dynamic_field}
        from (${org_sk_expression}) a(org_sk)
        inner join dm.dim_org_integration
                    on a.org_sk = dim_org_integration.org_sk
                    and dim_org_integration.status = '正常'

    </select>

    <select id="is_human_tag_exists_by_value_expression">
        select exists(
            select 1 from biz.base_feature_human
            where ${value_expression}
            limit 1
        )
    </select>

    <select id="list_grouped_human_tag_by_condition">
        with temp as (
            select
            json_object_agg(feature_code, feature_value) as feature_value_object,
            feature_code_object
            from biz.base_feature_human
            <if test="need_auth_flag">
                inner join ${auth_orgs_sql_expression}
                on ${on_expression}
            </if>
            where operating_unit_sk = #{operating_unit_sk}
            and feature_code in ${feature_codes}
            group by feature_code_object
        )
        select
        feature_code_object,
        feature_value_object
        from temp
        where ${feature_related_expression}
        order by feature_code_object
        <if test="is_pagination">
            limit #{page_size} offset #{offset}
        </if>
    </select>

    <select id="count_grouped_human_tag_by_condition">
        with temp as (
            select
            json_object_agg(feature_code, feature_value) as feature_value_object,
            feature_code_object
            from biz.base_feature_human
            <if test="need_auth_flag">
                inner join ${auth_orgs_sql_expression}
                on ${on_expression}
            </if>
            where operating_unit_sk = #{operating_unit_sk}
            and feature_code in ${feature_codes}
            group by feature_code_object
        )
        select
        count(*) as count
        from temp
        where ${feature_related_expression}
    </select>
    <select id="list_human_tag_by_condition">
        select
        feature_code,
        feature_value
        <if test="extract_expression">
            ,${extract_expression}
        </if>
        from biz.base_feature_human
        <if test="need_auth_flag">
            inner join ${auth_orgs_sql_expression}
            on ${on_expression}
        </if>
        where operating_unit_sk = #{operating_unit_sk}
        and feature_code in ${feature_codes}
    </select>

    <select id="list_ordered_feature_by_feature_name">
        select
            id,
            biz_view_id,
            biz_view_dimension_id,
            biz_view_filter_id,
            feature_code,
            feature_name,
            feature_type,
            data_type,
            feature_subtype,
            data_type_extension
        from biz.base_feature_config
        where is_deleted = 0
        <if test="feature_codes">
            and feature_code in ${feature_codes}
        </if>

        <if test="feature_ids">
            and id in ${feature_ids}
        </if>
        order by convert_to(feature_name,'GBK');
    </select>

    <delete id="batch_delete_authorized_human_tags">
        with should_deleted_ids as (
            select
            id as should_deleted_id
            from biz.base_feature_human
            <if test="need_auth_flag">
                inner join ${auth_orgs_sql_expression}
                on ${on_expression}
            </if>
            where operating_unit_sk = #{operating_unit_sk}
            and feature_code in ${feature_codes}
        )
        delete
        from biz.base_feature_human a
        using should_deleted_ids b
        where
        a.id = b.should_deleted_id
    </delete>

    <select id="query_feature_human_value">
        select distinct
        feature_code,
        feature_value
        from biz.base_feature_human
        where feature_code in ${feature_codes}
    </select>

</mapper>