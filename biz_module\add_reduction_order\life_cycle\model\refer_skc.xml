<?xml version="1.0"?>
<mapper namespace="life_cycle_refer_skc">
    <select id="query_refer_skc_org_filter">
        select
            <if test="filter_key != 'org_sk'">
                ${filter_key} as label, ${filter_key} as value
            </if>
            <if  test="filter_key == 'org_sk'">
                large_region as label, large_region_sk as value
            </if>
        from biz.qrs_life_cycle_base_kpi
        where 1=1
        <if test="filter_key == 'org_sk'">
            and large_region_sk is not null
        </if>
        <if test="filter_key != 'org_sk'">
            and ${filter_key} is not null
        </if>
        <if test="not org_sk_list">
            and 1 != 1
        </if>
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="filter_key == 'org_sk'">
            group by large_region_sk, large_region
            order by large_region_sk
        </if>
        <if test="filter_key != 'org_sk'">
            group by ${filter_key}
            order by ${filter_key} desc
        </if>

    </select>

    <select id="query_refer_skc_filter">
        select
            <if test="filter_key == 'skc_sk'">
                skc_code as label, skc_sk as value
            </if>
            <if test="filter_key != 'skc_sk'">
                ${filter_key} as label, ${filter_key} as value
            </if>
        from biz.qrs_life_cycle_base_kpi as a
        inner join biz.qrs_current_quarter_config as b on a.product_year_quarter = EXTRACT(YEAR FROM CURRENT_DATE) || b.quarter
        and current_date between  (EXTRACT(YEAR FROM CURRENT_DATE) || '-' || b.begin_date)::DATE and (EXTRACT(YEAR FROM CURRENT_DATE) || '-' || b.end_date)::DATE
        where ${filter_key} is not null and is_include_group_buy = '含团购'
        <if test="not is_admin">
            and a.product_range in (select product_range from sys.user_product where user_id = #{user_id})
            and big_class in (select big_class from sys.user_product where user_id = #{user_id})
        </if>
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="org_sk">
            and large_region_sk = #{org_sk}
        </if>
        <if test="big_class">
            and big_class in ${big_class}
        </if>
        <if test="product_range">
            and product_range in ${product_range}
        </if>
        <if test="product_year_quarter">
            and product_year_quarter in ${product_year_quarter}
        </if>
        <if test="product_belong_name">
            and product_belong_name in ${product_belong_name}
        </if>
        <if test="ps_mid_class">
            and ps_mid_class in ${ps_mid_class}
        </if>
        <if test="sale_mid_tiny_class">
            and sale_mid_tiny_class in ${sale_mid_tiny_class}
        </if>
        <if test="gender">
            and gender in ${gender}
        </if>
        <if test="product_name">
            and product_name in ${product_name}
        </if>
        <if test="filter_key == 'skc_sk'">
            group by skc_code, skc_sk
        </if>
        <if test="filter_key == 'product_year_quarter'">
            group by product_year_quarter, quarter
        </if>
        <if test="filter_key not in ('skc_sk', 'product_year_quarter')">
            group by ${filter_key}
        </if>
        order by ${filter_key} desc
    </select>

    <select id="query_refer_skc_kpi">
        WITH current_quarter_skc AS (
            SELECT channel_type, large_region_sk as org_sk, skc_sk as current_quarter_skc_sk, skc_code as current_quarter_skc_code, skc_code, skc_code as refer_skc_code, 0::int as priority, product_year_quarter, real_listing_date
                , '当前款'::text as source
                , gender, big_class, product_range, ps_mid_class, sale_mid_tiny_class, skc_name, target_price
                , color_name, total_shipment_qty, order_qty, total_sale_qty
                , total_sale_qty::numeric / nullif(total_shipment_qty, 0) as total_sale_rate
                , total_sale_amt::numeric / nullif(total_sale_tag_amt, 0) as discount
            FROM biz.qrs_life_cycle_base_kpi
            WHERE channel_type = #{channel_type} AND large_region_sk = #{org_sk} and org_sk is null and is_include_group_buy = '含团购'
            <if test="not is_admin">
                and product_range in (select product_range from sys.user_product where user_id = #{user_id})
                and big_class in (select big_class from sys.user_product where user_id = #{user_id})
            </if>
            <if test="big_class">
                and big_class in ${big_class}
            </if>
        <if test="product_range">
            and product_range in ${product_range}
        </if>
        <if test="product_year_quarter">
            and product_year_quarter in ${product_year_quarter}
        </if>
        <if test="gender">
            and gender in ${gender}
        </if>
        <if test="ps_mid_class">
            and ps_mid_class in ${ps_mid_class}
        </if>
        <if test="sale_mid_tiny_class">
            and sale_mid_tiny_class in ${sale_mid_tiny_class}
        </if>
        <if test="product_name">
            and product_name in ${product_name}
        </if>
        <if test="skc_sk">
            and skc_sk in ${skc_sk}
        </if>
        <if test="not skc_sk">
            and skc_sk is not null
        </if>
        )
        , refer_skc AS (
            SELECT
                c.channel_type, c.large_region_sk as org_sk, b.current_quarter_skc_sk, b.current_quarter_skc_code, b.skc_code, c.skc_code as refer_skc_code, a.priority, c.product_year_quarter, c.real_listing_date
                , a.source
                , c.gender, c.big_class, c.product_range, c.ps_mid_class, c.sale_mid_tiny_class, c.skc_name, c.target_price
                , c.color_name, c.total_shipment_qty, c.order_qty, c.total_sale_qty
                , c.total_sale_qty::numeric / nullif(c.total_shipment_qty, 0) as total_sale_rate
                , c.total_sale_amt::numeric / nullif(c.total_sale_tag_amt, 0) as discount
            FROM biz.qrs_refer_skc_item as a
            INNER JOIN current_quarter_skc as b ON a.skc_sk = b.current_quarter_skc_sk AND a.channel_type = b.channel_type AND a.org_sk = b.org_sk
            INNER JOIN biz.qrs_life_cycle_base_kpi AS c ON a.refer_skc_sk = c.skc_sk AND a.channel_type = c.channel_type AND a.org_sk = c.large_region_sk and c.org_sk is null and c.is_include_group_buy = '含团购'
            WHERE a.is_deleted = 0
        )
        ,base_data AS (
            SELECT * FROM  current_quarter_skc
            UNION ALL
            SELECT * FROM refer_skc
        )
        SELECT a.*, b.picture_url as image_url, count(1) over() as total_count
        FROM base_data as a
        left join dm.dim_skc as b on a.refer_skc_code = b.skc_code
        ORDER BY skc_code, priority
        LIMIT ${page_size}
        OFFSET ${page_offset}
    </select>

    <select id="query_refer_skc_week_kpi">
        WITH current_quarter_skc AS (
            SELECT channel_type, large_region_sk as org_sk, skc_sk, skc_code, skc_code as refer_skc_code
                , ROW_NUMBER() OVER (PARTITION BY channel_type, large_region_sk, skc_sk ORDER BY natural_year, natural_time) AS listing_time, sale_qty as week_sale_qty, sale_qty::numeric / nullif(shipment_qty, 0) as week_sale_out
                , sale_amt::numeric / nullif(sale_tag_amt, 0) as week_discount
            FROM biz.qrs_life_cycle_week_kpi
            WHERE channel_type = #{channel_type} AND large_region_sk = #{org_sk} and org_sk is null
            and is_include_group_buy = '含团购' and type = 'week' and skc_sk in ${current_quarter_skc_sk}
        <if test="not is_admin">
            and product_range in (select product_range from sys.user_product where user_id = #{user_id})
            and big_class in (select big_class from sys.user_product where user_id = #{user_id})
        </if>
            <if test="big_class">
                and big_class in ${big_class}
            </if>
        <if test="product_range">
            and product_range in ${product_range}
        </if>
        <if test="product_year_quarter">
            and product_year_quarter in ${product_year_quarter}
        </if>
        <if test="gender">
            and gender in ${gender}
        </if>
        <if test="ps_mid_class">
            and ps_mid_class in ${ps_mid_class}
        </if>
        <if test="sale_mid_tiny_class">
            and sale_mid_tiny_class in ${sale_mid_tiny_class}
        </if>
        <if test="product_name">
            and product_name in ${product_name}
        </if>
        <if test="skc_sk">
            and skc_sk in ${skc_sk}
        </if>
        <if test="not skc_sk">
            and skc_sk is not null
        </if>
        )
        ,distinct_current_skc as (
            select skc_sk, channel_type, org_sk, skc_code
            from current_quarter_skc
            group by skc_sk, channel_type, org_sk, skc_code
        )
        , refer_skc AS (
            SELECT
                c.channel_type, c.large_region_sk as org_sk, b.skc_sk, b.skc_code, c.skc_code as refer_skc_code
                , ROW_NUMBER() OVER (PARTITION BY c.channel_type, c.large_region_sk, c.skc_code ORDER BY c.natural_year, c.natural_time) AS listing_time, c.sale_qty as week_sale_qty, c.sale_qty::numeric / nullif(c.shipment_qty, 0) as week_sale_out
                , c.sale_amt::numeric / nullif(c.sale_tag_amt, 0) as week_discount
            FROM biz.qrs_refer_skc_item as a
            INNER JOIN distinct_current_skc as b ON a.skc_sk = b.skc_sk AND a.channel_type = b.channel_type AND a.org_sk = b.org_sk
            INNER JOIN biz.qrs_life_cycle_week_kpi AS c ON a.refer_skc_sk = c.skc_sk AND a.channel_type = c.channel_type AND a.org_sk = c.large_region_sk and c.org_sk is null and c.is_include_group_buy = '含团购' and type = 'week'
            WHERE a.is_deleted = 0
            and c.skc_code in ${refer_skc_code}
        )
        ,base_data AS (
            SELECT * FROM  current_quarter_skc
            UNION ALL
            SELECT * FROM refer_skc
        )
        SELECT a.*
        FROM base_data as a
        ORDER BY a.skc_code, a.refer_skc_code, a.listing_time
    </select>

    <select id="query_refer_skc_refer_skc_filter">
        select
            <if test="filter_key == 'skc_sk'">
                skc_code as label, skc_sk as value
            </if>
            <if test="filter_key != 'skc_sk'">
                ${filter_key} as label, ${filter_key} as value
            </if>
        from biz.qrs_life_cycle_base_kpi as a
        left join biz.qrs_current_quarter_config as b on a.product_year_quarter = EXTRACT(YEAR FROM CURRENT_DATE) || b.quarter
        and current_date between  (EXTRACT(YEAR FROM CURRENT_DATE) || '-' || b.begin_date)::DATE and (EXTRACT(YEAR FROM CURRENT_DATE) || '-' || b.end_date)::DATE
        where ${filter_key} is not null and is_include_group_buy = '含团购' and b.quarter is null
        <if test="channel_type">
            and channel_type = #{channel_type}
        </if>
        <if test="org_sk">
            and large_region_sk = #{org_sk}
        </if>
        <if test="big_class">
            and big_class in ${big_class}
        </if>
        <if test="product_range">
            and product_range in ${product_range}
        </if>
        <if test="product_year_quarter">
            and product_year_quarter in ${product_year_quarter}
        </if>
        <if test="product_belong_name">
            and product_belong_name in ${product_belong_name}
        </if>
        <if test="ps_mid_class">
            and ps_mid_class in ${ps_mid_class}
        </if>
        <if test="sale_mid_tiny_class">
            and sale_mid_tiny_class in ${sale_mid_tiny_class}
        </if>
        <if test="gender">
            and gender in ${gender}
        </if>
        <if test="product_name">
            and product_name in ${product_name}
        </if>
        <if test="filter_key == 'skc_sk'">
            group by skc_code, skc_sk
        </if>
        <if test="filter_key != 'skc_sk'">
            group by ${filter_key}
        </if>
        order by ${filter_key} desc
    </select>

    <select id="query_refer_skc_refer_skc_list">

        WITH current_skc as (
            SELECT
                skc_code as current_quarter_skc_code
                , skc_sk as current_quarter_skc_sk
                , skc_code as refer_skc_code
                , skc_sk as refer_skc_sk
                , skc_code
                , 0::int as priority
                , product_year_quarter
                , real_listing_date
                , '当前款'::text source
                , gender, big_class, product_range, ps_mid_class, sale_mid_tiny_class, skc_name, target_price
                , color_name, total_shipment_qty, order_qty, total_arrive_qty
                , total_sale_qty
                , total_sale_qty::numeric / nullif(total_shipment_qty, 0) as total_sale_rate
                , total_sale_amt::numeric / nullif(total_sale_tag_amt, 0) as discount
                , null::int as is_refer_skc
                , 0::int AS order_
            FROM biz.qrs_life_cycle_base_kpi as a
            WHERE a.channel_type = #{channel_type} and a.large_region_sk = #{org_sk}
            and a.org_sk is null and a.is_include_group_buy = '含团购'
            and  a.skc_sk = #{current_quarter_skc_sk}
         )
        , refer_skc AS (
            SELECT
                c.skc_code as current_quarter_skc_code, a.skc_sk current_quarter_skc_sk
                , b.skc_code as refer_skc_code, b.skc_sk as refer_skc_sk, b.skc_code
                , a.priority, b.product_year_quarter, b.real_listing_date
                , a.source
                , b.gender, b.big_class, b.product_range, b.ps_mid_class, b.sale_mid_tiny_class, b.skc_name, b.target_price
                , b.color_name, b.total_shipment_qty, b.order_qty, b.total_arrive_qty
                , b.total_sale_qty
                , b.total_sale_qty::numeric / nullif(b.total_shipment_qty, 0) as total_sale_rate
                , b.total_sale_amt::numeric / nullif(b.total_sale_tag_amt, 0) as discount
                , 1::int as is_refer_skc
                , 1::int AS order_
            FROM biz.qrs_refer_skc_item as a
            INNER JOIN biz.qrs_life_cycle_base_kpi AS b ON a.refer_skc_sk = b.skc_sk AND a.channel_type = b.channel_type AND a.org_sk = b.large_region_sk and b.org_sk is null and b.is_include_group_buy = '含团购'
            INNER JOIN dm.dim_skc AS c ON a.skc_sk = c.skc_sk
            WHERE a.is_deleted = 0 and a.skc_sk = #{current_quarter_skc_sk} and a.channel_type = #{channel_type} and a.org_sk = #{org_sk}
        )
        , select_skc as (
            SELECT
                CAST(#{current_quarter_skc_code} as text) as current_quarter_skc_code
                , CAST(#{current_quarter_skc_sk} AS INT) as current_quarter_skc_sk
                , skc_code as refer_skc_code
                , skc_sk as refer_skc_sk
                , skc_code
                , null::int priority
                , product_year_quarter
                , real_listing_date
                , null::text source
                , gender, big_class, product_range, ps_mid_class, sale_mid_tiny_class, skc_name, target_price
                , color_name, total_shipment_qty, order_qty, total_arrive_qty
                , total_sale_qty
                , total_sale_qty::numeric / nullif(total_shipment_qty, 0) as total_sale_rate
                , total_sale_amt::numeric / nullif(total_sale_tag_amt, 0) as discount
                , 0::int as is_refer_skc
                , 2::int AS order_
            FROM biz.qrs_life_cycle_base_kpi as a
            where a.channel_type = #{channel_type} and a.large_region_sk = #{org_sk}
            and a.org_sk is null and a.is_include_group_buy = '含团购' and a.skc_sk is not null
            <if test="big_class">
                and a.big_class in ${big_class}
            </if>
            <if test="product_range">
                and a.product_range in ${product_range}
            </if>
            <if test="product_year_quarter">
                and a.product_year_quarter in ${product_year_quarter}
            </if>
            <if test="product_belong_name">
                and a.product_belong_name in ${product_belong_name}
            </if>
            <if test="ps_mid_class">
                and a.ps_mid_class in ${ps_mid_class}
            </if>
            <if test="sale_mid_tiny_class">
                and a.sale_mid_tiny_class in ${sale_mid_tiny_class}
            </if>
            <if test="gender">
                and a.gender in ${gender}
            </if>
            <if test="product_name">
                and a.product_name in ${product_name}
            </if>
            <if test="skc_sk">
                and a.skc_sk in ${skc_sk}
            </if>
            and a.skc_sk not in (select refer_skc_sk from refer_skc)
        )
        ,base_result as (
            select * from refer_skc
            union all
            select * from current_skc
            union all
            select * from select_skc
        )
        SELECT a.*, b.picture_url as image_url
        FROM base_result as a
        left join dm.dim_skc as b on a.refer_skc_code = b.skc_code
        ORDER BY order_, refer_skc_code
    </select>

    <select id="is_exist_refer_skc">
         SELECT EXISTS (
            SELECT 1 FROM  biz.qrs_refer_skc_item
            WHERE is_deleted = 0 and channel_type = #{channel_type}
            and org_sk = #{org_sk} and skc_sk = #{current_quarter_skc_sk}
            and refer_skc_sk = #{refer_skc_sk}
        );
    </select>

    <insert id="add_refer_skc">
        INSERT INTO biz.qrs_refer_skc_item
        (channel_type, org_sk, skc_sk, refer_skc_sk, source, creator_id, modifier_id)
        values
        (#{channel_type}, #{org_sk}, #{current_quarter_skc_sk}, #{refer_skc_sk}, '人工添加', #{user_id}, #{user_id})
    </insert>

    <insert id="add_refer_skc">
        INSERT INTO biz.qrs_refer_skc_item
        (channel_type, org_sk, skc_sk, refer_skc_sk, source, creator_id, modifier_id)
        values
        (#{channel_type}, #{org_sk}, #{current_quarter_skc_sk}, #{refer_skc_sk}, '人工添加', #{user_id}, #{user_id})
    </insert>

    <insert id="add_refer_skc_list">
        DROP TABLE IF EXISTS temp_update;
        CREATE TEMP TABLE temp_update AS
        with t as (
            select *
            from json_to_recordset(#{req})
            as temp_table("channel_type" varchar, "org_sk" int, "current_quarter_skc_sk" int, "refer_skc_sk" int, "source" varchar, "user_id" int)
        )
        SELECT * FROM t;

        insert into biz.qrs_refer_skc_item
        (channel_type, org_sk, skc_sk, refer_skc_sk, source, creator_id, modifier_id)
        select
        channel_type, org_sk, current_quarter_skc_sk, refer_skc_sk, source, user_id, user_id
        from temp_update;
    </insert>

    <update id="remove_refer_skc">
        UPDATE biz.qrs_refer_skc_item
        SET is_deleted = 1, modifier_id = #{user_id}, modify_time = now()
        WHERE
            channel_type = #{channel_type}
            and org_sk = #{org_sk}
            and skc_sk = #{current_quarter_skc_sk}
            and refer_skc_sk = #{refer_skc_sk}
    </update>

    <update id="remove_refer_skc_list">
        UPDATE biz.qrs_refer_skc_item
        SET is_deleted = 1, modifier_id = #{user_id}, modify_time = now()
        WHERE
            channel_type = #{channel_type}
            and org_sk = #{org_sk}
            and skc_sk = #{current_quarter_skc_sk}
            and refer_skc_sk in ${delete_refer_skc_sk}
    </update>



</mapper>