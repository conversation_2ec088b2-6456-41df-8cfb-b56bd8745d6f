from pathlib import Path
from typing import List, Dict, ClassVar, Union

from lxml.etree import Element
from pydantic import BaseModel

from ourbatis.const import DEFAULT_NS
from ourbatis.sql_source_parser.base_parser import BaseParser


class ForeachValueInfo(BaseModel):
    attr_names: List[str] = []


class SQLMapperMeta(BaseModel):
    file_name: Union[Path, str]
    namespace: str = DEFAULT_NS
    session: "SqlSession" = None  # 特殊场景使用


class BatisElement:
    """Element 是工厂函数不支持继承， 采用代理模式."""

    def __init__(
        self, meta: SQLMapperMeta, parser: ClassVar[BaseParser], element: [Element, str]
    ):
        self.meta = meta
        self.element = element
        self.parser = parser

    def __bool__(self):
        return self.element.__bool__

    def __len__(self):
        return self.element.__len__

    def __getattr__(self, attr):
        return getattr(self.element, attr)

    def __setitem__(self, instance, value):
        self.element.__setitem__(instance, value)

    def __getitem__(self, item):
        return self.element.__getitem__(item)

    def __delitem__(self, key):
        self.element.__delitem__(key)

    def __str__(self):
        return f"sql: {self.element} \n meta: {self.meta}"

    __repr__ = __str__


SessionSQLMapper = Dict[str, Dict[str, BatisElement]]
SQLMapper = Dict[str, BatisElement]
