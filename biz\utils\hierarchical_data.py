# *_*coding:utf-8 *_*
# @Time    : 2018/12/3 15:26
# <AUTHOR> hl_python
# @Email   : <EMAIL>
# @File    : get_menus.py
# @Software: PyCharm

# page = [{"page_id": 1, "parent_id": None, "page_name": "首页1"},
#         {"page_id": 2, "parent_id": None, "page_name": "首页2"},
#         {"page_id": 3, "parent_id": None, "page_name": "首页3"},
#
#         {"page_id": 4, "parent_id": 1, "page_name": "首页1-1"},
#         {"page_id": 5, "parent_id": 1, "page_name": "首页1-2"},
#         {"page_id": 6, "parent_id": 2, "page_name": "首页2-1"},
#         {"page_id": 7, "parent_id": 2, "page_name": "首页2-2"},
#         {"page_id": 8, "parent_id": 3, "page_name": "首页3-1"},
#         {"page_id": 9, "parent_id": 3, "page_name": "首页3-2"},
#
#         {"page_id": 10, "parent_id": 4, "page_name": "首页1-1-1"},
#         {"page_id": 11, "parent_id": 4, "page_name": "首页1-1-2"},
#         {"page_id": 12, "parent_id": 5, "page_name": "首页1-2-1"},
#         {"page_id": 13, "parent_id": 5, "page_name": "首页1-2-2"},
#         {"page_id": 14, "parent_id": 6, "page_name": "首页2-1-1"},
#         {"page_id": 15, "parent_id": 6, "page_name": "首页2-1-2"},
#         {"page_id": 16, "parent_id": 7, "page_name": "首页2-2-1"},
#         {"page_id": 17, "parent_id": 7, "page_name": "首页2-2-2"},
#         {"page_id": 18, "parent_id": 8, "page_name": "首页3-1-1"},
#         {"page_id": 19, "parent_id": 8, "page_name": "首页3-1-2"},
#         {"page_id": 20, "parent_id": 9, "page_name": "首页3-2-1"},
#
#         ]


# operation = [{"operation_id": 1, "page_id": 32, "api_name": "首页1-1-1-1-1_修改"},
#              {"operation_id": 2, "page_id": 32, "api_name": "首页1-1-1-1-1_查看"},
#              {"operation_id": 3, "page_id": 33, "api_name": "首页1-1-2-1-1_修改"},
#              {"operation_id": 4, "page_id": 33, "api_name": "首页1-1-2-1-1_查看"},
#              {"operation_id": 5, "page_id": 34, "api_name": "首页1-2-1-1-1_修改"},
#              {"operation_id": 6, "page_id": 34, "api_name": "首页1-2-1-1-1_查看"},
#              {"operation_id": 7, "page_id": 35, "api_name": "首页1-2-2-1-1_修改"},
#              {"operation_id": 8, "page_id": 35, "api_name": "首页1-2-2-1-1_查看"},
#              {"operation_id": 9, "page_id": 36, "api_name": "首页2-1-1-1-1_修改"},
#              {"operation_id": 10, "page_id": 36, "api_name": "首页2-1-1-1-1_查看"},
#              {"operation_id": 11, "page_id": 37, "api_name": "首页2-1-2-1-1_修改"},
#              {"operation_id": 12, "page_id": 37, "api_name": "首页2-1-2-1-1_查看"},
#              {"operation_id": 13, "page_id": 38, "api_name": "首页2-2-1-1-1_修改"},
#              {"operation_id": 14, "page_id": 39, "api_name": "首页2-2-1-1-1_查看"},
#              {"operation_id": 15, "page_id": 39, "api_name": "首页2-2-2-1-1_修改"},
#              {"operation_id": 16, "page_id": 40, "api_name": "首页2-2-2-1-1_查看"},
#              {"operation_id": 17, "page_id": 40, "api_name": "首页3-1-1-1-1_修改"},
#              {"operation_id": 18, "page_id": 41, "api_name": "首页3-1-1-1-1_查看"},
#              {"operation_id": 19, "page_id": 41, "api_name": "首页3-1-2-1-1_修改"},
#              {"operation_id": 20, "page_id": 42, "api_name": "首页3-1-2-1-1_查看"},
#              {"operation_id": 21, "page_id": 42, "api_name": "首页3-2-1-1-1_修改"},
#              {"operation_id": 22, "page_id": 43, "api_name": "首页3-2-1-1-1_查看"},
#              {"operation_id": 23, "page_id": 43, "api_name": "首页3-2-1-1-2_修改"}]

def get_tree_data(data_list, data_id, operation_data=None, top_tree_id=None, role_resources=None, role_page_ids=[],
                  role_operation_ids=[]):
    """
    获取多级层级数据
    :param data_list: 数据
    :param data_id: 数据表id名称
    :param operation_data: 要展示的功能权限数据
    :param role_resources: 要展示的数据权限数据
    :param top_tree_id: 数的顶端id列表
    :param role_page_ids: 数的顶端id列表
    :param role_operation_ids: 数的顶端id列表
    :return: 
    """
    # 添加每一级的下级id列表
    for data in data_list:
        page_id = data.get(data_id)
        child_id_list = []
        for item in data_list:
            if item.get("parent_id") == page_id:
                child_id_list.append(item.get(data_id))

        data["child_id_list"] = child_id_list
        # 角色的组织资源数据
        if role_resources:
            is_checked_resource = [i.get("resource_id") for i in role_resources]
            is_read_resource = [i.get("resource_id") for i in role_resources if i.get("operation") == 0]
            data["is_checked"] = True if data.get("resource_id") in is_checked_resource else False
            data["is_read"] = True if data.get("resource_id") in is_read_resource else False
        # 角色的页面权限数据
        elif role_page_ids:
            data["is_checked"] = True if data.get("page_id") in role_page_ids else False

    # 获取对应下级数据
    for data_item in data_list:
        child_id_list = data_item.get("child_id_list")
        items = []
        is_del = []
        if child_id_list:
            for child_id in child_id_list:
                item = [i for i in data_list if i.get(data_id) == child_id]
                if item:
                    items.append(item[0])
                    is_del.append(item[0])
        # 当前层级的下级数据
        data_item["next_data"] = items
        if items:
            data_item["has_next"] = True
        else:
            data_item["has_next"] = False
            # 是否是页面权限数据展示功能权限数据
            if operation_data:
                item_list = []
                # 最底层的页面添加功能权限数据
                for operation in operation_data:
                    if operation.get(data_id) == data_item.get(data_id):
                        operation["is_checked"] = True if operation.get("operation_id") in role_operation_ids else False
                        operation["parent_id"] = operation.get("page_id")
                        operation["page_id"] = operation.get("operation_id")
                        operation["operation_id"] = operation.get("operation_id")/10000
                        operation["next_data"] = []
                        item_list.append(operation)

                data_item["next_data"] = item_list
    # 要显示指定的多棵树数据
    if top_tree_id is not None:
        result = []
        for top_id in top_tree_id:
            top_item = [i for i in data_list if i.get(data_id) == top_id]
            if top_item:
                result.append(top_item[0])
    else:
        result = [i for i in data_list if i.get("parent_id") is None]
    return result
