@startuml
'https://plantuml.com/sequence-diagram

autonumber

actor actor

actor -> bizService
box "业务部分" #lightgrey

participant 业务服务 as bizService

end box

box "Excel服务" #Seashell

participant "ExcelService" as excelService

bizService -> bizService: 确认业务类型
bizService -> bizService: 查询业务数据

bizService -> excelService: 配置code, 业务数据

excelService -> excelService: 查询配置数据
excelService -> excelService: 生成文件

excelService -> bizService: 返回模版文件

end box


participant 文件服务 as fileService
participant 队列服务 as asyncService

bizService -> fileService: 上传文件备份


alt #Honeydew 同步
 bizService -> actor: 返回文件
else #palegreen
 bizService -> asyncService: 添加任务记录
 asyncService -> asyncService: 更新任务关联文件名称

 actor -> asyncService: 任务列表
 actor -> asyncService: 下载任务文件
 asyncService -> actor: 返回文件

end alt





@enduml