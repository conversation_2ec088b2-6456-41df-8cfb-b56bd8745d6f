import functools
import logging
import random
import string
import time
from collections import namedtuple

import redis
import redis_lock
from pydantic import BaseModel
from redis.exceptions import RedisError
from tenacity import retry, retry_if_exception_type, wait_random, stop_after_attempt, stop_after_delay, before_log

from biz import config
# Python 3 compatibility
from biz.api_exception import ServerError, ParameterException

string_type = getattr(__builtins__, 'basestring', str)

try:
    basestring
except NameError:
    basestring = str

Lock = namedtuple("Lock", ("validity", "resource", "key"))


class CannotObtainLock(Exception):
    pass


class MultipleRedlockException(Exception):
    def __init__(self, errors, *args, **kwargs):
        super(MultipleRedlockException, self).__init__(*args, **kwargs)
        self.errors = errors

    def __str__(self):
        return ' :: '.join([str(e) for e in self.errors])

    def __repr__(self):
        return self.__str__()


class Redlock(object):
    default_retry_count = 3
    default_retry_delay = 0.2
    clock_drift_factor = 0.01
    unlock_script = """
    if redis.call("get",KEYS[1]) == ARGV[1] then
        return redis.call("del",KEYS[1])
    else
        return 0
    end"""

    def __init__(self, connection_list, retry_count=None, retry_delay=None):
        self.servers = []
        for connection_info in connection_list:
            try:
                if isinstance(connection_info, string_type):
                    server = redis.StrictRedis.from_url(connection_info)
                elif type(connection_info) == dict:
                    server = redis.StrictRedis(**connection_info)
                else:
                    server = connection_info
                self.servers.append(server)
            except Exception as e:
                raise Warning(str(e))
        self.quorum = (len(connection_list) // 2) + 1

        if len(self.servers) < self.quorum:
            raise CannotObtainLock(
                "Failed to connect to the majority of redis servers")
        self.retry_count = retry_count or self.default_retry_count
        self.retry_delay = retry_delay or self.default_retry_delay

    def lock_instance(self, server, resource, val, ttl):
        try:
            assert isinstance(ttl, int), 'ttl {} is not an integer'.format(ttl)
        except AssertionError as e:
            raise ValueError(str(e))
        return server.set(resource, val, nx=True, px=ttl)

    def unlock_instance(self, server, resource, val):
        try:
            server.eval(self.unlock_script, 1, resource, val)
        except Exception as e:
            logging.exception("Error unlocking resource %s in server %s", resource, str(server))

    def get_unique_id(self):
        CHARACTERS = string.ascii_letters + string.digits
        return ''.join(random.choice(CHARACTERS) for _ in range(22)).encode()

    def lock(self, resource, ttl):
        retry = 0
        val = self.get_unique_id()

        # Add 2 milliseconds to the drift to account for Redis expires
        # precision, which is 1 millisecond, plus 1 millisecond min
        # drift for small TTLs.
        drift = int(ttl * self.clock_drift_factor) + 2

        redis_errors = list()
        while retry < self.retry_count:
            n = 0
            start_time = int(time.time() * 1000)
            del redis_errors[:]
            for server in self.servers:
                try:
                    if self.lock_instance(server, resource, val, ttl):
                        n += 1
                except RedisError as e:
                    redis_errors.append(e)
            elapsed_time = int(time.time() * 1000) - start_time
            validity = int(ttl - elapsed_time - drift)
            if validity > 0 and n >= self.quorum:
                if redis_errors:
                    raise MultipleRedlockException(redis_errors)
                return Lock(validity, resource, val)
            else:
                for server in self.servers:
                    try:
                        self.unlock_instance(server, resource, val)
                    except:
                        pass
                retry += 1
                time.sleep(self.retry_delay)
        return False

    def unlock(self, lock):
        redis_errors = []
        for server in self.servers:
            try:
                self.unlock_instance(server, lock.resource, lock.key)
            except RedisError as e:
                redis_errors.append(e)
        if redis_errors:
            raise MultipleRedlockException(redis_errors)


dlm = Redlock([config.APP_REDIS_URL])

conn = redis.StrictRedis.from_url(config.APP_REDIS_URL)


def redlock_decorator(lock_name, exp, **kwargs):
    """
    Docs:
        kwargs expect datastructure: List[Tuple(key_name, kw_seq), ...]
            key_name: means args in `meth:wrapper` params.
            kw_seq: key_name order number in `func:params`.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kw):
            keys = kwargs.get('keys', [])
            retry_times = kwargs.get('retry_times') or 3
            except_msg = kwargs.get('except_msg') or '存在并发冲突，请稍后再试'
            retry_params = {
                'retry': retry_if_exception_type(redis_lock.AlreadyAcquired),
                'wait': wait_random(min=1, max=2),
                'stop': (stop_after_delay(exp) | stop_after_attempt(retry_times)),
                'reraise': True,
                'before': before_log(logging.getLogger(__name__), logging.DEBUG)
            }
            retry_params.update(kwargs.get('retry_params', {}))

            params_str = ''
            for k, v in keys:
                if k in kw:
                    val = kw.get(k)
                elif len(args) > v:
                    val = str(args[v])
                else:
                    val = v
                val = f'{k}_{val}_'
                params_str += val
            tenant_lock_name = f'{lock_name}_{config.TENANT_ID}_{params_str}'
            logging.info(msg=f'{"*"*30} level_table tenant_lock_name: {tenant_lock_name}')

            @retry(**retry_params)
            def lock_context():
                with redis_lock.Lock(conn, tenant_lock_name, expire=exp, auto_renewal=True):
                    fun = func(*args, **kw)
                    return fun

            try:
                res = lock_context()
            except ParameterException as e:
                raise ServerError(msg=e.msg)
            except RuntimeError as e:
                logging.warning(msg=e)
                raise ServerError(msg=except_msg)
            return res

        return wrapper

    return decorator


def redlock_decrator(lock_name, exp):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kw):
            brand_code = ''
            if len(args) > 1 and isinstance(args[1], BaseModel) and hasattr(args[1], 'brand_code'):
                brand_code = args[1].brand_code

            tenant_lock_name = '{}_{}_{}'.format(lock_name, config.TENANT_ID, brand_code)
            lock = dlm.lock(tenant_lock_name, exp)
            while not lock:
                lock = dlm.lock(tenant_lock_name, exp)
                time.sleep(0.8)

            try:
                fun = func(*args, **kw)
            except Exception as e:
                dlm.unlock(lock)
                raise e
            dlm.unlock(lock)
            return fun

        return wrapper

    return decorator
