# *_*coding:utf-8 *_*
# @Time    : 2018/9/13 11:23
# <AUTHOR> hl_python
# @Email   : <EMAIL>
# @File    : base_db.py
# @Software: PyCharm

import logging

from flask_sqlalchemy import SQLAlchemy as _SQLAlchemy, BaseQuery

from sqlalchemy import ForeignKey as _ForeignKey, Table as _Table
from contextlib import contextmanager

from biz import config
from biz.api_exception import NotFound, DBAccessFail

logger = logging.getLogger()


CONSTANT = config['schema_constant']


class SQLAlchemy(_SQLAlchemy):
    @contextmanager
    def auto_commit(self):
        try:
            yield
            self.session.commit()
        except Exception as e:
            # import pdb;pdb.set_trace()
            # from nose.tool import set_trace; set_trace()
            logger.exception(e)
            db.session.rollback()
            raise DBAccessFail()


class Query(BaseQuery):

    def get_or_404(self, ident):
        rv = self.get(ident)
        if not rv:
            raise NotFound()
        return rv

    def first_or_404(self):
        rv = self.first()
        if not rv:
            raise NotFound()
        return rv


class ForeignKey(_ForeignKey):

    def __init__(self, column, *args, **kwargs):
        column = CONSTANT["SYSTEM_SCHEMA_NAME"] + '.' + column
        super().__init__(column, *args, **kwargs)


class Table(_Table):

    def __new__(cls, *args, **kwargs):
        kwargs['schema'] = CONSTANT['SYSTEM_SCHEMA_NAME']
        return super().__new__(cls, *args, **kwargs)

    def __init__(self, *args,  **kwargs):
        kwargs['schema'] = CONSTANT['SYSTEM_SCHEMA_NAME']
        super().__init__(*args, **kwargs)


def include_object(object, name, type_, reflected, compare_to):
    print("object.info: \n", object.info)
    # if type_ != "column" and reflected:
    #     return False
    # else:
    #     return True
    if type_ == 'table' and object.schema not in (CONSTANT['SYSTEM_SCHEMA_NAME'], CONSTANT['SCHEMA_NAME']):
        return False

    return True


db = SQLAlchemy(query_class=Query)
db.ForeignKey = ForeignKey
db.Table = Table
