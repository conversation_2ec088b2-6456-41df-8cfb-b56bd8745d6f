from flask import request, send_file

from biz import http
from biz.typedef import Result, AnyResult

from . import typedef
from .service.aggregation_dimension import aggregation_dimension_service
from .service.refer_skc import refer_skc_service


class LifeCycleController(http.Controller):
    """生命周期controller"""

    # ============================== 聚合维度 ===============================

    @http.route(
        "/api/v6.0.0/life-cycle/aggregation-dimension/filter",
        methods=["POST"],
    )
    def aggregation_dimension_filter(self):
        """聚合维度筛选"""

        query_info = typedef.AggregationDimensionFilterRequest(**request.json)
        data = aggregation_dimension_service.query_aggregation_dimension_filter(
            query_info
        )
        return AnyResult(data=data).dict()


    @http.route(
        "/api/v6.0.0/life-cycle/aggregation-dimension/base-kpi",
        methods=["POST"],
    )
    def query_aggregation_dimension_base_kpi(self):
        """聚合维度基础指标"""

        req = typedef.AggregationDimensionBaseKpiRequest(**request.json)

        data = aggregation_dimension_service.query_aggregation_dimension_base_kpi(req)
        return AnyResult(data=data).dict()


    @http.route(
        "/api/v6.0.0/life-cycle/aggregation-dimension/line-chart",
        methods=["POST"],
    )
    def query_aggregation_dimension_line_chart(self):
        """聚合维度图表"""

        req = typedef.AggregationDimensionLineChartRequest(**request.json)

        data = aggregation_dimension_service.query_aggregation_dimension_line_chart(req)
        return AnyResult(data=data).dict()



    @http.route(
        "/api/v6.0.0/life-cycle/aggregation-dimension/list",
        methods=["POST"],
    )
    def query_aggregation_dimension_list(self):
        """聚合维度列表"""

        req = typedef.AggregationDimensionListRequest(**request.json)

        data = aggregation_dimension_service.query_aggregation_dimension_list(req)
        return AnyResult(data=data).dict()


    @http.route(
        "/api/v6.0.0/life-cycle/aggregation-dimension/export",
        methods=["POST"],
    )
    def export_aggregation_dimension(self):
        """聚合维度导出"""
        # todo 暂不实现
        req = typedef.AggregationDimensionListRequest(**request.json)

        data = aggregation_dimension_service.export_aggregation_dimension(req)
        return AnyResult(data=data).dict()


    @http.route(
        "/api/v6.0.0/life-cycle/aggregation-dimension/skc-list",
        methods=["POST"],
    )
    def query_aggregation_dimension_skc_list(self):
        """聚合维度包含款清单"""

        req = typedef.AggregationDimensionFilterRequest(**request.json)

        data = aggregation_dimension_service.query_aggregation_dimension_skc_list(req)
        return AnyResult(data=data).dict()


    @http.route(
        "/api/v6.0.0/life-cycle/aggregation-dimension/skc-list-export",
        methods=["POST"],
    )
    def export_aggregation_dimension_skc_list(self):
        """参考款筛选"""

        req = typedef.AggregationDimensionFilterRequest(**request.json)

        data = aggregation_dimension_service.export_aggregation_dimension_skc_list(req)
        return AnyResult(data=data).dict()



    @http.route(
        "/api/v6.0.0/life-cycle/aggregation-dimension/refer-skc-list",
        methods=["POST"],
    )
    def query_aggregation_dimension_refer_skc_list(self):
        """参考款列表"""

        req = typedef.AggregationDimensionFilterRequest(**request.json)

        data = aggregation_dimension_service.query_aggregation_dimension_refer_skc_list(req)
        return AnyResult(data=data).dict()

    # ============================== 参考款 =====================================
    @http.route(
        "/api/v6.0.0/life-cycle/refer-skc/filter",
        methods=["POST"],
    )
    def query_refer_skc_filter(self):
        """参考款筛选器"""

        req = typedef.ReferSkcFilterRequest(**request.json)

        data = refer_skc_service.query_refer_skc_filter(req)
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/life-cycle/refer-skc/list",
        methods=["POST"],
    )
    def query_refer_skc_list(self):
        """参考款筛选器"""

        req = typedef.ReferSkcListRequest(**request.json)

        data = refer_skc_service.query_refer_skc_list(req)
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/life-cycle/refer-skc/export",
        methods=["POST"],
    )
    def export_refer_skc_list(self):
        """导出"""

        req = typedef.ReferSkcListRequest(**request.json)

        # data = refer_skc_service.export_refer_skc_list(req)
        data={}
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/life-cycle/refer-skc/refer-skc-filter",
        methods=["POST"],
    )
    def query_refer_skc_refer_skc_filter(self):
        """参考款筛选器"""

        req = typedef.ReferSkcFilterRequest(**request.json)

        data = refer_skc_service.query_refer_skc_refer_skc_filter(req)
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/life-cycle/refer-skc/refer-skc-list",
        methods=["POST"],
    )
    def query_refer_skc_refer_skc_list(self):
        """参考款筛选器"""

        req = typedef.ReferSkcReferSkcListRequest(**request.json)

        data = refer_skc_service.query_refer_skc_refer_skc_list(req)
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/life-cycle/refer-skc/add",
        methods=["POST"],
    )
    def add_refer_skc(self):
        """"""

        req = typedef.AddOrRemoveReferSkcRequest(**request.json)

        data = refer_skc_service.add_refer_skc(req)
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/life-cycle/refer-skc/remove",
        methods=["GET"],
    )
    def remove_refer_skc(self):
        """参考款筛选器"""

        req = typedef.AddOrRemoveReferSkcRequest(**request.json)

        data = refer_skc_service.remove_refer_skc(req)
        return AnyResult(data=data).dict()

    @http.route(
        "/api/v6.0.0/life-cycle/refer-skc/save",
        methods=["POST"],
    )
    def save_refer_skc(self):
        """参考款筛选器"""

        req = typedef.SaveReferSkcRequest(**request.json)

        data = refer_skc_service.save_refer_skc(req)
        return AnyResult(data=data).dict()









