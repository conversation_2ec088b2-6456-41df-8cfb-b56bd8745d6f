from typing import Dict, Any

from ourbatis.sql_source_parser.objects import ForeachValueInfo, BatisElement
from pydantic import BaseModel


class SqlContext(BaseModel):
    input_params: Dict[str, Any]  # sql mapper外部入参
    sql_mapper: Dict[str, BatisElement]
    inner_gen_params: Dict[str, Any] = {}  # sql mapper 内部自动生成的参数(foreach/bind)语法生成
    foreach_temp_values: Dict[str, ForeachValueInfo] = {}  # todo for循环临时丑陋实现方案
    no_annotate: bool = False  # 是否生成注释

    @property
    def params(self) -> Dict[str, Any]:
        all_params = {**self.input_params, **self.inner_gen_params}
        return all_params

    class Config:
        arbitrary_types_allowed = True
