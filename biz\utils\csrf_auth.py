import json
import logging
import os
from datetime import datetime

import redis
from flask import request
from flask import session
from flask_log_request_id import current_request_id

from biz import config
from biz.api_exception import InvalidCsrfToken, NeedLogin, NotFound
from oslo_utils.uuidutils import generate_uuid
from flask_login import current_user
from flask_login.config import EXEMPT_METHODS
from flask import current_app

from biz.modules.module import blueprint_module_info as business_bp_info
from config.config_manager import Settings
from custom_module.skip_auth import CUSTOM_SKIP_AUTH

log = logging.getLogger()

env = os.getenv("biz_ENV", "dev")

SKIP_AUTH = (
    "auth.login",
    "auth.pt_login",
    "auth.captcha",
    "public_key.public_key",
    "auth.forget_pd_change_pd",
    "auth.forget_pd_send_email",
    "constraints.detail_constraints",
    "heartbeat.heartbeat",
    "admin.create_admin",
    "admin.modify_admin",
    "module_operation.get",
    "module_operation.add",
    "module_operation.disable",
    "module_operation.enable",
    "forgot_pwd.forgot_pwd",
    "forgot_pwd.verify_token",
    "forgot_pwd.set_new_pwd",
    "data.query_human_allot_data",
    "data.query_human_replenish_data",
    "data.query_human_extra_allot_data",
    "data.query_replenish_allot_data_general",
    "quick_response_detail.calculate_callback",
    "plan_info.run_plan_info",
    "plan_info.remove_plan_rerun_info",
    "plan_info.query_run_progress",
    "plan_info.callback_by_model_finish",
    "plan_info.save_plan_info",
    "plan_info.re_run_plan",
    "plan_info.re_run_plan_finish",
    "plan_info.re_run_plan_info",
    "feature_template.query_term_dict",
    "ra_decision.get_available_page_configs",
    "batch_process.query_batch_process_order_list",
    "batch_process.update_batch_process_order_status",
    "batch_process.inner_query_batch_process_order_list",
    "batch_process.trigger_batch_process",
    "batch_process.inner_update_batch_process_order_status",
    "gto_instruct.auto_order_commit",
    "batch_process.query_batch_process_reorder_order_list",
    "batch_process.update_batch_process_reorder_order_status",
    "filter.hit_condition_sql_script",
    "plan_info.preview_plan_info",
    "repeat_order.list_channel_type",
    "repeat_order.list_decision_skc",
    "repeat_order.query_skc_base_info",
    "repeat_order.query_base_skc_target_info",
    "repeat_order.query_otb_info",
    "repeat_order.query_purchase_info",
    "repeat_order.get_repeat_order_parameter",
    "repeat_order.same_skc_history",
    "repeat_order.simulation_line",
    "repeat_order.channel_forecast_info",
    "repeat_order.cache_request",
    "repeat_order.all_forecast_info",
    "auth_record.break_up_auth_record_by_airflow",
    "report_table.query_report_content",
    "report_table.gto_simulation_calculate",
    "tenant_conf.tenant_conf",
    "base.bpmn_file",
    "feature_workbench.feature_expression",
    "page_filter.delete_page_filter_cache",

)

CSRF_TOKEN_KEY = "csrf-token"

# redis连接实例
cache = redis.from_url(config.APP_REDIS_URL)


def tenant_csrf_token_key():
    return CSRF_TOKEN_KEY
    # return f'{config.APP_ID}-{CSRF_TOKEN_KEY}'  # 后续优化


def generate_csrf_token():
    csrf_key = tenant_csrf_token_key()
    if csrf_key not in session:
        session[csrf_key] = generate_uuid()
    return session[csrf_key]


def csrf_filter():
    user_csrf_token = request.headers.get(CSRF_TOKEN_KEY, "")
    csrf_key = tenant_csrf_token_key()
    csrf_token = session.get(csrf_key, "")
    if user_csrf_token != csrf_token:
        raise NeedLogin()


def lz_login_required():

    username = Settings.LOCAL_USER_NAME
    if username:
        # 调试使用
        from flask_login import login_user
        from biz_module.base.auth.models import User
        user = User.query.filter_by(username=username).first()
        login_user(user)
        from biz_module.base.authorization.auth_record.service import auth_record_service
        auth_record_service.set_default_auth_to_cache_if_exist(current_user.user_id)

    log.info(
        """|~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~|
    \r|url        : {meth} {url}
    \r|form       : {values}
    \r|json       : {json}
    \r|end|bp       : {end} {bp}

    """.format(
            meth=request.method,
            url=request.url,
            values=request.form or None,
            json=request.get_json() if request.content_type and 'application/json' in  request.content_type else None,
            end=request.endpoint,
            bp=request.blueprint,
        )
    )

    if request.method in EXEMPT_METHODS:
        return

    if not username:
        try:
            if current_user.is_authenticated:
                username = current_user.username
            else:
                username = "未知用户"

            session_data = None
            if session:
                session_dict = dict(session._get_current_object())
                session_dict["session_id"] = session.sid
                session_data = json.dumps(session_dict, ensure_ascii=False)

            req_body = {
                "json": request.get_json() or "",
                "form_data": request.form or "",
                "file": request.files.get("file").filename if request.files else "",
            }
            user_record_info_dict = {
                "username": username,
                "method": request.method,
                "url": request.url,
                "user_agent": str(request.headers.get("User-Agent")),
                "endpoint": request.endpoint,
                "json_data": json.dumps(req_body, ensure_ascii=False),
                "session_data": session_data,
                "request_id": current_request_id(),
                "create_time": datetime.now(),
            }
            from biz_module.base.users_record.service.users_record import put_task

            put_task(user_record_info_dict)
        except Exception as e:
            log.info(e)

    if request.blueprint in business_bp_info:
        if business_bp_info[request.blueprint].get("is_enable", False) and business_bp_info[request.blueprint].get(
            "is_display", False
        ):
            pass
        else:
            raise NotFound(msg="该模块已被禁用")

    if request.endpoint in SKIP_AUTH or request.endpoint in CUSTOM_SKIP_AUTH:
        return
    elif current_app.login_manager._login_disabled:
        return
    elif not current_user.is_authenticated:
        raise NeedLogin()

    # TODO 接口级别权限控制
    # # username = "admin"
    # # user = User.query.filter_by(username=username).first()
    # user = current_user
    # if user.username != 'admin':
    #     roles = user.roles
    #     allow_api_list = []
    #     for role in roles:
    #         for operation_permission in role.operation_permissions:
    #             allow_api_list.append(operation_permission.api_endpoint)
    #
    #     if request.endpoint not in allow_api_list:
    #         raise InsufficientPermissionsError()

    csrf_filter()


