import inspect
from copy import deepcopy
from typing import Callable, Dict, Any, Sequence, Mapping, Union, List, Tuple, Literal

from pydantic.error_wrappers import <PERSON>rrorWrapper
from pydantic.fields import <PERSON><PERSON>ield
from pydantic.main import ModelMetaclass

from ourbatis.const import REMOVE_PARAMS
from ourbatis.dependant import Dependant
from ourbatis.exceptions import ParamValidFailed
from ourbatis.utils import logger


def modify_args(arg_type: Literal[REMOVE_PARAMS], call: Callable, args):
    if len(args) == 0:
        return args

    call_name = call.__qualname__.split(".")[0]
    if arg_type == "self" and args[0].__class__.__qualname__ == call_name:
        return args[1:]
    elif arg_type == "cls" and args[0].__qualname__ == call_name:
        return args[1:]
    else:
        return args


def parse_user_params(call: Callable, *args, **kwargs) -> Dict[str, Any]:
    param_dict = {}
    params = inspect.signature(call).parameters

    # todo 暂时通过参数名self,cls识别实例和类方法（utils.inspect_type 不支持偏函数和装饰器函数)
    param_keys = list(params.keys())
    if param_keys and param_keys[0] in REMOVE_PARAMS:
        args = modify_args(param_keys[0], call, args)
        param_keys = param_keys[1:]

    if len(args) > 0:
        param_dict = {key: value for (key, value) in zip(param_keys, args)}
    if len(kwargs) > 0:
        param_dict.update(kwargs)

    return param_dict


def flat_params(param_dict: Dict[str, Any]) -> Dict[str, Any]:
    extend_dict = {}
    for value in param_dict.values():
        if not isinstance(value.__class__, ModelMetaclass):
            continue
        extend_dict.update(value.dict())
    param_dict.update(extend_dict)
    return param_dict


def request_params_to_args(
    required_params: Sequence[ModelField],
    received_params: Union[Mapping[str, Any]],
) -> Tuple[Dict[str, Any], List[ErrorWrapper]]:
    values = {}
    errors = []
    for field in required_params:
        value = received_params.get(field.alias)

        if value is None and not field.required:
            values[field.name] = deepcopy(field.default)
            continue

        v_, errors_ = field.validate(
            value,
            values,
            loc=(f"get value: {value!r}", f"check type: {field.type_}", field.alias),
        )
        if isinstance(errors_, ErrorWrapper):
            logger.warning(f"******* param parse error info => {errors_}")
            values[field.name] = v_  # 没追求的研发太多,只能告警了(即使报错了，还是要赋值)
            # errors.append(errors_)  # 没追求的研发太多,只能告警了
        elif isinstance(errors_, list):
            errors.extend(errors_)
        else:
            values[field.name] = v_
    return values, errors


def get_call_params(call: Callable, *args, **kwargs) -> dict:
    param_dict = parse_user_params(call, *args, **kwargs)  # 解析用户入参

    dependant: Dependant = call.__dependant__

    param_dict, error = request_params_to_args(
        dependant.params, param_dict
    )  # 将用户入参转为sql参数

    if error:
        error_str = f"param parse error info==>{error}"
        logger.error(error_str)
        raise ParamValidFailed(error_str)

    flat_param_dict = flat_params(param_dict)  # 将对象内的参数展开
    return flat_param_dict
