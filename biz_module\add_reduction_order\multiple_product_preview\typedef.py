from datetime import datetime, date
from typing import List, Optional, Union, Dict
from pydantic import BaseModel, validator, root_validator
from werkzeug.datastructures import FileStorage

from biz.typedef import CommonBaseModel


class PageConfigReq(BaseModel):
    """
    请求页面配置
    """

    config_name: Optional[str]  # 页面配置名
    module_name: Optional[str]
    page_name: Optional[str]
    user_id: Optional[int]
    is_product: Optional[bool]
    is_tenant: Optional[bool]
    is_common: Optional[bool]


class PageConfigIdReq(PageConfigReq):
    """
    保存配置
    """

    page_id: int


class PageConfigSaveReq(BaseModel):
    """
    保存配置
    """

    page_id: int
    parent_code: Optional[str]
    feature_type: str
    feature_code: str
    module_name: str
    en_name: Optional[str]
    user_id: Optional[int]


class FeatureConfigEgg(BaseModel):
    feature_code: Optional[str]
    feature_name: Optional[str]
    feature_dim: Optional[str]
    feature_type: Optional[str]
    status: Optional[int] = 0
    from_table: Optional[str]
    target_table: Optional[str]
    targe_history_table: Optional[str]


class FeatureConfigParam(BaseModel):
    feature_name: str
    feature_code: str
    value_type: str
    data_type: Optional[str]
    serial_number: Optional[int]
    table_alias: Optional[str]
    parent_code: Optional[str]


class FeatureConfigResp(BaseModel):
    feature_code: Optional[str]
    feature_name: Optional[str]
    feature_dim: Optional[str]
    feature_type: Optional[str]
    status: Optional[int] = 0
    from_table: Optional[str]
    target_table: Optional[str]
    targe_history_table: Optional[str]


class MultipleProductPreviewFilterRequest(BaseModel):

    filter_key: Optional[str]
    channel_type: Optional[str]
    org_sk: Optional[int]
    big_class: Optional[List[str]]
    product_range: Optional[List[str]]
    product_year_quarter: Optional[List[str]]
    product_belong_name: Optional[List[str]]
    ps_mid_class: Optional[List[str]]
    sale_mid_tiny_class: Optional[List[str]]
    gender: Optional[List[str]]
    product_name: Optional[List[str]]
    skc_sk: Optional[List[int]]
    day_date: Optional[str]
    org_sk_list: Optional[List[int]]
    is_admin: Optional[bool] = False
    user_id: Optional[int]

    @root_validator(pre=True)
    def change_str_to_list(cls, values):
        if isinstance(values.get('big_class'), str):
            values['big_class'] = [values['big_class']]
        if isinstance(values.get('product_range'), str):
            values['product_range'] = [values['product_range']]
        if isinstance(values.get('product_year_quarter'), str):
            values['product_year_quarter'] = [values['product_year_quarter']]
        if isinstance(values.get('product_belong_name'), str):
            values['product_belong_name'] = [values['product_belong_name']]
        if isinstance(values.get('ps_mid_class'), str):
            values['ps_mid_class'] = [values['ps_mid_class']]
        if isinstance(values.get('sale_mid_tiny_class'), str):
            values['sale_mid_tiny_class'] = [values['sale_mid_tiny_class']]
        if isinstance(values.get('gender'), str):
            values['gender'] = [values['gender']]
        if isinstance(values.get('product_name'), str):
            values['product_name'] = [values['product_name']]
        if isinstance(values.get('skc_sk'), int):
            values['skc_sk'] = [values['skc_sk']]
        return values



class MultipleProductPreviewListRequest(MultipleProductPreviewFilterRequest):

    sort_filter: Optional[List[Dict]]
    page_size: Optional[int] = 10
    page_no: Optional[int] = 1
    page_offset: Optional[int] = 0
    column_orders_str: Optional[str]
    status: Optional[List[int]]
    decision_suggest: Optional[List[str]]
    order_by: Optional[str]


class UpdateMultipleProductPreviewRequest(BaseModel):

    uuid: str
    decision_suggest: Optional[str]
    change_reason: Optional[str]
    human_decision_qty: Optional[int]
    day_date: Optional[str]
    org_sk: Optional[int]
    skc_sk: Optional[int]
    user_id: Optional[int]


class SaveMultipleProductPreviewDetail(BaseModel):

    uuid: str
    decision_suggest: Optional[str]
    change_reason: Optional[str]
    human_decision_qty: Optional[Union[int, str]]
    day_date: Optional[str]
    org_sk: Optional[int]
    skc_sk: Optional[int]



class SaveMultipleProductPreviewRequest(BaseModel):

    data: List[SaveMultipleProductPreviewDetail]
    user_id: Optional[int]


class ConfirmMultipleProductPreviewRequest(MultipleProductPreviewListRequest):

    uuid_list: Optional[List[str]]
    all: bool = False
    target_status: int


class MultipleProductPreviewDetailKpiRequest(BaseModel):

    day_date: str
    skc_sk: int
    org_sk: int


class SingleDecisionFilterRequest(BaseModel):
    filter_key: Optional[str]
    channel_type: Optional[str]
    org_sk: Optional[int]
    big_class: Optional[List[str]]
    product_range: Optional[List[str]]
    product_year_quarter: Optional[List[str]]
    product_belong_name: Optional[List[str]]
    ps_mid_class: Optional[List[str]]
    sale_mid_tiny_class: Optional[List[str]]
    gender: Optional[List[str]]
    product_name: Optional[List[str]]
    skc_sk: Optional[int]
    day_date: Optional[str]
    org_sk_list: Optional[List[int]]
    is_admin: Optional[bool] = False
    user_id: Optional[int]

    @root_validator(pre=True)
    def change_str_to_list(cls, values):
        if isinstance(values.get('big_class'), str):
            values['big_class'] = [values['big_class']]
        if isinstance(values.get('product_range'), str):
            values['product_range'] = [values['product_range']]
        if isinstance(values.get('product_year_quarter'), str):
            values['product_year_quarter'] = [values['product_year_quarter']]
        if isinstance(values.get('product_belong_name'), str):
            values['product_belong_name'] = [values['product_belong_name']]
        if isinstance(values.get('ps_mid_class'), str):
            values['ps_mid_class'] = [values['ps_mid_class']]
        if isinstance(values.get('sale_mid_tiny_class'), str):
            values['sale_mid_tiny_class'] = [values['sale_mid_tiny_class']]
        if isinstance(values.get('gender'), str):
            values['gender'] = [values['gender']]
        if isinstance(values.get('product_name'), str):
            values['product_name'] = [values['product_name']]
        return values



class SingleDecisionSkcInfoRequest(BaseModel):

    channel_type: str
    skc_sk: int
    day_date: str
    org_sk: int



class UpdateSingleDecisionDetailRequest(BaseModel):

    uuid: str
    human_decision_qty: int
