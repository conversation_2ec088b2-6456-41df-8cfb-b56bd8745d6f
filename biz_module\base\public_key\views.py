from biz import http, config
from biz.extensions import cache
from biz.common.lz_wsme.lz_signature import signature
# from biz.utils import rsa
from .types import PublicKeyResult, TenantPublicKey
from biz.settings import config
# from .models import KeyPair
# from biz.constants import PublicKeyC


class PublicKey(http.Controller):
    @http.route('/api/public_key')
    @cache.cached(timeout=1000)
    # @signature(PublicKeyResult)
    def public_key(self):
        # key_pairs = KeyPair.query.filter_by(id=PublicKeyC.GLOBAL_KEY_PAIR_ID).all()
        # if not key_pairs:
        #     private_key, public_key = rsa.get_key_pair()
        #     key_pair = KeyPair(id=PublicKeyC.GLOBAL_KEY_PAIR_ID, public_key=public_key, private_key=private_key)
        #     KeyPair.add(key_pair)
        # else:
        #     public_key = key_pairs[0].public_key
        public_key = config.RSA_PUBLIC_KEY

        ret = PublicKeyResult(content=TenantPublicKey(public_key=public_key, tenant_id=config.TENANT_ID))
        return ret.json()
