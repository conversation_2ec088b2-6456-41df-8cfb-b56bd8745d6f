import logging
import threading
from collections import OrderedDict, defaultdict
from datetime import datetime

from fastboot.utils.nest import egg
from flask_login import current_user

from biz import BaseService, Config
from biz.api_exception import ParameterException
from biz.utils.redlock import redlock_decrator
from biz.utils.tools import context_wrapper
from biz_module.add_reduction_order.base.constant import ComponentType, TAG_DIM_MAP, TAG_FROM_TABLE, FEATURE_CONFIG
from biz_module.add_reduction_order.base.dao import PageComponentConfigDao, AddOrderFeatureConfigDao
from biz_module.add_reduction_order.base.typedef import PageConfigReq, PageConfigIdReq, PageConfigSaveReq, \
    FeatureConfigEgg, FeatureConfigParam
from biz_module.base.page_config.service.page_config import (
    page_config_service as conf_srv,
)
from biz_module.base.page_config.service.page_config import PageConfigService
from biz_module.base.page_config.typedef.page_config import GetConfigListReq
from biz_module.common.feature_workbench.service.feature_service import FeatureService


logger = logging.getLogger(__name__)


@egg
class PageComponentConfigService(BaseService):
    dao: PageComponentConfigDao

    feature_service: FeatureService

    feature_config_dao: AddOrderFeatureConfigDao

    module_table_mapping = {
        "multiple_product_preview": {
            "target_table": "qrs_add_order_multiple_product_preview_dynamic_field",
            "targe_history_table": "qrs_add_order_multiple_product_preview_dynamic_field_history",
        },
        "single_product_decision": {
            "target_table": "qrs_add_order_single_product_decision_dynamic_field",
            "targe_history_table": "qrs_add_order_single_product_decision_dynamic_field_history",
        },
    }

    target_table_p_name_mapping = {
        "qrs_add_order_multiple_product_preview_dynamic_field": "p_qrs_skc_org_feature_realtime",
        "qrs_add_order_single_product_decision_dynamic_field": "p_qrs_skc_channel_org_feature_realtime",
    }

    def get_page_config(self, params: PageConfigReq):
        params.user_id = current_user.user_id
        user_page = PageConfigReq(user_id=current_user.user_id)
        user_page.page_name = params.page_name
        user_page.module_name = params.module_name
        page_id = self.dao.get_user_page_config_mapping(user_page)
        if current_user.type_code == 1:
            # 能看到配置的级别
            params.is_product = True
            params.is_tenant = True
        else:
            # 能看到配置的级别
            params.is_common = True
        if page_id:
            config_content = self.dao.get_page_config_by_id(page_id)
        else:
            config_content = self.dao.query_qrs_page_config(params)
        return config_content

    def get_page_config_by_id(self, page_id):
        config_content = self.dao.get_page_config_by_id(page_id)
        return config_content

    def list_page_config(self, params: GetConfigListReq):
        params.user_id = current_user.user_id
        res = conf_srv.get_config_list(params)
        res["page_id"] = None
        user_page = PageConfigReq(user_id=current_user.user_id)
        user_page.page_name = params.page_name
        user_page.module_name = params.module_name
        page_id = self.dao.get_user_page_config_mapping(user_page)
        if page_id is not None:
            for data in res.get("content", []):
                if data.get("page_id") == page_id:
                    res["page_id"] = page_id
                    break
        return res

    def update_config_application(self, params: PageConfigIdReq):
        result = self.dao.get_page_config_basic_by_id(params.page_id)
        if not result:
            logger.error("page_id 不存在 {}".format(params.page_id))
            raise ParameterException("非法操作！")
        params.user_id = current_user.user_id
        params.page_name = result.page_name
        params.module_name = result.module_name
        self.dao.add_user_page_config_mapping(params)
        return {"msg": "操作成功"}

    def handler_column_orders(self, sort_content_list, filed_table_alis_dict):

        order_sqls = []
        alias_dict = defaultdict(set)

        for column_order in sort_content_list:
            filed_table_alis = filed_table_alis_dict.get(column_order["en_name"])
            if not filed_table_alis or filed_table_alis.get("is_calculate"):
                continue
            table_alias = filed_table_alis.get("table_alias")
            alias_dict[table_alias].add(column_order["en_name"])
            value_type = filed_table_alis.get("value_type")
            column_order["value_type"] = value_type
            data_type = filed_table_alis.get("data_type")
            column_order["data_type"] = data_type
            if table_alias:
                alias_name = table_alias + "."
            else:
                alias_name = "a."

            # if column_order["type"].upper() == "DESC":
            #     order_type = " nulls last"
            # else:
            #     order_type = " nulls first"

            order_type = " nulls last"
            column_order["order_type"] = order_type

            column_order["alias_name"] = alias_name
            if data_type in ("numeric", "int"):
                order_sql = "{alias_name}{en_name}::{data_type} {type} {order_type}"
            elif data_type == "text":
                order_sql = (
                    " convert_to({alias_name}{en_name}, 'GBK') {type} {order_type}"
                )
            else:
                order_sql = "{alias_name}{en_name} {type} {order_type}"
            order_sqls.append(order_sql.format(**column_order))
        return " ,".join(order_sqls), alias_dict

    def parse_page_config_field(self, params: PageConfigReq):
        # feature_config_list = self.feature_config_dao.qrs_feature_code_list()
        # feature_config_dict = {
        #     feature_config.feature_code: feature_config
        #     for feature_config in feature_config_list
        # }
        # # for feature_config in feature_config_list:
        #     feature_config_dict[feature_config.feature_code] = feature_config
        config_content = self.get_page_config(params)
        component_type = config_content["type"]
        config_type = "fields"
        orders = "orders"
        header_dict_list = []
        table_alis_filed_dict = defaultdict(list)
        filed_table_alis_dict = defaultdict(dict)
        order_by = ""
        if component_type == ComponentType.type_tree.value:
            group_list = config_content.get(config_type)[0].get("children")
            for group_field in group_list:
                children_list = group_field.get("children")
                options = []
                display = group_field.get("display")
                for field_dict in children_list:
                    # if feature_config_dict.get(field_dict.get("en_name"), None):
                    #     field_dict["is_calculate"] = True
                        # continue
                    if field_dict.get("display") and display:
                        table_alias, en_name, header = self.parse_tree_node(field_dict)
                        if not field_dict.get("is_calculate"):
                            table_alis_filed_dict.setdefault(table_alias, []).append(
                                en_name
                            )
                        options.append(header)
                    filed_table_alis_dict[field_dict.get("en_name")] = field_dict
                # if options:
                if display:
                    header_dict = {
                        "title": group_field.get("cn_name"),
                        "options": options,
                    }
                    header_dict_list.append(header_dict)
        elif component_type == ComponentType.type_multiple_list.value:
            group_list = config_content.get(config_type)[0].get("children")
            for group_field in group_list:
                children_list = group_field.get("children")
                children = []
                display = group_field.get("display")
                for field_dict in children_list:
                    # if feature_config_dict.get(field_dict.get("en_name"), None):
                    #     field_dict["is_calculate"] = True
                        # continue
                    if field_dict.get("display") and display:
                        table_alias, en_name, header = self.parse_tree_node(field_dict)
                        if not field_dict.get("is_calculate"):
                            table_alis_filed_dict.setdefault(table_alias, []).append(
                                en_name
                            )
                        children.append(header)
                    filed_table_alis_dict[field_dict.get("en_name")] = field_dict
                # if options:
                if display:
                    header_dict = {
                        "name": group_field.get("cn_name"),
                        "code": group_field.get("en_name"),
                        "children": children,
                    }
                    header_dict_list.append(header_dict)
        else:
            content_list = config_content[config_type]
            for field_dict in content_list:
                # if feature_config_dict.get(field_dict.get("en_name"), None):
                #     field_dict["is_calculate"] = True
                if field_dict.get("display"):
                    table_alias, en_name, header = self.parse_list_node(field_dict)
                    if not field_dict.get("is_calculate"):
                        table_alis_filed_dict.setdefault(table_alias, []).append(
                            en_name
                        )
                    header_dict_list.append(header)
                filed_table_alis_dict[field_dict.get("en_name")] = field_dict

            order_by = self.handler_column_orders(
                config_content.get(orders, []), filed_table_alis_dict
            )

        return header_dict_list, table_alis_filed_dict, order_by, filed_table_alis_dict

    def parse_tree_node(self, node_content):
        table_alias = node_content.get("table_alias")
        en_name = node_content.get("en_name")
        name = (
            node_content.get("cn_name")
            if node_content.get("cn_name")
            else node_content.get("ori_name")
        )
        frontend_component = {
            "formatter": node_content.get("formatter")
            if not node_content.get("is_calculate")
            else "",
            "multiple": node_content.get("multiple"),
            "edit": node_content.get("edit"),
            "data_type": node_content.get("data_type"),
        }
        frontend_component["code"] = en_name
        frontend_component["lock"] = node_content.get("lock", "")
        frontend_component["name"] = name
        frontend_component["value"] = None
        frontend_component["col_width"] = node_content.get("col_width")
        frontend_component["width"] = node_content.get("width")
        frontend_component["value_type"] = node_content.get("value_type", None)
        return table_alias, en_name, frontend_component

    def parse_list_node(self, node_content):
        en_name = node_content.get("en_name")
        table_alias = node_content.get("table_alias")
        frontend_component = {
            "formatter": node_content.get("formatter")
            if not node_content.get("is_calculate")
            else "",
            "sort": node_content.get("sort"),
            "filter": node_content.get("filter"),
            "multiple": node_content.get("multiple"),
            "edit": node_content.get("edit"),
            "data_type": node_content.get("data_type"),
        }
        frontend_component["code"] = en_name
        frontend_component["col_width"] = node_content.get("col_width")
        frontend_component["name"] = (
            node_content.get("cn_name")
            if node_content.get("cn_name")
            else node_content.get("ori_name")
        )
        frontend_component["value_type"] = node_content.get("value_type", None)
        frontend_component["lock"] = node_content.get("lock", "")
        return table_alias, en_name, frontend_component

    def get_image_url_node(self, image_code, image_name):
        header = {
            "code": image_code,
            "name": image_name,
            "value_type": "string",
            "multiple": False,
            "col_width": "50",
            "formatter": "",
            "lock": "left",
            "edit": {},
        }
        return header

    @redlock_decrator("insert_qrs_feature_config", 10000)
    def insert_feature_to_config_and_table(self, query_info: PageConfigSaveReq):
        """
        插入特征到配置和配管管理表
        """
        module_name = query_info.module_name
        feature_info = self.feature_service.query_feature_info_by_code_and_dim(
            query_info
        )
        feature_dim = (
            TAG_DIM_MAP[feature_info.feature_dim]
            if feature_info.feature_type == "tag"
            else feature_info.feature_dim
        )
        feature_dim_str = ",".join(feature_dim)
        from_table = (
            TAG_FROM_TABLE[feature_info.feature_dim]
            if feature_info.feature_type == "tag"
            else TAG_FROM_TABLE[feature_info.feature_type]
        )
        feature_config = FeatureConfigEgg(
            feature_code=feature_info.feature_code,
            feature_name=feature_info.feature_name,
            feature_dim=feature_dim_str,
            feature_type=query_info.feature_type,
            from_table=from_table,
            target_table=self.module_table_mapping.get(module_name, {}).get(
                "target_table"
            ),
            targe_history_table=self.module_table_mapping.get(module_name, {}).get(
                "targe_history_table"
            ),
        )
        if not self.feature_config_dao.qrs_feature_code_exists(feature_config):
            self.feature_config_dao.qrs_insert_feature_to_table(feature_config)
            logger.info("async_create_job start")
            t = threading.Thread(
                target=context_wrapper(self.async_create_job),
                args=(
                    self.module_table_mapping.get(module_name, {}).get("target_table"),
                ),
            )
            t.start()
            logger.info("async_create_job end ...")
        return self.insert_feature_to_config(feature_info, query_info)

    def async_create_job(self, target_table: str):
        day_date = datetime.now().date()
        tenant_id = Config.TENANT_CODE
        schema = f"tenant_{tenant_id}_biz"

        try:
            self.feature_config_dao.qrs_skc_org_feature_realtime(
                self.target_table_p_name_mapping.get(target_table),
                day_date,
                target_table,
                schema,
            )
        except Exception as e:
            logging.info(f"调用数仓存过异常: {e}")

    def insert_feature_to_config(self, feature_info, query_info):
        # 获取配置
        page_config = self.get_page_config_by_id(query_info.page_id)
        if feature_info.feature_type == "kpi":
            if feature_info.data_type == "timestamp":
                value_type = "datetime"
            else:
                value_type = "number"
        else:
            value_type = "string"
        config_param = FeatureConfigParam(
            feature_name=feature_info.feature_name,
            feature_code=feature_info.feature_code,
            value_type=value_type,
            data_type=feature_info.data_type,
            serial_number=None,
            table_alias="df",
            parent_code="",
        )
        # 页面配置
        component_type = page_config["type"]
        config_type = "fields"
        if component_type == ComponentType.type_list.value:
            config_param.serial_number = (
                page_config[config_type][-1]["serial_number"] + 1
            )
            feature_config = FEATURE_CONFIG(config_param)
        elif component_type == ComponentType.type_tree.value:
            config_param.serial_number = 0
            feature_config = FEATURE_CONFIG(config_param)
        if feature_info.data_type:
            feature_config["data_type"] = feature_info.data_type
            if feature_info.data_type == "int":
                feature_config["formatter"] = "0,"
        feature_config["feature_type"] = feature_info.feature_type
        return feature_config

    def get_page_id_by_page_code(self, page_code):
        """
        通过页面名称获取页面id
        """
        return self.dao.get_page_id_by_page_code(page_code)


page_config_service = PageConfigService()
