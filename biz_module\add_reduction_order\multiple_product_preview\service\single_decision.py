import json
import logging
from collections import defaultdict
from typing import List

from fastboot.utils.nest import egg
from flask_login import current_user

from biz import BaseService
from biz.api_exception import ParameterException
from biz.utils.image_utils import get_images_name
from biz.utils.tools import check_non_negative_integer, check_integer
from ..constants import AddReductionOrderDecisionSuggestEnum, AddReductionOrderStatusEnum, StageEnum
from ..dao import SingleDecisionDao, MultipleProductPreviewDao
from biz_module.add_reduction_order.multiple_product_preview.service.page_config import PageComponentConfigService
from biz_module.add_reduction_order.multiple_product_preview import typedef as td

from biz_module.base.page_config.typedef.page_config import GetConfigListReq


logger = logging.getLogger(__name__)


@egg
class SingleDecisionService(BaseService):

    dao: SingleDecisionDao
    multiple_dao: MultipleProductPreviewDao
    page_config_service: PageComponentConfigService

    org_filter = ["channel_type", "org_sk"]
    skc_filter = ["day_date", "big_class","product_range","product_year_quarter","product_belong_name"
        ,"ps_mid_class","sale_mid_tiny_class","gender","product_name", "skc_sk"]

    @classmethod
    def column_order_sql_build(cls, column_orders: List[dict], filed_table_alis_dict):
        order_sqls = []
        error_msg = []
        for column_order in column_orders:
            field_column = filed_table_alis_dict.get(column_order["column_name"])
            if field_column.get("is_calculate"):
                error_msg.append(f'指标:{field_column["cn_name"]}正在计算中，不支持排序')
                continue
            table_alias = field_column.get("table_alias")
            value_type = field_column.get("value_type")
            column_order["value_type"] = value_type
            data_type = field_column.get("data_type")
            column_order["data_type"] = data_type

            if table_alias:
                alias_name = table_alias + "."

            # if column_order["order"].upper() == "DESC":
            #     order_type = " nulls last"
            # else:
            #     order_type = " nulls first"

            order_type = " nulls last"
            column_order["order_type"] = order_type

            column_order["alias_name"] = alias_name
            if data_type in ("numeric", "int"):
                order_sql = (
                    "{alias_name}{column_name}::{data_type} {order} {order_type}"
                )
            elif data_type == "text":
                order_sql = (
                    "convert_to({alias_name}{column_name}, 'GBK') {order} {order_type}"
                )
            else:
                order_sql = "{alias_name}{column_name} {order} {order_type}"
            order_sqls.append(order_sql.format(**column_order))
        if error_msg:
            raise ParameterException("; ".join(error_msg))
        return ",".join(order_sqls)

    @staticmethod
    def add_picture(item: dict):
        """添加图片"""
        if item.get("skc_code"):
            item["image_url"] = get_images_name(item["skc_code"])
        else:
            item["image_url"] = get_images_name(item["_skc_code"])


    def query_single_decision_filter(self, query_info: td.SingleDecisionFilterRequest):

        filter_key = query_info.filter_key
        data = []
        # 如果组织筛选 则需要过滤时间
        if filter_key in self.org_filter:
            index = self.org_filter.index(filter_key)
            for i in self.org_filter[index+1:]:
                setattr(query_info, i, None)
            from biz_module.base.authorization.auth_region.service import auth_region_service
            org_sk_list = auth_region_service.list_authorized_region_org_sk()
            query_info.org_sk_list = org_sk_list
            data = self.dao.query_single_decision_org_filter(query_info)

        elif filter_key in self.skc_filter:
            index = self.skc_filter.index(filter_key)
            # 将index更大的值置为None
            for i in self.skc_filter[index+1:]:
                setattr(query_info, i, None)
            query_info.is_admin = current_user.is_admin
            query_info.user_id = current_user.user_id
            data = self.dao.query_single_decision_filter(query_info)
        return data


    def query_single_decision_skc_info(self, query_info: td.SingleDecisionSkcInfoRequest):

        req = td.PageConfigReq(
            module_name="single_decision",
            page_name="base_kpi",
            user_id=current_user.user_id,
        )
        (
            header_dict_list,
            table_alis_filed_dict,
            order_by,
            filed_table_alis_dict,
        ) = self.page_config_service.parse_page_config_field(req)
        day_date_set = {
            j.get("code")
            for i in header_dict_list
            for j in i.get("options", {})
            if j.get("data_type") == "timestamp"
        }

        data = self.dao.query_single_decision_detail_kpi(query_info)

        for header_dict in header_dict_list:
            option_list = header_dict.get("options")
            for option_dict in option_list:
                if option_dict["code"] in day_date_set:
                    if data.get(option_dict.get("code")):
                        option_dict["value"] = data.get(
                            option_dict.get("code")
                        ).strftime("%Y-%m-%d")
                else:
                    option_dict["value"] = data.get(option_dict.get("code")) if data else None

        return {"data_list": header_dict_list}


    def query_single_decision_detail_info(self, query_info: td.SingleDecisionSkcInfoRequest):

        req = td.PageConfigReq(
            module_name="single_decision",
            page_name="decision_info",
            user_id=current_user.user_id,
        )
        (
            header_dict_list,
            table_alis_filed_dict,
            order_by,
            filed_table_alis_dict,
        ) = self.page_config_service.parse_page_config_field(req)
        day_date_set = {
            j.get("code")
            for i in header_dict_list
            for j in i.get("options", {})
            if j.get("data_type") == "timestamp"
        }
        result = []
        data = self.dao.query_single_decision_detail_info(query_info) or {}
        if not data:
            return {"header": header_dict_list, "data": []}
        date_list = []
        for i in header_dict_list:
            children = i.get('children', [])
            for j in children:
                value_type = j.get("value_type")
                if value_type == "datetime":
                    date_list.append(f"{j.get('code')}_edit_cancel")
        cancel_dict = {i: "" for i in date_list}
        channel_dict = {"id": 1, "channel_type": query_info.channel_type, "human_decision_qty_edit_cancel": ""}
        channel_dict.update(cancel_dict)
        result.append(channel_dict)
        data['parent_id'] = 1
        data['id'] = 2
        result.append(data)

        return {"header": header_dict_list, "data":  result}

    def query_single_decision_refer_skc_info(self, query_info: td.SingleDecisionSkcInfoRequest):

        data = self.dao.query_refer_skc_info(query_info)
        if not data:
            return {"xAxisData": [], "seriesData": [], "legendData": [], "typeData": [],
                    "backGround": []}
        xAxisData = [[], []]
        seriesData = [[], [], []]
        pre_stage = None
        stage_range = defaultdict(dict)
        listing_week = None
        for i in data:
            listing_week = i.get('listing_week')
            natural_week = i.get('natural_week')
            sale_qty =  i.get('sale_qty')
            refer_skc_sale_qty = i.get('refer_skc_sale_qty')
            xAxisData[0].append(f'{listing_week}W')
            xAxisData[1].append(f'{natural_week}W')
            stage = i.get('week_stage')
            seriesData[0].append(refer_skc_sale_qty)
            if stage == '已上市周':
                seriesData[1].append(sale_qty)
                seriesData[2].append(None)
            else:
                if pre_stage == '已上市周':
                    seriesData[1].append(sale_qty)
                else:
                    seriesData[1].append(None)
                seriesData[2].append(sale_qty)

            if pre_stage is None:
                stage_range[stage].update({"begin": listing_week})
            else:
                if pre_stage != stage:
                    stage_range[pre_stage].update({"end": listing_week})
                    stage_range[stage].update({"begin": listing_week})
            pre_stage = stage
        stage_range[pre_stage].update({"end": listing_week})
        backGround = []
        for key, value in stage_range.items():
            backGround.append({
                "name": key,
                "begin": value.get("begin") - 1,
                "end": value.get("end") - 1,
                "color": StageEnum.get_color_by_stage(key)
            })
        legendData = [{"name": '参照款', "color": "#FF0000"}, {"name": '当季款销量', "color": "#080a09"}, {"name": '预测', "color": "#080a09"}, ],
        typeData =  ['line', 'line', 'dashed_line']
        return {"xAxisData": xAxisData, "seriesData": seriesData, "legendData": legendData, "typeData": typeData,
                "backGround": backGround}


    def update_single_decision_detail_info(self, req: td.UpdateSingleDecisionDetailRequest):

        human_decision_qty = req.human_decision_qty
        # 校验人工决策量
        if not check_integer(human_decision_qty):
            raise ParameterException("请输入正确的数字")
        uuid = req.uuid
        org_sk, skc_sk, day_date = uuid.split('@@')
        self.dao.update_single_decision_detail_info(human_decision_qty, day_date, org_sk, skc_sk, current_user.user_id)

        return {"msg": "修改成功"}

single_decision_service = SingleDecisionService()

