# encoding: utf-8
from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel


class Result(BaseModel):
    """
    结果信息类
    """

    success: bool = True
    code: int = 0
    msg: str = "成功"
    data: dict = {}


class AnyResult(BaseModel):
    """
    结果信息类
    """

    success: bool = True
    code: int = 0
    msg: str = "成功"
    data: Any


class RequestInfo(BaseModel):
    """
    请求信息类
    """

    pass


class QueryPageInfo(BaseModel):
    """
    分页查询参数
    """

    page_no: int = 1  # 当前页
    page_size: int = 10  # 每页数量


class PageResultInfo(BaseModel):
    """
    分页结果信息
    """

    page_no: int  # 当前页
    page_size: int  # 每页数量
    total_rows: int  # 总记录数
    total_pages: Optional[int]  # 总页数
    items: list = []  # 具体记录列表


class CommonBaseModel(BaseModel):
    is_deleted: int = 0  # 是否删除
    create_time: Optional[datetime]  # 创建时间
    modify_time: Optional[datetime]  # 修改时间
    creator_id: Optional[int] = 0  # 创建人
    modifier_id: Optional[int] = 0  # 修改人
