from typing import Dict

import wsme
from pydantic import BaseModel
from wsme import types as wtypes
from biz.types import jsontype

from biz.types import BaseResult


class TenantPublicKey(BaseModel):
    public_key: bytes
    tenant_id: str

    class Config:
        json_encoders = {
            bytes: lambda v: v.decode('utf-8')
        }

class PublicKeyResult(BaseModel):
    content: Dict
    code: int = 0
