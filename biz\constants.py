from enum import Enum, unique


class CommonC(object):
    SUPER_USER = "admin"


class PublicKeyC(object):
    GLOBAL_KEY_PAIR_ID = "global"


class VersionC(object):
    GLOBAL_VERSION_ID = "global"


class ResourceC(object):
    FIELD_TYPE_SHOW = {"数值": "int", "小数": "numeric", "布尔": "bool", "文本": "text", "日期": "date",
                       "时间": "timestamp", "字符串": "varchar"}

    FIELD_TYPE_MAPPING = {"int": "int4", "numeric": "numeric", "bool": "bool", "text": "text", "date": "date",
                          "timestamp": "timestamp", "varchar": "varchar"}


@unique
class EnvEnum(Enum):
    product = "prod",
    quality = "qa_test",
    develop = "dev",
    test = "test",
    local = "local",
