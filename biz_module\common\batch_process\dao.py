# encoding: utf-8
from datetime import datetime
from typing import List, Optional

from ourbatis import sql_func

from biz_module.common.batch_process import typedef


@sql_func
class BaseBatchProcessJobDao(object):
    """
    规则类别数据访问对象
    """

    def query_base_batch_process_job(self, job_id) -> typedef.BaseBatchProcessJobResponse:
        """
        保存批处理工作表
        :param item:
        :return:
        """
        ...

    def insert_base_batch_process_job(self, item: typedef.BaseBatchProcessJobRequest) -> int:
        """
        保存批处理工作表
        :param item:
        :return:
        """
        ...

    def update_base_batch_process_job_task_status(self, job_id, task_status):
        """
        更新状态
        :param job_id:
        :param task_status:
        :return:
        """
        ...

    def insert_base_batch_process_job_detail(self, data_list: List[typedef.BaseBatchProcessJobDetailRequest]):
        """
        保存批处理工作明细表
        :param data_list:
        :return:
        """
        ...

    def query_sku_order_detail_list(self, job_id, page_size, offset, day_date=None):
        """
        获取单据明细数据
        """
        ...

    def query_repeat_order_batch_process_lines(self, job_id) -> int:
        """
        获取单据明细条目
        """
        ...

    def query_repeat_order_batch_process_list(self, job_id, page_size, offset):
        """
        获取单据明细数据
        """
        ...

    def update_ra_order_status(self, job_id, job_type, task_status, day_date):
        """

        :param job_id:
        :param job_type:
        :param task_status:
        :param day_date:
        :return:
        """
        ...

    def get_custom_config(self, job_type) -> typedef.CustomConfig:
        """
        获取job重试次数
        """
        ...

    def query_batch_process_order_count(self, params: typedef.BatchProcessJobOrderListRequest) -> int:
        """
        统计失败、及其待同步的单据
        """
        ...

    def query_batch_process_order_list(self, params: typedef.BatchProcessJobOrderListRequest) -> List[
        typedef.BaseBatchProcessJobDetailResponseList]:
        """
        查询失败、待同步的单据列表
        """
        ...

    def query_batch_record_list_count(self, params: typedef.QueryBaseBatchProcessJobDetailBO) -> int:
        """
        统计
        :param params:
        :return:
        """
        ...

    def query_batch_record_list(self, params: typedef.QueryBaseBatchProcessJobDetailBO) \
            -> List[typedef.BaseBatchProcessJobDetailResponseList]:
        """
        查询本批次运行的任务
        """
        ...

    def create_record_list_temp_table(self, table_name: str) -> None:
        """
        废弃
        :param temp_data_list:
        :param table_name:
        :return:
        """
        ...

    def insert_record_list_temp_data(self, temp_data_list: List[typedef.BaseBatchProcessJobDetailTempData],
                                     table_name: str) -> None:
        """
        废弃
        :param temp_data_list:
        :param table_name:
        :return:
        """
        ...

    def drop_record_detail_temp_table(self, table_name: str) -> None:
        """
        废弃
        :param temp_data_list:
        :param table_name:
        :return:
        """
        ...

    def create_record_list_temp_table_sub_batch(self, table_name: str, table_data_content: str):
        ...

    def insert_record_list_temp_data_sub_batch(self,
                                               temp_data_list: List[typedef.BaseBatchProcessJobDetailSubBatchRequest],
                                               table_name: str):
        """
        废弃
        :param temp_data_list:
        :param table_name:
        :return:
        """
        ...

    def update_record_detail_data_sub_batch_id(self, table_name):
        ...

    def update_record_detail_status_by_sub_batch_id(self, sub_batch_id, task_status):
        ...

    def update_record_detail_status_by_job_id(self, job_id, task_status):
        ...

    def update_record_detail_prepare_status_by_job_id(self, job_id, task_status):
        ...

    def query_record_detail_count_by_sub_batch_id(self, sub_batch_id, day_date) -> int:
        ...

    def query_job_id(self, sub_batch_id: str) -> List[str]:
        ...

    def check_base_batch_process_job_detail(self, job_id) -> List[str]:
        ...

    def get_order_id_list_by_job_type(self, item: typedef.BaseBatchProcessJobOrderListRequest) -> List[str]:
        ...

    def query_job_task_status(self, job_id_list: List[int]) -> List[typedef.BaseBatchProcessJobJobStatusResponse]:
        ...

    def batch_update_base_batch_process_job_task_status \
                    (self, batch_job_list: List[typedef.BaseBatchProcessJobJobStatusResponse]):
        ...

    def sync_batch(self, job_id, day_date=None):
        """
        批量新增单据信息
        :param job_id:
        :param day_date:
        :return:
        """
        ...

