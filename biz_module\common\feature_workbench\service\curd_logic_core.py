import logging
import re
from enum import Enum
from functools import cached_property
from typing import Union, List

from flask_login import current_user
from linezone_commonserver.services.biz_view.constants import ViewCombineEnum
from linezone_commonserver.services.biz_view.service import CommonBizViewService
from linezone_commonserver.services.feature.typedef import FeatureEntity, ListDependenciesReq
from linezone_commonserver.services.meta_data.service import MetaDataService

from biz.api_exception import ParameterException
from linezone_commonserver.services.feature.service import FeatureService

from biz.utils.tools import is_greater_than_0_int, is_int, is_float
from biz_module.common.feature_workbench.constants import ACTION_DELETE, ACTION_CREATE, ACTION_UPDATE
from biz_module.common.feature_workbench.dao import FeatureDao
from biz_module.common.feature_workbench.enums import (
    FeatureTypeEnum,
    CalculationTypeEnum,
    FeatureSubtypeEnum,
    DataTypeEnum,
    IsValueMultiple,
    FillValue,
    OperatePermissionEnum,
    IsCustomEnum,
)
from biz_module.common.feature_workbench.service import value_subtype_core
from biz_module.common.feature_workbench.tools import (
    FeatureOperatePermissionHelper,
    is_feature_value_id_new,
    generate_feature_code,
)

from biz_module.common.feature_workbench.typedef import (
    FeatureConfigEgg,
    UpsertData,
    CreateFeatureReq,
    FeatureValueEgg,
    UpdateFeatureReq,
    FeatureValueReq,
)

logger = logging.getLogger(__name__)

drop_action_re = re.compile(r"(drop\s+(?!table)\w+)")

# 只允许 drop table temp_****
drop_temp_prefix_re = re.compile(r"drop\s+table.*\s+(?!tmp_|temp_)(\w+)\s*(?=;)", re.I)

# 只允许create temp/temporary table
create_temp_table_re = re.compile(r"create\s+(?!temp|temporary)\w+\s+\w+", re.I)

# 非法字符
special_char_re = re.compile(r"[!\"'$%&()（）*+，,-.:：；<=>?@￥「」^{}|…~\s]")
expression_special_char_re = re.compile(r"[!\"'$%&（），:：；<=>?@￥「」^{}|…~]")
HISTORY_DATA_KEEP_YEAR_NUM = 3


class ValidatorErrEnum(Enum):
    EMPTY_SQL_TEXT = "参数错误, sql代码不能为空"
    EMPTY_FEATURE_NAME = "参数错误， 请填写有效的特征名称"
    NUMBER_FEATURE_NAME = "参数错误， 特征名称不能为数字"
    DUPLICATED_FEATURE_NAME = "特征名称重复"
    CHANGE_SUBTYPE_ERR = "构建方式只支持特征模板向SQL程序的转换"
    FEATURE_NOT_EXISTS = "特征不存在或已删除"
    PARAM_ERROR = "参数错误 {error_info}"
    VALUE_CONTAINS_ILLEGAL_CHAR = "特征名字或特征值包含非法字符{error}"
    WRONG_FEATURE_TYPE = "请选择正确的特征类型"
    WRONG_IS_CUSTOM = "请选择是否自定义"
    WRONG_BIZ_VIEW = "参数错误， 请选择业务视图"
    WRONG_BIZ_VIEW_DIMENSION = "参数错误， 请选择汇聚粒度"
    EMPTY_CALCULATION_TYPE = "参数错误， 请选择计算类型"
    WRONG_CALCULATION_TYPE = "参数错误， 计算类型非法"
    EMPTY_DATA_TYPE = "参数错误， 请选择特征值数据类型"
    WRONG_DATA_TYPE = "参数错误， 特征值数据类型非法"
    WRONG_FEATURE_SUBTYPE = "参数错误， 构建方式非法"
    WRONG_FILL_VALUE = "参数错误， 是否默认值为零非法"
    WRONG_IS_VALUE_MULTIPLE = "参数错误， 是否多选非法"
    WRONG_DROP_TABLE = "参数错误, drop table只允许以temp_开头的临时表名，请改造下<br/>:{error}"
    WRONG_TEMP_TABLE_NAME = "参数错误, drop table只允许以temp_开头的临时表名，请改造下<br/>:{error}"
    WRONG_CREATE_TABLE = "参数错误, 只允许创建临时表，请改造下<br/>:{error}"
    WRONG_KEYWORD_IN_SQL = "sql中不能包含 {keyword} 语法"
    SQL_MISSING_KEYWORDS = "sql中需要包含{keyword}请检查!"
    DIMENSION_MISSING = "特征{name}维度为空，请检查"
    WRONG_FEATURE_TYPE_CALCULATION_TYPE_COMBINE = "指标计算类型只能为:{error}"
    EMPTY_VALUE_LIST = "特征值配置列表不能为空"
    EMPTY_FEATURE_VALUE = "参数错误，特征值不能为空"
    DUPLICATED_FEATURE_VALUE = "特征值重复了, {error}"
    WRONG_FEATURE_VALUE_COUNT_GE_1 = "参数错误，指标类型不支持多个值"
    MID_CALCULATION_BIND_TO_SQL_APP = "模型中只支持sql程序"
    WRONG_GREATER_THAN_0_INT_CALCULATION_TYPE_MATCH = "正整数的构建方式仅支持人工"
    WRONG_TIMESTAMP_SUBTYPE_MATCH = "日期格式的构建方式仅支持sql程序"
    WRONG_HISTORY_DATA_KEEP_DAY = "数据保留天数仅支持正整数"
    WRONG_HISTORY_DATA_KEEP_DAY_MAX = f"特征数据，最大仅支持保留{HISTORY_DATA_KEEP_YEAR_NUM}年历史数据"
    FEATURE_BIZ_VIEW_MISSING = f"特征业务视图不存在，请检查"
    FEATURE_BIZ_VIEW_DIMENSION_MISSING = f"特征业务视图维度不存在，请检查"
    WRONG_KPI_RULE_AUTO_BIZ_VIEW = "规则自动指标的业务视图最少需要包含一个指标类型的特征!"
    FEATURE_NAME_NOT_IN_BIZ_VIEW = "请确认{feature_name}是否定义在业务视图中或类型是否为指标!"
    NO_DELETE_FEATURE_CONFIG_PERMISSION = "操作失败, 特征{feature_name}不允许删除"
    NO_APPEND_FEATURE_CONFIG_VALUE_PERMISSION = "操作失败, 特征{feature_name}不允许新增值"
    NO_EDIT_FEATURE_CONFIG_ATTR_PERMISSION = "操作失败, 不允许修改"
    NO_EDIT_FEATURE_VALUE_ATTR_PERMISSION = "操作失败, 不允许修改特征值{feature_value}"
    NO_DELETE_FEATURE_CONFIG_VALUE_PERMISSION = "操作失败, 不允许删除{feature_value}"
    # 特征值相关
    FEATURE_VALUE_NOT_EXISTS = "更新失败，特征值{feature_value}不存在"
    FEATURE_CIRCLE_DEPENDENCY = "更新失败，和特征{feature_code}存在循环依赖"


class LogicProcessor:
    common_feature_service = FeatureService()
    common_biz_view_service = CommonBizViewService()
    common_meta_data_service = MetaDataService()
    _sql_text_keywords = ["DELETE", "TRUNCATE", "ALTER"]
    _sql_should_include_keywords = ["feature_value"]
    operate_permission_helper = FeatureOperatePermissionHelper()

    def __init__(
        self, req_feature_config: FeatureConfigEgg, value_body: List[FeatureValueReq] = None, raise_error=True, action_flag=None
    ):
        # 前端传来的config body
        # 在某些校验场景下，需要填充一些信息，比如feature_code
        if not req_feature_config:
            self._raise_err(ValidatorErrEnum.PARAM_ERROR, {"error_info": "feature_config为空"})

        self.feature_config_req = req_feature_config
        self.raise_error = raise_error
        self.has_error = False
        self.err_msg = None
        # 某些场景下无需初始化此参数
        self.value_body = value_body
        self.feature_dao = FeatureDao()
        self._db_feature_config = None
        self._value_subtype_cls = value_subtype_core.FeatureValueSubtype
        self._action_flag = action_flag
        self._upsert_data = None

    @classmethod
    def delete_req_with_basic_validation(cls, feature_config_id):
        if not feature_config_id:
            cls._raise_err(ValidatorErrEnum.PARAM_ERROR, {"error_info": "特征ID为空"})

        feature_config = cls.common_feature_service.get_feature_by_id(feature_config_id)
        if not feature_config:
            cls._raise_err(ValidatorErrEnum.FEATURE_NOT_EXISTS)
        config_reg = FeatureConfigEgg(**feature_config.dict())
        _self = cls(config_reg, action_flag=ACTION_DELETE)
        _self.validate_delete_operate_permission()
        _self.validate_feature_is_used_by_third_party()
        _self._db_feature_config = feature_config
        return _self

    @classmethod
    def create_req_with_basic_validation(cls, req: CreateFeatureReq):
        req.config_body.feature_code = generate_feature_code(req.config_body.feature_type)
        req.config_body.is_custom = IsCustomEnum.YES.value
        _self = cls(req.config_body, req.value_body, action_flag=ACTION_CREATE)
        _self._common_basic_validation()
        _self.validate_kpi_rule_auto()
        _self._make_upsert_value_data()
        return _self

    @classmethod
    def update_req_with_basic_validation(cls, req: UpdateFeatureReq):
        _self = cls(req.config_body, req.value_body, action_flag=ACTION_UPDATE)
        _self._common_basic_validation()
        _self.validate_config_id_related_feature()
        _self.feature_config_req.feature_code = _self.db_feature_config.feature_code
        _self.validate_edit_feature_config_permission()
        _self.validate_change_subtype()
        _self._make_upsert_value_data()

        if _self._upsert_data.should_update_values:
            _self.validate_updated_value_permission()
        if _self._upsert_data.should_insert_values:
            _self.validate_has_append_value_permission()
        if _self.req_remove_value_egg_ids:
            _self.validate_delete_value_permission(_self.req_remove_value_egg_ids)
            _self.validate_feature_is_used_by_third_party()
        if _self._upsert_data.value_changed_ids:
            _self.validate_feature_is_used_by_third_party()

        return _self

    @cached_property
    def db_feature_config(self):
        if self._db_feature_config:
            return self._db_feature_config

        if not self.feature_config_req.id:
            self._raise_err(ValidatorErrEnum.PARAM_ERROR, {"error_info": "特征ID为空"})

        return self.common_feature_service.get_feature_by_id(self.feature_config_req.id)

    @cached_property
    def db_feature_values(self):
        exists_values = self.common_feature_service.list_feature_value_by_feature_code(self.db_feature_config.feature_code)

        return exists_values

    @cached_property
    def db_id_value_dict(self):
        exists_value_id_dict = {exist_value.id: exist_value for exist_value in self.db_feature_values}
        return exists_value_id_dict

    @cached_property
    def feature_type_enum(self):
        return FeatureTypeEnum(self.feature_config_req.feature_type)

    @cached_property
    def data_type_enum(self):
        return DataTypeEnum(self.feature_config_req.data_type)

    @cached_property
    def feature_subtype_enum(self):
        return FeatureSubtypeEnum(self.feature_config_req.feature_subtype)

    @cached_property
    def calculation_type_enum(self):
        return CalculationTypeEnum(self.feature_config_req.calculation_type)

    @cached_property
    def is_value_multiple_enum(self):
        return IsValueMultiple(self.feature_config_req.is_value_multiple)

    @cached_property
    def fill_value_enum(self):
        return FillValue(self.feature_config_req.fill_value)

    @property
    def is_kpi_rule_auto(self) -> bool:
        return self.feature_type_enum is FeatureTypeEnum.KPI and self.feature_subtype_enum is FeatureSubtypeEnum.RULE_AUTO

    @cached_property
    def biz_view(self):
        biz_view = self.common_biz_view_service.get_biz_view_by_id(self.feature_config_req.biz_view_id)
        if not biz_view:
            self._raise_err(ValidatorErrEnum.FEATURE_BIZ_VIEW_MISSING)
        return biz_view

    @cached_property
    def biz_view_dimension(self):
        biz_view_dimension = self.common_biz_view_service.get_view_dimension_by_dimension_id(
            self.feature_config_req.biz_view_dimension_id
        )

        if not biz_view_dimension:
            self._raise_err(ValidatorErrEnum.FEATURE_BIZ_VIEW_DIMENSION_MISSING)
        return biz_view_dimension

    @cached_property
    def biz_view_feature_codes(self):
        feature_codes = [
            _obj.object_code for _obj in self.biz_view.biz_view_combine if _obj.source_type == ViewCombineEnum.FEATURE.value
        ]
        return feature_codes

    @cached_property
    def biz_view_features(self):
        features: List[FeatureEntity] = self.common_feature_service.list_by_feature_codes(self.biz_view_feature_codes)
        return features or []

    @cached_property
    def biz_view_objects(self):
        biz_objects = self.common_meta_data_service.list_biz_object_by_codes(self.biz_view_object_codes)
        return biz_objects or []

    @cached_property
    def biz_view_object_codes(self):
        biz_object_codes = [
            _obj.object_code for _obj in self.biz_view.biz_view_combine if _obj.source_type == ViewCombineEnum.BIZ_OBJECT.value
        ]

        return biz_object_codes

    @cached_property
    def value_subtype_obj(self):
        feature_entity = (
            self.db_feature_config if self._action_flag == ACTION_UPDATE else FeatureEntity(**self.feature_config_req.dict())
        )
        req = value_subtype_core.SubtypeBuildReq(
            feature_code=self.feature_config_req.feature_code,
            biz_view_dimension_id=self.feature_config_req.biz_view_dimension_id,
            feature_type_enum=self.feature_type_enum,
            feature_subtype_enum=self.feature_subtype_enum,
            calculation_type_enum=self.calculation_type_enum,
            biz_view=self.biz_view,
            biz_view_dimension=self.biz_view_dimension,
            biz_view_object_codes=self.biz_view_object_codes,
            validator=self,
            req_feature_entity=feature_entity,
            action_flag=self._action_flag,
        )
        return self._value_subtype_cls.get_feature_subtype(req)

    @property
    def req_new_value_eggs(self) -> List[FeatureValueEgg]:
        return self._upsert_data.should_insert_values

    @property
    def req_old_value_eggs(self) -> List[FeatureValueEgg]:
        return self._upsert_data.should_update_values

    @property
    def req_remove_value_egg_ids(self) -> List[int]:
        req_update_ids = [u_v.id for u_v in self._upsert_data.should_update_values]
        return list(set(self.db_id_value_dict.keys()).difference(req_update_ids))

    @property
    def value_hit_sql(self):
        subtype_obj = self.value_subtype_obj
        value_hit_sql = getattr(subtype_obj, "mid_hit_sql", subtype_obj.default_hit_sql)
        return value_hit_sql

    def validate_feature_name(self):
        if self.is_str_empty(self.feature_config_req.feature_name):
            self._handle_err(ValidatorErrEnum.EMPTY_FEATURE_NAME)
            return
        self.validate_special_char(self.feature_config_req.feature_name)

        # 验证是否纯数字
        if is_int(self.feature_config_req.feature_name) or is_float(self.feature_config_req.feature_name):
            self._handle_err(ValidatorErrEnum.NUMBER_FEATURE_NAME)

    def validate_special_char(self, input_text):
        special_char = special_char_re.findall(input_text)
        if special_char:
            self._handle_err(ValidatorErrEnum.VALUE_CONTAINS_ILLEGAL_CHAR, {"error": ",".join(special_char)})

    def validate_expression_special_char(self, expression):
        special_char = expression_special_char_re.findall(expression)
        if special_char:
            self._handle_err(ValidatorErrEnum.VALUE_CONTAINS_ILLEGAL_CHAR, {"error": ",".join(special_char)})

    def validate_feature_type(self):
        if not self.feature_config_req.feature_type:
            self._handle_err(ValidatorErrEnum.WRONG_FEATURE_TYPE)
        if not FeatureTypeEnum.has_value(self.feature_config_req.feature_type):
            self._handle_err(ValidatorErrEnum.WRONG_FEATURE_TYPE)

    def validate_is_custom(self):
        if not self.feature_config_req.is_custom:
            self._handle_err(ValidatorErrEnum.WRONG_IS_CUSTOM)

    def validate_biz_view(self):
        if not self.feature_config_req.biz_view_id:
            self._handle_err(ValidatorErrEnum.WRONG_BIZ_VIEW)

    def validate_biz_view_dimension(self):
        if not self.feature_config_req.biz_view_dimension_id:
            self._handle_err(ValidatorErrEnum.WRONG_BIZ_VIEW_DIMENSION)

    def validate_calculation_type(self):
        if not self.feature_config_req.calculation_type:
            self._handle_err(ValidatorErrEnum.EMPTY_CALCULATION_TYPE)
        if not CalculationTypeEnum.has_value(self.feature_config_req.calculation_type):
            self._handle_err(ValidatorErrEnum.WRONG_CALCULATION_TYPE)

    def validate_data_type(self):
        if not self.feature_config_req.data_type:
            self._handle_err(ValidatorErrEnum.EMPTY_DATA_TYPE)
        if not DataTypeEnum.has_value(self.feature_config_req.data_type):
            self._handle_err(ValidatorErrEnum.WRONG_DATA_TYPE)

    def validate_is_value_multiple(self):
        if not IsValueMultiple.has_value(self.feature_config_req.is_value_multiple):
            self._handle_err(ValidatorErrEnum.WRONG_IS_VALUE_MULTIPLE)

    def validate_feature_subtype(self):
        if not FeatureSubtypeEnum.has_value(self.feature_config_req.feature_subtype):
            self._handle_err(ValidatorErrEnum.WRONG_FEATURE_SUBTYPE)

    def validate_fill_value(self):
        if not FillValue.has_value(self.feature_config_req.fill_value):
            self._handle_err(ValidatorErrEnum.WRONG_FILL_VALUE)

    def validate_sql_text(self, sql_str: str):
        """验证特征中SQL代码"""
        if not sql_str:
            self._handle_err(ValidatorErrEnum.EMPTY_SQL_TEXT)
            return

        error_keys = set()
        illegal_drop_actions = drop_action_re.findall(sql_str)
        if illegal_drop_actions:
            self._handle_err(ValidatorErrEnum.WRONG_DROP_TABLE, {"error": "<br/>".join(illegal_drop_actions)})
            return

        # drop table语句检测 只允许 drop temp_开头的
        without_temp_table_names = drop_temp_prefix_re.findall(sql_str)
        if without_temp_table_names:
            self._handle_err(ValidatorErrEnum.WRONG_TEMP_TABLE_NAME, {"error": "<br/>".join(without_temp_table_names)})
            return

            # # create table检测 只允许 create temp table
        illegal_create_tables = create_temp_table_re.findall(sql_str)
        if illegal_create_tables:
            self._handle_err(ValidatorErrEnum.WRONG_CREATE_TABLE, {"error": "<br/>".join(illegal_create_tables)})
            return

        for key in self._sql_text_keywords:
            if re.search(rf"\b{key}\b", sql_str, re.IGNORECASE):
                error_keys.add(key)
        if error_keys:
            self._handle_err(ValidatorErrEnum.WRONG_KEYWORD_IN_SQL, {"keyword": " ,".join(error_keys)})
            return

        # 校验sql是否包含维度字段
        if not self.biz_view_dimension.meta_data_dimension_code:
            self._handle_err(ValidatorErrEnum.DIMENSION_MISSING, {"name": self.feature_config_req.feature_name})
            return
        should_include_keywords = self._sql_should_include_keywords + self.biz_view_dimension.meta_data_dimension_code
        missing_keyword = []
        for keyword in should_include_keywords:
            if keyword not in sql_str:
                missing_keyword.append(keyword)

        if missing_keyword:
            self._handle_err(ValidatorErrEnum.SQL_MISSING_KEYWORDS, {"keyword": " ,".join(missing_keyword)})
            return

    def validate_change_subtype(self):
        # 允许FeatureSubtypeEnum.FEATURE_TEMPLATE一键转为FeatureSubtypeEnum.SQL_APP
        old_subtype_enum = FeatureSubtypeEnum(self.db_feature_config.feature_subtype)
        if old_subtype_enum != self.feature_subtype_enum:
            if (
                old_subtype_enum != FeatureSubtypeEnum.FEATURE_TEMPLATE
                and self.feature_subtype_enum != FeatureSubtypeEnum.SQL_APP
            ):
                self._handle_err(ValidatorErrEnum.CHANGE_SUBTYPE_ERR)

    def validate_feature_type_calculation_type_combine(self):
        """验证特征类型和计算类型组合"""
        if self.feature_type_enum is FeatureTypeEnum.KPI and self.calculation_type_enum not in (
            CalculationTypeEnum.PRE,
            CalculationTypeEnum.MID,
            CalculationTypeEnum.AFTER,
        ):
            self._raise_err(
                f"指标计算类型只能为{CalculationTypeEnum.PRE.title}或者{CalculationTypeEnum.MID.title}或{CalculationTypeEnum.AFTER.title}"
            )

        if self.feature_type_enum is FeatureTypeEnum.TAG and self.calculation_type_enum not in (
            CalculationTypeEnum.PRE,
            CalculationTypeEnum.AFTER,
        ):
            self._raise_err(f"标签计算类型只能为{CalculationTypeEnum.PRE.title}或{CalculationTypeEnum.AFTER.title}")

    def validate_feature_type_and_subtype_combine(self):
        """验证特征类型和特征构建方式组合"""
        if self.feature_type_enum is FeatureTypeEnum.KPI and self.feature_subtype_enum not in (
            FeatureSubtypeEnum.FEATURE_TEMPLATE,
            FeatureSubtypeEnum.SQL_APP,
            FeatureSubtypeEnum.HUMAN_TAG,
            FeatureSubtypeEnum.RULE_AUTO,
        ):
            self._raise_err(
                f"指标构建方式类型只能为{FeatureSubtypeEnum.FEATURE_TEMPLATE.title},"
                f"{FeatureSubtypeEnum.SQL_APP.title},"
                f"{FeatureSubtypeEnum.HUMAN_TAG.title},"
                f"{FeatureSubtypeEnum.RULE_AUTO.title}"
            )

        if self.feature_type_enum is FeatureTypeEnum.TAG and self.feature_subtype_enum not in (
            FeatureSubtypeEnum.RULE_AUTO,
            FeatureSubtypeEnum.SQL_APP,
            FeatureSubtypeEnum.HUMAN_TAG,
        ):
            self._raise_err(
                f"标签构建方式类型只能为{FeatureSubtypeEnum.RULE_AUTO.title},"
                f"{FeatureSubtypeEnum.SQL_APP.title},"
                f"{FeatureSubtypeEnum.HUMAN_TAG.title}"
            )

    def validate_kpi_rule_auto(self):
        """
        校验指标规则自动

        5.8.5指标支持任意业务视图组装, 但是只支持指标的四则运算
        故校验业务视图最少包含一个指标特征
        """
        if self.is_kpi_rule_auto:
            if not self.biz_view_features:
                self._raise_err(ValidatorErrEnum.WRONG_KPI_RULE_AUTO_BIZ_VIEW)

            have_kpi = False
            for feature in self.biz_view_features:
                if feature.feature_type == FeatureTypeEnum.KPI.value:
                    have_kpi = True
                    break

            if not have_kpi:
                self._raise_err(ValidatorErrEnum.WRONG_KPI_RULE_AUTO_BIZ_VIEW)

    def validate_value_expression(self, feature_name):
        """
        验证表达式
        """

        biz_view_name_feature_dict = {
            feature.feature_name: feature
            for feature in self.biz_view_features
            if feature.feature_type == FeatureTypeEnum.KPI.value
        }
        if feature_name not in biz_view_name_feature_dict:
            self._handle_err(ValidatorErrEnum.FEATURE_NAME_NOT_IN_BIZ_VIEW, {"feature_name": feature_name})

        return biz_view_name_feature_dict

    def validate_biz_object_dimension(self, dimension):
        code_object_mapping = {biz_object.code: biz_object for biz_object in self.biz_view_objects}
        biz_object = code_object_mapping.get(dimension.object_code)
        if not biz_object:
            logger.error(f"解析汇聚粒度失败了, {dimension.object_code}未获取到对应的业务对象")
            raise ParameterException("业务对象不存在, 请检查")
        if not biz_object.include_table:
            logger.error(f"业务对象{biz_object.code}include_table设置不正确")
            raise ParameterException(f"{biz_object.name}包含的物理表属性未正确定义")
        return code_object_mapping

    def validate_feature_dimension(self, dimension):
        code_feature_mapping = {feature.feature_code: feature for feature in self.biz_view_features}
        feature = code_feature_mapping.get(dimension.object_code)
        if not feature:
            logger.error(f"解析汇聚粒度失败了, {dimension.object_code}未获取到对应的特征")
            raise ParameterException("业务视图维度特征不存在, 请检查")
        return code_feature_mapping

    def validate_feature_type_data_type_combine(self):
        """验证特征类型和数据类型组合"""
        if self.feature_type_enum is FeatureTypeEnum.KPI and self.data_type_enum not in (
            DataTypeEnum.INT,
            DataTypeEnum.NUMERIC,
            DataTypeEnum.INT_GREATER_THAN_ZERO,
            DataTypeEnum.TIMESTAMP,
        ):
            self._raise_err(
                f"指标数据类型只能为{DataTypeEnum.INT.title},"
                f"{DataTypeEnum.NUMERIC.title},"
                f"{DataTypeEnum.INT_GREATER_THAN_ZERO.title}"
                f"或{DataTypeEnum.TIMESTAMP.title}"
            )
        if self.feature_type_enum is FeatureTypeEnum.TAG and self.data_type_enum not in (DataTypeEnum.TEXT,):
            self._raise_err(f"标签数据类型只能为{DataTypeEnum.TEXT.title}")

    def validate_data_type_and_feature_subtype_combine(self):
        """验证数据类型和构建方式的组合"""
        if (
            self.data_type_enum is DataTypeEnum.INT_GREATER_THAN_ZERO
            and self.feature_subtype_enum is not FeatureSubtypeEnum.HUMAN_TAG
        ):
            self._handle_err(ValidatorErrEnum.WRONG_GREATER_THAN_0_INT_CALCULATION_TYPE_MATCH)

        if self.data_type_enum is DataTypeEnum.TIMESTAMP and self.feature_subtype_enum is not FeatureSubtypeEnum.SQL_APP:
            self._handle_err(ValidatorErrEnum.WRONG_TIMESTAMP_SUBTYPE_MATCH)

    def validate_feature_type_is_value_multiple_combine(self):
        """指标是否多选只能为否 标签均可"""
        if (
            self.feature_type_enum is FeatureTypeEnum.KPI
            and self.feature_config_req.is_value_multiple == self.is_value_multiple_enum.YES
        ):
            self._raise_err(f"参数错误， 指标是否多选只能为{IsValueMultiple.NO.title}")

    def validate_feature_type_fill_value_combine(self):
        """标签是否为零只能为否 指标均可"""
        if self.feature_type_enum is FeatureTypeEnum.TAG and self.fill_value_enum is not FillValue.NULL:
            self._raise_err(f"参数错误, 标签是否默认值为零只能为{FillValue.NULL.title}")

    def validate_calculation_type_feature_subtype_combine(self):
        """计算类型和构建方式验证"""
        if (
            self.calculation_type_enum is CalculationTypeEnum.MID
            and self.feature_subtype_enum is not FeatureSubtypeEnum.SQL_APP
        ):
            self._handle_err(ValidatorErrEnum.MID_CALCULATION_BIND_TO_SQL_APP)

    def validate_value_body_empty(self):
        if not self.value_body:
            self._handle_err(ValidatorErrEnum.EMPTY_VALUE_LIST)

    def validate_value_body_feature_value(self):
        self.validate_value_body_empty()
        for index, feature_value_obj in enumerate(self.value_body):
            if self.is_str_empty(feature_value_obj.feature_value):
                self._handle_err(ValidatorErrEnum.EMPTY_FEATURE_VALUE)
            if self.feature_type_enum is FeatureTypeEnum.TAG:
                self.validate_special_char(feature_value_obj.feature_value)
            else:
                self.validate_expression_special_char(feature_value_obj.feature_value)

    def validate_value_body_duplicated_feature_value(self):
        self.validate_value_body_empty()
        feature_value_set = set()
        for index, feature_value_obj in enumerate(self.value_body):
            if feature_value_obj.feature_value in feature_value_set:
                self._handle_err(ValidatorErrEnum.DUPLICATED_FEATURE_VALUE, {"error": feature_value_obj.feature_value})
                return

            feature_value_set.add(feature_value_obj.feature_value)

    def validate_duplicated_feature_name(self):
        """feature_config中如果有id则排除否则不排除"""
        if self.is_feature_name_exists():
            self._handle_err(ValidatorErrEnum.DUPLICATED_FEATURE_NAME)

    def validate_config_id_related_feature(self):
        """校验特征ID"""
        if not self.db_feature_config:
            self._handle_err(ValidatorErrEnum.FEATURE_NOT_EXISTS)

    def is_feature_name_exists(self):
        exists = self.feature_dao.feature_config_exist_by_name(self.feature_config_req.feature_name, self.feature_config_req.id)
        return True if exists else False

    def validate_delete_operate_permission(self):
        """检验删除特征权限"""
        permitted = self.operate_permission_helper.has_permission(
            self.feature_config_req.operate_permission, OperatePermissionEnum.DELETE
        )
        if not permitted:
            self._handle_err(
                ValidatorErrEnum.NO_DELETE_FEATURE_CONFIG_PERMISSION, {"feature_name": self.feature_config_req.feature_name}
            )

    def validate_updated_value_permission(self):
        """校验特征值编辑权限"""
        namespace = "config_value_attrs"
        for value in self._upsert_data.should_update_values:
            err_param = {"feature_value": value.feature_value}
            if self._has_violate_edit_perm(value, self.db_id_value_dict[value.id], OperatePermissionEnum.EDIT_1, namespace):
                logger.warning(f"特征{self.feature_config_req.feature_name}不具备修改修改特征值的feature_value权限")
                self._handle_err(ValidatorErrEnum.NO_EDIT_FEATURE_VALUE_ATTR_PERMISSION, err_param)
            if self._has_violate_edit_perm(value, self.db_id_value_dict[value.id], OperatePermissionEnum.EDIT_2, namespace):
                logger.warning(f"特征{self.feature_config_req.feature_name}不具备修改修改特征值hit_sql权限")
                self._handle_err(ValidatorErrEnum.NO_EDIT_FEATURE_VALUE_ATTR_PERMISSION, err_param)

    def validate_edit_feature_config_permission(self):
        """校验编辑特征权限"""
        namespace = "feature_config_attrs"

        if self._has_violate_edit_perm(
            self.feature_config_req, self.db_feature_config, OperatePermissionEnum.EDIT_1, namespace
        ):
            logger.warning(f"特征{self.feature_config_req.feature_name}不具备修改特征feature_name权限")
            self._handle_err(ValidatorErrEnum.NO_EDIT_FEATURE_CONFIG_ATTR_PERMISSION)

        if self._has_violate_edit_perm(
            self.feature_config_req, self.db_feature_config, OperatePermissionEnum.EDIT_2, namespace
        ):
            logger.warning(f"特征{self.feature_config_req.feature_name}不具备修改特征其他属性的权限")
            self._handle_err(ValidatorErrEnum.NO_EDIT_FEATURE_CONFIG_ATTR_PERMISSION)

    def validate_has_append_value_permission(self):
        """校验追加特征值权限"""
        permitted = self.operate_permission_helper.has_permission(
            self.db_feature_config.operate_permission, OperatePermissionEnum.APPEND_VALUE
        )
        if not permitted:
            self._handle_err(
                ValidatorErrEnum.NO_APPEND_FEATURE_CONFIG_VALUE_PERMISSION,
                {"feature_name": self.feature_config_req.feature_name},
            )
        return permitted

    def validate_delete_value_permission(self, value_ids: List[int]):
        """校验删除特征值权限"""
        values = self.common_feature_service.list_feature_value_by_ids(value_ids)
        for value in values:
            allowed = self.operate_permission_helper.has_permission(value.operate_permission, OperatePermissionEnum.DELETE)
            if not allowed:
                self._handle_err(
                    ValidatorErrEnum.NO_DELETE_FEATURE_CONFIG_VALUE_PERMISSION, dict(feature_value=value.feature_value)
                )

    def validate_edit_value_permission(self, value_ids: List[int]):
        """校验编辑特征值权限"""
        protected_attr = ["feature_name", "is_value_multiple", "calculation_type"]

        values = self.common_feature_service.list_feature_value_by_ids(value_ids)
        for value in values:
            allowed = self.operate_permission_helper.has_permission(value.operate_permission, OperatePermissionEnum.DELETE)
            if not allowed:
                self._handle_err(
                    ValidatorErrEnum.NO_DELETE_FEATURE_CONFIG_VALUE_PERMISSION, dict(feature_value=value.feature_value)
                )

    def validate_feature_is_used_by_third_party(self, other_msg=""):
        """
        是否被其他组件依赖，比如筛选器，规则参数
        @param other_msg:
        @return:
        """

        from biz_module.replenish_allot.gto_instruct.service.order_detail_service import order_srv

        if order_srv.has_feature_used_by_gto(self.db_feature_config.feature_code):
            self._raise_err(f"{self.db_feature_config.feature_name}已被流通指令占用, {other_msg}操作失败")
        # 是否被通用筛选器占用
        if self.feature_dao.query_filter_feature_code(self.db_feature_config.feature_code):
            self._raise_err(f"{self.db_feature_config.feature_name}已被通用筛选器占用, {other_msg}操作失败")

        # 是否被其他特征引用
        is_used = self.feature_dao.has_feature_used_by_other_feature_value([self.db_feature_config.feature_code])
        if is_used:
            self._raise_err(f"{self.db_feature_config.feature_name}已被其他特征占用, {other_msg}操作失败")
        # 是否被biz_view占用
        feature_code_str = f"%{self.db_feature_config.feature_code}%"
        is_used_by_biz_view = self.feature_dao.has_feature_used_by_biz_view(feature_code_str)
        if is_used_by_biz_view:
            self._raise_err(f"{self.db_feature_config.feature_name}已被业务视图占用, {other_msg}操作失败")
        filter_name = self.feature_dao.find_common_filter_used_feature(feature_code_str)
        if filter_name:
            self._raise_err(f"{self.db_feature_config.feature_name}已被{filter_name}筛选器占用, {other_msg}操作失败")

        if self.feature_dao.has_feature_used_by_rule_instance(feature_code_str):
            self._raise_err(f"{self.db_feature_config.feature_name}已被规则实例占用, {other_msg}操作失败")

        # 校验特征是否有人工指标
        if self.db_feature_config.feature_subtype == "human_tag" and self.feature_dao.exists_human_tag_by_feature_code(
            self.db_feature_config.id
        ):
            self._raise_err(f"{self.db_feature_config.feature_name}已被人工标签引用，请先删除对应的人工标签")

        parameter_name = self.feature_dao.get_parameter_name_with_value_used_feature(feature_code_str)
        if parameter_name:
            self._raise_err(f"{self.db_feature_config.feature_name}已被参数{parameter_name}占用，请先删除对应的引用")

        parameter_name = self.feature_dao.get_parameter_name_with_sub_value_used_feature(feature_code_str)
        if parameter_name:
            self._raise_err(f"{self.db_feature_config.feature_name}已被参数{parameter_name}占用，请先删除对应的引用")

        config_names: List[str] = self.feature_dao.get_stock_up_config_used_feature(feature_code_str)
        if config_names:
            self._raise_err(f"特征：{self.feature_config_req.feature_name}，已被备货粒度配置名称为：{','.join(config_names)}引用")

        # 校验特征是否被映射规则占用
        feature_id_str = f"%{self.db_feature_config.id}%"
        if self.feature_dao.has_feature_used_by_feature_mapping(feature_code_str, feature_id_str):
            self._raise_err(f"{self.db_feature_config.feature_name}已被特征映射服务引用，请先删除对应的引用")

    @staticmethod
    def is_str_empty(some_str):
        if not some_str or len(some_str.strip()) == 0:
            return True
        return False

    def validate_feature_value_count(self):
        """指标只支持设置一个值"""
        self.validate_value_body_empty()
        if self.feature_type_enum is FeatureTypeEnum.KPI and len(self.value_body) > 1:
            self._handle_err(ValidatorErrEnum.WRONG_FEATURE_VALUE_COUNT_GE_1)

    def validate_history_data_keep_day(self):
        """验证历史数据保存天数 1~3*365"""
        if not is_greater_than_0_int(self.feature_config_req.history_data_keep_day):
            self._handle_err(ValidatorErrEnum.WRONG_HISTORY_DATA_KEEP_DAY)

        if int(self.feature_config_req.history_data_keep_day) > HISTORY_DATA_KEEP_YEAR_NUM * 365:
            self._handle_err(ValidatorErrEnum.WRONG_HISTORY_DATA_KEEP_DAY_MAX)

    def validate_circle_dependency(self, source_feature: List[str]):
        if not source_feature:
            return

        dep_req = ListDependenciesReq(feature_code=self.feature_config_req.feature_code, is_cascaded=True)
        upstream = self.common_feature_service.list_feature_by_dependency(dep_req)
        upstream_feature_codes = [feature.feature_code for feature in upstream]
        if not upstream_feature_codes:
            return

        circle_intersection = set(source_feature).intersection(set(upstream_feature_codes))
        if circle_intersection:
            self._handle_err(ValidatorErrEnum.FEATURE_CIRCLE_DEPENDENCY, {"feature_code": ".".join(circle_intersection)})

    def _handle_err(self, msg_enum: ValidatorErrEnum, params=None):
        if self.raise_error:
            self._raise_err(msg_enum, params)

        msg = msg_enum.value
        if params:
            msg = msg.format(**params)
        self.has_error = True
        self.err_msg = msg

    @classmethod
    def _raise_err(cls, msg: Union[str, ValidatorErrEnum], params=None):
        if isinstance(msg, ValidatorErrEnum):
            msg = msg.value
        if params:
            msg = msg.format(**params)
        raise ParameterException(msg)

    def _common_basic_validation(self):
        self.validate_feature_name()
        self.validate_feature_type()
        # self.validate_is_custom()
        self.validate_biz_view()
        self.validate_biz_view_dimension()
        self.validate_calculation_type()
        self.validate_data_type()
        self.validate_is_value_multiple()
        self.validate_feature_subtype()
        self.validate_fill_value()
        self.validate_feature_type_data_type_combine()
        self.validate_feature_type_calculation_type_combine()
        self.validate_feature_type_and_subtype_combine()
        self.validate_feature_type_fill_value_combine()
        self.validate_feature_type_is_value_multiple_combine()
        self.validate_feature_value_count()
        self.validate_calculation_type_feature_subtype_combine()
        self.validate_data_type_and_feature_subtype_combine()
        self.validate_history_data_keep_day()
        # value_body
        self.validate_value_body_feature_value()
        self.validate_value_body_duplicated_feature_value()
        # db validate
        self.validate_duplicated_feature_name()

    def _value_req_to_value_egg(self, value_req: List[FeatureValueReq]) -> List[FeatureValueEgg]:
        value_eggs = []
        for index, value_body in enumerate(value_req):
            value_egg = self.value_subtype_obj(value_body)
            value_egg.priority = value_egg.priority if value_egg.priority else index + 1
            value_egg.creator = current_user.user_id
            value_egg.modifier = current_user.user_id
            value_eggs.append(value_egg)

        return value_eggs

    def _make_upsert_value_data(self):
        """获取新增/修改/删除的特征值"""
        should_insert_values, should_update_values, value_changed_ids = [], [], []
        if not self.value_body or self._action_flag not in (ACTION_CREATE, ACTION_UPDATE):
            return UpsertData()

        for index, value in enumerate(self.value_body):
            if is_feature_value_id_new(value.feature_value_id):
                should_insert_values.append(value)
            else:
                if value.feature_value_id not in self.db_id_value_dict:
                    self._handle_err(ValidatorErrEnum.FEATURE_VALUE_NOT_EXISTS, dict(feature_value=value.feature_value))

                    if self.db_id_value_dict[value.feature_value_id].feature_value != value.feature_value:
                        value_changed_ids.append(value.feature_value_id)
                should_update_values.append(value)

            # 统一梳理优先级
            value.priority = index + 1

        self._upsert_data = UpsertData(
            should_insert_values=self._value_req_to_value_egg(should_insert_values),
            should_update_values=self._value_req_to_value_egg(should_update_values),
            value_changed_ids=value_changed_ids,
        )

    def _has_violate_edit_perm(self, req_obj, db_obj, perm_enum, namespace):

        permitted = self.operate_permission_helper.has_permission(db_obj.operate_permission, perm_enum)
        if permitted:
            return False

        table_attr = perm_enum.get_table_attr()
        checked_attrs = table_attr.__dict__[namespace]
        for _attr in checked_attrs:
            if self._req_obj_attr_is_changed(req_obj, db_obj, _attr):
                return True
        return False

    @staticmethod
    def _req_obj_attr_is_changed(req_obj, db_obj, attr_name):
        return getattr(req_obj, attr_name) != getattr(db_obj, attr_name)
