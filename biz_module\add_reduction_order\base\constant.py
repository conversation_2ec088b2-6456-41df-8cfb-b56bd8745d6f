from enum import Enum


class PageNameEnum(Enum):
    """
    页面名称枚举
    """

    # 表格
    operational_strategy_main = "operational_strategy_main"
    # 指标
    operational_strategy_detail = "operational_strategy_detail"


class ModuleNameEnum(Enum):
    """
    模块名称枚举
    """

    operational_strategy = "operational_strategy"


class ComponentType(Enum):
    """组件"""

    type_tree = "tree"
    type_list = "list"
    type_multiple_list = 'multiple_list'


class OpsStatusEnum(Enum):
    # 0：未确认
    unconfirmed = 0
    # 1：已确认
    confirmed = 1

    @classmethod
    def get_label(cls, value: int) -> str:
        d = {
            cls.unconfirmed.value: "未确认",
            cls.confirmed.value: "已确认",
        }
        return d.get(value)


FIELD_TYPE_MAP = {
    "number": "numeric",
    "string": "text",
    "datetime": "timestamp",
    "numeric": "numeric",
}
SYMBOL = {"区间": "{} <= {} <= {}"}

FIELD_TEMP = lambda x: {"en_name": x, "display": True}

# 异常前端需要处理的额外字段
SIZE_FLAG = "{attr}#{size}@{size_group}$style"
SIZE_TOTAL_FLAG = "{attr}#total$style"
ATTR_FLAG = "{attr}$style"
MAIN_SIZE_DOT = "{attr}#{size_code}@{size_group_code}_dot"
MAIN_SIZE_COLOR = {"color": "red"}

DATA_TYPE_FORMATTER_MAPPING = {"int": "0,", "numeric": "0.000"}

FEATURE_CONFIG = lambda x: {
    "lock": "none",
    "cn_name": x.feature_name,
    "display": False,
    "hide": False,
    "en_name": x.feature_code,
    "can_edit": False,
    "ori_name": x.feature_name,
    "col_width": "60",
    "value_type": x.value_type,
    "serial_number": x.serial_number,
    "is_feature": True,
    "feature_type": "kpi",
    "data_type": x.data_type,
    "table_alias": "df",
    "unfold": False,
    "modify_disable": False,
    "except_flag": True,
    "relation": {"range": ["==", ">", ">=", "<", "<=", "区间"]}
    if x.value_type == "number"
    else {"range": ["==", "!="]},
    "formatter": DATA_TYPE_FORMATTER_MAPPING.get(x.data_type)
    if DATA_TYPE_FORMATTER_MAPPING.get(x.data_type)
    else "",
    "sort": True,
    "filter": True,
    "multiple": False,
    "edit": {},
}

TAG_FROM_TABLE = {
    "skc_org": "base_org_skc_tag",
    "org_skc": "base_org_skc_tag",
    "spu_org": "base_org_spu_tag",
    "org_spu": "base_org_spu_tag",
    "sku_org": "base_org_sku_tag",
    "org_sku": "base_org_sku_tag",
    "spu": "base_spu_tag",
    "sku": "base_sku_tag",
    "skc": "base_skc_tag",
    "org": "base_org_tag",
    "kpi": "dim_kpi_define",
}

TAG_DIM_MAP = {
    "skc_org": [
        TAG_FROM_TABLE["skc_org"] + ".skc_sk",
        TAG_FROM_TABLE["skc_org"] + ".org_sk",
    ],
    "org_skc": [
        TAG_FROM_TABLE["skc_org"] + ".skc_sk",
        TAG_FROM_TABLE["skc_org"] + ".org_sk",
    ],
    "spu_org": [
        TAG_FROM_TABLE["spu_org"] + ".spu_sk",
        TAG_FROM_TABLE["spu_org"] + ".org_sk",
    ],
    "org_spu": [
        TAG_FROM_TABLE["spu_org"] + ".spu_sk",
        TAG_FROM_TABLE["spu_org"] + ".org_sk",
    ],
    "sku_org": [
        TAG_FROM_TABLE["sku_org"] + ".sku_sk",
        TAG_FROM_TABLE["sku_org"] + ".org_sk",
    ],
    "org_sku": [
        TAG_FROM_TABLE["sku_org"] + ".sku_sk",
        TAG_FROM_TABLE["sku_org"] + ".org_sk",
    ],
    "spu": [TAG_FROM_TABLE["spu"] + ".spu_sk", ""],
    "sku": [TAG_FROM_TABLE["sku"] + ".sku_sk", ""],
    "skc": [TAG_FROM_TABLE["skc"] + ".skc_sk", ""],
    "org": ["", TAG_FROM_TABLE["org"] + ".org_sk"],
}
FIELD_TYPE_MAP = {
    "number": "numeric",
    "string": "text",
    "datetime": "timestamp",
    "numeric": "numeric",
}

DB_FIELD_TYPE_CONVERT = {
    "skc_sk": "int4",
    "model_allot_in_org_sk": "int4",
    "model_allot_out_org_sk": "int4",
    "human_allot_in_org_sk": "int4",
    "human_allot_out_org_sk": "int4",
    "day_date": "timestamp",
}


def db_field_type_convert(en_name):
    if DB_FIELD_TYPE_CONVERT.get(en_name):
        return f"{en_name}::{DB_FIELD_TYPE_CONVERT[en_name]}"
    else:
        return en_name
