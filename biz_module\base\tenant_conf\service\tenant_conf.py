import logging
from urllib.parse import urlparse

from fastboot.utils.nest import egg

from biz import BaseService
from config.config_manager import Settings

logger = logging.getLogger(__name__)


@egg
class TenantConfService(BaseService):

    @staticmethod
    def query_tenant_conf():
        socket_url = urlparse(Settings.TENANT_SOCKET_SERVER_API)
        return {
            'is_put_on_record': Settings.TENANT_IS_SHOW_PUT_ON_RECORD,
            'skin_conf': Settings.TENANT_SKIN_CONF,
            'tenant_version': Settings.TENANT_VERSION,
            'socket_server': f'{socket_url.scheme}://{socket_url.hostname}:{socket_url.port}',
            'tenant_switch_api': Settings.TENANT_SWITCH_API,
            'env': Settings.APP_ID,
            'tenant_id': Settings.TENANT_CODE,
            'version': Settings.TENANT_VERSION,
        }


tenant_conf_srv = TenantConfService()
