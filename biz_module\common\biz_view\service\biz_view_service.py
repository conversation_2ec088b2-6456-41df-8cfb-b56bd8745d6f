import logging
import json
from typing import List, Dict

from fastboot.utils.nest import egg
from flask_login import current_user
from linezone_commonserver.services.biz_view.constants import ViewCombineEnum
from linezone_commonserver.services.biz_view.service import CommonBizViewService
from linezone_commonserver.services.common.enums import DataTypeEnum
from linezone_commonserver.services.feature.constants import FeatureTypeEnum, FEATURE_VALUE_FIELD_NAME
from linezone_commonserver.services.feature.typedef import ListDependenciesReq, FeatureEntity
from linezone_commonserver.services.meta_data.service import MetaDataService
from linezone_commonserver.services.meta_data.typedef import MetaDataEntity
from ourbatis import transaction_manager
from pydantic.json import pydantic_encoder

from biz.api_exception import ParameterException
from biz_module.common.biz_view.constants import CODE_DELIMITER, BizViewObjectTypeEnum, OPERATING_UNIT_SK_FIELD_NAME
from biz_module.common.biz_view.dao import (
    <PERSON>iz<PERSON>iewDao,
    BizViewDimensionDao,
    BizViewFilterDao,
)
from biz_module.common.biz_view.validator import (
    CombineObjectAndRelationValidator,
    CombineObjectSourceValidator,
    DimensionRequestParamValidator,
    CombineObjectExistenceValidator,
    ObjectAttributeExistenceValidator,
    DimensionValidator,
)
from linezone_commonserver.services.feature.service import FeatureService as CommonFeatureService
from biz_module.common.biz_view.handler.join_sql_generator import JoinSqlGenerator
from biz_module.common.biz_view.helper import get_tenant_table_schema
from biz_module.common.biz_view.typedef import (
    BizViewEntity,
    ObjectDimension,
    CombineObject,
    ObjectRelation,
    BizViewAndDimensionRequest,
    BizViewDimensionEntity,
    BizViewCombineDropListResponse,
    BizViewDimensionDropListResponse,
    GroupBy,
    TableJoinRelation,
    TableRelation,
    MetaDataIntegration,
    BizViewFilter,
)
from biz_module.common.filter.service.feature_filter_service import FeatureFilterService


def generate_sorted_code(input_list: List[str]) -> str:
    return CODE_DELIMITER.join(sorted(input_list))


@egg
class BizViewService:
    # 默认生成condition_sql
    default_view_filter = {"day_date": "{table_alias_name}.day_date = '%1$s'::timestamp"}
    meta_data_service: MetaDataService
    common_feature_service: CommonFeatureService
    feature_filter_service: FeatureFilterService
    common_biz_view_service: CommonBizViewService
    combine_dao = BizViewDao()
    dimension_dao = BizViewDimensionDao()
    view_filter_dao = BizViewFilterDao()
    code_delimiter = "."
    validator_map = {
        "construct": [
            CombineObjectAndRelationValidator(),
            CombineObjectSourceValidator(),
            DimensionRequestParamValidator(),
            CombineObjectExistenceValidator(),
            ObjectAttributeExistenceValidator(),
            DimensionValidator(),
        ]
    }

    logger = logging.getLogger(__name__)

    def list_biz_view_kpi_names(self, biz_view_id: int) -> List[str]:
        """
        根据biz_view_id获取内部kpi名字列表
        Parameters
        ----------
        biz_view_id

        Returns
        -------

        """
        if not biz_view_id:
            raise ParameterException("请选择业务视图")
        biz_view = self.common_biz_view_service.get_biz_view_by_id(biz_view_id)
        if not biz_view:
            raise ParameterException("参数错误, 选的业务视图不存在或已删除")

        feature_codes = [
            _obj.object_code for _obj in biz_view.biz_view_combine if _obj.source_type == ViewCombineEnum.FEATURE.value
        ]

        features: List[FeatureEntity] = self.common_feature_service.list_by_feature_codes(feature_codes)
        return [feature.feature_name for feature in features if feature.feature_type == FeatureTypeEnum.KPI.value]

    def list_biz_view_combine(self) -> List[BizViewCombineDropListResponse]:
        """获取业务视图列表"""
        biz_view_entities = self.combine_dao.list_biz_view()
        biz_views: List[BizViewCombineDropListResponse] = []
        for biz_view_entity in biz_view_entities:
            # todo dimension filter 暂不实现
            dimension_entities = self.dimension_dao.list_by_biz_view_id(biz_view_entity.id) or []
            dimension_dict = {}
            for dimension_entity in dimension_entities:
                dimension_dict.setdefault(dimension_entity.dimension_id, []).append(dimension_entity.dimension_title)
            dimension_dto = [
                BizViewDimensionDropListResponse(dimension_id=k, biz_view_id=biz_view_entity.id, dimension_title="+".join(v))
                for k, v in dimension_dict.items()
            ]
            biz_view_combine = BizViewCombineDropListResponse(
                biz_view_id=biz_view_entity.id, biz_view_name=biz_view_entity.alias_name, biz_view_dimensions=dimension_dto
            )

            biz_views.append(biz_view_combine)

        return biz_views

    def list_attributes(self, biz_view_dimension_id: int, feature_id: int = 0):
        """
        筛选器属性由业务对象的属性 + 拖进来的特征属性+维度关联的特征(排除拖进来的特征？)组成
        """
        feature = None
        exclude_feature_code = set()
        if feature_id:
            feature = self.common_feature_service.get_feature_by_id(feature_id)
            if feature:
                # 排除自己
                exclude_feature_code.add(feature.feature_code)
                # v5.7.0新增排除存在循环依赖的特征
                req = ListDependenciesReq(feature_code=feature.feature_code, is_cascaded=True)
                should_exclude_features = self.common_feature_service.list_feature_by_dependency(req)
                if should_exclude_features:
                    exclude_feature_code.update([feature.feature_code for feature in should_exclude_features])

        if feature is None and not biz_view_dimension_id:
            self.logger.warning("参数错误，feature和biz_view_dimension_id均为空")
            raise ParameterException("参数错误")

        biz_view_dimension_entity = self.dimension_dao.find_view_dimension_by_id(biz_view_dimension_id)
        if not biz_view_dimension_entity:
            self.logger.warning(f"参数错误，找不到视图维度组合biz_view_dimension_id:{biz_view_dimension_id}")
            raise ParameterException("参数错误，找不到视图维度组合")

        biz_view = self.combine_dao.find_biz_view_by_id(biz_view_dimension_entity.biz_view_id)

        ret = []
        biz_object_ret = self.__construct_biz_object_filter_attributes(biz_view.biz_view_combine)
        feature_ret = self.__construct_feature_filter_attributes(biz_view.biz_view_combine)

        # 排除业务视图本身已存在的特征
        exclude_feature_code.update(
            {
                combine_object.object_code
                for combine_object in biz_view.biz_view_combine
                if combine_object.source_type == BizViewObjectTypeEnum.feature.name
            }
        )
        associated_features = self.__construct_associated_feature_filter_attributes(
            biz_view_dimension_entity.meta_data_dimension_code, list(exclude_feature_code)
        )
        ret.extend(biz_object_ret)
        ret.extend(feature_ret)
        ret.extend(associated_features)
        return ret

    def construct(self, construct_body: BizViewAndDimensionRequest) -> Dict:
        """
        构造业务视图
        @param construct_body:
        @return:
        """
        validators = self.validator_map.get("construct")
        for validator in validators:
            validator(construct_body)

        biz_view_entity = self.find_view_by_code(construct_body.objects)
        if biz_view_entity:
            with transaction_manager():
                duplication_biz_view = self.combine_dao.find_by_alias_name(
                    construct_body.alias_name, exclude_biz_view_id=biz_view_entity.id
                )
                if duplication_biz_view:
                    raise ParameterException("参数错误，业务视图名称已存在")

                biz_view_entity = self.update_biz_view(biz_view_entity, construct_body)
                view_dimension = self.find_view_dimension_by_code(biz_view_entity.id, construct_body.dimensions)
                if not view_dimension:
                    meta_data = self.__make_dimension_upsert_data(construct_body.objects, construct_body.dimensions)
                    view_dimension = self.save_view_dimension(biz_view_entity.id, construct_body.dimensions, meta_data)
                else:
                    meta_data = self.__make_dimension_upsert_data(construct_body.objects, construct_body.dimensions)
                    self.update_view_dimension(view_dimension, construct_body.dimensions, meta_data)
                self.upsert_biz_view_default_filter(biz_view_entity.id)
        else:
            with transaction_manager():
                biz_view = self.combine_dao.find_by_alias_name(construct_body.alias_name)
                if biz_view:
                    raise ParameterException("参数错误，业务视图名称已存在")
                biz_view_entity = self.save_biz_view_combine(construct_body)
                meta_data = self.__make_dimension_upsert_data(construct_body.objects, construct_body.dimensions)
                view_dimension = self.save_view_dimension(biz_view_entity.id, construct_body.dimensions, meta_data)
                self.upsert_biz_view_default_filter(biz_view_entity.id)

        return {"biz_view_id": biz_view_entity.id, "biz_view_dimension_id": view_dimension.dimension_id}

    def find_view_by_code(self, objects: List[CombineObject]):
        view_code = generate_sorted_code([self.__gen_view_code(obj) for obj in objects])
        biz_view_entity = self.combine_dao.find_biz_view_by_code(view_code)
        return biz_view_entity

    def find_view_dimension_by_code(self, biz_view_id: int, object_dimensions: List[ObjectDimension]):
        dimension_code = generate_sorted_code([self.__gen_dimension_code(dimension) for dimension in object_dimensions])
        dimension_entity = self.dimension_dao.find_biz_view_dimension_by_code(biz_view_id, dimension_code)
        return dimension_entity

    def update_biz_view(self, biz_view_entity: BizViewEntity, update_body: BizViewAndDimensionRequest) -> BizViewEntity:
        """
        修改业务视图
        被特征占用的业务视图不允许修改
        内置的业务视图不允许修改
        @param biz_view_entity:
        @param update_body:
        @return:
        """
        count = self.common_feature_service.count_feature_by_biz_view_id(biz_view_entity.id)
        if count > 0:
            self.logger.warning(f"业务视图id:{biz_view_entity.id}已被特征占用，跳过修改～")
            return biz_view_entity

        if biz_view_entity.is_builtin:
            self.logger.warning(f"内置业务视图id:{biz_view_entity.id}不允许修改，跳过～")
            return biz_view_entity

        biz_view_entity.alias_name = update_body.alias_name
        biz_view_entity.main_object_code = self.__get_main_object_code(update_body.object_relations, update_body.objects)
        biz_view_entity.join_relation = json.dumps(update_body.object_relations, default=pydantic_encoder)
        biz_view_entity.relation_sql = self.__generate_join_sql(update_body.objects, update_body.object_relations)
        biz_view_entity.modifier = current_user.user_id
        self.combine_dao.update_biz_view_by_id(biz_view_entity)
        return biz_view_entity

    def save_biz_view_combine(self, combine_request: BizViewAndDimensionRequest) -> BizViewEntity:
        biz_view_entity = BizViewEntity()
        biz_view_entity.alias_name = combine_request.alias_name
        biz_view_entity.view_code = generate_sorted_code([self.__gen_view_code(obj) for obj in combine_request.objects])
        biz_view_entity.biz_view_combine = json.dumps(combine_request.objects, default=pydantic_encoder)
        biz_view_entity.join_relation = json.dumps(combine_request.object_relations, default=pydantic_encoder)

        biz_view_entity.relation_sql = self.__generate_join_sql(combine_request.objects, combine_request.object_relations)
        biz_view_entity.main_object_code = self.__get_main_object_code(
            combine_request.object_relations, combine_request.objects
        )
        biz_view_entity.creator = current_user.user_id
        biz_view_entity.id = self.combine_dao.save_biz_view(biz_view_entity)
        return biz_view_entity

    def save_view_dimension(self, biz_view_id: int, dimensions: List[ObjectDimension], meta_data: MetaDataIntegration):
        view_dimension = BizViewDimensionEntity()
        view_dimension.biz_view_id = biz_view_id
        view_dimension.dimension_code = generate_sorted_code([self.__gen_dimension_code(dimension) for dimension in dimensions])
        view_dimension.meta_data_dimension_code = meta_data.meta_data_dimension_codes
        view_dimension.biz_object_attribute_code = meta_data.biz_object_attribute_codes
        view_dimension.table_field_code = meta_data.table_field_codes
        view_dimension.group_by = json.dumps(meta_data.group_bys, default=pydantic_encoder)
        view_dimension.raw_dimension = json.dumps(dimensions, default=pydantic_encoder)
        view_dimension.creator = current_user.user_id
        view_dimension.dimension_id = self.dimension_dao.save_dimension(view_dimension)
        return view_dimension

    def update_view_dimension(
        self, view_dimension: BizViewDimensionEntity, dimensions: List[ObjectDimension], meta_data: MetaDataIntegration
    ):

        view_dimension.meta_data_dimension_code = meta_data.meta_data_dimension_codes
        view_dimension.biz_object_attribute_code = meta_data.biz_object_attribute_codes
        view_dimension.table_field_code = meta_data.table_field_codes
        view_dimension.group_by = json.dumps(meta_data.group_bys, default=pydantic_encoder)
        view_dimension.raw_dimension = json.dumps(dimensions, default=pydantic_encoder)
        view_dimension.modifier = current_user.user_id
        self.dimension_dao.update_dimension(view_dimension)
        return view_dimension.dimension_id

    def upsert_biz_view_default_filter(self, biz_view_id: int):
        biz_view_entity = self.combine_dao.find_biz_view_by_id(biz_view_id)
        if not biz_view_entity:
            self.logger.warning(f"找不到业务视图，无法展开默认条件,biz_view_id:{biz_view_id}")
            raise ParameterException("找不到业务视图，无法展开默认条件")

        default_condition_column = "day_date"
        biz_object_codes = [
            _obj.object_code
            for _obj in biz_view_entity.biz_view_combine
            if _obj.source_type == BizViewObjectTypeEnum.biz_object.name
        ]

        self.view_filter_dao.delete_by_biz_view_id(biz_view_entity.id, current_user.user_id)

        for biz_object_code in biz_object_codes:
            biz_object = self.meta_data_service.get_biz_object_by_code(biz_object_code)
            if not biz_object:
                self.logger.warning(f"生成default_filter错误，找不到{biz_object_code}的业务对象")
                raise ParameterException(f"生成default_filter错误，找不到{biz_object_code}的业务对象")
            # 特征
            if not biz_object.include_table:
                self.logger.warning(f"生成default_filter错误，{biz_object_code}的include_table为空")
                raise ParameterException(f"生成default_filter错误，{biz_object_code}的include_table配置错误")
            include_table = biz_object.include_table[0]

            if not include_table.table_code:
                self.logger.warning(f"生成default_filter错误，{biz_object_code}的include_table的table_code配置错误")
                raise ParameterException(f"生成default_filter错误，{biz_object_code}的include_table的table_code配置错误")

            if not include_table.alias:
                self.logger.warning(f"生成default_filter错误，{biz_object_code}的include_table的alias配置错误")
                raise ParameterException(f"生成default_filter错误，{biz_object_code}的include_table的alias配置错误")

            if self.meta_data_service.exists_field_name_and_table_code(include_table.table_code, default_condition_column):
                view_filter = BizViewFilter()
                view_filter.biz_view_id = biz_view_entity.id
                view_filter.filter_instance_id = 0
                view_filter.source_type = 0
                # todo alias 逻辑统一
                view_filter.default_condition_sql = self.default_view_filter.get(default_condition_column).format(
                    table_alias_name=include_table.alias
                )
                view_filter.creator = current_user.user_id
                self.view_filter_dao.save_view_filter(view_filter)

    def get_meta_data_by_dimension_request(self, objects: List[CombineObject], dimensions: List[ObjectDimension]):
        """
        根据dimension获取关联的元数据
        @param objects: req objects
        @param dimensions: req dimensions
        @return: List[MetaDataEntity]
        """
        object_source_type_dict = {_obj.object_code: _obj.source_type for _obj in objects}
        feature_dim_tuples = []
        biz_dim_tuples = []
        meta_data_objs = []
        for dimension in dimensions:
            if object_source_type_dict.get(dimension.object_code) == BizViewObjectTypeEnum.feature.name:
                special_meta_data_obj = self._get_feature_special_dimension_meta_data(dimension)
                if special_meta_data_obj:
                    meta_data_objs.append(special_meta_data_obj)
                    continue

                feature_dim_tuples.append((dimension.object_code, dimension.dimension_code))
            if object_source_type_dict.get(dimension.object_code) == BizViewObjectTypeEnum.biz_object.name:
                biz_dim_tuples.append((dimension.object_code, dimension.dimension_code))

        for dim_tuple in feature_dim_tuples:
            feature_code, ordinary_dim_code = dim_tuple
            feature_meta_data_obj = self.meta_data_service.get_dimension_field_attribute_by_feature_code(
                feature_code, ordinary_dim_code
            )
            if not feature_meta_data_obj:
                raise ParameterException(f"业务视图组装，获取维度{feature_code}.{ordinary_dim_code}失败")
            feature_meta_data_obj.belong_object_code = feature_code
            feature_meta_data_obj.meta_table_name = feature_code
            meta_data_objs.append(feature_meta_data_obj)

        for dim_tuple in biz_dim_tuples:
            biz_object_code, ordinary_dim_code = dim_tuple
            biz_object_meta_data = self.meta_data_service.get_meta_data_dimension_field_attribute(
                biz_object_code, ordinary_dim_code
            )
            if not biz_object_meta_data:
                raise ParameterException(f"业务视图组装，获取维度{biz_object_code}.{ordinary_dim_code}失败")

            meta_data_table = self.meta_data_service.get_meta_data_table_by_code(biz_object_meta_data.belong_table_code)
            if "." in meta_data_table.table_name:
                biz_object_meta_data.meta_table_name = meta_data_table.table_name.split(".")[1]
            else:
                biz_object_meta_data.meta_table_name = meta_data_table.table_name

            meta_data_objs.append(biz_object_meta_data)

        return meta_data_objs

    def list_dimension_associated_feature_by_view_combine(self, biz_view_combine: List[CombineObject]):
        """获取维度相关联的特征列表"""
        biz_view_dimensions = self.__construct_biz_view_dimensions(biz_view_combine)

        if not biz_view_dimensions:
            return []
        dimension_codes = [dimension.dimension_code for dimension in biz_view_dimensions]
        features = self.common_feature_service.list_feature_by_dimension_codes(dimension_codes)

        if not features:
            return []

        return features

    def _make_operating_unit_sk_meta_data(self, dimension):
        operating_unit_sk_meta_data = MetaDataEntity(
            belong_object_code=dimension.object_code,
            meta_table_name=dimension.object_code,
            dimension_code=OPERATING_UNIT_SK_FIELD_NAME,
            table_field_name=OPERATING_UNIT_SK_FIELD_NAME,
            data_type=DataTypeEnum.INT.value,
        )
        return operating_unit_sk_meta_data

    def _get_feature_special_dimension_meta_data(self, dimension):
        if dimension.dimension_code == OPERATING_UNIT_SK_FIELD_NAME:
            return self._make_operating_unit_sk_meta_data(dimension)

    def __make_dimension_upsert_data(
        self, objects: List[CombineObject], dimensions: List[ObjectDimension]
    ) -> MetaDataIntegration:
        meta_data = self.get_meta_data_by_dimension_request(objects, dimensions)
        sorted_meta_data = []
        for dimension in dimensions:
            if dimension.dimension_code == OPERATING_UNIT_SK_FIELD_NAME:
                continue
            for x in meta_data:
                if x.belong_object_code == dimension.object_code and x.dimension_code == dimension.dimension_code:
                    sorted_meta_data.append(x)

        if not sorted_meta_data:
            self.logger.error(f"生成meta_data为空, meta_data:{meta_data}, sorted_meta_data:{sorted_meta_data}")
            raise ParameterException("元数据为空")

        meta_data_dimension_codes: List[str] = [x.dimension_code for x in sorted_meta_data]
        biz_object_attribute_codes = [x.attribute_code for x in sorted_meta_data if x.attribute_code]
        table_field_codes = [x.table_field_code for x in sorted_meta_data if x.table_field_code]
        group_bys = [
            GroupBy(column=md.meta_table_name + "." + md.table_field_name, data_type=md.data_type)
            for md in sorted_meta_data
            if md.meta_table_name and md.table_field_name and md.data_type
        ]
        return MetaDataIntegration(
            meta_data_dimension_codes=meta_data_dimension_codes,
            biz_object_attribute_codes=biz_object_attribute_codes,
            table_field_codes=table_field_codes,
            group_bys=group_bys,
        )

    @staticmethod
    def __generate_alias_name(table_name) -> str:
        return table_name.split(".").pop() if "." in table_name else table_name

    def __generate_join_sql(self, objects: List[CombineObject], object_relations: List[ObjectRelation]) -> str:
        code_table_name_dict = self.__get_object_code_to_table_name(objects)

        table_relations: List[TableJoinRelation] = []
        join_generator = JoinSqlGenerator()
        if len(objects) == 1:  # 只有有一个组成对象
            _object = objects[0]
            table_name = code_table_name_dict.get(_object.object_code, "")
            if not table_name:
                self.logger.warning(f"无法找到{_object.object_code}对应的物理表")
                raise ParameterException(f"无法找到_object.object_code:{_object.object_code}对应的物理表")

            alias_name = (
                _object.object_code
                if _object.source_type == BizViewObjectTypeEnum.feature.name
                else self.__generate_alias_name(table_name)
            )
            table_join_relations = [TableRelation(table_name=table_name, alias_name=alias_name)]
            table_relations.append(TableJoinRelation(join="None", table_relation=table_join_relations))
            return join_generator.generate_join_sql(table_relations)

        # 多个对象组合 此时关系保存在了object_relations对象中
        for object_relation in object_relations:
            table_join_relation = TableJoinRelation(join=object_relation.join)
            for join_relation in object_relation.join_relation:
                table_name = code_table_name_dict.get(join_relation.object_code, "")
                if not table_name:
                    self.logger.warning(f"无法找到join_relation.object_code:{join_relation.object_code}对应的物理表")
                    raise ParameterException(f"无法找到{join_relation.object_code}对应的物理表")
                code_source_type_dict = {obj.object_code: obj.source_type for obj in objects}

                # 业务对象的表别名为物理表名称
                # 特征别名为 特征code
                alias_name = (
                    join_relation.object_code
                    if code_source_type_dict[join_relation.object_code] == BizViewObjectTypeEnum.feature.name
                    else self.__generate_alias_name(table_name)
                )

                table_join_relation.table_relation.append(
                    TableRelation(on=join_relation.on, table_name=table_name, alias_name=alias_name)
                )

            table_relations.append(table_join_relation)

        join_sql = join_generator.generate_join_sql(table_relations)
        return join_sql

    def __get_object_code_to_table_name(self, objects: List[CombineObject]) -> Dict:
        # 特征 从dm.dim_feature_define中select_sql
        # 业务对象暂时从biz_object_attribute找出来
        # 暂时只支持原子业务对象(只包含一个物理表的业务对象)
        biz_object_codes = []
        feature_codes = []
        for obj in objects:
            if obj.source_type == BizViewObjectTypeEnum.biz_object.name:
                biz_object_codes.append(obj.object_code)
            if obj.source_type == BizViewObjectTypeEnum.feature.name:
                feature_codes.append(obj.object_code)

        code_to_table_name = {}
        for biz_object_code in biz_object_codes:
            table_alias = self.meta_data_service.list_table_alias_by_belong_object_code(biz_object_code)
            table_alias = table_alias.pop()
            if len(table_alias) != 1:
                self.logger.warning(f"不支持biz_object_code:{biz_object_code}包含{len(table_alias)}个物理表的业务对象")
                raise ParameterException("只支持包含一个物理表的业务对象")
            meta_table = self.meta_data_service.find_meta_table_by_code(f"%{table_alias[0]}%")
            if not meta_table:
                self.logger.warning(f"无法找到biz_object_code：{biz_object_code}对应的物理表")
                raise ParameterException(f"无法找到{biz_object_code}对应的物理表")
            code_to_table_name[biz_object_code] = get_tenant_table_schema(meta_table.table_name)

        for feature_code in feature_codes:
            dm_feature_define = self.common_feature_service.get_dm_feature_by_code(feature_code)
            if not dm_feature_define:
                self.logger.warning(f"无法在dm层找到feature_code：{feature_code}对应的物理表,数据未从biz->dm正确同步")
                raise ParameterException(f"无法在dm层找到{feature_code}对应的特征定义")
            code_to_table_name[feature_code] = f"({dm_feature_define.select_sql})"

        return code_to_table_name

    def __get_main_object_code(self, object_relations: List[ObjectRelation], objects: List[CombineObject]) -> str:
        if object_relations and len(object_relations) > 0:
            main_object_code = object_relations[0].join_relation[0].object_code
        else:
            main_object_code = objects[0].object_code
        return main_object_code

    def __gen_view_code(self, combine_object: CombineObject):
        return combine_object.source_type + self.code_delimiter + combine_object.object_code

    def __gen_dimension_code(self, dimension: ObjectDimension):
        return dimension.object_code + self.code_delimiter + dimension.dimension_code

    def __construct_biz_view_dimensions(self, biz_view_combine: List[CombineObject]):
        biz_object_codes = [
            obj.object_code for obj in biz_view_combine if obj.source_type == BizViewObjectTypeEnum.biz_object.name
        ]

        feature_codes = [obj.object_code for obj in biz_view_combine if obj.source_type == BizViewObjectTypeEnum.feature.name]
        dimensions = []
        if biz_object_codes:
            biz_object_dimensions = self.meta_data_service.list_dimension_by_biz_object_code(biz_object_codes)
            dimensions.extend(biz_object_dimensions)

        if feature_codes:
            feature_dimensions = self.meta_data_service.list_dimension_by_feature_code(feature_codes)
            dimensions.extend(feature_dimensions)

        return dimensions

    def __construct_feature_filter_attributes(self, biz_view_combine: List[CombineObject]):
        """
        构建业务数据包含特征的 筛选器属性
        @version 5.5.0 过滤掉特征维度属性，原因参考BUG #103313
        把特征作为业务视图的组成，如果把维度作为条件，其值的范围 应该是在特征表的值范围内，这点后续版本在优化
        """
        # 特征本身属性
        feature_codes = [obj.object_code for obj in biz_view_combine if obj.source_type == BizViewObjectTypeEnum.feature.name]

        return self.feature_filter_service.list_feature_condition_tree(feature_codes)

    def __construct_associated_feature_filter_attributes(
        self, biz_view_dimension_codes: List[str], exclude_feature_codes: List[str] = None
    ):
        """构建具有维度交叉的其他筛选器特征"""

        return self.feature_filter_service.list_feature_condition_tree_by_dimension_codes(
            biz_view_dimension_codes, exclude_feature_codes
        )

    def __construct_biz_object_filter_attributes(self, biz_view_combine: List[CombineObject]):
        """
        构建业务对象筛选器属性
        """
        biz_object_codes = [
            obj.object_code for obj in biz_view_combine if obj.source_type == BizViewObjectTypeEnum.biz_object.name
        ]

        return self.feature_filter_service.list_biz_object_condition_tree(biz_object_codes)

    def query_view_dimension_by_id(self, dimension_id: int):
        return self.dimension_dao.find_view_dimension_by_id(dimension_id)

    def query_biz_view_by_id(self, biz_view_id: int):
        return self.combine_dao.find_biz_view_by_id(biz_view_id)


biz_view_service = BizViewService()
