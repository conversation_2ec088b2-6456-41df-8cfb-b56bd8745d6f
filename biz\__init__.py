#! ../env/bin/python
# -*- coding: utf-8 -*-
import json
import random
import logging

from datetime import datetime
from pathlib import Path
from typing import Optional
from _decimal import Decimal
from dataclasses import is_dataclass, asdict

import numpy as np
import pandas as pd
from fastboot.utils.config import init_config
from fastboot.utils.nest import init_eggs
from fastboot.utils.ourbatis_utils import init_db, init_session, DBType
from flask_babel import LazyString
# from fastboot.utils.tracing import init_tracing
from ourbatis import SqlSession

from pandas import Timestamp

from linezone_commonserver.services import SQL_DIR_PATHS

from biz.settings import config, Config  # noqa
from flask import Flask, make_response, jsonify, Request
from flask_log_request_id import RequestID
from biz.common.lz_db.base_db import db
from biz import api_exception
from biz.utils.csrf_auth import lz_login_required
from biz.utils.process_response import process_response
from biz.extensions import cache, session, mail, babel, login_manager, lzlog, api_log, add_ins, limiter

from biz import modules  # noqa
from biz import http  # noqa
from biz.api_exception import APIException
from biz.base import BaseService

GP_SESSION: Optional[SqlSession] = None
PG_SESSION: Optional[SqlSession] = None


def register_error_handler(app):
    @app.errorhandler(429)
    def rate_limit_handler(e):
        return make_response(jsonify(success=False, code=429, msg="超出限流"), 429)

    @app.errorhandler(500)
    def internal_error(e):
        return make_response(jsonify(success=False, code=500, msg="服务器内部异常"), 500)

    @app.errorhandler(404)
    def not_found(e):
        return make_response(jsonify(success=False, code=10000005, msg="请求内容不存在"), 404)

    @app.errorhandler(APIException)
    def exception_error(e: APIException):
        return make_response(jsonify(success=False, code=e.error_code, msg=e.msg), 200)


def init_token_auth(app):
    schema_name = config["schema_constant"]["system_schema_name"]
    sql = 'select exists (select * from {schema_name}."token_auth" limit 1);'
    with app.app_context():
        auth_token_exists = db.engine.execute(sql.format(schema_name=schema_name)).scalar()
    if not auth_token_exists:
        sql = """
        INSERT INTO {schema_name}."token_auth" (app_id, access_key, secret_key)
        VALUES ('{app_id}', '{access_key}', '{secret_key}');
        """
        base_string = "abcdefghkmnpqrstuvwxyzABCDEFGHGKMNOPQRSTUVWXYZ23456789"
        with app.app_context():
            db.engine.execute(
                sql.format(
                    schema_name=schema_name,
                    app_id=config["app_id"],
                    access_key="".join(random.sample(base_string, 24)),
                    secret_key="".join(random.sample(base_string, 24)),
                )
            )


class CustomRequest(Request):
    @property
    def json(self):
        """
        避免json数据未传递而返回 None
        :return:
        """
        try:
            data = self.get_json()
        except Exception as e:
            data = None
        return data or {}

    @property
    def params(self):
        """
        将参数全都放入一个变量
        处理参数顺序 args > form > json
        :return: {dict}
        """
        data = {}

        for key, value in self.args.items():
            data.setdefault(key, value)

        for key, value in self.form.items():
            data.setdefault(key, value)

        for key, value in self.json.items():
            data.setdefault(key, value)

        return data


def init_dbs():
    product_dir_path = str(Path(__file__).resolve().parents[1] / "biz_module")
    project_dir_path = str(Path(__file__).resolve().parents[1] / "custom_module")
    session_map = {
        DBType.MASTER_DB: init_db(SQL_DIR_PATHS + [product_dir_path, project_dir_path], not_support_sql=True)}
    init_session(session_map)


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if hasattr(obj, '_asdict'):
            return obj._asdict()
        if is_dataclass(obj):
            return asdict(obj)

        # 注册类型处理函数的映射
        type_mapping = {
            datetime: self.handle_datetime,
            Timestamp: self.handle_datetime,
            Decimal: self.handle_decimal,
            # bytes: self.handle_bytes,
        }

        # 获取对象类型的处理函数
        handler = type_mapping.get(type(obj))
        if handler:
            return handler(obj)
        if isinstance(obj, LazyString):
            return str(obj)
        # 如果没有找到处理函数，回退到父类的处理方法

        # 获取对象类型的处理函数
        handler = type_mapping.get(type(obj))
        if handler:
            return handler(obj)
        if isinstance(obj, LazyString):
            return str(obj)
        # 如果没有找到处理函数，回退到父类的处理方法
        # 处理 numpy 数据类型
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, pd.Timestamp):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()

        return super().default(obj)

    @staticmethod
    def handle_datetime(obj):
        """处理 datetime 类型的对象"""
        return obj.strftime("%Y-%m-%d %H:%M:%S")

    @staticmethod
    def handle_decimal(obj):
        """处理 Decimal 类型的对象"""
        return f'{obj:f}'

    @staticmethod
    def handle_bytes(obj):
        """处理 bytes 类型的对象"""
        return obj.decode('utf-8')


def create_app(object_name):
    app = Flask(__name__)
    app.json_encoder = CustomJSONEncoder
    app.request_class = CustomRequest
    # app.wsgi_app = http.root
    # app = http.LzFlask(__name__)
    app.config.setdefault("LOG_REQUEST_ID_LOG_ALL_REQUESTS", True)
    RequestID(app)
    app.config.from_object(object_name)
    # initialize the cache
    cache.init_app(app)
    session.init_app(app)

    babel.init_app(app)

    mail.init_app(app)

    # initialize SQLAlchemy
    db.init_app(app)
    login_manager.init_app(app)

    limiter.init_app(app)
    register_error_handler(app)

    # register our blueprints
    app.errorhandler(Exception)(api_exception.lz_exception_process)
    app.before_request(lz_login_required)
    app.after_request(process_response)
    api_log.init_app(app)
    #  加载modules
    lzlog.init_app(app)
    add_ins.init_app(app, db)
    init_eggs()
    init_config(old_system_configs=Config.__dict__)
    init_dbs()  # 初始化数据库会话

    init_token_auth(app)

    logging.basicConfig()
    logging.getLogger("sqlalchemy").setLevel(logging.ERROR)
    # init_tracing(config.APP_NAME)
    # 注册报表组件
    from biz.utils.register_report_component import register_report_component
    register_report_component()
    return app
