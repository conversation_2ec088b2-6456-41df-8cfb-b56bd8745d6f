from datetime import datetime, date
from typing import List, Optional, Union
from pydantic import BaseModel, validator
from werkzeug.datastructures import FileStorage

from biz.typedef import CommonBaseModel


class PageConfigReq(BaseModel):
    """
    请求页面配置
    """

    config_name: Optional[str]  # 页面配置名
    module_name: Optional[str]
    page_name: Optional[str]
    user_id: Optional[int]
    is_product: Optional[bool]
    is_tenant: Optional[bool]
    is_common: Optional[bool]


class PageConfigIdReq(PageConfigReq):
    """
    保存配置
    """

    page_id: int


class PageConfigSaveReq(BaseModel):
    """
    保存配置
    """

    page_id: int
    parent_code: Optional[str]
    feature_type: str
    feature_code: str
    module_name: str
    en_name: Optional[str]
    user_id: Optional[int]


class FeatureConfigEgg(BaseModel):
    feature_code: Optional[str]
    feature_name: Optional[str]
    feature_dim: Optional[str]
    feature_type: Optional[str]
    status: Optional[int] = 0
    from_table: Optional[str]
    target_table: Optional[str]
    targe_history_table: Optional[str]


class FeatureConfigParam(BaseModel):
    feature_name: str
    feature_code: str
    value_type: str
    data_type: Optional[str]
    serial_number: Optional[int]
    table_alias: Optional[str]
    parent_code: Optional[str]


class FeatureConfigResp(BaseModel):
    feature_code: Optional[str]
    feature_name: Optional[str]
    feature_dim: Optional[str]
    feature_type: Optional[str]
    status: Optional[int] = 0
    from_table: Optional[str]
    target_table: Optional[str]
    targe_history_table: Optional[str]


class ScriptFeatureInfoResult(BaseModel):

    id: int
    feature_code: str
    feature_name_tag: Optional[str]
    feature_value_id: int


class ScriptPageConfigFormatUpdateDao(BaseModel):

    page_id: int
    content: str


class ScriptPageConfigAddDao(BaseModel):

    page_id: int
    content: dict
