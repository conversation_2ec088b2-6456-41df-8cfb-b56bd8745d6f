class FeatureDefine(object):
    def __init__(self, schema_prefix, feature_code, feature_dim, feature_type, feature_subtype, data_type, 
                 is_value_multiple, calculation_type, hit_sql, is_compatible=1):
        # dim_column: skc_sk, org_sk
        # dim_column_data_type: skc_sk int, org_sk int
        # dim_json_build: 'skc_sk', skc_sk, 'org_sk', org_sk
        # dim_column_build: (feature_object ->> 'skc_sk')::int as skc_sk, (feature_object ->> 'org_sk')::int as org_sk
        dim_column_list, dim_column_data_type_list, dim_json_build_list, dim_column_build_list = [], [], [], []
        product_dim_count, org_dim_count, other_dim_count = 0, 0, 0
        product_table, product_column, product_data_type, org_table, org_column, org_data_type = "", "", "", "", "", ""
        product_obj, org_obj = "", ""

        for d in feature_dim:
            t = d["column"].split(".")[0]
            c = d["column"].split(".")[1]
            dt = d["data_type"]
            dim_column_list.append("{c}".format(c=c))
            dim_column_data_type_list.append("{c} {dt}".format(c=c, dt=dt))
            dim_json_build_list.append("'{c}', {c}".format(c=c))
            dim_column_build_list.append("(feature_object ->> '{c}')::{dt} as {c}".format(c=c, dt=dt))

            if t in ["dim_sku", "dim_skc", "dim_spu"]:
                product_dim_count += 1
                product_table, product_column, product_data_type = t, c, dt
                if c == "sku_sk":
                    product_obj = "sku"
                elif c == "skc_sk":
                    product_obj = "skc"
                elif c == "product_sk":
                    product_obj = "spu"
            elif t in ["dim_org_integration"]:
                org_dim_count += 1
                org_table, org_column, org_data_type = t, c, dt
                if c == "org_sk" or c.startswith("org_hierarchy_"):
                    org_obj = "org"
            else:
                other_dim_count += 1

        dim_json_build = ", ".join(dim_json_build_list)
        dim_column_data_type = ", ".join(dim_column_data_type_list)
        dim_column = ", ".join(dim_column_list)
        dim_column_build = ", ".join(dim_column_build_list)

        product_org_obj_list = [product_obj, org_obj]
        product_org_obj = "_".join([i for i in product_org_obj_list if i])
        org_product_obj_list = [org_obj, product_obj]
        org_product_obj = "_".join([i for i in org_product_obj_list if i])

        self.params_dict = {
            "schema_prefix": schema_prefix,
            "feature_code": feature_code,
            "dim_column": dim_column,
            "dim_column_data_type": dim_column_data_type,
            "dim_json_build": dim_json_build,
            "dim_column_build": dim_column_build,
            "product_table": product_table,
            "product_column": product_column,
            "product_data_type": product_data_type,
            "org_table": org_table,
            "org_column": org_column,
            "org_data_type": org_data_type,
            "product_obj": product_obj,
            "org_obj": org_obj,
            "product_org_obj": product_org_obj,
            "org_product_obj": org_product_obj,
            "data_type": data_type,
            "feature_type": feature_type,
            "feature_subtype": feature_subtype,
            "is_value_multiple": is_value_multiple,
            "calculation_type": calculation_type,
            "hit_sql": hit_sql,
            "kpi_tag_define_sql": "",
            "tag_result_sql": "",
        }

        # 是否兼容老kpi和tag
        if is_compatible and product_dim_count <= 1 and org_dim_count <= 1 and other_dim_count == 0:
            if feature_type == "kpi":
                self.params_dict["kpi_tag_define_sql"] = self.get_kpi_define_sql()
            elif feature_type == "tag":
                if (product_dim_count == 1 and product_obj == "") or (org_dim_count == 1 and org_obj == ""):
                    pass
                else:
                    self.params_dict["kpi_tag_define_sql"] = self.get_tag_define_sql()
                    self.params_dict["tag_result_sql"] = self.get_tag_result_sql()

        self.params_dict["create_sql"] = self.get_create_sql()
        self.params_dict["select_sql"] = self.get_select_sql()

    def get_feature_define(self):
        sql = """
        --获取特征及业务视图配置
        drop table if exists tmp_feature_config;
        create temp table tmp_feature_config as 
        select
            a.id, a.feature_code, a.feature_name, c.group_by as feature_dim, a.feature_type, a.feature_subtype, a.data_type, 
            a.calculation_type, a.calculation_cycle, a.storage_type, a.is_custom, a.is_auto_sql, a.is_value_multiple, 
            a.is_select, a.fill_value, a.biz_view_id, a.biz_view_dimension_id, a.biz_view_filter_id, b.biz_view_combine,
            a.history_data_keep_day
        from {schema_prefix}biz.base_feature_config a
        inner join {schema_prefix}biz.base_biz_view b 
            on a.biz_view_id = b.id
        inner join {schema_prefix}biz.base_biz_view_dimension c 
            on a.biz_view_dimension_id = c.dimension_id
        where a.feature_code = '{feature_code}' and a.is_deleted = 0 and a.is_active = 1
        ;
    
        --获取特征维度数组
        drop table if exists tmp_feature_dim_array;
        create temp table tmp_feature_dim_array as 
        select 
            feature_code, array_agg(feature_dim_array) as feature_dim_array
        from (select feature_code, (jsonb_array_elements(feature_dim) ->> 'column') as feature_dim_array from tmp_feature_config) a
        group by 
            feature_code
        ;
    
        --获取特征值配置
        drop table if exists tmp_feature_config_value;
        create temp table tmp_feature_config_value as 
        select
            a.feature_code, b.feature_value, b.source_feature
        from tmp_feature_config a
        inner join {schema_prefix}biz.base_feature_config_value b 
            on a.id = b.feature_config_id
        where b.is_deleted = 0
        ;
    
        --获取特征值数组
        drop table if exists tmp_feature_value;
        create temp table tmp_feature_value as 
        select
            feature_code, array_agg(feature_value) as feature_value
        from tmp_feature_config_value
        group by 
            feature_code
        ;
    
        --业务视图组合展开
        drop table if exists tmp_biz_view_combine;
        create temp table tmp_biz_view_combine as 
        select 
            feature_code, 
            jsonb_array_elements(biz_view_combine) ->> 'object_code' as object_code, 
            jsonb_array_elements(biz_view_combine) ->> 'source_type' as source_type
        from tmp_feature_config
        ;
        
        --合并业务视图和特征值来源
        drop table if exists tmp_source_feature_code;
        create temp table tmp_source_feature_code as 
        select 
            feature_code, object_code as source_feature
        from tmp_biz_view_combine
        where source_type = 'feature'
        union all
        select
            feature_code, unnest(source_feature) as source_feature
        from tmp_feature_config_value
        ;
        
        drop table if exists tmp_biz_view_combine;
        drop table if exists tmp_feature_config_value;
        
        --获取来源特征
        drop table if exists tmp_source_feature;
        create temp table tmp_source_feature as 
        select 
            feature_code, array_agg(distinct source_feature) as source_feature
        from tmp_source_feature_code
        group by 
            feature_code
        ;
        
        drop table if exists tmp_source_feature_code;
        
        --写入特征定义
        delete from {schema_prefix}dm.dim_feature_define where feature_code = '{feature_code}';
        insert into {schema_prefix}dm.dim_feature_define(
            feature_code, feature_name, feature_dim, feature_dim_array, feature_type, feature_subtype, feature_value, 
            data_type, calculation_type, calculation_cycle, storage_type, is_custom, is_auto_sql, is_value_multiple, 
            is_select, fill_value, biz_view_id, biz_view_dimension_id, biz_view_filter_id, source_feature, etl_time,
            history_data_keep_day
        )
        select
            a.feature_code, a.feature_name, a.feature_dim, b.feature_dim_array, a.feature_type, a.feature_subtype, coalesce(c.feature_value, array[]::text[]) as feature_value, 
            a.data_type, a.calculation_type, a.calculation_cycle, a.storage_type, a.is_custom, a.is_auto_sql, a.is_value_multiple, 
            a.is_select, a.fill_value, a.biz_view_id, a.biz_view_dimension_id, a.biz_view_filter_id, d.source_feature, 
            current_timestamp as etl_time,
            a.history_data_keep_day
        from tmp_feature_config a
        inner join tmp_feature_dim_array b 
            on a.feature_code = b.feature_code
        left join tmp_feature_value c 
            on a.feature_code = c.feature_code
        left join tmp_source_feature d 
            on a.feature_code = d.feature_code
        ;
        
        drop table if exists tmp_feature_config;
        drop table if exists tmp_feature_dim_array;
        drop table if exists tmp_feature_value;
        drop table if exists tmp_source_feature;
        
        update {schema_prefix}dm.dim_feature_define 
        set create_sql = $create_sql${create_sql}$create_sql$
        where feature_code = '{feature_code}'
        ;
        
        update {schema_prefix}dm.dim_feature_define 
        set select_sql = $select_sql${select_sql}$select_sql$
        where feature_code = '{feature_code}'
        ;
        
        {kpi_tag_define_sql}
        """.format(**self.params_dict)
        return sql
    
    def get_feature_result_delete(self):
        tag_result_delete_sql = ""
        if self.params_dict["tag_result_sql"]:
            tag_result_delete_sql = "delete from {schema_prefix}edw.base_{org_product_obj}_tag where tag_code = '{feature_code}';".format(**self.params_dict)
        
        sql = """
        drop table if exists {schema_prefix}feature.{feature_code};
        {tag_result_delete_sql}
        """.format(**self.params_dict, tag_result_delete_sql=tag_result_delete_sql)
        return sql

    def get_create_sql(self):
        feature_type = self.params_dict["feature_type"]
        feature_subtype = self.params_dict["feature_subtype"]
        is_value_multiple = self.params_dict["is_value_multiple"]
        calculation_type = self.params_dict["calculation_type"]
        
        # 构建方式为规则自动 或 计算类型为模型中，需要拼上tmp_hit_sql前缀
        hit_sql_prefix = "'drop table if exists tmp_hit_sql; create temp table tmp_hit_sql as ' || " if feature_subtype == "rule_auto" or calculation_type == "mid" else ""
        
        if feature_type == "tag" and is_value_multiple == 0:
            priority_rn = "row_number() over(partition by operating_unit_sk, feature_object order by priority) as rn"
            priority_condition = "where rn = 1"
        else:
            priority_rn = "1 as rn"
            priority_condition = ""
        
        tmp_priority_sql = """
        --获取特征定义
        drop table if exists tmp_feature_define;
        create temp table tmp_feature_define as 
        select
            a.feature_code, a.feature_dim, a.feature_type, a.feature_subtype, a.data_type, 
            a.calculation_type, a.calculation_cycle, a.storage_type, a.is_value_multiple, 
            c.feature_value, c.priority, c.hit_sql, row_number() over(order by c.priority) as rn
        from {schema_prefix}dm.dim_feature_define a
        inner join {schema_prefix}biz.base_feature_config b 
            on b.is_deleted = 0 and b.is_active = 1 and a.feature_code = b.feature_code
        inner join {schema_prefix}biz.base_feature_config_value c 
            on c.is_deleted = 0 and c.is_runable = 1 and b.id = c.feature_config_id
        where a.feature_code = '{feature_code}'
        ;
        
        --创建临时结果表
        drop table if exists tmp_feature_object; 
        create temp table tmp_feature_object(
            priority int, operating_unit_sk int, feature_object jsonb, {dim_column_data_type}, feature_value {data_type}
        );
        
        --取最大规则数
        select coalesce(max(rn), 0) from tmp_feature_define into v_max_rn;
        raise notice '遍历循环，总共有%轮循环', v_max_rn;
        --判断是否有规则
        if v_max_rn > 0 then 
            --循环执行
            for v_rn in 1..v_max_rn loop
                raise notice '开始第%轮循环', v_rn;
                --条件变量赋值
                select priority, hit_sql from tmp_feature_define where rn = v_rn into v_priority, v_hit_sql;
                v_sql := format(
                    {hit_sql_prefix}{schema_prefix}proc.replace_format(v_hit_sql), '${{day_date}}'
                );
                raise notice 'v_sql: %', v_sql; 
                execute v_sql;
                
                --插入临时结果表
                insert into tmp_feature_object(priority, operating_unit_sk, feature_object, {dim_column}, feature_value)
                select 
                    v_priority as priority, operating_unit_sk, json_build_object({dim_json_build})::jsonb as feature_object, 
                    {dim_column}, feature_value
                from tmp_hit_sql
                ;
                drop table if exists tmp_hit_sql;
            end loop;
        end if;
        
        --生成优先级序号
        drop table if exists priority_rn;
        create temp table priority_rn as 
        select 
            '{feature_code}' as feature_code, priority, operating_unit_sk, feature_object, {dim_column}, feature_value, 
            {priority_rn}
        from tmp_feature_object
        ;
        
        drop table if exists tmp_feature_object;
        
        --做优先级比较
        drop table if exists tmp_priority;
        create temp table tmp_priority as 
        select 
            feature_code, priority, operating_unit_sk, feature_object, {dim_column}, feature_value
        from priority_rn
        {priority_condition}
        ;
        
        drop table if exists priority_rn;
        """.format(**self.params_dict, hit_sql_prefix=hit_sql_prefix, priority_rn=priority_rn, 
                   priority_condition=priority_condition)

        # 构建方式为人工打标，直接取人工表数据
        if feature_subtype == "human_tag":
            tmp_priority_sql = """
            --取人工表数据
            drop table if exists tmp_priority;
            create temp table tmp_priority as 
            select 
                feature_code, operating_unit_sk, feature_object, {dim_column_build}, feature_value::{data_type}
            from {schema_prefix}biz.base_feature_human
            where feature_code = '{feature_code}'
            ;
            """.format(**self.params_dict)
        
        sql = """
        do 
        $body$
        declare
            v_max_rn int;
            v_rn int;
            v_priority int;
            v_hit_sql text;
            v_sql text;
        begin
        
        {tmp_priority_sql}
        
        --插入数据
        perform {schema_prefix}feature.handle_feature_partition('{schema_prefix}feature.{feature_code}', '${{day_date}}'::timestamp, '{data_type}');
        insert into {schema_prefix}feature.{feature_code}(
            feature_code, operating_unit_sk, feature_object, feature_value, etl_time, day_date
        )
        select 
            feature_code, operating_unit_sk, feature_object, feature_value, 
            current_timestamp as etl_time, '${{day_date}}'::timestamp as day_date
        from tmp_priority
        ;
        
        {tag_result_sql}
        
        drop table if exists tmp_priority;
        
        end
        $body$;
        """.format(**self.params_dict, tmp_priority_sql=tmp_priority_sql)
        return sql

    def get_select_sql(self):
        sql = """
        select
            feature_code, operating_unit_sk, feature_object, feature_value, {dim_column_build}
        from {schema_prefix}feature.{feature_code}
        where day_date = '%1$s'::timestamp
        """.format(**self.params_dict)

        # 计算类型为模型中，直接使用hit_sql作为select_sql
        if self.params_dict["calculation_type"] == "mid":
            sql = "{hit_sql}".format(**self.params_dict)
            
        return sql

    def get_kpi_define_sql(self):
        calculation_type = self.params_dict["calculation_type"]
        kpi_product_dim, kpi_product_dim_value, kpi_org_dim, kpi_org_dim_value = "''", "''", "''", "''"

        if self.params_dict["product_column"]:
            kpi_product_dim = "{t}.{c}".format(t=self.params_dict["product_table"], c=self.params_dict["product_column"])
            kpi_product_dim_value = "(feature_object ->> '{c}')::{dt}".format(
                c=self.params_dict["product_column"], dt=self.params_dict["product_data_type"]
            )
            # 计算类型为模型中，直接取维度字段
            if calculation_type == "mid":
                kpi_product_dim_value = "{c}".format(c=self.params_dict["product_column"])
                
        if self.params_dict["org_column"]:
            kpi_org_dim = "{t}.{c}".format(t=self.params_dict["org_table"], c=self.params_dict["org_column"])
            kpi_org_dim_value = "(feature_object ->> '{c}')::{dt}".format(
                c=self.params_dict["org_column"], dt=self.params_dict["org_data_type"]
            )
            # 计算类型为模型中，直接取维度字段
            if calculation_type == "mid":
                kpi_org_dim_value = "{c}".format(c=self.params_dict["org_column"])

        from_table = "{schema_prefix}feature.{feature_code}".format(**self.params_dict)
        condition = "where day_date = '%1$s'::timestamp"

        # 计算类型为模型中，用hit_sql做子查询
        if calculation_type == "mid":
            from_table = "({hit_sql}) as t".format(**self.params_dict)
            condition = ""
        
        sql = """
        delete from {schema_prefix}dm.dim_kpi_define where kpi_code = '{feature_code}';
        insert into {schema_prefix}dm.dim_kpi_define(
            kpi_code, kpi_name, kpi_dim, data_type, calculation_flag, is_custom, is_select, select_sql, fill_value, etl_time
        )
        select 
            feature_code as kpi_code, feature_name as kpi_name, 
            array[$${kpi_product_dim}$$, $${kpi_org_dim}$$] as kpi_dim, data_type, 
            case 
                when calculation_type = 'pre' then 1 
                when calculation_type = 'mid' then 2 
                when calculation_type = 'after' then 3 
            end as calculation_flag, 
            is_custom, is_select, 
            $sql$
                select 
                    '{feature_code}' as kpi_code, operating_unit_sk, 
                    {kpi_product_dim_value} as product_dim_value, {kpi_org_dim_value} as org_dim_value, 
                    feature_value as kpi_value 
                from {from_table}
                {condition}
            $sql$ as select_sql, fill_value, current_timestamp as etl_time
        from {schema_prefix}dm.dim_feature_define
        where feature_code = '{feature_code}' and feature_type = 'kpi'
        ;
        """.format(**self.params_dict, kpi_product_dim=kpi_product_dim, kpi_org_dim=kpi_org_dim,
                   kpi_product_dim_value=kpi_product_dim_value, kpi_org_dim_value=kpi_org_dim_value,
                   from_table=from_table, condition=condition)
        return sql

    def get_tag_define_sql(self):
        sql = """
        delete from {schema_prefix}edw.base_tag where tag_code = '{feature_code}';
        insert into {schema_prefix}edw.base_tag (
            tag_code, tag_name, object_type, tag_type, etl_time
        )
        select 
            feature_code as tag_code, feature_name as tag_name, 
            '{product_org_obj}' as object_type, 
            'user_user_user' as tag_type, current_timestamp as etl_time
        from {schema_prefix}dm.dim_feature_define
        where feature_code = '{feature_code}' and feature_type = 'tag'
        ;
        
        delete from {schema_prefix}edw.base_tag_value where tag_code = '{feature_code}';
        insert into {schema_prefix}edw.base_tag_value (
            tag_code, tag_value, etl_time
        )
        select 
            feature_code as tag_code, unnest(feature_value) as tag_value, current_timestamp as etl_time
        from {schema_prefix}dm.dim_feature_define
        where feature_code = '{feature_code}' and feature_type = 'tag'
        ;
        """.format(**self.params_dict)
        return sql

    def get_tag_result_sql(self):
        product_obj, org_obj = self.params_dict["product_obj"], self.params_dict["org_obj"]

        insert_column_list, select_column_list = [], []
        insert_column_list.append("operating_unit_sk, org_sk" if org_obj else "")
        insert_column_list.append(product_obj + "_sk" if product_obj else "")
        insert_column = ", ".join([i for i in insert_column_list if i])

        select_column_list.append("b.operating_unit_sk, b.org_sk" if org_obj else "")
        select_column_list.append("a." + self.params_dict["product_column"] if product_obj else "")
        select_column = ", ".join([i for i in select_column_list if i])

        upstream_org_sql = """
        --获取上游组织维表
        drop table if exists tmp_upstream_org;
        create temp table tmp_upstream_org as
        select
            operating_unit_sk, org_sk,  unnest(string_to_array(org_long_sk, ':')::int[]) as upstream_org_sk
        from {schema_prefix}dm.dim_org_integration
        ;
        """.format(**self.params_dict) if org_obj else ""

        join_sql = """
        inner join tmp_upstream_org b on a.operating_unit_sk = b.operating_unit_sk and a.{org_column} = b.upstream_org_sk
        """.format(**self.params_dict) if org_obj else ""

        sql = """
        {upstream_org_sql}
        
        --插入
        delete from {schema_prefix}edw.base_{org_product_obj}_tag where tag_code = '{feature_code}';
        insert into {schema_prefix}edw.base_{org_product_obj}_tag (
            {insert_column}, tag_code, tag_value, etl_time
        )
        select 
            {select_column}, a.feature_code as tag_code, a.feature_value as tag_value, 
            current_timestamp as etl_time
        from tmp_priority a {join_sql}
        ;
        """.format(**self.params_dict, upstream_org_sql=upstream_org_sql,
                   insert_column=insert_column, select_column=select_column, join_sql=join_sql)
        return sql


if __name__ == "__main__":
    # schema_prefix = "tenant_bsgj_"
    # feature_code = "kpi$00841$20220331141346"
    # feature_dim = [
    #     {"column": "dim_skc.skc_sk", "data_type": "int"},
    #     {"column": "dim_org_integration.org_sk", "data_type": "int"},
    #     # {"column": "model.step_id", "data_type": "integer"},
    # ]
    # feature_type = "kpi"
    # feature_subtype = "sql_app"
    # data_type = "numeric"
    # is_value_multiple = 0
    # calculation_type = "mid"
    # hit_sql = """
    #     select
    #         'skc_store_last_template_after_residual_flag' as feature_code, operating_unit_sk, skc_sk, stockorg_sk as org_sk, is_residual_size as feature_value
    #     from
    #     (
    #         --期初库存状态赋值batch_id=0，取各款店最大batch_id残码数据
    #         select operating_unit_sk,skc_sk,stockorg_sk,is_residual_size,batch_id
    #         from (
    #             select operating_unit_sk,skc_sk,stockorg_sk,is_residual_size,batch_id
    #                 ,row_number() over(partition by operating_unit_sk,skc_sk,stockorg_sk order by batch_id desc) as row_num
    #             from (
    #                 select operating_unit_sk,skc_sk,stockorg_sk,is_residual_size as is_residual_size,0 as batch_id
    #                 from "tenant_bsgj_adm".adm_skc_store_stock_status
    #                 where day_date = '%1$s'::timestamp
    #                 union all
    #                 select operating_unit_sk,skc_sk,stockorg_sk,is_residual_size_current as is_residual_size,batch_id::int as batch_id
    #                 from "tenant_bsgj_adm".gto_skc_store_stock_status_workflow
    #                 where day_date = '%1$s'::timestamp
    #             ) aa
    #         ) bb
    #         where row_num='1'
    #     ) cc
    # """
    # 
    # feature_define = FeatureDefine(schema_prefix=schema_prefix, feature_code=feature_code, feature_dim=feature_dim,
    #                                feature_type=feature_type, feature_subtype=feature_subtype, data_type=data_type,
    #                                is_value_multiple=is_value_multiple, calculation_type=calculation_type,
    #                                hit_sql=hit_sql)
    # print(feature_define.get_feature_define())
    # print(feature_define.get_feature_result_delete())
    pass

