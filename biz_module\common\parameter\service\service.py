import json
import logging
import math
import threading
from itertools import groupby
from operator import itemgetter
from typing import List, Tuple, Union, Dict

import pandas as pd
from deprecated.classic import deprecated
from fastboot.utils.nest import egg
from flask_login import current_user
from linezone_commonserver.services.parameter.service import CommonParameterService
from ourbatis.session import transaction_manager

from biz.api_exception import ParameterException
from biz.base import BaseService
from biz.typedef import PageResultInfo
from biz.utils import tools
from biz.utils.tools import LabelValue, context_wrapper
from biz_module.base.authorization.auth_region.constants import RegionQueryWayEnum, OrgFlagEnum
from biz_module.common.parameter.tools.parameter_value_parse import ParameterTypeEnum, parse_parameter_value_by_type
from biz_module.common.parameter.typedef import ArgsDefineFront, TableParameterValueDTO, \
    ParameterSubValueInstance, GroupVO, CollectionVO, GroupReq
from biz_module.unity_service.service.context import rule_instance_set_service, rule_instance_service, \
    auth_region_service
from .value_service import value_service
from ..constants import ParameterDefineMode, PriorityWayEnum, ParameterDefineType, \
    syz_ra_exception_validator, PARAMETER_CONFIG_KEY
from ..dao import ParameterDao, ParameterSubValueDao
from ..tools.collection_node import collections_nodes
from ..tools.data_transfer_structure import list_to_map, type_transfer_for_front, zip_to_map
from ..tools.pagination import page_params_to_limit
from ...parameter import typedef
from ...rule_engine.service.rule import rule_instance_service, rule_instance_set_service, rule_definition_service
from ...rule_engine.typedef import (
    RemoveRuleInstanceRequest,
    CopyRuleInstanceRequest,
    SaveRuleInstanceSetRequest,
    PageQueryRuleInstanceRequest,
    RuleInstanceRequest,
    RemoveRuleInstanceSetBySourceObjectRequest,
    HitConditionSqlReq,
)

logger = logging.getLogger(__name__)


def reverse_is_enable(value_item_list: List, index: int) -> List:
    is_enable = 0 if value_item_list[index]['is_enable'] else 1
    value_item_list[index]['is_enable'] = is_enable
    if is_enable:
        priority = min([i.get('priority') for i in value_item_list]) - 1
    else:
        priority = max([i.get('priority') for i in value_item_list]) + 1
    value_item_list[index]['priority'] = priority
    value_item_list = sorted(value_item_list, key=itemgetter('priority'))
    return value_item_list


@egg
class ParameterService(BaseService):
    """
    参数服务
    """
    common_parameter_service: CommonParameterService
    sub_value_dao: ParameterSubValueDao

    dao: ParameterDao

    def find_category_tree(self, category_tree: typedef.CategoryTreeRequest):
        """
        查询类别树
        :param category_tree:
        :return:
        """
        codes = category_tree.codes
        applied_type = category_tree.applied_type
        category_result_list = []
        # 参数集
        category_group = GroupReq(**category_tree.dict())
        category_group.applied_types = [applied_type] if isinstance(applied_type, str) else applied_type
        view_mapping = {i.biz_action_type_code: i.view_code for i in self.dao.query_action_code_view_mapping()}
        view_codes = [view_mapping.get(i) for i in category_group.action_code_list if i in view_mapping]
        category_group.view_codes = view_codes or None
        collections = self.get_param_condition(category_group)

        parameter_names_map = zip_to_map(collections, key='collection_code', val='filter_title')
        collection_map = list_to_map(collections, key='collection_code')
        # 参数分类
        category_list = self.dao.list_parameter_recursive_categories(codes)
        if not category_list:
            return category_result_list
        for category in category_list:
            category_node = typedef.CategoryTreeNode(
                key=category.category_code,
                parent_id=category.parent_code,
                title=category.category_name,
                layout=None,
                type=0,
                seq=category.seq
            )
            if category_tree.trunk_only:
                collections = []
            else:
                cols = category.collection_codes
                collections = collections_nodes(category_node, collection_map, cols, parameter_names_map)
            category_node.children = collections
            category_result_list.append(category_node.dict())
        key = ['key', 'parent_id', 'title', 'layout', 'type', 'seq', 'children', 'is_configured', 'is_required']
        tree, _ = tools.get_tree(pd.DataFrame(category_result_list, columns=key), "key", "parent_id")
        return tree, collection_map, parameter_names_map

    def query_parameter_remark_by_code(self, parameter_code):
        res = self.dao.get_parameter_define_by_code(parameter_code)
        if not res:
            raise ParameterException(msg=f'{parameter_code} 不存在')

        return res.remark

    def get_param_condition(self, param: typedef.GroupReq) -> List[typedef.CollectionCondDTO]:
        conditions = self.dao.query_collection_by_cond(param)
        return conditions

    def find_rule_parameter_value_by_code(self, table_id, org_sk, parameter_code, page_no, page_size, key_word):
        """
        分页查询规则实例
        :param key_word:
        :param table_id:
        :param org_sk:
        :param parameter_code:
        :param page_no:
        :param page_size:
        :return:
        """
        # 1.查询参数对应的具体值
        parameter_table_value = typedef.QueryBaseParameterTableValue(
            parameter_code=parameter_code, parameter_table_id=table_id, biz_type="template"
        )
        parameter_table_value_result = self.dao.find_parameter_define_value_by_params(parameter_table_value)
        if parameter_table_value_result:
            define = value_service.get_parameter_define_by_code(parameter_code)
            if define.parameter_type == ParameterDefineType.table:
                value = parameter_table_value_result
                res = value_service.page_value_mode_table_values(value, define, page_no, page_size)
                return res
            if define.parameter_type == ParameterDefineType.level_table.value:
                value = parameter_table_value_result
                res = value_service.page_value_mode_table_values(value, define, page_no, page_size, key_word)
                return res
            new_rule_instance_set_id = parameter_table_value_result.parameter_value.get("code")
            return self.page_rule_value(new_rule_instance_set_id, parameter_code, page_no, page_size)
        pagination = page_params_to_limit(page_no, page_size)
        res = PageResultInfo(
            page_no=pagination.page_no,
            page_size=pagination.page_size,
            total_rows=0,
            total_pages=0,
            items=[],
        )
        return res.dict()

    def page_rule_value(self, rule_instance_set_id, parameter_code, page_no, page_size) -> Dict:
        """处理规则类型参数返回值
        Args:
            rule_instance_set_id:
            parameter_code:
            page_no:
            page_size:
        Return:
            Dict:
        """
        pagination = page_params_to_limit(page_no, page_size)
        rules_query = PageQueryRuleInstanceRequest(
            page_no=page_no, page_size=page_size, rule_instance_set_id=rule_instance_set_id
        )

        define = self.dao.find_parameter_define_detail_by_code(parameter_code)
        rules_query.deal_order_attr(define.parameter_value.get('order_attr_sql'))
        rules = rule_instance_service.page_rule_instance_by_set_id(rules_query)
        data = []
        last = len(rules.items)
        total_pages = math.ceil(rules.total_rows / pagination.page_size)
        enable_flag = True
        first_disable_index = -1
        for idx, i in enumerate(rules.items):
            if enable_flag and i.is_enable == 0:
                first_disable_index = idx
                enable_flag = False
            description = i.hit_condition.get('description')
            item = {
                "priority": (idx + 1 + (rules.page_no - 1) * page_size) if i.is_enable else '_',
                "id": i.id,
                "remark": i.description,
                "description": description,
                "is_enable": i.is_enable,
                "up_move": 0 if (rules.page_no == 1 and idx == 0) or i.is_enable == 0 else 1,
                "down_move": 0 if (rules.page_no == total_pages and idx == last - 1) or i.is_enable == 0 else 1,
                "satisfy_condition": i.satisfy_condition
            }
            value = i.hit_execute_action.get("action_value") if isinstance(i.hit_execute_action, dict) \
                else i.hit_execute_action
            v = value_service.transfer_data_fmt(define, value)
            item[parameter_code] = v
            if isinstance(v, dict):
                item.update(v)
            data.append(item)
        if enable_flag is False:
            data[first_disable_index - 1]['down_move'] = 0
        res = PageResultInfo(
            page_no=pagination.page_no,
            page_size=pagination.page_size,
            total_rows=rules.total_rows,
            total_pages=total_pages,
            items=data,
        )
        return res.dict()

    def save_template_rule_parameter(self, table_id: int, parameter_code: str, values: Dict, belong_org_sk: int):
        """
        保存规则参数
        :param table_id:
        :param parameter_code:
        :param values:
        :param belong_org_sk:
        :return:
        """
        # 1.参数校验
        define = self.dao.find_parameter_define_detail_by_code(parameter_code)
        if define.parameter_type == ParameterDefineType.table:
            res = value_service.save_template_table_parameter(table_id, define, values)
            return res

        if define.parameter_type == ParameterDefineType.level_table.value:
            res = value_service.upsert_template_value_table_parameter_item(parameter_code, table_id,
                                                                           belong_org_sk, define, values)
            return res
        value_service.validate_parameter_value(parameter_code, values, table_id=table_id)
        # 2.查询参数对应的具体值
        parameter_table_value = typedef.QueryBaseParameterTableValue(
            parameter_code=parameter_code, parameter_table_id=table_id, biz_type="template"
        )
        parameter_table_value_result = self.dao.find_parameter_define_value_by_params(parameter_table_value)
        if not parameter_table_value_result:
            # 保存参数值表
            value_instance = typedef.ParameterValueInstance(
                parameter_code=parameter_code,
                parameter_table_id=table_id,
                parameter_value=json.dumps({}, ensure_ascii=False),
                modifier_id=current_user.user_id,
                creator_id=current_user.user_id
            )
            value_id = self.dao.insert_base_parameter_value(value_instance)

            # 保存规则实例集
            rule_define_id = define.rule_define_id
            rule_define = rule_definition_service.find_rule_definition_by_id(rule_define_id)
            rule_definition_code = rule_define.code
            save_rule_set = SaveRuleInstanceSetRequest(
                source_object_id=value_id,
                source_object_type="parameter_config",
                rule_definition_code=rule_definition_code,
            )
            ins_set_id = rule_instance_set_service.save_rule_instance_set(save_rule_set).get(
                "rule_instance_set_id"
            )
            update_parameter_value_list = [
                typedef.ParameterValueInstance(
                    id=value_id,
                    parameter_code=define.parameter_code,
                    parameter_value=json.dumps({
                        "code": ins_set_id,
                        "parameter_value": define.parameter_value.get("parameter_value"),
                        parameter_code: define.parameter_value.get(parameter_code),
                        "parameter_value_type": define.parameter_value.get("parameter_value_type")
                    }),
                    modifier_id=current_user.user_id,
                )
            ]
            # d.更新参数值表对应的规则id
            self.dao.batch_update_parameter_value(update_parameter_value_list)
        else:
            ins_set_id = parameter_table_value_result.parameter_value.get("code")
        # 保存至 规则实例
        remark = values.get("remark")
        # 根据参数定义 查询规则定义
        priority = rule_instance_service.get_priority_min_value(ins_set_id) - 1
        condition_set = values.get('condition_set')

        if define.parameter_value.get('parameter_value_type') in (ParameterTypeEnum.bool.value,
                                                                  ParameterTypeEnum.boolean.value):
            if values.get(parameter_code) is not None:
                values[parameter_code] = True if values.get(parameter_code) else False

        parameter_value = value_service.transfer_data_for_save(define, values)
        hit_action = {"action": "assignment", "action_value": parameter_value}
        action_sql = ""
        satisfy_condition = values.get('satisfy_condition')
        rule_ins = RuleInstanceRequest(
            belong_rule_instance_set_id=ins_set_id,
            priority=priority,
            description=remark,
            hit_condition=condition_set,
            hit_execute_action=hit_action,
            hit_condition_sql=action_sql,
            satisfy_condition=satisfy_condition
        )
        res = rule_instance_service.save_rule_instance(rule_ins.convert(), org_sk=belong_org_sk)
        return res

    def update_template_rule_parameter(self, parameter_code: str, values: Dict, belong_org_sk: int,
                                       table_id: int):
        """
        编辑规则参数
        :param parameter_code:
        :param values:
        :param belong_org_sk:
        :param table_id:
        :return:
        """
        # 1.参数校验
        define = self.dao.find_parameter_define_detail_by_code(parameter_code)
        value_service.validate_parameter_value(parameter_code, values, table_id=table_id)

        rule_instance_id = values.get("id")
        ori_rule_ins = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
        if define.parameter_value.get("parameter_value_type") in (
                ParameterTypeEnum.bool.value,
                ParameterTypeEnum.boolean.value,
        ):
            if values.get(parameter_code) is not None:
                values[parameter_code] = True if values.get(parameter_code) else False
        parameter_value = value_service.transfer_data_for_save(define, values)
        hit_action = {"action": "assignment", "action_value": parameter_value}
        satisfy_condition = values.get('satisfy_condition')
        rule_ins = RuleInstanceRequest(**ori_rule_ins.dict()).convert()
        rule_ins.hit_condition = values.get("condition_set")
        rule_ins.description = values.get("remark")
        rule_ins.hit_execute_action = hit_action
        rule_ins.satisfy_condition = satisfy_condition

        rule_instance_service.update_rule_instance(rule_ins.convert(), belong_org_sk)
        return rule_instance_id

    def template_parameter_table(self, org_sk, biz_id, biz_type):
        """
        新增模板-参数表
        :param org_sk:
        :param biz_id:
        :param biz_type:
        :return:
        """
        user_id = current_user.user_id
        table_id = self.dao.save_base_parameter_table(
            typedef.BaseParameterTable(
                org_sk=org_sk,
                biz_type=biz_type,
                biz_id=biz_id,
                creator_id=user_id,
                modifier_id=user_id,
            )
        )
        return table_id

    def copy_parameter_by_table_id(self, biz_id, source_table_id, target_table_id):
        """
        复制参数
        :param biz_id:
        :param source_table_id:
        :param target_table_id:
        :return:
        """
        logger.info("copy_parameter_by_table_id 复制参数开始。。。。")
        user_id = current_user.user_id
        # 1.新增参数表id
        t = threading.Thread(target=context_wrapper(self.async_copy_table_parameter_value_list),
                             args=(biz_id, source_table_id, target_table_id, user_id,))
        t.start()
        logger.info("copy_parameter_by_table_id 复制参数结束。。。。")

    def async_copy_table_parameter_value_list(self, biz_id, source_table_id, target_table_id, user_id):
        """
        批量复制绑定模板参数
        :param biz_id:
        :param source_table_id:
        :param target_table_id:
        :param user_id:
        :return:
        """
        logger.info("async_copy_table_parameter_value_list start ...")
        #  1.查询参数视图关联的参数
        parameter_define_value_list = self.dao.find_parameter_define_value_by_table_id(source_table_id) or []
        parameter_value_list = []
        rule_instance_value_list_dict = {}
        level_table_value_list_dict = {}
        rule_parameter_code_list = []
        for parameter_define_value in parameter_define_value_list:
            parameter_code = parameter_define_value.parameter_code
            parameter_value = parameter_define_value.parameter_value
            if parameter_define_value.mode == ParameterDefineMode.RULE.value:
                rule_parameter_code_list.append(parameter_code)
                rule_instance_value_list_dict[parameter_code] = parameter_value
                parameter_value = {}
            if parameter_define_value.parameter_type == ParameterDefineType.level_table.value:
                level_table_value_list_dict[parameter_code] = parameter_value
            parameter_value_list.append(
                typedef.ParameterValueInstance(
                    parameter_code=parameter_code,
                    parameter_value=json.dumps(parameter_value),
                    creator_id=user_id,
                    modifier_id=user_id,
                    org_sk=parameter_define_value.org_sk,
                )
            )
        # 2.批量新增参数
        if parameter_define_value_list:
            self.save_table_parameter_value_list(target_table_id, biz_id, parameter_value_list,
                                                 rule_instance_value_list_dict, level_table_value_list_dict,
                                                 user_id)
        else:
            syn_status = 1
            self.dao.update_biz_template_parameter_syn_status_by_id(syn_status, biz_id)
        logger.info("async_copy_table_parameter_value_list end ...")

    def batch_add_parameter(self, org_sk, biz_id, table_id, biz_action_type_code):
        """
        业务动作模板->批量新增参数表关联的参数
        :param org_sk: 组织id
        :param biz_id: 业务id
        :param table_id: 业务类型
        :param biz_action_type_code: 业务动作类型
        """
        logger.info("batch_add_parameter 批量绑定参数开始。。。。")
        user_id = current_user.user_id
        t = threading.Thread(target=context_wrapper(self.async_batch_table_parameter_value_list),
                             args=(org_sk, table_id, biz_id, biz_action_type_code, user_id,))
        t.start()
        logger.info("batch_add_parameter 批量绑定参数结束。。。。")

    def async_batch_table_parameter_value_list(self, org_sk, table_id, biz_id, biz_action_type_code, user_id):
        """
        批量绑定模板参数
        :param org_sk:
        :param table_id:
        :param biz_id:
        :param biz_action_type_code:
        :param user_id:
        :return:
        """
        logger.info("async_batch_table_parameter_value_list start ...")
        #  1.查询参数视图关联的参数
        parameter_define_list = self.dao.find_parameter_list_by_biz_action_type_code(biz_action_type_code)
        parameter_value_list, rule_instance_value_list_dict, level_table_value_list_dict = \
            self.__parse_parameter_value_list(org_sk, parameter_define_list, user_id)
        if parameter_value_list:
            # 4.批量新增参数
            self.save_table_parameter_value_list(table_id, biz_id, parameter_value_list, rule_instance_value_list_dict,
                                                 level_table_value_list_dict, user_id)
        else:
            syn_status = 1
            self.dao.update_biz_template_parameter_syn_status_by_id(syn_status, biz_id)
        logger.info("async_batch_table_parameter_value_list end  ...")

    def save_table_parameter_value_list(self, table_id, biz_id, parameter_value_list, rule_instance_value_list_dict,
                                        level_table_value_list_dict, user_id):
        """
        异步绑定参数
        :param user_id
        :param biz_id
        :param table_id:
        :param parameter_value_list:
        :param rule_instance_value_list_dict:
        :return:
        """
        # a.新增参数值
        save_parameter_value_list = self.dao.batch_save_base_parameter_value(table_id, parameter_value_list) or []

        # level_table参数需要用到org_sk
        parent_org_sk_map = {pv.parameter_code: pv.org_sk for pv in parameter_value_list}

        # b.复制规则实例集、复制规则实例
        update_parameter_value_list = []
        for save_parameter_value in save_parameter_value_list:
            rule_parameter_code = save_parameter_value.parameter_code
            rule_parameter_code_value = rule_instance_value_list_dict.get(rule_parameter_code)
            level_table_parameter_code_value = level_table_value_list_dict.get(rule_parameter_code)
            rule_ins_set_id = None
            if rule_parameter_code_value:
                rule_ins_set_id = rule_parameter_code_value.get("code", None)
            rule_define = rule_definition_service.find_rule_definition_by_id(save_parameter_value.rule_define_id)
            source_object_type = "parameter_config"
            rule_definition_code = rule_define.code
            if rule_ins_set_id:
                copy_info = CopyRuleInstanceRequest(
                    rule_instance_set_id=rule_ins_set_id,
                    source_object_id=save_parameter_value.id,  # 来源对象ID
                    source_object_type=source_object_type,  # 来源对象类型
                    rule_definition_code=rule_definition_code,  # 规则定义编码
                    creator_id=user_id,  # 操作人id
                )
                new_rule_instance_set_id = rule_instance_set_service.copy_rule_instance_set(copy_info)
            elif level_table_parameter_code_value:
                # 复制二级参数
                param_code = save_parameter_value.parameter_code
                parent_org_sk = parent_org_sk_map.get(param_code)
                value_service.template_parameter_inherited(parent_org_sk, save_parameter_value, user_id)
                continue
            else:
                save_rule_set = SaveRuleInstanceSetRequest(
                    source_object_id=save_parameter_value.id,
                    source_object_type=source_object_type,
                    rule_definition_code=rule_definition_code,
                    creator_id=user_id,  # 操作人id
                )
                new_rule_instance_set_id = rule_instance_set_service.save_rule_instance_set(save_rule_set).get(
                    "rule_instance_set_id"
                )
            update_parameter_value_list.append(
                typedef.ParameterValueInstance(
                    id=save_parameter_value.id,
                    parameter_code=rule_parameter_code,
                    parameter_value=
                    json.dumps({"code": new_rule_instance_set_id, PARAMETER_CONFIG_KEY:
                        save_parameter_value.parameter_value.get(PARAMETER_CONFIG_KEY)}, ensure_ascii=False),
                    modifier_id=user_id,
                )
            )
        # c.更新参数值表对应的规则id
        if update_parameter_value_list:
            self.dao.batch_update_parameter_value(update_parameter_value_list)
        # d.更新模板参数同步状态
        syn_status = 1
        self.dao.update_biz_template_parameter_syn_status_by_id(syn_status, biz_id)

    def __parse_parameter_value_list(self, org_sk, parameter_define_list, user_id):
        """
        :param parameter_define_list:
        :return:
        """
        parameter_value_list = []
        rule_instance_value_list_dict = {}
        level_table_value_list_dict = {}
        if parameter_define_list:
            for parameter in parameter_define_list:
                parameter_code = parameter.parameter_code
                parameter_value, copy_org_sk = self.__inner_get_parameter_value(org_sk, parameter_code)
                if parameter_value:
                    if parameter.mode == ParameterDefineMode.RULE.value:
                        rule_instance_value_list_dict[parameter_code] = parameter_value
                    elif parameter.parameter_type == ParameterDefineType.level_table.value:
                        level_table_value_list_dict[parameter_code] = parameter_value

                    parameter_value_list.append(
                        typedef.ParameterValueInstance(
                            parameter_code=parameter.parameter_code,
                            parameter_value=json.dumps(parameter_value, ensure_ascii=False),
                            creator_id=user_id,
                            modifier_id=user_id,
                            org_sk=copy_org_sk,
                        )
                    )
        return parameter_value_list, rule_instance_value_list_dict, level_table_value_list_dict

    def __inner_get_parameter_value(self, org_sk, parameter_code: str):
        """
            根据层级查询参数值
        Args:
            parameter_code
            org_sk
        Return:
            List[ParameterView]
        """
        way = RegionQueryWayEnum.up
        # warning 当前参数值设置只能到 区域，设置到门店级不支持。
        org_flags = [OrgFlagEnum.unit]
        org_sk_list = auth_region_service.get_inherit_org_by_sk(org_sk, way, org_flags)
        for sk in org_sk_list:
            value = self.dao.get_parameter_value_join_table_by_org_sk(sk, parameter_code)
            if value:
                return value.parameter_value, sk
        return {}, None

    def batch_remove_parameter(self, parameter_table_id, modifier_id):
        """
        业务动作模板->批量删除参数表关联的参数
        :param parameter_table_id: 参数表id
        :param modifier_id: 操作人
        :return:
        """
        parameter_value_ids = self.dao.find_rule_parameter_value_ids_by_table_id(parameter_table_id)
        # 数据入库
        with transaction_manager():
            # a.逻辑删除参数表
            self.dao.remove_parameter_table_by_id(parameter_table_id, modifier_id)
            # b.逻辑删除参数值
            self.dao.remove_parameter_value_by_table_id(parameter_table_id, modifier_id)
            # c.逻辑删除规则实例集
            if parameter_value_ids:
                source_object_type = "parameter_config"
                remove_rule_set = RemoveRuleInstanceSetBySourceObjectRequest(
                    source_object_type=source_object_type,
                    source_object_id_list=list(map(str, parameter_value_ids)),
                    modifier_id=modifier_id,
                )
                rule_instance_set_service.remove_rule_instance_set_by_source_object_info(remove_rule_set)

    def find_parameter_by_view(self, biz_action_type_code):
        """业务动作模板->通过参数视图和参数表id获取参数"""
        parameter_define_list = self.dao.find_parameter_list_by_biz_action_type_code(biz_action_type_code)
        parameter_code_collection = self.dao.find_collection_name_and_parameter_code()
        parameter_code_collection_dict = {i.parameter_code: i.collection_name for i in parameter_code_collection}
        res = []
        if parameter_define_list is None:
            return res
        for i in parameter_define_list:
            is_editable = 1
            define = typedef.ArgsDefineFront(
                parameter_code=i.parameter_code,
                parameter_name=i.parameter_name,
                parameter_mode=i.mode,
                parameter_type=i.parameter_type,  # INPUT/VALUE_LIST/JSON
                parameter_selector=i.parameter_value.get("parameter_selector"),
                custom_parameter_type=i.parameter_value.get('custom_parameter_type'),
                selector_api_properties=i.parameter_value.get('selector_api_properties'),
                selector_api_code=i.parameter_value.get('selector_api_code'),
                is_editable=is_editable,
                bind_title_key=i.parameter_value.get("bind_title_key"),
            )
            define.biz_object_code = ""
            if i.mode == ParameterDefineMode.RULE.value:
                rule_definition = rule_definition_service.find_rule_definition_by_id(i.rule_define_id)
                define.biz_object_code = rule_definition.rule_input_definition[0].get("applicable_biz_object_code")
                define.table_header = i.parameter_value.get("table_header")
                define.form_layout = i.parameter_value.get("form_layout")
            if i.parameter_type == ParameterDefineType.table:
                define.table_header = i.parameter_value.get('table_header')
                define.form_layout = i.parameter_value.get("form_layout")
                define.table_header_opera = i.parameter_value.get("table_header_opera")
            elif i.parameter_type == ParameterDefineType.json.value:
                # /text /number /date /percent /checkbox /radio
                define.parameter_define_list = []
                for item in i.parameter_value.get("parameter_define_list") or []:
                    sub_define = typedef.ArgsDefineFront(
                        parameter_code=item.get("parameter_code"),
                        parameter_name=item.get("parameter_name"),
                        parameter_mode=item.get("parameter_mode"),
                        parameter_type=item.get("parameter_type"),  # INPUT/VALUE_LIST/JSON
                        parameter_selector=item.get("parameter_selector"),
                        is_editable=is_editable,
                    )
                    sub_define.value_type = type_transfer_for_front(item.get("parameter_value_type"))
                    sub_define.is_enable = True
                    define.parameter_define_list.append(sub_define)
            elif i.parameter_type == ParameterDefineType.level_table.value:
                rule_definition = rule_definition_service.find_rule_definition_by_id(i.rule_define_id)
                define.biz_object_code = rule_definition.rule_input_definition[0].get("applicable_biz_object_code")
                define.table_header = i.parameter_value.get('table_header')
                define.form_layout = i.parameter_value.get("form_layout")
                define.table_header_opera = i.parameter_value.get("table_header_opera")

            define.value_type = type_transfer_for_front(i.parameter_value.get("parameter_value_type"))
            define.is_enable = True
            define_dict = define.dict()
            define_dict["collection_name"] = parameter_code_collection_dict.get(i.parameter_code)
            res.append(define_dict)
        return res

    def find_parameter_group_by_view(self, template_info):
        """
        业务动作模板->通过参数视图查询参数集分组
        """
        parameter_group = self.dao.query_parameter_group_by_action_code(template_info.biz_action_type_code
                                                                        , template_info.id)
        data = []
        rule_ids = list(set([i.rule_define_id for i in parameter_group if i.rule_define_id]))
        rule_definitions = rule_definition_service.find_rule_definition_by_ids(rule_ids)
        rule_definition_map = list_to_map(rule_definitions)
        for key, items in groupby(parameter_group, lambda x: (x.category_code, x.category_name)):
            category_code, category_name = key
            group = GroupVO(category_code=category_code, category_name=category_name)
            for collection_key, col_items in groupby(items, lambda c: (c.collection_code, c.collection_name)):
                col_items = list(col_items)
                collection_code, collection_name = collection_key
                collection = CollectionVO(collection_code=collection_code, collection_name=collection_name)
                collection.is_configured = 1 if all([i.is_configured for i in col_items]) else 0
                collection.is_required = 1 if any([i.is_required for i in col_items]) else 0
                parameter_define_list = self.trans_parameter_define_template_vo(col_items, rule_definition_map)
                collection.parameters = parameter_define_list
                filter_title = '|'.join([i.get('parameter_name') for i in parameter_define_list])
                collection.filter_title = filter_title
                group.collections.append(collection)
            data.append(group)
        return data

    @staticmethod
    def trans_parameter_define_template_vo(lst: List[typedef.GroupParameter], rule_definition_map: Dict):
        res = []
        if not lst:
            return res
        is_editable = 1
        for i in lst:
            define = typedef.ArgsDefineFront(
                parameter_code=i.parameter_code,
                parameter_name=i.parameter_name,
                parameter_mode=i.mode,
                parameter_type=i.parameter_type,  # INPUT/VALUE_LIST/JSON
                parameter_selector=i.parameter_value.get("parameter_selector"),
                custom_parameter_type=i.parameter_value.get('custom_parameter_type'),
                selector_api_properties=i.parameter_value.get('selector_api_properties'),
                selector_api_code=i.parameter_value.get('selector_api_code'),
                is_editable=is_editable,
                bind_title_key=i.parameter_value.get("bind_title_key"),
                buttons=i.parameter_value.get('buttons'),
                table_header_opera=i.parameter_value.get('table_header_opera')
            )
            define.biz_object_code = ""
            if i.mode == ParameterDefineMode.RULE.value:
                rule_definition = rule_definition_map.get(i.rule_define_id)
                if not rule_definition:
                    raise ParameterException(f'{i.rule_define_id} not exists')
                define.biz_object_code = rule_definition.rule_input_definition[0].get("applicable_biz_object_code")
                define.table_header = i.parameter_value.get("table_header")
                define.form_layout = i.parameter_value.get("form_layout")
            if i.parameter_type == ParameterDefineType.table:
                define.table_header = i.parameter_value.get('table_header')
                define.form_layout = i.parameter_value.get("form_layout")
                define.table_header_opera = i.parameter_value.get("table_header_opera")
            elif i.parameter_type == ParameterDefineType.json.value:
                # /text /number /date /percent /checkbox /radio
                define.parameter_define_list = []
                for item in i.parameter_value.get("parameter_define_list") or []:
                    sub_define = typedef.ArgsDefineFront(
                        parameter_code=item.get("parameter_code"),
                        parameter_name=item.get("parameter_name"),
                        parameter_mode=item.get("parameter_mode"),
                        parameter_type=item.get("parameter_type"),  # INPUT/VALUE_LIST/JSON
                        parameter_selector=item.get("parameter_selector"),
                        is_editable=is_editable,
                    )
                    sub_define.value_type = type_transfer_for_front(item.get("parameter_value_type"))
                    sub_define.is_enable = True
                    define.parameter_define_list.append(sub_define)
            elif i.parameter_type == ParameterDefineType.level_table.value:
                rule_definition = rule_definition_map.get(i.rule_define_id)
                if not rule_definition:
                    raise ParameterException(f'{i.rule_define_id} not exists')
                define.biz_object_code = rule_definition.rule_input_definition[0].get("applicable_biz_object_code")
                define.table_header = i.parameter_value.get('table_header')
                define.form_layout = i.parameter_value.get("form_layout")
                define.table_header_opera = i.parameter_value.get("table_header_opera")

            define.value_type = type_transfer_for_front(i.parameter_value.get("parameter_value_type"))
            define.is_enable = True
            define_dict = define.dict()
            res.append(define_dict)
        return res

    @deprecated(version='5.8.1', reason="This function will be removed soon")
    def get_parameter_value_by_code_and_template_id(self,
                                                    sk: Union[int, Tuple],
                                                    sk_type: str,
                                                    parameter_code: str,
                                                    biz_action_template_id):
        """

        :param sk:
        :param sk_type:
        :param parameter_code:
        :param biz_action_template_id:
        :return:
        """
        is_template_parameter = self.dao.is_template_parameter(parameter_code, biz_action_template_id)
        if is_template_parameter:
            value = self.dao.query_parameter_table_info(parameter_code, biz_action_template_id)
            if value:
                return parse_parameter_value_by_type(value)
            return self.common_parameter_service.get_default_value_by_code(parameter_code)
        return self.common_parameter_service.get_parameter_value_by_code(sk, sk_type, parameter_code)

    def template_get_parameter_value_by_code(self, parameter_table_id, org_sk, parameter_code):
        """
        获取全局参数值
        :param parameter_table_id:
        :param org_sk:
        :param parameter_code:
        :return:
        """
        # 1.查询参数是否关联了参数表
        # 1.查询参数对应的具体值
        parameter_table_value = typedef.QueryBaseParameterTableValue(
            parameter_code=parameter_code, parameter_table_id=parameter_table_id, biz_type="template"
        )
        parameter_table_value_result = self.dao.find_parameter_define_value_by_params(parameter_table_value)
        parameter_define = self.dao.find_parameter_define_detail_by_code(parameter_code)
        if not parameter_table_value_result:
            parameter_value, copy_org_sk = self.__inner_get_parameter_value(org_sk, parameter_code)
            if not bool(parameter_value):
                parameter_value.update({
                    "parameter_value": parameter_define.parameter_value.get("parameter_value"),
                    parameter_code: parameter_define.parameter_value.get(parameter_code),
                    "parameter_value_type": parameter_define.parameter_value.get("parameter_value_type")
                })
        else:
            parameter_value = parameter_table_value_result.parameter_value
        parameter_value = dict(parameter_value)
        res = value_service.parameter_value_fmt(parameter_define, parameter_value)
        return res

    def edit_template_value_parameter(self, parameter_table_id: int, parameter_code: str, values: Dict):
        """
        模板编辑参数
        :param parameter_table_id:
        :param parameter_code:
        :param values:
        :return:
        """
        define = self.dao.get_parameter_define_by_code(parameter_code)
        value_service.validate_parameter_value(parameter_code, values)
        parameter_value = value_service.structure_method_strategy(define, values)
        exist_parameter_value = self.dao.get_parameter_value_join_table_by_id_parameter(
            parameter_table_id, parameter_code)
        value_instance = typedef.ParameterValueInstance(
            parameter_code=parameter_code,
            parameter_table_id=parameter_table_id,
            parameter_value=json.dumps(parameter_value, ensure_ascii=False),
            modifier_id=current_user.user_id,
            creator_id=current_user.user_id
        )
        if not exist_parameter_value:
            self.dao.insert_base_parameter_value(value_instance)
        else:
            self.dao.update_parameter_value_by_table_id_code(value_instance)
        return {"parameter_code": parameter_code}

    def get_inherit_org_by_sk(self, org_sk_list: Union[List[int], int],
                              way_enum: Union[RegionQueryWayEnum, None],
                              org_flags: Union[List[OrgFlagEnum], OrgFlagEnum, None],
                              is_only_parent: bool = False,
                              operating_unit_sk=None) -> List[int]:
        """

        Args:
            org_sk_list: 兼容单个 org_sk 不推荐
            way_enum: 查询 方向
            is_only_parent: 是否只查询 父级; 如果 is_only_parent = True 只有参数 org_sk_list 起作用
            org_flags: List[OrgFlagEnum] 要查询的组织单元类型
            operating_unit_sk: 区域树结构根节点类型
        Return:
            List[int] : list[org_sk]
        """
        # 兼容单个 org_sk
        if isinstance(org_sk_list, int):
            org_sk_list = [org_sk_list]
        if is_only_parent:
            res = self.dao.parameter_list_parent_sk(org_sk_list)
            return res

        if not isinstance(way_enum, RegionQueryWayEnum):
            raise ParameterException(msg="层级查询方向参数： way_enum must be RegionQueryWayEnum")
        if isinstance(org_flags, OrgFlagEnum):
            org_flags = [org_flags]

        org_flags = [i.value if isinstance(i, OrgFlagEnum) else OrgFlagEnum.get(i) for i in org_flags]

        if way_enum == RegionQueryWayEnum.all:
            res_up = self.dao.parameter_list_inherit_region_sk(org_sk_list, org_flags, operating_unit_sk,
                                                               way=RegionQueryWayEnum.up.value)
            res_down = self.dao.parameter_list_inherit_region_sk(org_sk_list, org_flags, operating_unit_sk,
                                                                 way=RegionQueryWayEnum.down.value)
            if res_up is None:
                res_up = []
            if res_down is None:
                res_down = []
            res = list(set(res_up + res_down))
            return res
        res = self.dao.parameter_list_inherit_region_sk(org_sk_list, org_flags, operating_unit_sk, way=way_enum.value)
        return res

    def enable_level_table_parameter_value(self, parameter_define, instance_id: int, org_sk: int) -> int:
        """禁用启用值类型 LEVEL_TABLE 参数行数据
        """
        parameter_code = parameter_define.parameter_code
        way = RegionQueryWayEnum.up
        org_flags = [OrgFlagEnum.unit]
        org_sk_list = auth_region_service.get_inherit_org_by_sk(org_sk, way, org_flags)

        for sk in org_sk_list:
            value = self.dao.get_parameter_value_join_table_by_org_sk(sk, parameter_code)
            if value:
                value_instance, index = self.get_exists_instance_parameter_value(parameter_code, instance_id, sk)
                p_value = value_instance.parameter_value.get('parameter_value')
                need_validate = not p_value[index].get('is_enable')
                if need_validate:
                    param = TableParameterValueDTO(org_sk=org_sk, values=p_value[index], define=parameter_define)
                    value_service.validate_level_table_parameter_value(param)
                break

        item_id = value_service.deal_with_parameter_inherited(parameter_code, org_sk, {"id": instance_id})
        value_instance, index = self.get_exists_instance_parameter_value(parameter_code, item_id, org_sk)
        p_value = value_instance.parameter_value.get('parameter_value')

        if not p_value[index].get('is_enable'):
            param = TableParameterValueDTO(org_sk=org_sk, values=p_value[index], define=parameter_define)
            value_service.validate_level_table_parameter_value(param)

        p_value = reverse_is_enable(p_value, index)
        update_value = {"value_id": value_instance.parameter_value_id,
                        "parameter_code": value_instance.parameter_code,
                        "parameter_value": json.dumps({"parameter_value": p_value, parameter_code: p_value}),
                        "modifier_id": current_user.user_id}
        self.dao.update_parameter_value(**update_value)
        return instance_id

    def get_exists_instance_by_table_id(self, parameter_code: str, instance_id: int, table_id: int):
        """"""
        parameter_table_value = typedef.QueryBaseParameterTableValue(
            parameter_code=parameter_code, parameter_table_id=table_id, biz_type="template"
        )
        value_instance = self.dao.find_parameter_define_value_by_params(parameter_table_value)
        p_value = value_instance.parameter_value.get('parameter_value')
        for idx, i in enumerate(p_value):
            if i.get('id') == instance_id:
                index = idx
                break
        else:
            raise ParameterException(msg='参数错误: {} 不存在'.format(instance_id))
        return value_instance, index

    def get_exists_instance_parameter_value(self, parameter_code: str, instance_id: int, org_sk: int):
        """"""
        value_instance = self.dao.get_parameter_value_join_table_by_org_sk(org_sk, parameter_code)
        p_value = value_instance.parameter_value.get('parameter_value')
        for idx, i in enumerate(p_value):
            if i.get('id') == instance_id:
                index = idx
                break
        else:
            raise ParameterException(msg='参数错误: {} 不存在'.format(instance_id))
        return value_instance, index

    def enable_table_parameter_value(self, define, instance_id: int, org_sk: int) -> int:
        """禁用启用值类型TABLE参数行数据
        """
        parameter_code = define.parameter_code
        value_instance, index = self.get_exists_instance_parameter_value(parameter_code, instance_id, org_sk)
        p_value = value_instance.parameter_value.get('parameter_value')
        if not p_value[index].get('is_enable'):
            param = TableParameterValueDTO(org_sk=org_sk, values=p_value[index], define=define)
            value_service.validate_level_table_parameter_value(param)

        p_value = reverse_is_enable(p_value, index)
        update_value = {"value_id": value_instance.parameter_value_id,
                        "parameter_code": value_instance.parameter_code,
                        "parameter_value": json.dumps({
                            "parameter_value": p_value, parameter_code: p_value
                        }),
                        "modifier_id": current_user.user_id}
        self.dao.update_parameter_value(**update_value)
        return instance_id

    def enable_sys_ra_instruct_exception_parameter_value(self, parameter_code: str, instance_id: int, org_sk: int
                                                         ) -> int:
        """禁用启用值sys_ra_instruct_exception参数行数据
        """
        value_instance, index = self.get_exists_instance_parameter_value(parameter_code, instance_id, org_sk)
        p_value = value_instance.parameter_value.get('parameter_value')

        exception_code = p_value[index].get('exception_code')
        exception = self.dao.get_exception_algorithm(exception_code=exception_code).first()
        if not exception:
            raise ParameterException(msg='不存在异常类型代码')
        exception_name = p_value[index].get('exception_name')
        if not p_value[index].get('is_enable'):
            syz_ra_exception_validator(p_value, exception, exception_name)
        p_value = reverse_is_enable(p_value, index)

        update_value = {"value_id": value_instance.parameter_value_id,
                        "parameter_code": value_instance.parameter_code,
                        "parameter_value": json.dumps({"parameter_value": p_value, parameter_code: p_value}),
                        "modifier_id": current_user.user_id}
        self.dao.update_parameter_value(**update_value)
        return instance_id

    def delete_level_table_parameter_value(self, define, instance_id: int, org_sk: int) -> int:
        """删除 LEVEL_TABLE参数行数据
        """
        parameter_code = define.parameter_code
        way = RegionQueryWayEnum.up
        org_flags = [OrgFlagEnum.unit]
        org_sk_list = auth_region_service.get_inherit_org_by_sk(org_sk, way, org_flags)
        for sk in org_sk_list:
            value_id = instance_id
            if value_id:
                value_service.validate_has_sub_value(value_id, parameter_code, sk, error_type='delete')
        item_id = value_service.deal_with_parameter_inherited(parameter_code, org_sk, {'id': instance_id})
        sub_ins = ParameterSubValueInstance(value_id=item_id,
                                            parameter_code=parameter_code,
                                            org_sk=org_sk)
        sub_value_count = self.sub_value_dao.count_base_parameter_sub_value(sub_ins)
        if sub_value_count > 0:
            raise ParameterException(msg=f"请删除详细配置后再删除")

        return self.delete_table_parameter_value(parameter_code, instance_id, org_sk)

    def delete_tpl_table_parameter_value(self, parameter_code: str, instance_id: int, table_id: int) -> int:
        """删除 值类型TABLE参数行数据
        """
        value_instance, index = self.get_exists_instance_by_table_id(parameter_code, instance_id, table_id)
        p_value = value_instance.parameter_value.get('parameter_value')
        deleted = p_value.pop(index)
        if not deleted.get('is_enable'):
            raise ParameterException(msg=f"数据已禁用，不能删除")
        if len(p_value) == 0:
            with transaction_manager():
                self.dao.remove_parameter_value_by_id(value_instance.id, current_user.user_id)
        else:
            update_value = {"value_id": value_instance.id,
                            "parameter_code": value_instance.parameter_code,
                            "parameter_value": json.dumps({"parameter_value": p_value, parameter_code: p_value}),
                            "modifier_id": current_user.user_id}
            self.dao.update_parameter_value(**update_value)
        return instance_id

    def delete_table_parameter_value(self, parameter_code: str, instance_id: int, org_sk: int) -> int:
        """删除 值类型TABLE参数行数据
        """
        try:
            value_instance, index = self.get_exists_instance_parameter_value(parameter_code, instance_id, org_sk)
            p_value = value_instance.parameter_value.get('parameter_value')
            p_value.pop(index)
            if len(p_value) == 0:
                with transaction_manager():
                    self.dao.remove_parameter_value_by_table_id(value_instance.id, current_user.user_id)
                    self.dao.remove_parameter_table_by_id(value_instance.id, current_user.user_id)
            else:
                update_value = {"value_id": value_instance.parameter_value_id,
                                "parameter_code": value_instance.parameter_code,
                                "parameter_value": json.dumps({"parameter_value": p_value, parameter_code: p_value}),
                                "modifier_id": current_user.user_id}
                self.dao.update_parameter_value(**update_value)
        except Exception as e:
            logger.error(msg=f'{e}')
            logger.error(msg="删除失败")
        return instance_id

    def update_tpl_parameter_level_value_priority(self, parameter_code: str, instance_id: int,
                                                  way_enum: PriorityWayEnum, table_id: int) -> int:
        """更新值类型LEVEL_TABLE参数优先级
        """
        try:
            value_instance, index = self.get_exists_instance_by_table_id(parameter_code, instance_id, table_id)
            p_value = value_instance.parameter_value.get('parameter_value')
            move = index - 1 if way_enum == PriorityWayEnum.UP else index + 1
            p_value[index]['priority'], p_value[move]['priority'] = \
                p_value[move]['priority'], p_value[index]['priority']
            p_value = sorted(p_value, key=itemgetter('priority'))
            for idx, i in enumerate(p_value):
                i['priority'] = idx + 1
            update_value = {"value_id": value_instance.id,
                            "parameter_value": json.dumps({"parameter_value": p_value, parameter_code: p_value}),
                            "parameter_code": value_instance.parameter_code,
                            "modifier_id": current_user.user_id}
            self.dao.update_parameter_value(**update_value)
        except Exception as e:
            logger.error(msg=f'{e}')
            logger.error(msg="更新优先级失败")
        return instance_id

    def update_parameter_level_value_priority(self, parameter_code: str, instance_id: int,
                                              way_enum: PriorityWayEnum, org_sk: int) -> int:
        """更新值类型LEVEL_TABLE参数优先级
        """
        try:
            item_id = value_service.deal_with_parameter_inherited(parameter_code, org_sk, {"id": instance_id})

            value_instance, index = self.get_exists_instance_parameter_value(parameter_code, item_id, org_sk)
            p_value = value_instance.parameter_value.get('parameter_value')
            move = index - 1 if way_enum == PriorityWayEnum.UP else index + 1
            p_value[index]['priority'], p_value[move]['priority'] = \
                p_value[move]['priority'], p_value[index]['priority']
            p_value = sorted(p_value, key=itemgetter('priority'))
            for idx, i in enumerate(p_value):
                i['priority'] = idx + 1
            update_value = {"value_id": value_instance.parameter_value_id,
                            "parameter_value": json.dumps({"parameter_value": p_value, parameter_code: p_value}),
                            "parameter_code": value_instance.parameter_code,
                            "modifier_id": current_user.user_id}
            self.dao.update_parameter_value(**update_value)
        except Exception as e:
            logger.error(msg=f'{e}')
            logger.error(msg="更新优先级失败")
        return instance_id

    def update_parameter_value_priority(self, parameter_code: str, instance_id: int,
                                        way_enum: PriorityWayEnum, org_sk: int) -> int:
        """更新值类型TABLE参数优先级
        """
        try:
            item_id = value_service.deal_with_parameter_inherited(parameter_code, org_sk, {"id": instance_id})
            value_instance, index = self.get_exists_instance_parameter_value(parameter_code, item_id, org_sk)
            p_value = value_instance.parameter_value.get('parameter_value')
            move = index - 1 if way_enum == PriorityWayEnum.UP else index + 1
            p_value[index]['priority'], p_value[move]['priority'] = \
                p_value[move]['priority'], p_value[index]['priority']
            p_value = sorted(p_value, key=itemgetter('priority'))
            for idx, i in enumerate(p_value):
                i['priority'] = idx + 1
            update_value = {"value_id": value_instance.parameter_value_id,
                            "parameter_value": json.dumps({"parameter_value": p_value, parameter_code: p_value}),
                            "parameter_code": value_instance.parameter_code,
                            "modifier_id": current_user.user_id}
            self.dao.update_parameter_value(**update_value)
        except Exception as e:
            logger.error(msg=f'{e}')
            logger.error(msg="更新优先级失败")
        return instance_id

    def update_parameter_rule_priority(self, rule_instance_id: int, way_enum: PriorityWayEnum, org_sk: int) -> int:
        """更新参数规则优先级
        """
        try:
            rule_instance = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
            if rule_instance.is_enable == 0:
                raise ParameterException("数据已禁用，不能更新优先级")
            rule_ins_set_id = rule_instance.belong_rule_instance_set_id
            rule_set = rule_instance_set_service.find_rule_instance_set_by_id(rule_ins_set_id)
            parameter_value = value_service.get_parameter_value_by_id(rule_set.source_object_id)
            rule_instance_id = value_service.deal_inherited(
                parameter_value.parameter_code,
                org_sk,
                {'id': rule_instance_id}
            )
            rule_instance_service.update_rule_instance_priority(rule_instance_id, way_enum.value)
        except Exception as e:
            logger.error(msg=f'{e}')
            logger.error(msg="更新优先级失败")
        return rule_instance_id

    def template_update_tpl_parameter_priority(self, rule_instance_id: int, way_enum: PriorityWayEnum,
                                               parameter_code: str = None, action_template=None) -> int:
        """更新table类型 参数规则优先级
        """
        define = value_service.get_parameter_define_by_code(parameter_code)
        if define and (define.parameter_type == ParameterDefineType.table
                       or define.parameter_type == ParameterDefineType.level_table.value):
            table_id = action_template.parameter_table_id
            res = self.update_tpl_parameter_level_value_priority(parameter_code, rule_instance_id, way_enum, table_id)
            return res
        return self.template_update_parameter_rule_priority(rule_instance_id, way_enum)

    def template_update_parameter_rule_priority(self, rule_instance_id: int, way_enum: PriorityWayEnum) -> int:
        """更新参数规则优先级
        """
        try:
            rule_instance = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
            if rule_instance.is_enable == 0:
                raise ParameterException("数据已禁用，不能更新优先级")
            rule_instance_service.update_rule_instance_priority(rule_instance_id, way_enum.value)
        except Exception as e:
            logger.error(msg=f'{e}')
            logger.error(msg="更新优先级失败")
        return rule_instance_id

    def delete_parameter_rule_by_rule_instance_id(self, rule_instance_id: int, org_sk: int) -> int:
        try:
            rule_instance_req = RemoveRuleInstanceRequest(id=rule_instance_id, modifier_id=current_user.user_id)
            rule_instance = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
            if rule_instance.is_enable == 0:
                raise ParameterException("数据已禁用，不能删除")
            rule_ins_set_id = rule_instance.belong_rule_instance_set_id
            rule_set = rule_instance_set_service.find_rule_instance_set_by_id(rule_ins_set_id)
            parameter_value = value_service.get_parameter_value_by_id(rule_set.source_object_id)
            define = value_service.get_parameter_define_by_code(parameter_value.parameter_code)
            rule_instance_id = value_service.deal_inherited(
                parameter_value.parameter_code,
                org_sk,
                {'id': rule_instance_id}
            )
            if rule_instance_id:
                rule_instance_req.id = rule_instance_id
            # 删除前先查出对应的表
            current_rule_instance = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
            current_rule_set = rule_instance_set_service.find_rule_instance_set_by_id(
                current_rule_instance.belong_rule_instance_set_id)
            value_service.modify_auth_validate(org_sk, define)
            deleted_set_id = rule_instance_service.remove_rule_instance_by_id(rule_instance_req)
            if deleted_set_id:
                current_parameter_value = value_service.get_parameter_value_by_id(current_rule_set.source_object_id)
                current_parameter_table = value_service.get_parameter_table_by_id(
                    current_parameter_value.parameter_table_id)
                with transaction_manager():
                    self.dao.remove_parameter_value_by_table_id(current_parameter_table.id, current_user.user_id)
                    self.dao.remove_parameter_table_by_id(current_parameter_table.id, current_user.user_id)
            else:
                update_value = {"parameter_code": "",
                                "value_id": parameter_value.id,
                                "modifier_id": current_user.user_id}
                self.dao.update_parameter_value(**update_value)

        except Exception as e:
            logger.error(msg="删除规则实例异常")
            raise e
        return rule_instance_id

    def delete_table_parameter_rule(self, rule_instance_id: int, parameter_code,
                                    action_template, is_template: bool = False) -> int:
        """删除规则实例
        """
        define = value_service.get_parameter_define_by_code(parameter_code)
        if define and define.parameter_type == ParameterDefineType.table:
            table_id = action_template.parameter_table_id
            res = self.delete_tpl_table_parameter_value(parameter_code, rule_instance_id, table_id)
            return res
        if define and define.parameter_type == ParameterDefineType.level_table.value:
            # level_table 类型参数
            sub_ins = ParameterSubValueInstance(value_id=rule_instance_id,
                                                parameter_code=parameter_code,
                                                org_sk=action_template.belong_org_sk)
            sub_value_count = self.sub_value_dao.count_base_parameter_sub_value(sub_ins)
            if sub_value_count > 0:
                raise ParameterException(msg=f"请删除详细配置后再删除")
            table_id = action_template.parameter_table_id
            res = self.delete_tpl_table_parameter_value(parameter_code, rule_instance_id, table_id)
            return res

        self.delete_parameter_rule(rule_instance_id, is_template)
        return rule_instance_id

    @staticmethod
    def delete_parameter_rule(rule_instance_id: int, is_template: bool = False) -> int:
        """删除规则实例
        """
        try:

            rule_instance_req = RemoveRuleInstanceRequest(id=rule_instance_id, modifier_id=current_user.user_id)
            rule_instance = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
            if rule_instance.is_enable == 0:
                raise ParameterException("数据已禁用，不能删除")
            rule_instance_service.remove_rule_instance_by_id(rule_instance_req, is_template)

        except Exception as e:
            logger.error(msg="删除规则实例异常")
            raise e
        return rule_instance_id

    def enable_tpl_table_parameter_value(self, define, instance_id: int, table_id: int) -> int:
        """禁用启用值类型模板TABLE参数行数据
        """
        parameter_code = define.parameter_code
        value_instance, index = self.get_exists_instance_by_table_id(parameter_code, instance_id, table_id)
        p_value = value_instance.parameter_value.get('parameter_value')
        if not p_value[index].get('is_enable'):
            param = TableParameterValueDTO(values=p_value[index], define=define, exists=value_instance)
            value_service.validate_level_table_parameter_value(param)

        p_value = reverse_is_enable(p_value, index)
        update_value = {"value_id": value_instance.id,
                        "parameter_code": value_instance.parameter_code,
                        "parameter_value": json.dumps({
                            "parameter_value": p_value, parameter_code: p_value
                        }),
                        "modifier_id": current_user.user_id}
        self.dao.update_parameter_value(**update_value)
        return instance_id

    def enable_table_parameter_rule(self, rule_instance_id, parameter_code, action_template) -> int:
        define = value_service.get_parameter_define_by_code(parameter_code)
        if define and (define.parameter_type == ParameterDefineType.table
                       or define.parameter_type == ParameterDefineType.level_table.value):
            table_id = action_template.parameter_table_id
            res = self.enable_tpl_table_parameter_value(define, rule_instance_id, table_id)
            return res
        res = self.enable_parameter_rule(rule_instance_id)
        return res

    @staticmethod
    def enable_parameter_rule(rule_instance_id: int) -> int:
        """禁用启用
        """
        try:
            rule_instance = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
            # rule.is_enable
            enable = 0 if rule_instance.is_enable else 1
            rule_instance_service.edit_enable_rule_instance(rule_instance.id, enable)
        except Exception as e:
            logger.error(msg=f'{e}')
            logger.error(msg="禁用启用规则实例")
        return rule_instance_id

    @staticmethod
    def enable_parameter_rule_by_org_ins_id(rule_instance_id: int, org_sk: int) -> int:
        """禁用启用
        """
        try:
            rule_instance = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
            rule_ins_set_id = rule_instance.belong_rule_instance_set_id
            rule_set = rule_instance_set_service.find_rule_instance_set_by_id(rule_ins_set_id)
            parameter_value = value_service.get_parameter_value_by_id(rule_set.source_object_id)
            rule_instance_id = value_service.deal_inherited(
                parameter_value.parameter_code,
                org_sk,
                {'id': rule_instance_id}
            )
            # 删除前先查出对应的表
            current_rule_instance = rule_instance_service.find_rule_instance_by_id(rule_instance_id)
            # rule.is_enable
            enable = 0 if rule_instance.is_enable else 1
            rule_instance_service.edit_enable_rule_instance(current_rule_instance.id, enable)
        except Exception as e:
            logger.error(msg=f'{e}')
            logger.error(msg="禁用启用规则实例")
        return rule_instance_id

    def is_in_current_users_favorites(self, collection_code: str) -> int:
        """
        Args:
            collection_code
        Return:
            int: for bool
        """
        user_id = current_user.user_id
        with transaction_manager():
            favorite = self.dao.query_parameter_favorites(user_id)
            if not favorite:
                collection_codes = [
                    'collection_new_store_protect',
                    'collection_new_product',
                    'collection_ra_protect',
                    'collection_single_skc_store_max_pkg_receive',
                    'collection_single_skc_store_max_pkg_send',
                    'collection_single_store_max_pkg_receive',
                    'collection_single_store_max_pkg_send',
                    'collection_store_least_unit',
                    'collection_store_least_mnt',
                    'collection_store_one_city_least_unit',
                    'collection_store_one_city_least_mnt',
                    'collection_store_cross_city_least_unit',
                    'collection_store_cross_city_least_mnt',
                    # 'collection_replenish_standard',
                    'collection_satisfy_week',
                    'collection_demand_rate',
                    'collection_max_sku_stock_qty',
                    # 'collection_group_order_sales_reduction',
                    'collection_not_allow_allot_in',
                    'collection_not_allow_allot_out',
                    'collection_not_allow_replenish_in',
                ]
                favorite = typedef.BaseParameterFavorites(
                    favorites_code=current_user.username,
                    favorites_name='常用配置参数',
                    collection_codes=collection_codes,
                    user_id=user_id,
                    modifier_id=current_user.user_id,
                    creator_id=current_user.user_id
                )
                self.dao.insert_parameter_favorites(favorite)
        collection_codes = list(favorite.collection_codes)
        data = 1 if collection_code in collection_codes else 0
        return data

    def collect_parameter_collection(self, collection_code: str) -> int:
        """
        收藏或取消 参数集收藏
        Args:
            collection_code: type: str
        Return:
            int: for bool 当前收藏状态
        """
        user_id = current_user.user_id
        with transaction_manager():
            favorite = self.dao.query_parameter_favorites(user_id)
            if not favorite:
                collection_codes = [
                    'collection_new_store_protect',
                    'collection_new_product',
                    'collection_ra_protect',
                    'collection_single_skc_store_max_pkg_receive',
                    'collection_single_skc_store_max_pkg_send',
                    'collection_single_store_max_pkg_receive',
                    'collection_single_store_max_pkg_send',
                    'collection_store_least_unit',
                    'collection_store_least_mnt',
                    'collection_store_one_city_least_unit',
                    'collection_store_one_city_least_mnt',
                    'collection_store_cross_city_least_unit',
                    'collection_store_cross_city_least_mnt',
                    'collection_satisfy_week',
                    'collection_demand_rate',
                    'collection_max_sku_stock_qty',
                    'collection_not_allow_allot_in',
                    'collection_not_allow_allot_out',
                    'collection_not_allow_replenish_in',
                ]
                favorite = typedef.BaseParameterFavorites(
                    favorites_code=current_user.username,
                    favorites_name='常用配置参数',
                    collection_codes=collection_codes,
                    user_id=user_id,
                    modifier_id=current_user.user_id,
                    creator_id=current_user.user_id
                )
                self.dao.insert_parameter_favorites(favorite)
        collection_codes = list(favorite.collection_codes)
        if collection_code in collection_codes:
            collection_codes.remove(collection_code)
        else:
            collection_codes.append(collection_code)
        favorite.collection_codes = collection_codes
        self.dao.update_parameter_favorites(favorite)
        data = 1 if collection_code in collection_codes else 0
        return data

    def get_favorites_collections(self, user_id: int, collection_map, parameter_names_map) -> List[Dict]:
        """"""
        favorites = self.dao.query_parameter_favorite_list(user_id)
        if not favorites:
            return []
        # 参数集
        # collections = self.get_param_condition(category_group)
        # collection_map = list_to_map(collections, key='collection_code')
        favorite_list = []
        for favor in favorites:
            favor_node = typedef.CategoryTreeNode(
                key=favor.favorites_code,
                title=favor.favorites_name,
                layout=None,
                type=0,
                seq=favor.id
            )
            fav_codes = favor.collection_codes
            favor_node.children = collections_nodes(favor_node, collection_map, fav_codes, parameter_names_map)
            favorite_list.append(favor_node.dict())
        return favorite_list

    def get_parameter_by_collection(self, collection_code: str, org_sk: str) -> List[Dict]:
        """
        根据参数集 查找参数定义
        """
        if not org_sk:
            raise ParameterException(msg='您没有组织权限，无法编辑参数')
        if not isinstance(org_sk, int):
            raise ParameterException(msg='org_sk 参数类型错误')

        org_level = auth_region_service.get_region_unit_level_dict(org_sk).get(org_sk) or 9999

        parameters = self.dao.parameter_list_by_collection_code(collection_code)
        res = []
        if not parameters:
            return res
        for i in parameters:
            is_editable = 1 if i.level == '0' else 1 if org_level <= int(i.level) else 0
            define = ArgsDefineFront(
                parameter_code=i.parameter_code,
                parameter_name=i.parameter_name,
                parameter_mode=i.mode,
                parameter_type=i.parameter_type,  # INPUT/VALUE_LIST/JSON
                parameter_selector=i.parameter_value.get('parameter_selector'),
                custom_parameter_type=i.parameter_value.get('custom_parameter_type'),
                custom_parameter_type_config=i.parameter_value.get('custom_parameter_type_config'),
                selector_api_properties=i.parameter_value.get('selector_api_properties'),
                selector_api_code=i.parameter_value.get('selector_api_code'),
                is_editable=is_editable,
                bind_title_key=i.parameter_value.get('bind_title_key'),
                buttons=i.parameter_value.get('buttons'),
                table_header_opera=i.parameter_value.get('table_header_opera')
            )
            if i.rule_define_id:
                rule_definition = rule_definition_service.find_rule_definition_by_id(i.rule_define_id)
                define.biz_object_code = rule_definition.rule_input_definition[0].get('applicable_biz_object_code')
            else:
                define.biz_object_code = ''
            define.table_header = i.parameter_value.get('table_header')
            define.form_layout = i.parameter_value.get('form_layout')
            # /text /number /date /percent /checkbox /radio
            if i.parameter_type == ParameterDefineType.json.value:
                define.parameter_define_list = []
                parameter_list = i.parameter_value.get('parameter_define_list') or []
                for item in parameter_list:
                    sub_define = ArgsDefineFront(
                        parameter_code=item.get('parameter_code'),
                        parameter_name=item.get('parameter_name'),
                        parameter_mode=item.get('parameter_mode'),
                        parameter_type=item.get('parameter_type'),  # INPUT/VALUE_LIST/JSON
                        parameter_selector=item.get('parameter_selector'),
                        custom_parameter_type=i.parameter_value.get('custom_parameter_type'),
                        custom_parameter_type_config=i.parameter_value.get('custom_parameter_type_config'),
                        selector_api_properties=i.parameter_value.get('selector_api_properties'),
                        selector_api_code=i.parameter_value.get('selector_api_code'),
                        is_editable=is_editable
                    )
                    sub_define.value_type = type_transfer_for_front(item.get('parameter_value_type'))
                    sub_define.is_enable = True
                    define.parameter_define_list.append(sub_define)
            define.value_type = type_transfer_for_front(i.parameter_value.get('parameter_value_type'))
            define.is_enable = True
            res.append(define.dict())
        return res

    def get_exception_algorithm_handler(self, biz_module: str, source: str, **kwargs) -> List[LabelValue]:
        data = self.dao.get_exception_algorithm(biz_module, source)
        res = []
        for i in data:
            item = LabelValue(k=i.algorithm_name, v=i.exception_code)
            res.append(item.as_dict())
        return res

    def hit_condition_sql_script(self):
        for hit_condition, belong_org_sk, inst_id, set_id in self.dao.find_rule_hit_condition() or []:
            rule_req = HitConditionSqlReq(
                hit_condition=json.dumps(hit_condition),
                belong_rule_instance_set_id=set_id,
                belong_org_sk=belong_org_sk
            )
            rule_instance_service.update_condition_sql_by_id(rule_req, inst_id)
        return 1


parameter_service = ParameterService()
