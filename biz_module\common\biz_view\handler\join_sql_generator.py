import logging
from typing import List, Optional, Dict

from pydantic import BaseModel

from biz.api_exception import ParameterException
from biz_module.common.biz_view.constants import CombineObjectJoinTypeEnum
from biz_module.common.biz_view.typedef import TableJoinRelation, TableRelation


class _JoinSqlParam(BaseModel):
    first_table_name: str
    first_table_alias_name: str
    join: str
    second_table_name: str
    second_table_alias_name: str
    on: Optional[str] = ""


class JoinSqlGenerator:
    """
    生成FROM/JOIN/ON sql片段
    :version 5.4.0 init
    """

    schema_table_name_separator = "."
    logger = logging.getLogger(__name__)

    def __init__(self):
        self.supported_join = CombineObjectJoinTypeEnum.all_allowed_types()

    def generate_join_sql(self, table_join_conditions: List[TableJoinRelation]):
        if not table_join_conditions or len(table_join_conditions) == 0:
            self.logger.warning(f"join_conditions为空，无关联条件，无法生成join sql")
            raise ParameterException("无关联条件，无法生成join sql")

        join_condition = table_join_conditions[0]
        # None表示无多表关联
        if len(table_join_conditions) == 1 and join_condition.join == CombineObjectJoinTypeEnum.none_join:
            return self.__generate_simple_relation(join_condition)

        already_joined_table_name_set = set()
        join_sql = ""
        for index, join_condition in enumerate(table_join_conditions, 1):
            if len(join_condition.table_relation) != 2:
                self.logger.warning(f"参数非法，join_condition:{join_condition} 非法")
                raise ParameterException("join_condition 参数非法")

            table_relation_1 = join_condition.table_relation[0]
            table_relation_2 = join_condition.table_relation[1]
            if index == 1:
                already_joined_table_name_set.add(table_relation_1.alias_name)
                already_joined_table_name_set.add(table_relation_2.alias_name)
                join_sql += "FROM "
                join_param = _JoinSqlParam(
                    first_table_name=table_relation_1.table_name,
                    first_table_alias_name=table_relation_1.alias_name,
                    join=join_condition.join,
                    second_table_name=table_relation_2.table_name,
                    second_table_alias_name=table_relation_2.alias_name,
                )
                if join_condition.join != CombineObjectJoinTypeEnum.cross_join:
                    join_param.on = self.__generate_on_sql_text(table_relation_1, table_relation_2)

                join_sql += self.__generate_join_sql_text(join_param)
            else:
                first_joined = table_relation_1.alias_name in already_joined_table_name_set
                second_joined = table_relation_2.alias_name in already_joined_table_name_set
                self.__check_joined_status(index, first_joined, second_joined)
                already_joined_table_name_set.add(table_relation_1.alias_name)
                already_joined_table_name_set.add(table_relation_2.alias_name)
                portion_sql_dict = {"on": "", "join": join_condition.join}
                if join_condition.join != CombineObjectJoinTypeEnum.cross_join:
                    portion_sql_dict["on"] = self.__generate_on_sql_text(table_relation_1, table_relation_2)

                if first_joined and second_joined:
                    portion_sql_dict["and_condition"] = portion_sql_dict["on"].replace("on", "")
                    portion_join_sql = self.__generate_fully_joined_sql_text(portion_sql_dict)

                elif first_joined and not second_joined:
                    portion_sql_dict["table_name"] = table_relation_2.table_name
                    portion_sql_dict["table_alias_name"] = table_relation_2.alias_name
                    portion_join_sql = self.__generate_portion_join_sql_text(portion_sql_dict)
                else:
                    portion_sql_dict["table_name"] = table_relation_1.table_name
                    portion_sql_dict["table_alias_name"] = table_relation_1.alias_name
                    portion_join_sql = self.__generate_portion_join_sql_text(portion_sql_dict)
                join_sql += portion_join_sql

        return join_sql

    def __check_joined_status(self, index: int, first_joined: bool, second_joined: bool):
        # if first_joined and second_joined:
        #     self.logger.warning(f"第{index}组关联关系设置有误first_joined:{first_joined},second_joined:{second_joined}")
        #     raise ParameterException("第{}组关联关系设置有误, fully joined".format(index))

        if not first_joined and not second_joined:
            self.logger.warning(f"第{index}组关联关系设置有误 not first_joined:{not first_joined},not second_joined:{not second_joined}")
            raise ParameterException("第{}组关联关系设置有误, 请检查".format(index))

    @staticmethod
    def __generate_simple_relation(table_join_relation: TableJoinRelation) -> str:
        table_relation = table_join_relation.table_relation.pop()
        sql_text = "FROM {table_name} as {alias_name}".format(
            table_name=table_relation.table_name, alias_name=table_relation.alias_name
        )
        return sql_text

    @staticmethod
    def __generate_join_sql_text(join_param: _JoinSqlParam):
        sql = "{first_table_name} as {first_table_alias_name}" " {join} {second_table_name} as {second_table_alias_name} {on}"

        return sql.format(**join_param.dict())

    @staticmethod
    def __generate_portion_join_sql_text(params: Dict):
        sql_text = " {join} {table_name} as {table_alias_name} {on}"
        return sql_text.format(**params)

    @staticmethod
    def __generate_fully_joined_sql_text(params: Dict):
        """
        在一组条件中，已经都join过了，直接使用别名
        @return:
        """
        sql_text = " and {and_condition}"
        return sql_text.format(**params)

    def __generate_on_sql_text(self, table_1_relation: TableRelation, table_2_relation: TableRelation):
        if type(table_1_relation.on) != type(table_2_relation.on):
            self.logger.warning(
                f"关联关系ON错误,数据类型要一致,table_1_relation.on类型{type(table_1_relation.on)}:table_2_relation.on{type(table_2_relation.on)}"
            )
            raise ParameterException("关联关系ON错误,数据类型要一致")

        on_sql_text = ""
        if isinstance(table_1_relation.on, str):
            # str类型
            on_sql_text = (
                f"{table_1_relation.alias_name}.{table_1_relation.on} = {table_2_relation.alias_name}.{table_2_relation.on}"
            )
        else:
            # list[str] 类型
            if len(table_1_relation.on) != len(table_2_relation.on):
                self.logger.warning(
                    f"关联关系ON错误,数据类型要一致,table_1_relation.on类型{type(table_1_relation.on)}:table_2_relation.on{type(table_2_relation.on)}"
                )
                raise ParameterException("关联关系ON错误, 数量不匹配")

            for index, on in enumerate(table_1_relation.on, 0):
                _and = "" if index == 0 else " AND "
                on_sql_text += f"{_and}{table_1_relation.alias_name}.{table_1_relation.on[index]} = {table_2_relation.alias_name}.{table_2_relation.on[index]}"

        return " on " + on_sql_text
