import logging
from typing import List

import paramiko

logger = logging.getLogger(__name__)


class SSHClient:
    def __init__(self, hostname: str, port: int = 22, username: str = None, password: str = None, conn_name: str = ""):
        self.hostname = hostname
        self.port = port
        self.username = username
        self.password = password
        self.conn_name = conn_name  # 用于标识这个连接的名称，打印命令时会用到

        self._ssh = None
        self._sftp = None

        self._ssh = self.ssh  # 建立ssh连接

    def __del__(self):
        self.close()

    @property
    def ssh(self) -> paramiko.SSHClient:
        """SSHClient实例"""
        if not self._ssh or not self.is_active():
            self._ssh = paramiko.SSHClient()
            # 这行代码的作用是允许连接不在know_hosts文件中的主机。
            self._ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self._ssh.connect(self.hostname, self.port, self.username, self.password)

        return self._ssh

    def is_active(self) -> bool:
        """检查当前连接是否关闭"""
        res = False
        if self._ssh:
            if self._ssh.get_transport() is not None:
                res = self._ssh.get_transport().is_active()
        return res

    def close(self):
        if self._ssh:
            self._ssh.close()

    def exec_command(self, cmd: str, async_run: bool = False, ignore_errors: bool = False, **kwargs) -> (List[str], List[str]):
        """
        调用SSHClient.exec_command方法，在目标服务器上执行shell命令

        :param cmd: shell命令语句
        :param async_run: 是否异步执行（如果为异步执行，则忽略错误）
        :param ignore_errors: 非异步执行的情况下，是否忽略错误
        :return: (List[str], List[str]) 如果为异步执行，返回的内容可能不是命令执行后的所有输出
            标准输出内容列表
            标准错误内容列表
        :raise: IOError
        """
        self._print_cmd(cmd)
        _stdin, stdout, stderr = self.ssh.exec_command(cmd, **kwargs)

        # 如果非异步执行，则等待命令执行完毕，获取返回的状态码
        exit_status = 0 if async_run else stdout.channel.recv_exit_status()

        err_ = stderr.readlines()
        if exit_status != 0 and not ignore_errors:
            raise IOError("Fail to execute commands: {0}\n{1}".format(cmd, "\n".join(err_)))

        out_ = stdout.readlines()
        return out_, err_

    @property
    def sftp(self) -> paramiko.SFTPClient:
        if not self._sftp:
            self._sftp = self.ssh.open_sftp()

        return self._sftp

    def _print_cmd(self, cmd):
        if self.conn_name:
            logger.info("[{}]>> {}".format(self.conn_name, cmd))
        else:
            logger.info(cmd)


if __name__ == "__main__":
    ssh_client = SSHClient("192.168.200.103", 22, "linezone_t2", "YM6nSYBjqMlrp1rf")
    out, err = ssh_client.exec_command("cd /home/<USER>/workspace/linezone_stable_model_1 && ls -al")
    print(out)
    print(err)
