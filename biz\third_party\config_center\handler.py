#!/usr/bin/env python
# encoding: utf-8

import logging
import os
from collections import OrderedDict
from typing import Optional

import requests
from fastboot.utils.config import ConfigMeta
from fastboot.utils.nest import egg

from biz.api_exception import ServerError

Logger = logging.getLogger(__name__)


@egg
class MetaConfig:
    CONFIG_CENTER_HOST: Optional[str] = ConfigMeta(name="config_center.host")
    CONFIG_CENTER_PORT: Optional[int] = ConfigMeta(name="config_center.port")


class ConfigCenterHandler:
    """
    配置中心处理类
    """

    @classmethod
    def find_rsa_key(cls):
        """
        获取公私钥信息
        :return:
        """

        api_path = "/api/md-master/v5/config-center/key-config/outer/rsa-key"
        method_desc = "获取公私钥信息"
        params = dict()
        result = ConfigCenterHandler.__call_config_center_api(api_path, method_desc, params)
        data = result.get("data")

        return data

    @classmethod
    def __call_config_center_api(cls, api_path, method_desc, params):
        """
        调用配置中心的api
        :param params:
        :return:
        """

        result = OrderedDict()
        center_name = "配置中心"
        msg_header = "++++++ config_center::__call_config_center_api【配置中心】 "
        try:
            config_center_domain = "http://{host}:{port}".format(
                host=MetaConfig.CONFIG_CENTER_HOST or os.getenv('CONFIG_CENTER_HOST'),
                port=MetaConfig.CONFIG_CENTER_PORT or os.getenv('CONFIG_CENTER_PORT'))
            url = config_center_domain + str(api_path)
            Logger.info(msg_header + "start,url:{url},params:{params}".format(url=url, params=params))
            resp = requests.post(url=url, json=params)
        except Exception as e:
            error_msg = "调用" + center_name + "服务" + method_desc + "异常，请求参数：{params}"
            Logger.error(msg_header + error_msg.format(params=params))
            Logger.error(e)
            raise ServerError()

        if 200 != resp.status_code:
            error_msg = "调用" + center_name + "服务" + method_desc + "返回状态异常，请求参数：{params}, 状态status_code为：{status_code} "
            Logger.error(msg_header + error_msg.format(params=params, status_code=resp.status_code))
            raise ServerError()

        resp_json = resp.json()
        if not resp_json.get("success", False):
            error_msg = "调用" + center_name + "服务" + method_desc + "异常，请求参数：{params}, code为：{code}, msg为：{msg} "
            Logger.error(
                msg_header + error_msg.format(params=params, code=resp_json.get("code"), msg=resp_json.get("msg")))
            raise ServerError()

        result["data"] = resp_json["data"]
        Logger.info(msg_header + " end!")
        return result
