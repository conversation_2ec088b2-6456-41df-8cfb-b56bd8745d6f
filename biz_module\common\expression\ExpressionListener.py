# Generated from biz_module\common\expression\Expression.g4 by ANTLR 4.9.3
from antlr4 import *
if __name__ is not None and "." in __name__:
    from .ExpressionParser import ExpressionParser
else:
    from ExpressionParser import ExpressionParser

# This class defines a complete listener for a parse tree produced by ExpressionParser.
class ExpressionListener(ParseTreeListener):

    # Enter a parse tree produced by ExpressionParser#statement.
    def enterStatement(self, ctx:ExpressionParser.StatementContext):
        pass

    # Exit a parse tree produced by ExpressionParser#statement.
    def exitStatement(self, ctx:ExpressionParser.StatementContext):
        pass


    # Enter a parse tree produced by ExpressionParser#condition_body.
    def enterCondition_body(self, ctx:ExpressionParser.Condition_bodyContext):
        pass

    # Exit a parse tree produced by ExpressionParser#condition_body.
    def exitCondition_body(self, ctx:ExpressionParser.Condition_bodyContext):
        pass


    # Enter a parse tree produced by ExpressionParser#base_if.
    def enterBase_if(self, ctx:ExpressionParser.Base_ifContext):
        pass

    # Exit a parse tree produced by ExpressionParser#base_if.
    def exitBase_if(self, ctx:ExpressionParser.Base_ifContext):
        pass


    # Enter a parse tree produced by ExpressionParser#option_if.
    def enterOption_if(self, ctx:ExpressionParser.Option_ifContext):
        pass

    # Exit a parse tree produced by ExpressionParser#option_if.
    def exitOption_if(self, ctx:ExpressionParser.Option_ifContext):
        pass


    # Enter a parse tree produced by ExpressionParser#default_if.
    def enterDefault_if(self, ctx:ExpressionParser.Default_ifContext):
        pass

    # Exit a parse tree produced by ExpressionParser#default_if.
    def exitDefault_if(self, ctx:ExpressionParser.Default_ifContext):
        pass


    # Enter a parse tree produced by ExpressionParser#branch.
    def enterBranch(self, ctx:ExpressionParser.BranchContext):
        pass

    # Exit a parse tree produced by ExpressionParser#branch.
    def exitBranch(self, ctx:ExpressionParser.BranchContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Add.
    def enterAdd(self, ctx:ExpressionParser.AddContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Add.
    def exitAdd(self, ctx:ExpressionParser.AddContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Sub.
    def enterSub(self, ctx:ExpressionParser.SubContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Sub.
    def exitSub(self, ctx:ExpressionParser.SubContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Eql.
    def enterEql(self, ctx:ExpressionParser.EqlContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Eql.
    def exitEql(self, ctx:ExpressionParser.EqlContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Null.
    def enterNull(self, ctx:ExpressionParser.NullContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Null.
    def exitNull(self, ctx:ExpressionParser.NullContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Or.
    def enterOr(self, ctx:ExpressionParser.OrContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Or.
    def exitOr(self, ctx:ExpressionParser.OrContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Func.
    def enterFunc(self, ctx:ExpressionParser.FuncContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Func.
    def exitFunc(self, ctx:ExpressionParser.FuncContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Mul.
    def enterMul(self, ctx:ExpressionParser.MulContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Mul.
    def exitMul(self, ctx:ExpressionParser.MulContext):
        pass


    # Enter a parse tree produced by ExpressionParser#In.
    def enterIn(self, ctx:ExpressionParser.InContext):
        pass

    # Exit a parse tree produced by ExpressionParser#In.
    def exitIn(self, ctx:ExpressionParser.InContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Parens.
    def enterParens(self, ctx:ExpressionParser.ParensContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Parens.
    def exitParens(self, ctx:ExpressionParser.ParensContext):
        pass


    # Enter a parse tree produced by ExpressionParser#NotEql.
    def enterNotEql(self, ctx:ExpressionParser.NotEqlContext):
        pass

    # Exit a parse tree produced by ExpressionParser#NotEql.
    def exitNotEql(self, ctx:ExpressionParser.NotEqlContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Lt.
    def enterLt(self, ctx:ExpressionParser.LtContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Lt.
    def exitLt(self, ctx:ExpressionParser.LtContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Gt.
    def enterGt(self, ctx:ExpressionParser.GtContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Gt.
    def exitGt(self, ctx:ExpressionParser.GtContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Int.
    def enterInt(self, ctx:ExpressionParser.IntContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Int.
    def exitInt(self, ctx:ExpressionParser.IntContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Str.
    def enterStr(self, ctx:ExpressionParser.StrContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Str.
    def exitStr(self, ctx:ExpressionParser.StrContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Div.
    def enterDiv(self, ctx:ExpressionParser.DivContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Div.
    def exitDiv(self, ctx:ExpressionParser.DivContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Neg.
    def enterNeg(self, ctx:ExpressionParser.NegContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Neg.
    def exitNeg(self, ctx:ExpressionParser.NegContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Float.
    def enterFloat(self, ctx:ExpressionParser.FloatContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Float.
    def exitFloat(self, ctx:ExpressionParser.FloatContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Not.
    def enterNot(self, ctx:ExpressionParser.NotContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Not.
    def exitNot(self, ctx:ExpressionParser.NotContext):
        pass


    # Enter a parse tree produced by ExpressionParser#And.
    def enterAnd(self, ctx:ExpressionParser.AndContext):
        pass

    # Exit a parse tree produced by ExpressionParser#And.
    def exitAnd(self, ctx:ExpressionParser.AndContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Gte.
    def enterGte(self, ctx:ExpressionParser.GteContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Gte.
    def exitGte(self, ctx:ExpressionParser.GteContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Id.
    def enterId(self, ctx:ExpressionParser.IdContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Id.
    def exitId(self, ctx:ExpressionParser.IdContext):
        pass


    # Enter a parse tree produced by ExpressionParser#Lte.
    def enterLte(self, ctx:ExpressionParser.LteContext):
        pass

    # Exit a parse tree produced by ExpressionParser#Lte.
    def exitLte(self, ctx:ExpressionParser.LteContext):
        pass



del ExpressionParser