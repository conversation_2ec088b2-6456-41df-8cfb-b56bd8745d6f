#!/usr/bin/env python
# _*_ coding: utf-8 _*_

"""
@author: yang<PERSON><PERSON><PERSON>
@license: Copyright 2017-2020, LineZoneData.
@contact: yang<PERSON><PERSON><PERSON>@linezonedata.com
@software: pycharm
@time: 2020/4/1 10:00
@desc:
"""

import enum
from typing import Tuple
from urllib.parse import urljoin

from biz.settings import config


@enum.unique
class TaskType(enum.IntEnum):
    """任务类型"""

    # 区域内补调导出
    INTRA_RA_EXPORT = 0
    # 区域间调拨导出
    EXTRA_ALLOT_EXPORT = 1
    # 区域内补调导入
    INTRA_RA_IMPORT = 2
    # 区域间调拨导入
    EXTRA_ALLOT_IMPORT = 3
    # 快速反应全部计算
    QUICK_RESPONSE_DETAIL = 4
    # 概览页导出
    OVER_VIEW_EXPORT = 5
    # 门店汇总计算
    STORE_SUMMARY = 6
    # 按场景导出补调单
    RA_SCENE_EXPORT = 7
    # 按场景导入补调单
    RA_SCENE_IMPORT = 8
    # 团购/客订集货计算
    RA_COLLECTION_ACTIVITY = 9
    # 补调决策数据 skc 计算
    SKC_CALCULATE = 12
    # 补调决策数据 sku_data 计算
    SKU_DATA_CALCULATE = 13
    # 团购/客订集货导入
    RA_COLLECTION_STORE_PRODUCT_DETAIL_IMPORT = 14
    # 快反导出
    QUICK_RESPONSE_EXPORT = 15
    # 铺货计划校验
    DISTRIBUTE_PLAN_VERIFY = 16
    # 铺货计划计算
    DISTRIBUTE_PLAN_CALCULATE = 17

    # 团购/客订集货校验
    RA_COLLECTION_ACTIVITY_CHECK = 19

    # 决策池商品配置-历史决策池导出
    HISTORY_DECISION_POOL_EXPORT = 20
    # 决策池商品配置-商品列表导出
    DECISION_POLL_PRODUCT_LIST_EXPORT = 21
    # OTB销售计划导出
    OTB_SALE_PLAN_IMPORT = 23
    OTB_SALE_PLAN_EXPORT = 24
    OTB_MID_OTB_EXPORT = 25
    # 流通指令特征计算
    GTO_FEATURE_CALCULATE = 26
    # 仿真计算
    GTO_SIMULATION_CALCULATE = 27
    # 复盘计算
    GTO_REPLAY_CALCULATE = 28
    # 门店概览计算
    STORE_OVERVIEW_CALCULATE = 29
    # 铺货计算
    PLAN_DISTRIBUTION_CALCULATE = 30
    # 业务验证全部导出
    BizVerifyExportAll = 31
    @classmethod
    def get_label(cls, value: int) -> str:
        d = {
            cls.INTRA_RA_EXPORT.value: "区域内补货调拨导出",
            cls.EXTRA_ALLOT_EXPORT.value: "区域间调拨导出",
            cls.INTRA_RA_IMPORT.value: "区域内补货调拨导入",
            cls.EXTRA_ALLOT_IMPORT.value: "区域间调拨导入",
            cls.QUICK_RESPONSE_DETAIL.value: "快速反应全部计算",
            cls.OVER_VIEW_EXPORT.value: "补调门店概览导出",
            cls.STORE_SUMMARY.value: "补调门店概览计算",
            cls.RA_COLLECTION_ACTIVITY.value: "团购/客订集货计算",
            cls.RA_COLLECTION_STORE_PRODUCT_DETAIL_IMPORT.value: "团购/客订集货导入",
            cls.RA_SCENE_IMPORT.value: "流通指令导入",
            cls.RA_SCENE_EXPORT.value: "流通指令导出",
            cls.SKC_CALCULATE.value: "流通指令保存/提交",
            cls.SKU_DATA_CALCULATE.value: "补调决策数据 sku_data 计算",
            cls.QUICK_RESPONSE_EXPORT.value: "快速反应导出",
            cls.DISTRIBUTE_PLAN_VERIFY.value: "分货计划数据校验",
            cls.DISTRIBUTE_PLAN_CALCULATE.value: "分货计划计算",
            cls.RA_COLLECTION_ACTIVITY_CHECK.value: "团购/客订集货校验",
            cls.HISTORY_DECISION_POOL_EXPORT.value: "决策池商品配置-历史决策池导出",
            cls.DECISION_POLL_PRODUCT_LIST_EXPORT.value: "决策池商品配置-商品列表导出",
            cls.OTB_SALE_PLAN_IMPORT: "OTB销售计划导入",
            cls.OTB_SALE_PLAN_EXPORT: "OTB销售计划导出",
            cls.OTB_MID_OTB_EXPORT: "季中OTB导出",
            cls.GTO_FEATURE_CALCULATE: "流通指令特征计算",
            cls.GTO_SIMULATION_CALCULATE: "仿真计算",
            cls.GTO_REPLAY_CALCULATE: "复盘计算",
            cls.STORE_OVERVIEW_CALCULATE: "门店概览计算",
            cls.PLAN_DISTRIBUTION_CALCULATE: "计划铺货计算",
            cls.BizVerifyExportAll: "业务验证全部导出"
        }
        return d.get(value)

    @classmethod
    def related_page_id(cls, value: int) -> int:
        """任务类型对应的页面的id"""
        d = {
            cls.INTRA_RA_EXPORT.value: 4,  # 区域内调拨页面
            cls.INTRA_RA_IMPORT.value: 4,
            cls.EXTRA_ALLOT_EXPORT.value: 21,  # 区域间调拨页面
            cls.EXTRA_ALLOT_IMPORT.value: 21,
            cls.QUICK_RESPONSE_DETAIL.value: 102,  # 快反页面
            cls.OVER_VIEW_EXPORT.value: 5,  # 门店总览页面
            cls.STORE_SUMMARY.value: 5,  # 门店总览页面
            cls.RA_SCENE_IMPORT.value: 20400000,  # 补调决策页面
            cls.RA_SCENE_EXPORT.value: 20400000,  # 补调决策页面
            cls.SKC_CALCULATE.value: 20400000,  # 补调决策页面
            cls.SKU_DATA_CALCULATE.value: 20400000,  # 补调决策页面
            cls.RA_COLLECTION_ACTIVITY.value: 6,  # 团购/客订集货计算
            cls.RA_COLLECTION_STORE_PRODUCT_DETAIL_IMPORT.value: 6,  # 团购/客订集货导入
            cls.HISTORY_DECISION_POOL_EXPORT.value: 206,  # 决策池商品配置-历史决策池导出
            cls.DECISION_POLL_PRODUCT_LIST_EXPORT.value: 206,  # 决策池商品配置-商品列表导出
            cls.GTO_FEATURE_CALCULATE: 20400000,
            cls.GTO_SIMULATION_CALCULATE: 20400000,
            cls.GTO_REPLAY_CALCULATE: 20400000,
            cls.STORE_OVERVIEW_CALCULATE: 20400000,
            cls.PLAN_DISTRIBUTION_CALCULATE: 20400000,
            cls.BizVerifyExportAll: 60600000
        }
        return d.get(value)

    @classmethod
    def task_filter(cls, value: int) -> int:
        """该方法列出来的任务才会被加入到任务类型的下拉列表中"""
        d = {
            cls.RA_SCENE_IMPORT.value: 20400000,  # 补调决策页面
            cls.RA_SCENE_EXPORT.value: 20400000,  # 补调决策页面
            cls.SKC_CALCULATE.value: 20400000,
            cls.OVER_VIEW_EXPORT.value: 5,  # 补调门店概览页面
            cls.STORE_SUMMARY.value: 5,  # 补调门店概览页面
            cls.RA_COLLECTION_ACTIVITY.value: 6,  # 集货操作页面
            cls.RA_COLLECTION_STORE_PRODUCT_DETAIL_IMPORT.value: 6,  # 团购/客订集货导入
            cls.QUICK_RESPONSE_DETAIL.value: 102,  # 快速反应页面
            # 铺货计划校验
            cls.DISTRIBUTE_PLAN_VERIFY: 204,
            # 铺货计划计算
            cls.DISTRIBUTE_PLAN_CALCULATE: 204,
            # 团购客订集货校验
            cls.RA_COLLECTION_ACTIVITY_CHECK.value: 6,
            cls.HISTORY_DECISION_POOL_EXPORT: 206,
            cls.DECISION_POLL_PRODUCT_LIST_EXPORT: 206,
            cls.GTO_FEATURE_CALCULATE: 20400000,
            cls.OTB_SALE_PLAN_IMPORT: 30802000,
            cls.OTB_SALE_PLAN_EXPORT: 30802000,
            cls.OTB_MID_OTB_EXPORT: 30804000,
            cls.GTO_SIMULATION_CALCULATE: 20400000,
            cls.GTO_REPLAY_CALCULATE: 20400000,
            cls.STORE_OVERVIEW_CALCULATE: 20400000,
            cls.PLAN_DISTRIBUTION_CALCULATE: 20400000,
            cls.BizVerifyExportAll: 60600000
        }
        return d.get(value)

    @classmethod
    def decision_pool_task_codes(cls):
        return {cls.DECISION_POLL_PRODUCT_LIST_EXPORT.value, cls.HISTORY_DECISION_POOL_EXPORT.value}


@enum.unique
class ScheduleType(enum.IntEnum):
    """计划类型"""

    # 实时任务
    IMMEDIATE_TASK = 0
    # 定时任务
    PLANNED_TASK = 1

    @classmethod
    def get_label(cls, value: int) -> str:
        d = {
            cls.IMMEDIATE_TASK.value: "实时任务",
            cls.PLANNED_TASK.value: "定时任务",
        }
        return d.get(value)


@enum.unique
class ScheduleTimeType(enum.IntEnum):
    """计划定时类型"""

    # 模型决策后执行
    AFTER_MODEL = 0

    @classmethod
    def get_label(cls, value: int) -> str:
        d = {
            cls.AFTER_MODEL.value: "模型决策后执行",
        }
        return d.get(value)


@enum.unique
class TaskStatus(enum.IntEnum):
    """任务状态"""

    # 排队中
    PENDING = 0
    # 执行中
    STARTED = 1
    # 成功
    SUCCESS = 2
    # 失败
    FAILED = 3
    # 取消
    CANCELED = 4
    # 中止
    TERMINATED = 5

    @classmethod
    def get_label(cls, value: int) -> str:
        d = {
            cls.PENDING.value: "正在排队",
            cls.STARTED.value: "正在执行",
            cls.SUCCESS.value: "任务成功",
            cls.FAILED.value: "任务失败",
            cls.CANCELED.value: "任务取消",
            cls.TERMINATED.value: "任务中止",
        }
        return d.get(value)


class SceneEnum(enum.Enum):
    """
    场景枚举
    """

    # 换店
    EXCHANGE = "11"
    # 下沉
    SINK = "4"

    @classmethod
    def get_scene_codes(cls) -> set:

        return {cls.EXCHANGE.value, cls.SINK.value}

    @classmethod
    def all_name_dict(cls):
        names = {cls.EXCHANGE.value: "换店", cls.SINK.value: "下沉"}
        return names

    @classmethod
    def get_name(cls, code):
        """
        获取名字
        :param code:
        :return:
        """
        return cls.all_name_dict().get(code)

    @classmethod
    def get_serial_number_by_key(cls, key):
        serial_number_dict = {cls.EXCHANGE.value: 1, cls.SINK.value: 2}
        return serial_number_dict.get(key)


class SelectionType(enum.Enum):
    """选款模式枚举"""

    # 手动
    MANUAL = "manual"
    # 自动
    AUTOMATIC = "automatic"

    @classmethod
    def all_names(cls):
        return [cls.MANUAL.value, cls.AUTOMATIC.value]

    @classmethod
    def all_name_dict(cls):
        names = {cls.MANUAL.value: "手动选款", cls.AUTOMATIC.value: "自动选款"}
        return names

    @classmethod
    def get_name(cls, code):
        return cls.all_name_dict().get(code)


class ManageType(enum.Enum):
    """管理模式枚举"""

    # 全国
    NATIONAL = "all"
    # 管理区域
    MANAGE_AREA = "manage_area"

    @classmethod
    def all_names(cls):
        return [cls.NATIONAL.value, cls.MANAGE_AREA.value]

    @classmethod
    def all_name_dict(cls):
        names = {cls.NATIONAL.value: "全国", cls.MANAGE_AREA.value: "管理区域"}
        return names

    @classmethod
    def get_name(cls, code):

        return cls.all_name_dict().get(code)


# 任务管理接口
EXPORT_CENTER_TASK_LIST_API = urljoin(config.EXPORT_CENTER_URL, "/api/md-master/v1/export-center/task-list")
EXPORT_CENTER_TASK_DETAIL_API = urljoin(config.EXPORT_CENTER_URL, "/api/md-master/v1/export-center/task-detail")
EXPORT_CENTER_DOWNLOAD_TASK_FILE_API = urljoin(config.EXPORT_CENTER_URL, "/api/md-master/v2.1/export-center/download-task-file")
EXPORT_CENTER_TASK_N_WAITING_API = urljoin(config.EXPORT_CENTER_URL, "/api/md-master/v1/export-center/task-n-waiting")
EXPORT_CENTER_ADD_TASK_API = urljoin(config.EXPORT_CENTER_URL, "/api/md-master/v1/export-center/add-task")
EXPORT_CENTER_MODIFY_TASK_API = urljoin(config.EXPORT_CENTER_URL, "/api/md-master/v1/export-center/modify-task")
EXPORT_CENTER_EXECUTE_QR_SP_API = urljoin(config.EXPORT_CENTER_URL, "/api/md-master/v2.3.1/export-center/execute-qr-sp")

# 不同的导出请求，可接受的参数定义
#   dim_code: str, 字段代码（前端使用的名称）
#   alias: str, 后端使用的字段名称
#   field_label: str, 字段名称
#   is_mandatory: bool, 是否必选
#   drop_down_list_model: int, 前端用的参数
#   dim_value_list: List[Dict], 参数的可选项列表
INTRA_RA_EXPORT_DIM_INFOS = [
    {
        "dim_code": "brand",
        "alias": "brand_code",
        "drop_down_list_model": 1,
        "field_label": "品牌",
        "is_mandatory": True,
        "dim_value_list": [],
    },
    {
        "dim_code": "manage_area",
        "alias": "manager_org_code",
        "field_label": "管理区域",
        "drop_down_list_model": 1,
        "is_mandatory": True,
        "dim_value_list": [],
    },
    {
        "dim_code": "sell_level",
        "alias": "product_class",
        "field_label": "商品畅销等级",
        "drop_down_list_model": 1,
        "is_mandatory": False,
        "dim_value_list": [],
    },
    {
        "dim_code": "seasonal_goods",
        "alias": "year_quarter",
        "field_label": "商品季节",
        "drop_down_list_model": 1,
        "is_mandatory": False,
        "dim_value_list": [],
    },
    {
        "dim_code": "product_categories",
        "alias": "category",
        "field_label": "商品类别",
        "drop_down_list_model": 1,
        "is_mandatory": False,
        "dim_value_list": [],
    },
]
EXTRA_ALLOT_EXPORT_DIM_INFOS = [
    {
        "dim_code": "brand",
        "alias": "brand_code",
        "drop_down_list_model": 1,
        "field_label": "品牌",
        "is_mandatory": True,
        "dim_value_list": [],
    },
    {
        "dim_code": "sell_level",
        "alias": "product_class",
        "field_label": "商品畅销等级",
        "drop_down_list_model": 1,
        "is_mandatory": False,
        "dim_value_list": [],
    },
    {
        "dim_code": "seasonal_goods",
        "alias": "year_quarter",
        "field_label": "商品季节",
        "drop_down_list_model": 1,
        "is_mandatory": False,
        "dim_value_list": [],
    },
    {
        "dim_code": "product_categories",
        "alias": "category",
        "field_label": "商品类别",
        "drop_down_list_model": 1,
        "is_mandatory": False,
        "dim_value_list": [],
    },
]

# warning: !!modify mapping must todo: synchronize with get_filter_dict_handler
PAGE_CONFIG_MAP = {
    "human_allot_in_up_org_code": "manager_org",
    "human_allot_out_up_org_code": "manager_org",
    "model_allot_in_up_org_code": "manager_org",
    "model_allot_out_up_org_code": "manager_org",
    "model_allot_out_org_sk": "org",
    "human_allot_out_org_sk": "org",
    "model_allot_in_org_sk": "org",
    "human_allot_in_org_sk": "org",
    "out_sales_level_code": "sales_level_code",
    "in_sales_level_code": "sales_level_code",
    "reserved7": "store_manager",
    "reserved8": "store_manager",
}

TASK_TYPE_DIM_INFOS_MAP = {
    TaskType.INTRA_RA_EXPORT.value: INTRA_RA_EXPORT_DIM_INFOS,
    TaskType.EXTRA_ALLOT_EXPORT.value: EXTRA_ALLOT_EXPORT_DIM_INFOS,
}

EXPORT_CENTER_DUPLICATED_TASK_ERROR_CODE = 20000001
