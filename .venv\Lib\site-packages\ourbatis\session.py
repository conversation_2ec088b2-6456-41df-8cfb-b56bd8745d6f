from contextlib import contextmanager
from pathlib import Path
from typing import Optional, Union, List

from pydantic import BaseModel
from sqlalchemy import create_engine
from sqlalchemy.engine import Transaction
from sqlalchemy.future import Engine, Connection

from ourbatis.const import DEFAULT_NS, StatementType, UNSAFE_NS
from ourbatis.exceptions import NoInitSessionException
from ourbatis.gobal import ConnectStack, Global, ThreadLocalSession
from ourbatis.sql_source_parser.base_parser import BaseParser
from ourbatis.sql_source_parser.objects import SessionSQLMapper
from ourbatis.sql_source_parser.sql_mapper.sql_parser import SQLParser
from ourbatis.sql_source_parser.xml_mapper.xml_parser import XMLParser
from ourbatis.utils import logger


class SessionParams(BaseModel):
    url: str
    engine: Optional[Engine] = None
    sql_source_path: Union[Path, str, None] = None
    sql_source_dir_path: Union[List, Path, str, None] = None
    recursive = False
    reuse_mapper = True
    not_support_sql = False
    application_name: str

    class Config:
        arbitrary_types_allowed = True


class SqlSession:
    SQL_PARSERS = (
        SQLParser,
        XMLParser,
    )

    PARSE_MAP = {StatementType.ST_XML: XMLParser, StatementType.ST_SQL: SQLParser}

    @classmethod
    def gen_sql_mapper(
        cls,
        sql_source_path: Union[Path, str, None],
        sql_source_dir_path: Union[List, Path, str, None],
        recursive: False,
        not_support_sql: False,
    ) -> SessionSQLMapper:
        session_sql_mapper: SessionSQLMapper = {}
        if not_support_sql:
            run_parsers = (XMLParser,)
        else:
            run_parsers = cls.SQL_PARSERS

        for sql_parser in run_parsers:
            sql_mapper = sql_parser.gen_sql_mapper(
                sql_source_path, sql_source_dir_path, recursive
            )
            BaseParser.merge_sql_mapper(session_sql_mapper, sql_mapper)

        return session_sql_mapper

    @staticmethod
    def __create_engine(params: SessionParams, **kwargs) -> Engine:
        engine = params.engine
        if not engine:
            inner_kwargs = {"future": True}
            if params.url.find("postgresql") >= 0:
                params.url = f"{params.url}?application_name={params.application_name}"
            inner_kwargs.update(kwargs)
            engine: Engine = create_engine(
                params.url, pool_pre_ping=True, **inner_kwargs
            )
        return engine

    def __init_single_session__(
        self,
        params: SessionParams,
        **kwargs,
    ):

        if not (params.sql_source_path or params.sql_source_dir_path):
            logger.error("sql_source_path and sql_source_dir_path at least have one")
            return

        if hasattr(Global, "session"):
            logger.warning(f"already have session {Global.session}")
        else:
            self.engine: Engine = self.__create_engine(params, **kwargs)
            Global.session = self

            self.session_sql_mapper = self.gen_sql_mapper(
                sql_source_path=params.sql_source_path,
                sql_source_dir_path=params.sql_source_dir_path,
                recursive=params.recursive,
                not_support_sql=params.not_support_sql,
            )
            self.__patch_pandas_to_sql()

    def __init__multi_session__(
        self,
        params: SessionParams,
        **kwargs,
    ):
        if (not params.reuse_mapper) and not (
            params.sql_source_path or params.sql_source_dir_path
        ):
            logger.error("sql_source_path and sql_source_dir_path at least have one")
            return

        self.engine = self.__create_engine(params, **kwargs)

        if params.reuse_mapper:
            if not hasattr(Global, "session"):
                logger.warning(f"have not init one session")
                raise NoInitSessionException
            session: SqlSession = Global.session
            self.session_sql_mapper = session.session_sql_mapper
        else:
            self.session_sql_mapper = self.gen_sql_mapper(
                sql_source_path=params.sql_source_path,
                sql_source_dir_path=params.sql_source_dir_path,
                recursive=params.recursive,
                not_support_sql=params.not_support_sql,
            )

    @staticmethod
    def __patch_pandas_to_sql():
        """
        pandas to_sql方法自动注入con连接
        """
        from pandas import DataFrame
        from pandas.core.generic import NDFrame

        def patch_to_sql(
            frame_self: DataFrame, name, con: Optional[Engine] = None, **kwargs
        ):
            con = con if con else Global.session.engine
            return frame_self.to_sql_ori(name, con, **kwargs)

        setattr(NDFrame, "to_sql_ori", NDFrame.to_sql)
        setattr(NDFrame, "to_sql", patch_to_sql)

    def __init__(
        self,
        url: str,  # sqlalchemy engine支持的DB URL格式 (mysql://scott:tiger@localhost/test)
        engine: Engine = None,  # 如果有现成sqlalchemy engine，可传入复用
        xml_path: Optional[str] = None,  # 单个xml文件路径(已废弃,兼容旧版本)
        xml_dir_path: Union[List, str, None] = None,  # 多xml文件夹路径(已废弃,兼容旧版本)
        recursive=False,  # 是否递归遍历文件夹
        multi_session=False,  # 是否允许创建多个session,默认全局只能创建单个session
        reuse_mapper=True,  # 如果支持多session，新创建session是否复现有全局session的sql_mapper
        use_default_logger=False,  # 已废弃
        sql_source_path: Union[Path, str, None] = None,  # 单个sql源文件路径
        sql_source_dir_path: Union[List, Path, str, None] = None,  # 多sql源文件夹路径
        not_support_sql=False,
        application_name="ourbatis",
        slow_sql_threshold=5 * 60,  # 默认慢sql时长阈值 分钟
        **kwargs,
    ):
        if xml_path:
            logger.warning("'xml_path' will discard future. use 'sql_source_path' now")
            sql_source_path = xml_path
        if xml_dir_path:
            logger.warning(
                "'xml_dir_path' will discard future. use 'sql_source_dir_path' now"
            )
            sql_source_dir_path = xml_dir_path

        params = SessionParams(
            url=url,
            engine=engine,
            sql_source_path=sql_source_path,
            sql_source_dir_path=sql_source_dir_path,
            recursive=recursive,
            reuse_mapper=reuse_mapper,
            not_support_sql=not_support_sql,
            application_name=application_name,
        )
        if not multi_session:
            self.__init_single_session__(
                params,
                **kwargs,
            )

        else:
            self.__init__multi_session__(
                params,
                **kwargs,
            )

        self.slow_sql_threshold = slow_sql_threshold
        # sql_mapper代废弃字段,兼容老系统
        if hasattr(self, "session_sql_mapper"):
            self.sql_mapper = self.session_sql_mapper.get(DEFAULT_NS, {})

    def inject_sql_statement(
        self,
        statement: str,
        statement_type: StatementType = StatementType.ST_XML,
        namespace: str = DEFAULT_NS,
    ) -> bool:
        self.PARSE_MAP[statement_type].inject_sql_statement(
            statement,
            self.session_sql_mapper,
            namespace,
        )
        return True

    def deject_sql_statement(
        self,
        sql_id: str,
        statement_type: StatementType = StatementType.ST_XML,
        namespace: str = UNSAFE_NS,
    ):
        self.PARSE_MAP[statement_type].deject_sql_statement(
            sql_id,
            self.session_sql_mapper,
            namespace,
        )
        return True

    def execute(self, *args, **kwargs):
        if not self.engine:
            logger.error(f"engine is not inited successfully.")
        if self.engine:
            try:
                return self.engine.execute(*args, **kwargs)
            except Exception:
                logger.error(
                    f"execute sql failed!args={args} kwargs={kwargs}!", exc_info=True
                )

    def begin(self):
        return self.engine.begin()

    def connect(self):
        return self.engine.connect()


@contextmanager
def transaction_manager(*_, read_only=False, **__):
    session: SqlSession = Global.session
    if not session:
        logger.error("have not init a sql session!")
        raise NoInitSessionException

    trans: Optional[Transaction] = None
    connect: Optional[Connection] = None
    try:
        connect = session.engine.connect()
        trans = connect.begin()
        if read_only:
            connect.exec_driver_sql("SET TRANSACTION READ ONLY")
        ConnectStack.push(connect)
        yield trans
    except Exception as e:
        logger.exception("session rollback!: %s", e)
        if trans:
            trans.rollback()
        raise e
    else:
        if trans:
            trans.commit()
    finally:
        ConnectStack.pop()
        if connect:
            connect.close()


def swap_global_session(new_session: SqlSession) -> SqlSession:
    old_session = Global.session
    Global.session = new_session
    return old_session


@contextmanager
def swap_session(new_session: SqlSession):
    old_session = swap_global_session(new_session)
    try:
        yield new_session
    finally:
        swap_global_session(old_session)


@contextmanager
def swap_session_thread_safe(new_session: SqlSession):
    ThreadLocalSession.swap_session = new_session
    try:
        yield new_session
    finally:
        del ThreadLocalSession.swap_session


def inject_sql_statement(
    statement: str,
    statement_type: StatementType = StatementType.ST_XML,
    namespace: str = DEFAULT_NS,
    session: Optional[SqlSession] = None,
) -> bool:
    # todo 实现不够简练,可优化
    session: SqlSession = session if session else Global.session
    result = session.inject_sql_statement(statement, statement_type, namespace)
    return result


def deject_sql_statement(
    sql_id: str,
    statement_type: StatementType = StatementType.ST_XML,
    namespace: str = UNSAFE_NS,  # deject 现只支持UNSAFE_NS命名空间
    session: Optional[SqlSession] = None,
) -> bool:
    session: SqlSession = session if session else Global.session
    result = session.deject_sql_statement(sql_id, statement_type, namespace)
    return result
