# Generated from biz_module\common\expression\Expression.g4 by ANTLR 4.9.3
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
    from typing import TextIO
else:
    from typing.io import TextIO



def serializedATN():
    with String<PERSON>() as buf:
        buf.write("\3\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964\2 ")
        buf.write("\u00e2\b\1\4\2\t\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7")
        buf.write("\t\7\4\b\t\b\4\t\t\t\4\n\t\n\4\13\t\13\4\f\t\f\4\r\t\r")
        buf.write("\4\16\t\16\4\17\t\17\4\20\t\20\4\21\t\21\4\22\t\22\4\23")
        buf.write("\t\23\4\24\t\24\4\25\t\25\4\26\t\26\4\27\t\27\4\30\t\30")
        buf.write("\4\31\t\31\4\32\t\32\4\33\t\33\4\34\t\34\4\35\t\35\4\36")
        buf.write("\t\36\4\37\t\37\4 \t \3\2\3\2\3\3\3\3\3\4\3\4\3\5\3\5")
        buf.write("\3\6\3\6\3\7\3\7\3\b\3\b\3\t\3\t\3\n\3\n\3\13\3\13\3\f")
        buf.write("\3\f\3\f\3\r\3\r\3\r\3\16\3\16\3\17\3\17\3\20\3\20\3\20")
        buf.write("\3\21\3\21\3\21\3\22\3\22\3\22\3\23\3\23\3\23\3\23\3\23")
        buf.write("\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23")
        buf.write("\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23")
        buf.write("\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\5\23")
        buf.write("\u008e\n\23\3\24\3\24\3\24\3\25\3\25\3\25\3\25\3\25\3")
        buf.write("\26\3\26\3\26\3\26\3\26\3\27\3\27\3\27\3\27\3\30\3\30")
        buf.write("\3\30\3\30\3\31\3\31\3\31\3\32\3\32\3\32\3\32\3\32\3\33")
        buf.write("\3\33\3\33\7\33\u00b0\n\33\f\33\16\33\u00b3\13\33\3\34")
        buf.write("\3\34\3\34\3\34\6\34\u00b9\n\34\r\34\16\34\u00ba\3\34")
        buf.write("\3\34\7\34\u00bf\n\34\f\34\16\34\u00c2\13\34\3\34\3\34")
        buf.write("\3\35\6\35\u00c7\n\35\r\35\16\35\u00c8\3\36\6\36\u00cc")
        buf.write("\n\36\r\36\16\36\u00cd\3\37\7\37\u00d1\n\37\f\37\16\37")
        buf.write("\u00d4\13\37\3\37\3\37\6\37\u00d8\n\37\r\37\16\37\u00d9")
        buf.write("\3 \6 \u00dd\n \r \16 \u00de\3 \3 \2\2!\3\3\5\4\7\5\t")
        buf.write("\6\13\7\r\b\17\t\21\n\23\13\25\f\27\r\31\16\33\17\35\20")
        buf.write("\37\21!\22#\23%\24\'\25)\26+\27-\30/\31\61\32\63\33\65")
        buf.write("\34\67\359\2;\36=\37? \3\2\30\4\2EEee\4\2QQqq\4\2CCcc")
        buf.write("\4\2NNnn\4\2GGgg\4\2UUuu\4\2XXxx\4\2IIii\4\2OOoo\4\2K")
        buf.write("Kkk\4\2PPpp\4\2ZZzz\4\2WWww\4\2VVvv\4\2HHhh\4\2FFff\4")
        buf.write("\2TTtt\3\2\62;\3\2^^\4\2))^^\6\2C\\aac|\u0082\1\5\2\13")
        buf.write("\f\17\17\"\"\2\u00f2\2\3\3\2\2\2\2\5\3\2\2\2\2\7\3\2\2")
        buf.write("\2\2\t\3\2\2\2\2\13\3\2\2\2\2\r\3\2\2\2\2\17\3\2\2\2\2")
        buf.write("\21\3\2\2\2\2\23\3\2\2\2\2\25\3\2\2\2\2\27\3\2\2\2\2\31")
        buf.write("\3\2\2\2\2\33\3\2\2\2\2\35\3\2\2\2\2\37\3\2\2\2\2!\3\2")
        buf.write("\2\2\2#\3\2\2\2\2%\3\2\2\2\2\'\3\2\2\2\2)\3\2\2\2\2+\3")
        buf.write("\2\2\2\2-\3\2\2\2\2/\3\2\2\2\2\61\3\2\2\2\2\63\3\2\2\2")
        buf.write("\2\65\3\2\2\2\2\67\3\2\2\2\2;\3\2\2\2\2=\3\2\2\2\2?\3")
        buf.write("\2\2\2\3A\3\2\2\2\5C\3\2\2\2\7E\3\2\2\2\tG\3\2\2\2\13")
        buf.write("I\3\2\2\2\rK\3\2\2\2\17M\3\2\2\2\21O\3\2\2\2\23Q\3\2\2")
        buf.write("\2\25S\3\2\2\2\27U\3\2\2\2\31X\3\2\2\2\33[\3\2\2\2\35")
        buf.write("]\3\2\2\2\37_\3\2\2\2!b\3\2\2\2#e\3\2\2\2%\u008d\3\2\2")
        buf.write("\2\'\u008f\3\2\2\2)\u0092\3\2\2\2+\u0097\3\2\2\2-\u009c")
        buf.write("\3\2\2\2/\u00a0\3\2\2\2\61\u00a4\3\2\2\2\63\u00a7\3\2")
        buf.write("\2\2\65\u00ac\3\2\2\2\67\u00b4\3\2\2\29\u00c6\3\2\2\2")
        buf.write(";\u00cb\3\2\2\2=\u00d2\3\2\2\2?\u00dc\3\2\2\2AB\7=\2\2")
        buf.write("B\4\3\2\2\2CD\7<\2\2D\6\3\2\2\2EF\7*\2\2F\b\3\2\2\2GH")
        buf.write("\7.\2\2H\n\3\2\2\2IJ\7+\2\2J\f\3\2\2\2KL\7/\2\2L\16\3")
        buf.write("\2\2\2MN\7,\2\2N\20\3\2\2\2OP\7\61\2\2P\22\3\2\2\2QR\7")
        buf.write("-\2\2R\24\3\2\2\2ST\7?\2\2T\26\3\2\2\2UV\7#\2\2VW\7?\2")
        buf.write("\2W\30\3\2\2\2XY\7>\2\2YZ\7@\2\2Z\32\3\2\2\2[\\\7@\2\2")
        buf.write("\\\34\3\2\2\2]^\7>\2\2^\36\3\2\2\2_`\7@\2\2`a\7?\2\2a")
        buf.write(" \3\2\2\2bc\7>\2\2cd\7?\2\2d\"\3\2\2\2ef\7k\2\2fg\7p\2")
        buf.write("\2g$\3\2\2\2hi\t\2\2\2ij\t\3\2\2jk\t\4\2\2kl\t\5\2\2l")
        buf.write("m\t\6\2\2mn\t\7\2\2no\t\2\2\2o\u008e\t\6\2\2pq\t\4\2\2")
        buf.write("qr\t\b\2\2r\u008e\t\t\2\2st\t\n\2\2tu\t\13\2\2u\u008e")
        buf.write("\t\f\2\2vw\t\n\2\2wx\t\4\2\2x\u008e\t\r\2\2yz\t\7\2\2")
        buf.write("z{\t\16\2\2{\u008e\t\n\2\2|}\t\2\2\2}~\t\3\2\2~\177\t")
        buf.write("\16\2\2\177\u0080\t\f\2\2\u0080\u008e\t\17\2\2\u0081\u0082")
        buf.write("\7p\2\2\u0082\u0083\7w\2\2\u0083\u0084\7n\2\2\u0084\u0085")
        buf.write("\7n\2\2\u0085\u0086\7k\2\2\u0086\u008e\7h\2\2\u0087\u0088")
        buf.write("\7P\2\2\u0088\u0089\7W\2\2\u0089\u008a\7N\2\2\u008a\u008b")
        buf.write("\7N\2\2\u008b\u008c\7K\2\2\u008c\u008e\7H\2\2\u008dh\3")
        buf.write("\2\2\2\u008dp\3\2\2\2\u008ds\3\2\2\2\u008dv\3\2\2\2\u008d")
        buf.write("y\3\2\2\2\u008d|\3\2\2\2\u008d\u0081\3\2\2\2\u008d\u0087")
        buf.write("\3\2\2\2\u008e&\3\2\2\2\u008f\u0090\t\13\2\2\u0090\u0091")
        buf.write("\t\20\2\2\u0091(\3\2\2\2\u0092\u0093\t\6\2\2\u0093\u0094")
        buf.write("\t\5\2\2\u0094\u0095\t\13\2\2\u0095\u0096\t\20\2\2\u0096")
        buf.write("*\3\2\2\2\u0097\u0098\t\6\2\2\u0098\u0099\t\5\2\2\u0099")
        buf.write("\u009a\t\7\2\2\u009a\u009b\t\6\2\2\u009b,\3\2\2\2\u009c")
        buf.write("\u009d\t\4\2\2\u009d\u009e\t\f\2\2\u009e\u009f\t\21\2")
        buf.write("\2\u009f.\3\2\2\2\u00a0\u00a1\t\f\2\2\u00a1\u00a2\t\3")
        buf.write("\2\2\u00a2\u00a3\t\17\2\2\u00a3\60\3\2\2\2\u00a4\u00a5")
        buf.write("\t\3\2\2\u00a5\u00a6\t\22\2\2\u00a6\62\3\2\2\2\u00a7\u00a8")
        buf.write("\t\f\2\2\u00a8\u00a9\t\16\2\2\u00a9\u00aa\t\5\2\2\u00aa")
        buf.write("\u00ab\t\5\2\2\u00ab\64\3\2\2\2\u00ac\u00b1\59\35\2\u00ad")
        buf.write("\u00b0\59\35\2\u00ae\u00b0\t\23\2\2\u00af\u00ad\3\2\2")
        buf.write("\2\u00af\u00ae\3\2\2\2\u00b0\u00b3\3\2\2\2\u00b1\u00af")
        buf.write("\3\2\2\2\u00b1\u00b2\3\2\2\2\u00b2\66\3\2\2\2\u00b3\u00b1")
        buf.write("\3\2\2\2\u00b4\u00c0\7)\2\2\u00b5\u00b6\7)\2\2\u00b6\u00bf")
        buf.write("\7)\2\2\u00b7\u00b9\7^\2\2\u00b8\u00b7\3\2\2\2\u00b9\u00ba")
        buf.write("\3\2\2\2\u00ba\u00b8\3\2\2\2\u00ba\u00bb\3\2\2\2\u00bb")
        buf.write("\u00bc\3\2\2\2\u00bc\u00bf\n\24\2\2\u00bd\u00bf\n\25\2")
        buf.write("\2\u00be\u00b5\3\2\2\2\u00be\u00b8\3\2\2\2\u00be\u00bd")
        buf.write("\3\2\2\2\u00bf\u00c2\3\2\2\2\u00c0\u00be\3\2\2\2\u00c0")
        buf.write("\u00c1\3\2\2\2\u00c1\u00c3\3\2\2\2\u00c2\u00c0\3\2\2\2")
        buf.write("\u00c3\u00c4\7)\2\2\u00c48\3\2\2\2\u00c5\u00c7\t\26\2")
        buf.write("\2\u00c6\u00c5\3\2\2\2\u00c7\u00c8\3\2\2\2\u00c8\u00c6")
        buf.write("\3\2\2\2\u00c8\u00c9\3\2\2\2\u00c9:\3\2\2\2\u00ca\u00cc")
        buf.write("\t\23\2\2\u00cb\u00ca\3\2\2\2\u00cc\u00cd\3\2\2\2\u00cd")
        buf.write("\u00cb\3\2\2\2\u00cd\u00ce\3\2\2\2\u00ce<\3\2\2\2\u00cf")
        buf.write("\u00d1\t\23\2\2\u00d0\u00cf\3\2\2\2\u00d1\u00d4\3\2\2")
        buf.write("\2\u00d2\u00d0\3\2\2\2\u00d2\u00d3\3\2\2\2\u00d3\u00d5")
        buf.write("\3\2\2\2\u00d4\u00d2\3\2\2\2\u00d5\u00d7\7\60\2\2\u00d6")
        buf.write("\u00d8\t\23\2\2\u00d7\u00d6\3\2\2\2\u00d8\u00d9\3\2\2")
        buf.write("\2\u00d9\u00d7\3\2\2\2\u00d9\u00da\3\2\2\2\u00da>\3\2")
        buf.write("\2\2\u00db\u00dd\t\27\2\2\u00dc\u00db\3\2\2\2\u00dd\u00de")
        buf.write("\3\2\2\2\u00de\u00dc\3\2\2\2\u00de\u00df\3\2\2\2\u00df")
        buf.write("\u00e0\3\2\2\2\u00e0\u00e1\b \2\2\u00e1@\3\2\2\2\16\2")
        buf.write("\u008d\u00af\u00b1\u00ba\u00be\u00c0\u00c8\u00cd\u00d2")
        buf.write("\u00d9\u00de\3\b\2\2")
        return buf.getvalue()


class ExpressionLexer(Lexer):

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    T__0 = 1
    T__1 = 2
    T__2 = 3
    T__3 = 4
    T__4 = 5
    T__5 = 6
    T__6 = 7
    T__7 = 8
    T__8 = 9
    T__9 = 10
    T__10 = 11
    T__11 = 12
    T__12 = 13
    T__13 = 14
    T__14 = 15
    T__15 = 16
    T__16 = 17
    Func = 18
    IF = 19
    ELIF = 20
    ELSE = 21
    AND = 22
    NOT = 23
    OR = 24
    NULL = 25
    ID = 26
    STR = 27
    INT = 28
    FLOAT = 29
    WS = 30

    channelNames = [ u"DEFAULT_TOKEN_CHANNEL", u"HIDDEN" ]

    modeNames = [ "DEFAULT_MODE" ]

    literalNames = [ "<INVALID>",
            "';'", "':'", "'('", "','", "')'", "'-'", "'*'", "'/'", "'+'", 
            "'='", "'!='", "'<>'", "'>'", "'<'", "'>='", "'<='", "'in'" ]

    symbolicNames = [ "<INVALID>",
            "Func", "IF", "ELIF", "ELSE", "AND", "NOT", "OR", "NULL", "ID", 
            "STR", "INT", "FLOAT", "WS" ]

    ruleNames = [ "T__0", "T__1", "T__2", "T__3", "T__4", "T__5", "T__6", 
                  "T__7", "T__8", "T__9", "T__10", "T__11", "T__12", "T__13", 
                  "T__14", "T__15", "T__16", "Func", "IF", "ELIF", "ELSE", 
                  "AND", "NOT", "OR", "NULL", "ID", "STR", "LETTER", "INT", 
                  "FLOAT", "WS" ]

    grammarFileName = "Expression.g4"

    def __init__(self, input=None, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.9.3")
        self._interp = LexerATNSimulator(self, self.atn, self.decisionsToDFA, PredictionContextCache())
        self._actions = None
        self._predicates = None


