from enum import Enum
from typing import Set

from pydantic import fields

RUN_CONFIG = "run_config"
FUNC_CONFIG = "func_config"
EXTEND_KWARGS = "extend_kwargs"


LOG_NAME = "ourbatis"
DEFAULT_NS = "default"
UNSAFE_NS = "unsafe"  # 该命名空间内,出现id重复时,老内容会被新内容替换
REMOVE_PARAMS = ("self", "cls")


LIST_LIKE_SHAPES: Set[int] = {
    fields.SHAPE_LIST,
    fields.SHAPE_SET,
    fields.SHAPE_TUPLE,
    fields.SHAPE_FROZENSET,
    fields.SHAPE_SEQUENCE,
}


class FuncType(Enum):
    FUNC_NORMAL = 0
    INSTANCE_FUNC = 1
    CLASS_FUNC = 2
    STATIC_FUNC = 3


class StatementType(Enum):
    ST_XML = 1  # xml 格式
    ST_SQL = 2  # sql 格式
