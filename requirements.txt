alembic==1.5.8
arrow==1.1.0
anyconfig==0.10.1
attrs==20.3.0
Babel==2.9.0
blinker==1.4
cachelib==0.1.1
certifi==2020.12.5
chardet==4.0.0
click==7.1.2
debtcollector==2.2.0
dynaconf==3.1.4
et-xmlfile==1.0.1
extras==1.0.0
fixtures==3.0.0
Flask==1.1.4
Flask-Babel==2.0.0
Flask-Caching==1.10.1
Flask-Cors==3.0.10
Flask-Limiter==1.4
Flask-Log-Request-ID==0.10.1
Flask-Login==0.5.0
Flask-Mail==0.9.1
Flask-Migrate==2.7.0
Flask-Script==2.0.6
Flask-Session==0.3.2
Flask-SQLAlchemy==2.5.1
greenlet==2.0.2
idna==2.10
importlib-metadata==4.0.1
iso8601==0.1.14
itsdangerous==1.1.0
jdcal==1.4.1
Jinja2==2.11.3
jsonschema==3.2.0
limits==1.5.1
linecache2==1.0.0
Mako==1.1.4
MarkupSafe==2.0.1
more-itertools==8.7.0
munch==2.5.0
netaddr==0.8.0
netifaces==0.10.9
nose==1.3.7
openpyxl==3.0.7
oslo.i18n==5.0.1
oslo.utils==4.8.0
ourbatis~=0.2.81
packaging==20.9
paramiko==2.7.2
pbr==5.5.1
Pillow==9.5.0
pluggy==0.13.1
psycopg2-binary==2.8.6
pycryptodome==3.10.1
pydantic==1.8.2
pyparsing==2.4.7
pypinyin==0.41.0
pyrsistent==0.17.3
python-dateutil==2.8.1
python-dotenv==0.17.1
python-editor==1.0.4
python-mimeparse==1.6.0
pytz==2021.1
PyYAML==6.0
redis==3.5.3
requests==2.27.1
simplegeneric==0.8.1
simplejson==3.17.2
six==1.15.0
SQLAlchemy==1.4.11
SQLAlchemy-Utils==0.37.0
sqlparse==0.4.1
testtools==2.4.0
toml==0.10.2
traceback2==1.4.0
typing-extensions==********
unittest2==1.1.0
urllib3==1.26.4

WebOb==1.8.7
Werkzeug==1.0.1
wrapt==1.12.1
WSME==0.10.1
wtypes==0.0.2
xlrd==2.0.1
XlsxWriter==1.4.0
zipp==3.4.1
paramiko==2.7.2
linezone-commonserver==0.1.91
fastboot==0.1.47
pymssql==2.2.8
PyMySQL==1.0.2
Deprecated==1.2.12
elasticsearch==7.13.1
line-profiler==4.0.2
pytest==6.2.4
python-redis-lock~=3.7.0
tenacity~=8.0.0
antlr4-python3-runtime==4.9.2
# skywalking相关
baselib~=0.1.8
protobuf==4.25.1
grpcio-tools==1.59.3
apache-skywalking[all]==1.0.1