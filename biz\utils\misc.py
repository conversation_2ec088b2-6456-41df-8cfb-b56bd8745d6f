# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.


"""
Miscellaneous tools used by OpenERP.
"""
import collections
import logging
import os
import socket
import subprocess
import sys
import threading
import time
import traceback
import types
from collections.abc import Iterable, Mapping, MutableMapping, MutableSet
from collections import  OrderedDict, defaultdict
from datetime import date, datetime
from functools import wraps
from itertools import chain
from typing import List, Any, Callable, Dict, Union, Optional

import arrow
from fastboot.utils.config import ConfigMeta
from fastboot.utils.nest import egg
from pydantic import BaseModel
from pypinyin import pinyin, Style

import biz

_logger = logging.getLogger(__name__)


# ----------------------------------------------------------
# Subprocesses
# ----------------------------------------------------------
def get_encodings(hint_encoding='utf-8'):
    fallbacks = {
        'latin1': 'latin9',
        'iso-8859-1': 'iso8859-15',
        'cp1252': '1252',
    }
    if hint_encoding:
        yield hint_encoding
        if hint_encoding.lower() in fallbacks:
            yield fallbacks[hint_encoding.lower()]

    # some defaults (also taking care of pure ASCII)
    for charset in ['utf8','latin1']:
        if not hint_encoding or (charset.lower() != hint_encoding.lower()):
            yield charset

    from locale import getpreferredencoding
    prefenc = getpreferredencoding()
    if prefenc and prefenc.lower() != 'utf-8':
        yield prefenc
        prefenc = fallbacks.get(prefenc.lower())
        if prefenc:
            yield prefenc


text_type = type(u'')
def ustr(value, hint_encoding='utf-8', errors='strict'):
    """This method is similar to the builtin `unicode`, except
    that it may try multiple encodings to find one that works
    for decoding `value`, and defaults to 'utf-8' first.

    :param value: the value to convert
    :param hint_encoding: an optional encoding that was detecte
        upstream and should be tried first to decode ``value``.
    :param str errors: optional `errors` flag to pass to the unicode
        built-in to indicate how illegal character values should be
        treated when converting a string: 'strict', 'ignore' or 'replace'
        (see ``unicode()`` constructor).
        Passing anything other than 'strict' means that the first
        encoding tried will be used, even if it's not the correct
        one to use, so be careful! Ignored if value is not a string/unicode.
    :raise: UnicodeError if value cannot be coerced to unicode
    :return: unicode string representing the given value
    """
    # We use direct type comparison instead of `isinstance`
    # as much as possible, in order to make the most common
    # cases faster (isinstance/issubclass are significantly slower)
    ttype = type(value)

    if ttype is text_type:
        return value

    # special short-circuit for str, as we still needs to support
    # str subclasses such as `odoo.tools.unquote`
    if ttype is bytes or issubclass(ttype, bytes):

        # try hint_encoding first, avoids call to get_encoding()
        # for the most common case
        try:
            return value.decode(hint_encoding, errors=errors)
        except Exception:
            pass

        # rare: no luck with hint_encoding, attempt other ones
        for ln in get_encodings(hint_encoding):
            try:
                return value.decode(ln, errors=errors)
            except Exception:
                pass

    # fallback for non-string values
    try:
        return text_type(value)
    except Exception:
        raise UnicodeError('unable to convert %r' % (value,))


def _exec_pipe(prog, args, env=None):
    cmd = (prog,) + args
    # on win32, passing close_fds=True is not compatible
    # with redirecting std[in/err/out]
    close_fds = os.name == "posix"
    pop = subprocess.Popen(cmd, bufsize=-1, stdin=subprocess.PIPE, stdout=subprocess.PIPE, close_fds=close_fds, env=env)
    return pop.stdin, pop.stdout


# def file_open(name, mode="r", subdir='addons', pathinfo=False):
#     """Open a file from the OpenERP root, using a subdir folder.
#
#     Example::
#
#     >>> file_open('hr/report/timesheer.xsl')
#     >>> file_open('addons/hr/report/timesheet.xsl')
#
#     @param name name of the file
#     @param mode file open mode
#     @param subdir subdirectory
#     @param pathinfo if True returns tuple (fileobject, filepath)
#
#     @return fileobject if pathinfo is False else (fileobject, filepath)
#     """
#     import biz.modules as addons
#     adps = addons.module.ad_paths
#     rtp = os.path.normcase(os.path.abspath(config['root_path']))
#
#     basename = name
#
#     if os.path.isabs(name):
#         # It is an absolute path
#         # Is it below 'addons_path' or 'root_path'?
#         name = os.path.normcase(os.path.normpath(name))
#         for root in adps + [rtp]:
#             root = os.path.normcase(os.path.normpath(root)) + os.sep
#             if name.startswith(root):
#                 base = root.rstrip(os.sep)
#                 name = name[len(base) + 1:]
#                 break
#         else:
#             # It is outside the OpenERP root: skip zipfile lookup.
#             base, name = os.path.split(name)
#         return _fileopen(name, mode=mode, basedir=base, pathinfo=pathinfo, basename=basename)
#
#     if name.replace(os.sep, '/').startswith('addons/'):
#         subdir = 'addons'
#         name2 = name[7:]
#     elif subdir:
#         name = os.path.join(subdir, name)
#         if name.replace(os.sep, '/').startswith('addons/'):
#             subdir = 'addons'
#             name2 = name[7:]
#         else:
#             name2 = name
#
#     # First, try to locate in addons_path
#     if subdir:
#         for adp in adps:
#             try:
#                 return _fileopen(name2, mode=mode, basedir=adp,
#                                  pathinfo=pathinfo, basename=basename)
#             except IOError:
#                 pass
#
#     # Second, try to locate in root_path
#     return _fileopen(name, mode=mode, basedir=rtp, pathinfo=pathinfo, basename=basename)
#
#
# def _fileopen(path, mode, basedir, pathinfo, basename=None):
#     name = os.path.normpath(os.path.normcase(os.path.join(basedir, path)))
#
#     import biz.modules as addons
#     paths = addons.module.ad_paths + [config['root_path']]
#     for addons_path in paths:
#         addons_path = os.path.normpath(os.path.normcase(addons_path)) + os.sep
#         if name.startswith(addons_path):
#             break
#     else:
#         raise ValueError("Unknown path: %s" % name)
#
#     if basename is None:
#         basename = name
#     # Give higher priority to module directories, which is
#     # a more common case than zipped modules.
#     if os.path.isfile(name):
#         if 'b' in mode:
#             fo = open(name, mode)
#         else:
#             fo = io.open(name, mode, encoding='utf-8')
#         if pathinfo:
#             return fo, name
#         return fo
#
#     # Support for loading modules in zipped form.
#     # This will not work for zipped modules that are sitting
#     # outside of known addons paths.
#     head = os.path.normpath(path)
#     zipname = False
#     while os.sep in head:
#         head, tail = os.path.split(head)
#         if not tail:
#             break
#         if zipname:
#             zipname = os.path.join(tail, zipname)
#         else:
#             zipname = tail
#         zpath = os.path.join(basedir, head + '.zip')
#         if zipfile.is_zipfile(zpath):
#             zfile = zipfile.ZipFile(zpath)
#             try:
#                 fo = io.BytesIO()
#                 fo.write(zfile.read(os.path.join(
#                     os.path.basename(head), zipname).replace(
#                     os.sep, '/')))
#                 fo.seek(0)
#                 if pathinfo:
#                     return fo, name
#                 return fo
#             except Exception:
#                 pass
#     # Not found
#     if name.endswith('.rml'):
#         raise IOError('Report %r does not exist or has been deleted' % basename)
#     raise IOError('File not found: %s' % basename)


# ----------------------------------------------------------
# iterables
# ----------------------------------------------------------
def flatten(list):
    """Flatten a list of elements into a unique list
    Author: Christophe Simonis (<EMAIL>)

    Examples::
    >>> flatten(['a'])
    ['a']
    >>> flatten('b')
    ['b']
    >>> flatten( [] )
    []
    >>> flatten( [[], [[]]] )
    []
    >>> flatten( [[['a','b'], 'c'], 'd', ['e', [], 'f']] )
    ['a', 'b', 'c', 'd', 'e', 'f']
    >>> t = (1,2,(3,), [4, 5, [6, [7], (8, 9), ([10, 11, (12, 13)]), [14, [], (15,)], []]])
    >>> flatten(t)
    [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
    """
    r = []
    for e in list:
        if isinstance(e, (bytes, str)) or not isinstance(e, collections.Iterable):
            r.append(e)
        else:
            r.extend(flatten(e))
    return r


def reverse_enumerate(l):
    """Like enumerate but in the other direction
    
    Usage::
    >>> a = ['a', 'b', 'c']
    >>> it = reverse_enumerate(a)
    >>> it.next()
    (2, 'c')
    >>> it.next()
    (1, 'b')
    >>> it.next()
    (0, 'a')
    >>> it.next()
    Traceback (most recent call last):
      File "<stdin>", line 1, in <module>
    StopIteration
    """
    return zip(range(len(l) - 1, -1, -1), reversed(l))


def partition(pred, elems):
    """ Return a pair equivalent to:
        ``filter(pred, elems), filter(lambda x: not pred(x), elems)` """
    yes, nos = [], []
    for elem in elems:
        (yes if pred(elem) else nos).append(elem)
    return yes, nos


def topological_sort(elems):
    """ Return a list of elements sorted so that their dependencies are listed
    before them in the result.

    :param elems: specifies the elements to sort with their dependencies; it is
        a dictionary like `{element: dependencies}` where `dependencies` is a
        collection of elements that must appear before `element`. The elements
        of `dependencies` are not required to appear in `elems`; they will
        simply not appear in the result.

    :returns: a list with the keys of `elems` sorted according to their
        specification.
    """
    # the algorithm is inspired by [Tarjan 1976],
    # http://en.wikipedia.org/wiki/Topological_sorting#Algorithms
    result = []
    visited = set()

    def visit(n):
        if n not in visited:
            visited.add(n)
            if n in elems:
                # first visit all dependencies of n, then append n to result
                for it in elems[n]:
                    visit(it)
                result.append(n)

    for el in elems:
        visit(el)

    return result


def to_xml(s):
    return s.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')


def logged(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        from pprint import pformat

        vector = ['Call -> function: %r' % f]
        for i, arg in enumerate(args):
            vector.append('  arg %02d: %s' % (i, pformat(arg)))
        for key, value in kwargs.items():
            vector.append('  kwarg %10s: %s' % (key, pformat(value)))

        timeb4 = time.time()
        res = f(*args, **kwargs)

        vector.append('  result: %s' % pformat(res))
        vector.append('  time delta: %s' % (time.time() - timeb4))
        _logger.debug('\n'.join(vector))
        return res

    return wrapper


def detect_ip_addr():
    """Try a very crude method to figure out a valid external
       IP or hostname for the current machine. Don't rely on this
       for binding to an interface, but it could be used as basis
       for constructing a remote URL to the server.
    """

    def _detect_ip_addr():
        from array import array
        from struct import pack, unpack

        try:
            import fcntl
        except ImportError:
            fcntl = None

        ip_addr = None

        if not fcntl:  # not UNIX:
            host = socket.gethostname()
            ip_addr = socket.gethostbyname(host)
        else:  # UNIX:
            # get all interfaces:
            nbytes = 128 * 32
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            names = array('B', '\0' * nbytes)
            # print 'names: ', names
            outbytes = unpack('iL', fcntl.ioctl(s.fileno(), 0x8912, pack('iL', nbytes, names.buffer_info()[0])))[0]
            namestr = names.tostring()

            # try 64 bit kernel:
            for i in range(0, outbytes, 40):
                name = namestr[i:i + 16].split('\0', 1)[0]
                if name != 'lo':
                    ip_addr = socket.inet_ntoa(namestr[i + 20:i + 24])
                    break

            # try 32 bit kernel:
            if ip_addr is None:
                ifaces = [namestr[i:i + 32].split('\0', 1)[0] for i in range(0, outbytes, 32)]

                for ifname in [iface for iface in ifaces if iface if iface != 'lo']:
                    ip_addr = socket.inet_ntoa(fcntl.ioctl(s.fileno(), 0x8915, pack('256s', ifname[:15]))[20:24])
                    break

        return ip_addr or 'localhost'

    try:
        ip_addr = _detect_ip_addr()
    except Exception:
        ip_addr = 'localhost'
    return ip_addr


DEFAULT_SERVER_DATE_FORMAT = "%Y-%m-%d"
DEFAULT_SERVER_TIME_FORMAT = "%H:%M:%S"
DEFAULT_SERVER_DATETIME_FORMAT = "%s %s" % (
    DEFAULT_SERVER_DATE_FORMAT,
    DEFAULT_SERVER_TIME_FORMAT)


# port of python 2.6's attrgetter with support for dotted notation
def resolve_attr(obj, attr):
    for name in attr.split("."):
        obj = getattr(obj, name)
    return obj


def attrgetter(*items):
    if len(items) == 1:
        attr = items[0]

        def g(obj):
            return resolve_attr(obj, attr)
    else:
        def g(obj):
            return tuple(resolve_attr(obj, attr) for attr in items)
    return g


# ---------------------------------------------
# String management
# ---------------------------------------------

_ph = object()


class CountingStream(object):
    """ Stream wrapper counting the number of element it has yielded. Similar
    role to ``enumerate``, but for use when the iteration process of the stream
    isn't fully under caller control (the stream can be iterated from multiple
    points including within a library)

    ``start`` allows overriding the starting index (the index before the first
    item is returned).

    On each iteration (call to :meth:`~.next`), increases its :attr:`~.index`
    by one.

    .. attribute:: index

        ``int``, index of the last yielded element in the stream. If the stream
        has ended, will give an index 1-past the stream
    """

    def __init__(self, stream, start=-1):
        self.stream = iter(stream)
        self.index = start
        self.stopped = False

    def __iter__(self):
        return self

    def next(self):
        if self.stopped: raise StopIteration()
        self.index += 1
        val = next(self.stream, _ph)
        if val is _ph:
            self.stopped = True
            raise StopIteration()
        return val

    __next__ = next


class ConstantMapping(Mapping):
    """
    An immutable mapping returning the provided value for every single key.

    Useful for default value to methods
    """
    __slots__ = ['_value']

    def __init__(self, val):
        self._value = val

    def __len__(self):
        """
        defaultdict updates its length for each individually requested key, is
        that really useful?
        """
        return 0

    def __iter__(self):
        """
        same as len, defaultdict updates its iterable keyset with each key
        requested, is there a point for this?
        """
        return iter([])

    def __getitem__(self, item):
        return self._value


def dumpstacks(sig=None, frame=None):
    """ Signal handler: dump a stack trace for each existing thread."""
    code = []

    def extract_stack(stack):
        for filename, lineno, name, line in traceback.extract_stack(stack):
            yield 'File: "%s", line %d, in %s' % (filename, lineno, name)
            if line:
                yield "  %s" % (line.strip(),)

    # code from http://stackoverflow.com/questions/132058/getting-stack-trace-from-a-running-python-application#answer-2569696
    # modified for python 2.5 compatibility
    threads_info = {th.ident: {'repr': repr(th),
                               'uid': getattr(th, 'uid', 'n/a'),
                               'dbname': getattr(th, 'dbname', 'n/a'),
                               'url': getattr(th, 'url', 'n/a')}
                    for th in threading.enumerate()}
    for threadId, stack in sys._current_frames().items():
        thread_info = threads_info.get(threadId, {})
        code.append("\n# Thread: %s (db:%s) (uid:%s) (url:%s)" %
                    (thread_info.get('repr', threadId),
                     thread_info.get('dbname', 'n/a'),
                     thread_info.get('uid', 'n/a'),
                     thread_info.get('url', 'n/a')))
        for line in extract_stack(stack):
            code.append(line)

    if biz.evented:
        # code from http://stackoverflow.com/questions/12510648/in-gevent-how-can-i-dump-stack-traces-of-all-running-greenlets
        import gc
        from greenlet import greenlet
        for ob in gc.get_objects():
            if not isinstance(ob, greenlet) or not ob:
                continue
            code.append("\n# Greenlet: %r" % (ob,))
            for line in extract_stack(ob.gr_frame):
                code.append(line)

    _logger.info("\n".join(code))


def freehash(arg):
    try:
        return hash(arg)
    except Exception:
        if isinstance(arg, Mapping):
            return hash(frozendict(arg))
        elif isinstance(arg, Iterable):
            return hash(frozenset(freehash(item) for item in arg))
        else:
            return id(arg)


def clean_context(context):
    """ This function take a dictionary and remove each entry with its key starting with 'default_' """
    return {k: v for k, v in context.items() if not k.startswith('default_')}


class frozendict(dict):
    """ An implementation of an immutable dictionary. """

    def __delitem__(self, key):
        raise NotImplementedError("'__delitem__' not supported on frozendict")

    def __setitem__(self, key, val):
        raise NotImplementedError("'__setitem__' not supported on frozendict")

    def clear(self):
        raise NotImplementedError("'clear' not supported on frozendict")

    def pop(self, key, default=None):
        raise NotImplementedError("'pop' not supported on frozendict")

    def popitem(self):
        raise NotImplementedError("'popitem' not supported on frozendict")

    def setdefault(self, key, default=None):
        raise NotImplementedError("'setdefault' not supported on frozendict")

    def update(self, *args, **kwargs):
        raise NotImplementedError("'update' not supported on frozendict")

    def __hash__(self):
        return hash(frozenset((key, freehash(val)) for key, val in self.items()))


class Collector(Mapping):
    """ A mapping from keys to lists. This is essentially a space optimization
        for ``defaultdict(list)``.
    """
    __slots__ = ['_map']

    def __init__(self):
        self._map = {}

    def add(self, key, val):
        vals = self._map.setdefault(key, [])
        if val not in vals:
            vals.append(val)

    def __getitem__(self, key):
        return self._map.get(key, ())

    def __iter__(self):
        return iter(self._map)

    def __len__(self):
        return len(self._map)


class StackMap(MutableMapping):
    """ A stack of mappings behaving as a single mapping, and used to implement
        nested scopes. The lookups search the stack from top to bottom, and
        returns the first value found. Mutable operations modify the topmost
        mapping only.
    """
    __slots__ = ['_maps']

    def __init__(self, m=None):
        self._maps = [] if m is None else [m]

    def __getitem__(self, key):
        for mapping in reversed(self._maps):
            try:
                return mapping[key]
            except KeyError:
                pass
        raise KeyError(key)

    def __setitem__(self, key, val):
        self._maps[-1][key] = val

    def __delitem__(self, key):
        del self._maps[-1][key]

    def __iter__(self):
        return iter({key for mapping in self._maps for key in mapping})

    def __len__(self):
        return sum(1 for key in self)

    def __str__(self):
        return u"<StackMap %s>" % self._maps

    def pushmap(self, m=None):
        self._maps.append({} if m is None else m)

    def popmap(self):
        return self._maps.pop()


class OrderedSet(MutableSet):
    """ A set collection that remembers the elements first insertion order. """
    __slots__ = ['_map']

    def __init__(self, elems=()):
        self._map = OrderedDict((elem, None) for elem in elems)

    def __contains__(self, elem):
        return elem in self._map

    def __iter__(self):
        return iter(self._map)

    def __len__(self):
        return len(self._map)

    def add(self, elem):
        self._map[elem] = None

    def discard(self, elem):
        self._map.pop(elem, None)


class LastOrderedSet(OrderedSet):
    """ A set collection that remembers the elements last insertion order. """

    def add(self, elem):
        OrderedSet.discard(self, elem)
        OrderedSet.add(self, elem)


def groupby(iterable, key=None):
    """ Return a collection of pairs ``(key, elements)`` from ``iterable``. The
        ``key`` is a function computing a key value for each element. This
        function is similar to ``itertools.groupby``, but aggregates all
        elements under the same key, not only consecutive elements.
    """
    if key is None:
        key = lambda arg: arg
    groups = defaultdict(list)
    for elem in iterable:
        groups[key(elem)].append(elem)
    return groups.items()


def wrap_module(module, attr_list):
    """Helper for wrapping a package/module to expose selected attributes

       :param Module module: the actual package/module to wrap, as returned by ``import <module>``
       :param iterable attr_list: a global list of attributes to expose, usually the top-level
            attributes and their own main attributes. No support for hiding attributes in case
            of name collision at different levels.
    """
    attr_list = set(attr_list)

    class WrappedModule(object):
        def __getattr__(self, attrib):
            if attrib in attr_list:
                target = getattr(module, attrib)
                if isinstance(target, types.ModuleType):
                    return wrap_module(target, attr_list)
                return target
            raise AttributeError(attrib)

    # module and attr_list are in the closure
    return WrappedModule()


def sort_and_groupby(models: List[BaseModel], key: Callable) -> Dict[Any, Any]:
    models = sorted(models, key=key)
    result_map = {
        key_item: list(model_items)
        for key_item, model_items in groupby(models, key)
    }
    return result_map


def get_second_to_tomorrow() -> int:
    now = arrow.now()
    tomorrow = now.shift(days=1).replace(hour=0, minute=0, second=0, microsecond=0)
    delta = tomorrow - now
    return delta.seconds

@egg
class MixConfig:
    DEV_TEST_CURRENT_DAY_DATE: Union[date, str, None] = ConfigMeta(name="dev_test_config.current_day_date",
                                                                   default=None,
                                                                   config_type=Union[date, str, None])


def get_real_day_date(day_date: Optional[datetime] = None) -> datetime:

    if type(MixConfig.DEV_TEST_CURRENT_DAY_DATE) is date:
        real_day_date = datetime.combine(MixConfig.DEV_TEST_CURRENT_DAY_DATE, datetime.min.time())
    else:
        real_day_date = day_date if day_date else datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

    return real_day_date


def to_pinyin(s):
    if not isinstance(s, list) and not isinstance(s, str):
        s = str(s)
    return ''.join(chain.from_iterable(pinyin(s, style=Style.TONE3)))
