#! ../env/bin/python
# -*- coding: utf-8 -*-
import base64
import json
import logging
import os
from pathlib import Path

from dotenv import load_dotenv, find_dotenv
from fastboot.utils.config import init_config, get_virtual_code
from fastboot.utils.nest import init_eggs
from redis import Redis

from biz.api_exception import ServerError
from biz.third_party.config_center.handler import ConfigCenterHandler
from config.config_manager import Settings

Logger = logging.getLogger(__name__)

# 基础目录
basedir = Path(__file__).resolve().parents[1]
# 加载.env
load_dotenv(find_dotenv())


def find_rsa_key():
    """
    调用配置中心的接口获取公私钥信息
    :return:
    """

    # 公私钥信息
    data = ConfigCenterHandler.find_rsa_key()

    return data


class Setting:
    """
    配置
    """

    def __init__(self, data):
        self._dict = data

    def get(self, key: str, nullable=False):
        """
        读取配置值
        :param key:
        :param nullable: 是否允许该值为空
        :return:
        """

        msg_header = "++++++ Setting::get "
        val = self._dict.get(key, None)
        if not (val is not None or nullable):
            Logger.error(msg_header + "租户实例的值不能为空，配置的key：{key}".format(key=key))
            config_json = json.dumps(self._dict, ensure_ascii=False)
            Logger.error(msg_header + "租户实例的配置为：{config_json}".format(config_json=config_json))
            raise ServerError()

        return val


def init_system_config():
    config_alias_names = {
        "tenant_instance.db.gp.host": "DB_HOST",
        "tenant_instance.db.gp.port": "DB_PORT",
        "tenant_instance.db.gp.db_name": "DB_NAME",
        "tenant_instance.db.gp.user_name": "DB_USERNAME",
        "tenant_instance.db.gp.pwd": "DB_PWD",
    }
    init_config(config_alias_names=config_alias_names)


class Config(object):
    init_eggs()
    init_system_config()

    IP_WHITELIST = []
    PAGE_SIZE = 10
    VERSION = Settings.TENANT_VERSION
    SECRET_KEY = "393a5eff-5ce4-4510-ab28-6aaef17a19d1"

    # 优先从环境变量读取，.env加入gitignore
    PERMANENT_SESSION_LIFETIME = int(os.getenv("PERMANENT_SESSION_LIFETIME", 0)) or 60 * 60 * 5  # 5h (seconds)

    DEBUG = int(os.getenv("DEBUG", 0))

    # mail config
    MAIL_SERVER = "smtp.qiye.aliyun.com"
    MAIL_USE_SSL = True
    MAIL_USE_TLS = False
    MAIL_PORT = 465
    MAIL_USERNAME = "<EMAIL>"
    MAIL_PASSWORD = "SPTshengchan1"

    # 租户号
    APP_ID = Settings.APP_ID
    # 租户实例端口
    APP_PORT = os.getenv("APP_PORT")

    # 租户编码
    TENANT_CODE = Settings.TENANT_CODE
    # 虚拟环境编码
    VIRTUAL_CODE = get_virtual_code()

    # 读取配置信息方式  0：配置中心 1：本地文件
    # 默认为从配置中心获取
    READ_CONFIG_MODE = 1

    # 告警邮件配置
    MAIL_ALARM_RECEIVER = Settings.MAIL_ALARM_RECEIVER

    # 租户实例的Redis连接信息
    _db_redis_host = Settings.CACHE_HOST
    _db_redis_port = Settings.CACHE_PORT
    _db_redis_pwd = Settings.CACHE_PWD
    _db_redis_db = Settings.CACHE_DB

    APP_REDIS_URL = "redis://:{pwd}@{host}:{port}/{db}".format(
        host=_db_redis_host, port=_db_redis_port, pwd=_db_redis_pwd, db=_db_redis_db
    )

    # set  cache
    CACHE_TYPE = "redis"
    CACHE_REDIS_HOST = _db_redis_host
    CACHE_REDIS_PORT = _db_redis_port
    CACHE_REDIS_DB = _db_redis_db
    CACHE_REDIS_PASSWORD = _db_redis_pwd
    CACHE_OPTIONS = dict(socket_connect_timeout=5, socket_timeout=5)
    # for multi app on same cache store
    CACHE_KEY_PREFIX = "cache:{}:".format(APP_ID)

    # set session
    SESSION_TYPE = "redis"
    SESSION_REDIS = Redis(
        host=_db_redis_host,
        db=_db_redis_db,
        port=_db_redis_port,
        socket_connect_timeout=5,
        socket_timeout=5,
        password=_db_redis_pwd,
    )

    # set i18n
    BABEL_DEFAULT_LOCALE = "zh"
    BABEL_DEFAULT_TIMEZONE = "UTC"

    # 各中心的连接配置
    # 导入导出服务
    EXPORT_CENTER_HOST = Settings.EXPORT_CENTER_HOST
    EXPORT_CENTER_PORT = Settings.EXPORT_CENTER_PORT
    EXPORT_CENTER_URL = "http://{}:{}/".format(EXPORT_CENTER_HOST, EXPORT_CENTER_PORT)
    # 租户中心
    TENANT_CENTER_HOST = Settings.TENANT_CENTER_HOST
    TENANT_CENTER_PORT = Settings.TENANT_CENTER_PORT

    # 租户实例GP数据库
    DB_GP_TENANT_INSTANCE_HOST = Settings.DB_HOST
    DB_GP_TENANT_INSTANCE_PORT = Settings.DB_PORT
    DB_GP_TENANT_INSTANCE_USER_NAME = Settings.DB_USERNAME
    DB_GP_TENANT_INSTANCE_PWD = Settings.DB_PWD
    DB_GP_TENANT_INSTANCE_DB_NAME = Settings.DB_NAME

    # 新框架依赖的约定配置项
    DB_HOST = DB_GP_TENANT_INSTANCE_HOST
    DB_PORT = DB_GP_TENANT_INSTANCE_PORT
    DB_USERNAME = DB_GP_TENANT_INSTANCE_USER_NAME
    DB_PWD = DB_GP_TENANT_INSTANCE_PWD
    DB_NAME = DB_GP_TENANT_INSTANCE_DB_NAME
    TENANT_ID = APP_ID
    APP_NAME = f"tenant_instance_{APP_ID}"

    SQLALCHEMY_DATABASE_URI = SQLALCHEMY_DATABASE_GP_URI = "postgresql://{user_name}:{pwd}@{host}:{port}/{db_name}".format(
        user_name=DB_GP_TENANT_INSTANCE_USER_NAME,
        pwd=DB_GP_TENANT_INSTANCE_PWD,
        host=DB_GP_TENANT_INSTANCE_HOST,
        port=DB_GP_TENANT_INSTANCE_PORT,
        db_name=DB_GP_TENANT_INSTANCE_DB_NAME,
    )

    # 连接池配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {"pool_size": 30, "pool_timeout": 0, "pool_recycle": 10, "max_overflow": -1}
    SQLALCHEMY_ECHO = Settings.SQLALCHEMY_ECHO
    SQLALCHEMY_BINDS = {"appmeta": SQLALCHEMY_DATABASE_URI}
    ALEMBIC_SKIP_BIND = ["appmeta"]

    # 日志路径
    LOG_DIR = str(basedir / "logs")
    # 中心名称
    CENTER_NAME = "MODULE_INSTANCE"
    # LOG_FORMAT = "%(color)s%(asctime)s - {center} - %(name)s[line:%(lineno)d] - %(levelname)s: %(message)s".format(
    #     center=CENTER_NAME
    # )
    LOG_FORMAT = (
        "%(color)s%(asctime)s.%(msecs)03d - process:%(process)d - center:{center} "
        "- request_id:%(request_id)s - name:%(name)s - funcName:%(funcName)s "
        "- line:[line:%(lineno)d] - levelname:%(levelname)s - msg:%(message)s".format(center=CENTER_NAME)
    )
    # LOG_DEFAULT_LEVELS = ["sqlalchemy=INFO", "stevedore=INFO", "werkzeug=INFO"]
    LOG_DEFAULT_LEVELS = Settings.LOG_DEFAULT_LEVELS
    LOG_LEVEL = Settings.LOG_LEVEL

    # 模型地址信息
    MODEL_SERVER_HOST = Settings.MODEL_SERVER_HOST
    MODEL_SERVER_PORT = Settings.MODEL_SERVER_PORT
    MODEL_SERVER_USERNAME = Settings.MODEL_SERVER_USERNAME
    MODEL_SERVER_PASSWORD = Settings.MODEL_SERVER_PASSWORD
    MODEL_SERVER_PATH = Settings.MODEL_SERVER_PATH

    # 后端地址信息
    TENANT_SERVER_HOST = Settings.TENANT_SERVER_HOST
    TENANT_SERVER_POST = Settings.TENANT_SERVER_PORT

    TENANT_SOCKET_SERVER_API = Settings.TENANT_SOCKET_SERVER_API

    TENANT_IS_SHOW_PUT_ON_RECORD = Settings.TENANT_IS_SHOW_PUT_ON_RECORD
    TENANT_SKIN_CONF = Settings.TENANT_SKIN_CONF

    # 默认接口限流
    RATELIMIT_DEFAULT = "150000 per day,50000 per hour"

    # 公私钥信息
    rsa_data = ConfigCenterHandler.find_rsa_key()
    RSA_PRIVATE_KEY = base64.b64decode(rsa_data.get("private_key"))
    RSA_PUBLIC_KEY = base64.b64decode(rsa_data.get("public_key"))

    # obs skc_image_url信息
    OBS_SKC_IMAGE_URL = Settings.OBS_SKC_IMAGE_URL

    # 临时开关
    DELETE_DAY_DATE_RECORD = Settings.DELETE_DAY_DATE_RECORD  # 删除决策日的所有数据
    GEN_STOCK_ZIPPER = Settings.GEN_STOCK_ZIPPER  # 生成全天zipper表
    GEN_STOCK_SNAPSHOT = Settings.GEN_STOCK_SNAPSHOT  # 生成库存快照表
    RUN_MODEL_SERVER = Settings.RUN_MODEL_SERVER  # 触发模型运行
    CHECK_SAVE_AUTH = Settings.CHECK_SAVE_AUTH  # 保存流通计划时,不做鉴权

    PARAMETER_URL = Settings.PARAMETER_URL
    READ_URL = Settings.READ_URL
    WRITE_URL = Settings.WRITE_URL

    UPLOAD_ROOT = "./uploads"

    API_CONCURRENT_LIMIT = 1

    # 是否调用租户中心的开关
    SWITCH_CALL_TENANT_CENTER = Settings.SWITCH_CALL_TENANT_CENTER
    # 租户购买的模块
    TENANT_INSTANCE_PURCHASED_MODULES = Settings.TENANT_INSTANCE_PURCHASED_MODULES
    # 租户购买的模块页面
    TENANT_INSTANCE_PURCHASED_MODULE_PAGES = Settings.TENANT_INSTANCE_PURCHASED_MODULE_PAGES

    # ES
    ES_HOST = Settings.ES_HOST
    ES_INDEX = Settings.ES_INDEX

    BATCH_SIZE = Settings.BATCH_SIZE

    def __init__(self):
        self.options = {"custom_p": "custom_module"}
        self.options["root_path"] = os.path.abspath(os.path.expanduser(os.path.expandvars(os.path.dirname(__file__))))

        default_addons = []
        self.options["base_addons"] = os.path.join(self.options["root_path"], "biz_module")
        if os.path.exists(self.options["base_addons"]):
            default_addons.append(self.options["base_addons"])
        main_addons = os.path.abspath(os.path.join(self.options["root_path"], "../" + "biz_module"))
        if os.path.exists(main_addons):
            default_addons.append(main_addons)
        self.options["custom_addons"] = os.path.abspath(
            os.path.join(self.options["root_path"], "../" + self.options["custom_p"])
        )
        if os.path.exists(self.options["custom_addons"]):
            default_addons.append(self.options["custom_addons"])
        self.options["addons_path"] = ",".join(default_addons)

        self.options["app_id"] = self.APP_ID
        self.options["app_port"] = self.APP_PORT
        if self.APP_ID:
            biz = "tenant_{}_biz".format(self.TENANT_CODE)
            sys = "tenant_{}_sys".format(self.TENANT_CODE)
            edw = "tenant_{}_edw".format(self.TENANT_CODE)
            dm = "tenant_{}_dm".format(self.TENANT_CODE)
            adm = "tenant_{}_adm".format(self.TENANT_CODE)
        else:
            biz = "biz"
            sys = "sys"
            edw = "edw"
            dm = "dm"
            adm = "adm"

        # 租户中心那边有约定不允许
        if str(self.APP_ID).__contains__("_"):
            tenant_code = str(self.APP_ID).split("_")[0]
            self.options["skc_image_url"] = self.OBS_SKC_IMAGE_URL.format(tenant_code)
        else:
            self.options["skc_image_url"] = self.OBS_SKC_IMAGE_URL.format(self.APP_ID)

        self.options["schema_constant"] = dict(
            schema_name=biz,
            SCHEMA_NAME=biz,
            system_schema_name=sys,
            SYSTEM_SCHEMA_NAME=sys,
            edw_schema_name=edw,
            EDW_SCHEMA_NAME=edw,
            dm_schema_name=dm,
            DM_SCHEMA_NAME=dm,
            adm_schema_name=adm,
            ADM_SCHEMA_NAME=adm,
        )

    def __getitem__(self, key):
        return self.options[key]


config = Config()
