# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import ast
import imp
import logging
import os
import re
import sys
import types
from os.path import join as opj

import pkg_resources

import biz
from biz import config
# from biz.http import blueprint_module_info, controllers_per_blueprint
from biz.utils.misc import ustr

MANIFEST_NAMES = ('__manifest__.py', )

_logger = logging.getLogger(__name__)

# addons path as a list
ad_paths = []
hooked = False

# Modules already loaded
# loaded = []
loaded = {}
blueprint_module_info = {}  # 存储module的info信息，用于模块的蓝图启用禁用


class AddonsHook(object):
    """ Makes modules accessible through openerp.addons.* and odoo.addons.* """

    def find_module(self, name, path=None):
        if name.startswith(('biz.biz_module.', 'biz.custom_module.')) and name.count('.') == 2:
            return self

    def load_module(self, name, *args, **kwargs):
        # assert name not in sys.modules

        # get canonical names
        biz_name = re.sub(r'^biz.custom_module.(\w+)$', r'biz.biz_module.\g<1>', name)
        custom_name = re.sub(r'^biz.biz_module.(\w+)$', r'biz.custom_module.\g<1>', name)

        assert biz_name not in sys.modules
        assert custom_name not in sys.modules

        # get module name in addons paths
        _1, _2, addon_name = name.split('.')
        # load module
        f, path, (_suffix, _mode, type_) = imp.find_module(addon_name, ad_paths)
        # except Exception as e:
        #     print(e)
        if f: f.close()

        # TODO: fetch existing module from sys.modules if reloads permitted
        # create empty odoo.addons.* module, set name
        new_mod = types.ModuleType(biz_name)
        new_mod.__loader__ = self

        # module top-level can only be a package
        assert type_ == imp.PKG_DIRECTORY, "Odoo addon top-level must be a package"
        modfile = opj(path, '__init__.py')
        new_mod.__file__ = modfile
        new_mod.__path__ = [path]
        new_mod.__package__ = biz_name

        # both base and alias should be in sys.modules to handle recursive and
        # corecursive situations
        sys.modules[biz_name] = sys.modules[custom_name] = new_mod

        # execute source in context of module *after* putting everything in
        # sys.modules, so recursive import works
        exec(open(modfile, 'rb').read(), new_mod.__dict__)

        # people import openerp.addons and expect openerp.addons.<module> to work
        setattr(biz.biz_module, addon_name, new_mod)

        return sys.modules[name]


# need to register loader with setuptools as Jinja relies on it when using
# PackageLoader
pkg_resources.register_loader_type(AddonsHook, pkg_resources.DefaultProvider)


class AddonsPageHook(object):
    """ Makes modules accessible through openerp.addons.* and odoo.addons.* """

    def find_module(self, name, path=None):
        if name.startswith(('biz.biz_module.', 'biz.custom_module.')) and name.count('.') == 3:
            self.path = path
            return self

    def load_module(self, name):
        # assert name not in sys.modules

        # get canonical names
        biz_name = re.sub(r'^biz.custom_module.(\w+)$', r'biz.biz_module.\g<1>', name)
        custom_name = re.sub(r'^biz.biz_module.(\w+)$', r'biz.custom_module.\g<1>', name)

        # assert biz_name not in sys.modules
        # assert custom_name not in sys.modules

        # get module name in addons paths
        _1, _2, _3, addon_name = name.split('.')
        # load module
        f, path, (_suffix, _mode, type_) = imp.find_module(addon_name, self.path)
        if f: f.close()

        # create empty odoo.addons.* module, set name
        new_mod = types.ModuleType(biz_name)
        new_mod.__loader__ = self

        assert type_ == imp.PKG_DIRECTORY, "Odoo addon top-level must be a package"
        modfile = opj(path, '__init__.py')
        new_mod.__file__ = modfile
        new_mod.__path__ = [path]
        new_mod.__package__ = biz_name

        sys.modules[biz_name] = sys.modules[custom_name] = new_mod
        exec(open(modfile, 'rb').read(), new_mod.__dict__)
        setattr(biz.biz_module, name.split('.', 2)[2], new_mod)

        return sys.modules[name]


def initialize_sys_path():
    """
    Setup an import-hook to be able to import OpenERP addons from the different
    addons paths.

    This ensures something like ``import crm`` (or even
    ``import odoo.addons.crm``) works even if the addons are not in the
    PYTHONPATH.
    """
    global ad_paths
    global hooked
    # dd = os.path.normcase(tools.config.addons_data_dir)
    # if os.access(dd, os.R_OK) and dd not in ad_paths:
    #     ad_paths.append(dd)

    for ad in config['addons_path'].split(','):
        ad = os.path.normcase(os.path.abspath(ustr(ad.strip())))
        if ad not in ad_paths:
            ad_paths.append(ad)

    # add base module path
    base_path = os.path.normcase(
        os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'biz_module')))
    os.makedirs(base_path, exist_ok=True)
    if base_path not in ad_paths and os.path.isdir(base_path):
        ad_paths.append(base_path)

    # add odoo.addons.__path__
    for ad in __import__('biz.biz_module').biz_module.__path__:
        ad = os.path.abspath(ad)
        if ad not in ad_paths and os.path.isdir(ad):
            ad_paths.append(ad)

    if not hooked:
        # sys.meta_path.insert(0, OdooHook())
        sys.meta_path.insert(0, AddonsHook())
        sys.meta_path.insert(0, AddonsPageHook())
        hooked = True


def get_module_path(module, downloaded=False, display_warning=True):
    """Return the path of the given module.

    Search the addons paths and return the first path where the given
    module is found. If downloaded is True, return the default addons
    path if nothing else is found.

    """
    initialize_sys_path()
    for adp in ad_paths:
        files = [opj(adp, module, manifest) for manifest in MANIFEST_NAMES]
        if any(os.path.exists(f) for f in files):
            return opj(adp, module)

    if downloaded:
        return opj(config['base_addons'], module)
    if display_warning:
        _logger.warning('module %s: module not found', module)
    return False


def module_manifest(path):
    """Returns path to module manifest if one can be found under `path`, else `None`."""
    if not path:
        return None
    for manifest_name in MANIFEST_NAMES:
        if os.path.isfile(opj(path, manifest_name)):
            return opj(path, manifest_name)


def to_native(source, encoding='utf-8'):
    if not source:
        return ''
    if isinstance(source, bytes):
        return source.decode(encoding)
    return str(source)


def load_information_from_description_file(module, mod_path=None):
    """
        info里字段以及应用中心模块清单表对应关系：
            description： 模块中文名(module_name_cn)
            version：     模块版本，以及产品版本（暂定）(module_version)
            module：      模块英文名，即目录名(module_name_en)

            module_code、parent_code： 随机自增生成

    :param module: The name of the module (sale, purchase, ...)
    :param mod_path: Physical path of module, if not providedThe name of the module (sale, purchase, ...)
    """
    if not mod_path:
        mod_path = get_module_path(module, downloaded=True)
    manifest_file = module_manifest(mod_path)
    if manifest_file:
        # default values for descriptor
        info = {
            'depends': [],
            'description': '',
            'post_load': None,
            'version': '1.0',
            'is_display': False,
            'installable': True

        }
        info.update(zip(
            'depends data'.split(),
            iter(list, None)))

        f = open(manifest_file, mode='rb')
        try:
            info.update(ast.literal_eval(to_native(f.read())))
        except Exception as e:
            print('read info error: ', e)
        finally:
            f.close()
        # info['version'] = adapt_version(info['version'])
        return info

    _logger.debug('module %s: no manifest file found %s', module, MANIFEST_NAMES)
    return {}


def load_openerp_module(module_name, module_path=None, reload=False):
    """ Load an OpenERP module, if not already loaded.

    This loads the module and register all of its models, thanks to either
    the MetaModel metaclass, or the explicit instantiation of the model.
    This is also used to load server-wide module (i.e. it is also used
    when there is no model to register).
    """
    global loaded
    if module_name in loaded:
        return
    initialize_sys_path()
    if reload:
        # TODO: !!! update modules -- 20191116
        relational_m = (m for m in list(sys.modules.keys()) if module_name in m)
        list(map(sys.modules.pop, relational_m))

    __import__('biz.biz_module.' + module_name)

    try:
        # Call the module's post-load hook. This can done before any model or
        # data has been initialized. This is ok as the post-load hook is for
        # server-wide (instead of registry-specific) functionalities.

        info = load_information_from_description_file(module_name, module_path)
        # TODO: !!! 恢复模块备份时，加载完要先更新同步info信息（保存在loaded里），然后在进行钩子操作

        if info['post_load']:
            getattr(sys.modules['biz.biz_module.' + module_name], info['post_load'])(info)

    except Exception as e:
        msg = "Couldn't load module %s" % (module_name,)
        print(msg)
        _logger.critical(msg)
        _logger.critical(e)
        raise
    else:
        loaded[module_name] = info
        page_name = module_name.split('.')[-1]
        if info and info.get('is_display', False):
            blueprint_module_info[page_name] = info
        # else:
        #     blueprint_module_info.pop(page_name, None)
        #     controllers_per_blueprint.pop(page_name, None)


def get_modules():
    """Returns the list of module names
    """

    def listdir(dir):
        def clean(name):
            name = os.path.basename(name)
            if name[-4:] == '.zip':
                name = name[:-4]
            return name

        def is_really_module(name):
            for mname in MANIFEST_NAMES:
                if os.path.isfile(opj(dir, name, mname)):
                    return True

        return [
            clean(it)
            for it in os.listdir(dir)
            if is_really_module(it)
        ]

    plist = []
    initialize_sys_path()
    for ad in ad_paths:
        plist.extend(listdir(ad))
    return list(set(plist))


def get_pages(mod_path=None):
    """Returns the list of module names
    """

    def listdir(dir):
        _, module_name = os.path.split(dir)

        def clean(name):
            name = os.path.basename(name)
            if name[-4:] == '.zip':
                name = name[:-4]
            return os.path.join(dir, name), "%s.%s" % (module_name, name)

        def is_really_module(name):
            for mname in MANIFEST_NAMES:
                if os.path.isfile(opj(dir, name, mname)):
                    return True

        return [
            clean(it)
            for it in os.listdir(dir)
            if is_really_module(it)
        ]

    plist = []
    # initialize_sys_path()
    for ad in mod_path if isinstance(mod_path, list) else [mod_path]:
        plist.extend(listdir(ad))
    return list(set(plist))
