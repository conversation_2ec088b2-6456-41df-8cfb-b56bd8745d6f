from linezone_commonserver.services.feature.constants import FeatureCalculationEnum
from pydantic import BaseModel
from typing import Optional, List, Union
from biz_module.base.page_config.constants import ROLE_PAGE_ID


class GetConfigByIDReq(BaseModel):
    page_id: int


class ConfigEgg(BaseModel):
    page_id: int
    module_name: Optional[str]
    page_name: Optional[str]
    content: Optional[dict]
    status: Optional[int]
    creator: Optional[int]
    config_name: Optional[str]
    set_type: Optional[str]
    remark: Optional[str]
    config_type: Optional[str]


class GetConfigListReq(BaseModel):
    user_id: Optional[int]
    module_name: Optional[str]
    page_name: Optional[str]
    is_tenant: Optional[bool] = False
    is_product: Optional[bool] = False
    is_common: Optional[bool] = False


class GetDimValueReq(BaseModel):
    page_name: str


class SaveReq(BaseModel):
    page_id: int
    content: Optional[str]
    role_page_id: Optional[Union[int, str]] = ROLE_PAGE_ID


class SaveForReq(BaseModel):
    module_name: Optional[str]
    page_name: Optional[str]
    content: Optional[str]
    remark: Optional[str]
    config_name: Optional[str]
    creator: Optional[int]
    modifier: Optional[int]
    set_type: Optional[str]
    config_type: Optional[str]
    type_code: Optional[int]
    role_page_id: Optional[Union[int, str]] = ROLE_PAGE_ID


class CheckSysConf(BaseModel):
    creator: Optional[int]
    page_id: Optional[int]


class ListFeatureReq(BaseModel):
    module_name: Optional[str]
    feature_calculation_type: Optional[list] = [FeatureCalculationEnum.PRE.value]
    dimension_codes: Optional[list] = ['skc_sk', 'org_sk']
    feature_type: Optional[list] = ['tag']


class FeatureToConfigReq(BaseModel):
    feature_code: Optional[str]
    module_name: Optional[str] = 'stock_up'
