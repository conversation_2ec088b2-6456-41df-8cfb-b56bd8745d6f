import re
from typing import Any

from ourbatis.exceptions import (
    ForeachCollection<PERSON>iss,
    ForeachItemNameMiss,
    EvalFailed,
)
from ourbatis.safe_eval import save_eval
from ourbatis.sql_source_parser.objects import ForeachValueInfo, SQLMapper
from ourbatis.sql_source_parser.params import Param, basic_convert_parameters, get_param_attr
from ourbatis.sql_source_parser.sql_context import SqlContext
from ourbatis.utils import logger

QUERY_TYPES = ["sql", "select", "insert", "update", "delete", "mix"]
NON_WHITE = re.compile(r"\S")
PARAM_PATTERN = re.compile(fr":[a-zA-Z_][0-9a-zA-Z_]*")
INCLUDE_PARAM_PATTERN = re.compile(r"(?P<key>[0-9A-Za-z_]+)\s*=\s*(?P<value>[^,)]+)")


def convert_children(sql_mapper: SQLMapper, child, context: SqlContext):
    """
    Get children info
    """
    if child.tag in QUERY_TYPES:
        return convert_parameters(child, text=True, tail=True, context=context)
    elif child.tag == "include":
        return convert_include(sql_mapper, child, context)
    elif child.tag == "if":
        return convert_if(sql_mapper, child, context)
    elif child.tag in ("choose", "when", "otherwise"):
        return convert_choose_when_otherwise(sql_mapper, child, context)
    elif child.tag in ("trim", "where", "set"):
        return convert_trim_where_set(sql_mapper, child, context)
    elif child.tag == "foreach":
        return convert_foreach(sql_mapper, child, context)
    elif child.tag == "bind":
        return convert_bind(child, context)
    else:
        return ""


def get_include_params(param: Param, context: SqlContext):
    find_params = INCLUDE_PARAM_PATTERN.findall(param.mock_value)
    for find_param in find_params:
        context.inner_gen_params[find_param[0]] = find_param[1]


def covert_meta_parameters(_: str, param: Param, context: SqlContext) -> str:
    sql_mapper = context.sql_mapper
    include_child = sql_mapper.get(param.name)
    get_include_params(param, context)
    include_content = convert_parameters(include_child, text=True, context=context)
    for next_child in include_child:
        include_content += convert_children(sql_mapper, next_child, context)
    param.mock_value = include_content
    return param.mock_value


def convert_parameters(child, text=False, tail=False, context: SqlContext = None):
    """
    Get child text or tail
    """
    # Remove empty info
    child_text = child.text if child.text else ""
    child_tail = child.tail if child.tail else ""
    child_text = child_text if NON_WHITE.search(child_text) else ""
    child_tail = child_tail if NON_WHITE.search(child_tail) else ""
    # all
    if text and tail:
        convert_string = child_text + child_tail
    # only_text
    elif text:
        convert_string = child_text
    # only_tail
    elif tail:
        convert_string = child_tail
    else:
        convert_string = ""
    # replace params
    convert_string, params = basic_convert_parameters(convert_string, context)
    for param in params["@"]:
        covert_meta_parameters(convert_string, param, context)
        convert_string = convert_string.replace(param.full_name, param.mock_value)

    convert_cdata(convert_string)
    return convert_string


def convert_include(sql_mapper: SQLMapper, child, context: SqlContext):
    # Add Properties
    properties = (
        context.input_params.get("properties")
        if context.input_params.get("properties")
        else dict()
    )
    for next_child in child:
        if next_child.tag == "property":
            properties[next_child.attrib.get("name")] = next_child.attrib.get("value")
    convert_string = ""
    include_child_id = child.attrib.get("refid")
    for change in ["#", "$"]:
        string_regex = fr"\{change}{{.+?}}"
        if re.match(string_regex, include_child_id):
            include_child_id = include_child_id.replace(change + "{", "").replace(
                "}", ""
            )
            include_child_id = properties.get(include_child_id)
            break
    include_child = sql_mapper.get(include_child_id)
    convert_string += convert_children(sql_mapper, include_child, context)
    # add include text
    convert_string += convert_parameters(child, context=context, text=True)
    for next_child in include_child:
        context.input_params["properties"] = properties
        convert_string += convert_children(sql_mapper, next_child, context)
    # add include tail
    convert_string += convert_parameters(child, context=context, tail=True)
    return convert_string


def convert_if(sql_mapper, child, context: SqlContext):
    convert_string = ""
    test = child.attrib.get("test")

    success, result = save_eval(test, context.params)

    if not (success and result):
        logger_func = logger.info if success else logger.error
        logger_func(f"test_expression [{test}] eval result [{result}]")
    else:
        # Add if text
        convert_string += convert_parameters(child, text=True, context=context)
        for next_child in child:
            convert_string += convert_children(sql_mapper, next_child, context)
        if not context.no_annotate:
            convert_string += "-- if(" + test + ")\n"
    # Add if tail
    convert_string += convert_parameters(child, tail=True, context=context)
    return convert_string


def convert_choose_when_otherwise(sql_mapper, child, context: SqlContext):
    convert_string = ""
    when_element_cnt = 0
    for next_child in child:
        if when_element_cnt >= 1:
            break
        if next_child.tag == "when":
            test = next_child.attrib.get("test")
            success, result = save_eval(test, context.input_params)
            if not (success and result):
                logger_func = logger.info if success else logger.error
                logger_func(f"test_expression [{test}] eval result [{result}]")
                continue
            convert_string += convert_parameters(
                next_child, text=True, tail=True, context=context
            )
            if context.no_annotate:
                convert_string += "-- if(" + test + ")"
            when_element_cnt += 1
        elif next_child.tag == "otherwise":
            convert_string += convert_parameters(
                next_child, text=True, tail=True, context=context
            )
            if context.no_annotate:
                convert_string += "-- otherwise"
        convert_string += convert_children(sql_mapper, next_child, context)
    return convert_string


def convert_trim_where_set(sql_mapper, child, context: SqlContext):
    if child.tag == "trim":
        prefix = child.attrib.get("prefix")
        suffix = child.attrib.get("suffix")
        prefix_overrides = child.attrib.get("prefixOverrides")
        suffix_overrides = child.attrib.get("suffixOverrides")
    elif child.tag == "set":
        prefix = "SET"
        suffix = None
        prefix_overrides = None
        suffix_overrides = ","
    elif child.tag == "where":
        prefix = "WHERE"
        suffix = None
        prefix_overrides = "and|or"
        suffix_overrides = None
    else:
        return ""

    convert_string = ""
    # Add trim/where/set text
    convert_string += convert_parameters(child, text=True, context=context)
    # Convert children first
    for next_child in child:
        convert_string += convert_children(sql_mapper, next_child, context)
    # Remove prefixOverrides
    if prefix_overrides:
        regex = fr"^[\s]*?({prefix_overrides})"
        convert_string = re.sub(regex, "", convert_string, count=1, flags=re.I)
    # Remove suffixOverrides
    if suffix_overrides:
        regex = fr"({suffix_overrides})(\s*--.+)?$"
        convert_string = re.sub(regex, r"", convert_string, count=1, flags=re.I)

    # Add Prefix if String is not empty
    if NON_WHITE.search(convert_string):
        if prefix:
            convert_string = prefix + " " + convert_string
        if suffix:
            convert_string = convert_string + " " + suffix

    # Add trim/where/set tail
    convert_string += convert_parameters(child, tail=True, context=context)
    return convert_string


def gen_inner_params(inner_item_name: str, item_name: str, item: Any, context: SqlContext):
    if attr_names := context.foreach_temp_values[item_name].attr_names:
        for attr in attr_names:
            real_inner_item_name = f"{inner_item_name}_{attr}"
            item_attr = get_param_attr(item, attr)
            context.inner_gen_params.update({real_inner_item_name: item_attr})
    else:
        context.inner_gen_params.update({inner_item_name: item})


def convert_foreach(sql_mapper, child, context: SqlContext):
    collection = child.attrib.get("collection")
    item_name = child.attrib.get("item")
    index_name = child.attrib.get("index")
    open_ = child.attrib.get("open", "")
    close = child.attrib.get("close", "")
    separator = child.attrib.get("separator", "")
    convert_string = open_

    # todo 合理做法 for循环块应该重新一个上下文块, 并实现上下文链.否则不支持嵌套作用域.
    # todo 暂时公司用到这么复杂的场景, 先不实现
    collection_data = context.input_params.get(collection, context.inner_gen_params.get(collection, None))
    if collection_data is None:
        raise ForeachCollectionMiss(collection)
    if item_name is None:
        raise ForeachItemNameMiss()

    foreach_strings = []
    param_pattern = re.compile(fr"(?<=:){item_name}")
    for index, item in enumerate(collection_data):
        inner_item_name = f"{item_name}_{index}"
        # todo 带属性的参数与正常参数处理方式差异化了，后续考虑统一
        context.inner_gen_params.update({item_name: item})  # 供内部运行提供的参数列表
        if index_name:
            context.input_params.update({index_name: index})
        context.foreach_temp_values.update({item_name: ForeachValueInfo()})
        param_str = convert_parameters(child, text=True, context=context)

        for next_child in child:
            param_str += convert_children(sql_mapper, next_child, context)

        replace_param = param_pattern.sub(inner_item_name, param_str)
        if NON_WHITE.search(replace_param):
            foreach_strings.append(replace_param)
            gen_inner_params(inner_item_name, item_name, item, context)  # todo 临时解决方案，待参数整体重构

    convert_string += separator.join(foreach_strings)
    convert_string += close
    # Add foreach tail
    convert_string += convert_parameters(child, tail=True, context=context)

    return convert_string


def convert_bind(child, context: SqlContext):
    name = child.attrib.get("name")
    value = child.attrib.get("value")
    success, value_result = save_eval(value, context.input_params)
    if success:
        context.inner_gen_params.update({name: value_result})
        convert_string = ""
        convert_string += convert_parameters(child, tail=True, context=context)
        return convert_string
    else:
        raise EvalFailed(f"get bind {value} failed! by result {value_result}")


def convert_cdata(string, reverse=False):
    """
    Replace CDATA String
    :param string:
    :param reverse:
    :return:
    """
    if reverse:
        string = string.replace("&", "&amp;")
        string = string.replace("<", "&lt;")
        string = string.replace(">", "&gt;")
        string = string.replace('"', "&quot;")
    else:
        string = string.replace("&amp;", "&")
        string = string.replace("&lt;", "<")
        string = string.replace("&gt;", ">")
        string = string.replace("&quot;", '"')
    return string
