from pathlib import Path
from typing import Union, List, Generator

from ourbatis.const import DEFAULT_NS, UNSAFE_NS
from ourbatis.exceptions import SqlIDNamingConflictError, SqlIDMissError


class BaseParser:
    SOURCE_SUFFIX = tuple()

    @staticmethod
    def get_sql_statement(
        sql_mapper: "SQLMapper", child_id: str, context: "SqlContext"
    ) -> str:
        raise NotImplementedError

    @classmethod
    def create_mapper(cls, source_file: Union[Path, str]) -> "SessionSQLMapper":
        raise NotImplementedError

    @classmethod
    def create_mapper_by_statement(
        cls, statement: str, namespace: str = DEFAULT_NS
    ) -> "Session":
        raise NotImplementedError

    @classmethod
    def check_addon_ns(cls, ns_addon_mapper, ns_mapper, namespace: str):
        for sql_id, element in ns_addon_mapper.items():
            if sql_id is None:
                raise SqlIDMissError(element.meta.file_name, element.element.text)
            elif (namespace != UNSAFE_NS) and (sql_id in ns_mapper):
                raise SqlIDNamingConflictError(
                    sql_id,
                    ns_mapper[sql_id].meta.file_name,
                    element.meta.file_name,
                )

    @classmethod
    def merge_sql_mapper(
        cls, session_sql_mapper: "SessionSQLMapper", addon_mapper: "SessionSQLMapper"
    ):
        # 合并命名空间内sql
        for namespace in session_sql_mapper:
            ns_mapper = session_sql_mapper[namespace]
            ns_addon_mapper = addon_mapper.get(namespace, {})
            cls.check_addon_ns(ns_addon_mapper, ns_mapper, namespace)
            ns_mapper.update(ns_addon_mapper)
            addon_mapper.pop(namespace, None)

        # 合并入的命名空间
        session_sql_mapper.update(addon_mapper)

    @classmethod
    def find_parse_files(
        cls, recursive: bool, source_dir_path: Union[List, str, Path, None]
    ) -> Generator[str, None, None]:
        if not isinstance(source_dir_path, list):
            source_dir_path = [source_dir_path]

        path_glob = "**/*{suffix}" if recursive else "*{suffix}"
        for dir_path in source_dir_path:
            path = Path(dir_path)
            if path.is_file() and path.suffix in cls.SOURCE_SUFFIX:
                yield path

            elif path.is_dir():
                for suffix in cls.SOURCE_SUFFIX:
                    yield from (
                        sql_file
                        for sql_file in path.glob(path_glob.format(suffix=suffix))
                    )

    @classmethod
    def gen_sql_mapper(
        cls,
        source_path: Union[Path, str] = None,
        source_dir_path: Union[List, Path, str, None] = None,
        recursive: bool = False,
    ):
        session_sql_mapper: "SessionSQLMapper" = {}
        if source_path:
            session_sql_mapper = cls.create_mapper(source_file=source_path)

        if source_dir_path:
            for source_file in cls.find_parse_files(recursive, source_dir_path):
                source_mapper = cls.create_mapper(source_file=source_file)
                cls.merge_sql_mapper(session_sql_mapper, source_mapper)

        return session_sql_mapper

    @classmethod
    def inject_sql_statement(
        cls,
        statement: str,
        current_sql_mapper: "SessionSQLMapper",
        namespace: str = DEFAULT_NS,
    ):
        source_mapper = cls.create_mapper_by_statement(statement, namespace)
        cls.merge_sql_mapper(current_sql_mapper, source_mapper)
        return current_sql_mapper

    @classmethod
    def deject_sql_statement(
        cls,
        sql_id: str,
        current_sql_mapper: "SessionSQLMapper",
        namespace: str = UNSAFE_NS,
    ):
        if namespace != UNSAFE_NS:
            return
        # 当前只支持 UNSAFE_NS 空间内的sql卸载
        current_sql_mapper.get(namespace, {}).pop(sql_id, None)
