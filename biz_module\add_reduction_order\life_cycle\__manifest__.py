#!/usr/bin/env python
# encoding: utf-8

{
    "module_code": "700700",
    "parent_code": "700000",
    "module_name_en": "repeat_order1",
    "parent_name_en": "quick_response",
    "module_version": "1.0",
    "module_name_cn": "翻单",
    "module_type": 0,
    "page_info": {
        "page_id": 700700,  # 页面id
        "page_name": "翻单",  # 页面tab名称
        "page_route": "/config_center/business_action_table",  # 页面路由
        "page_shot_route": "business_action_table",  # 页面短路由
        "weight": 100,  # 页面权重，与页面展示顺序有关
        "parent_id": 700000,  # 父页面（一级菜单）
        "icon": "icon-RectangleCopy2",
    },
    "is_enable": True,  # 是否启用该模块
    "is_display": True,
    "is_custom": False,
    "table_depends": [],
    "post_load": "",
}
