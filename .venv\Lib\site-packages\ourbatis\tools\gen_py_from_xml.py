from ourbatis.session import SqlSession
from ourbatis.sql_source_parser.objects import SessionSQLMapper

PYTHON_TEMPLATE = """from ourbatis import sql_func

@sql_func
class DefaultClass:
    
"""

if __name__ == "__main__":
    global_mapper: SessionSQLMapper = SqlSession.gen_sql_mapper(
        xml_path="../../tests/chinook.xml", xml_dir_path=None
    )
    func_list = []
    for mapper in global_mapper.values():
        for func_name in mapper.keys():
            func_list.append(f"\tdef {func_name}(self):\n\t\t...")
    template = PYTHON_TEMPLATE + "\n\n".join(func_list)
    print(template)
