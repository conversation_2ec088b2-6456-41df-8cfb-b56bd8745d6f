# encoding: utf-8
from flask import request

from biz import http
from biz.typedef import Result, AnyResult
from biz_module.common.biz_view.service.biz_view_service import BizViewService
from biz_module.common.biz_view.typedef import BizViewAndDimensionRequest


class BizViewController(http.Controller):
    """
    业务视图
    """

    @http.route("/api/biz-view/construct", methods=["POST"])
    def construct_biz_view(self):
        """
        保存业务视图
        :return:
        """
        construct_body = BizViewAndDimensionRequest(**request.json)
        data = BizViewService().construct(construct_body)
        return Result(data=data).dict()

    @http.route("/api/biz-view/list", methods=["POST"])
    def list_biz_view(self):
        """
        获取业务视图
        :return:
        """
        view_combine_list = BizViewService().list_biz_view_combine()
        return AnyResult(data=view_combine_list).dict()

    @http.route("/api/biz-view/attribute/list", methods=["POST"])
    def list_biz_view_attribute(self):
        """
        获取业务视图属性
        :return:
        """
        biz_view_dimension_id = request.json.get("biz_view_dimension_id")
        feature_id = request.json.get("feature_id", 0)
        view_combine_list = BizViewService().list_attributes(biz_view_dimension_id, feature_id)
        return AnyResult(data=view_combine_list).dict()

    @http.route("/api/v5.8.5/biz-view/kpi-name/list", methods=["POST"])
    def list_biz_view_kpi_names(self):
        """
        获取业务视图指标名字列表
        :return:
        """
        biz_view_id = request.json.get("biz_view_id")
        kpi_names = BizViewService().list_biz_view_kpi_names(biz_view_id)
        return AnyResult(data=kpi_names).dict()
