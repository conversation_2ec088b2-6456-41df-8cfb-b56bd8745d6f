#!/usr/bin/env python
# _*_ coding: utf-8 _*_

"""
@author: yang<PERSON><PERSON><PERSON>
@license: Copyright 2017-2020, LineZoneData.
@contact: yangzhi<PERSON><PERSON>@linezonedata.com
@software: pycharm
@time: 2019/11/8 10:36
@desc: 
"""

import requests
import logging

from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from biz import config

logger = logging.getLogger()


def requests_retry_session(retries=3,
                           backoff_factor=0.3,
                           status_forcelist=(500, 502, 503, 504),
                           session: requests.Session = None):
    """
    :param retries: 重试次数
    :param backoff_factor: 在重试期间用于计算延时的 backoff 因子
    :param status_forcelist: 需要重试的http状态码
    :param session:
    :return:
    """
    session = session or requests.Session()
    retry = Retry(
        total=retries,
        read=retries,
        connect=retries,
        backoff_factor=backoff_factor,
        status_forcelist=status_forcelist,
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session


requests = requests_retry_session()
