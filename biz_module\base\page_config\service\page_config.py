from operator import itemgetter
from typing import Dict
from collections import namedtuple

from flask_login import current_user
from fastboot.utils.nest import egg
from linezone_commonserver.services.feature.constants import FeatureCalculationEnum

from biz import BaseService
from biz_module.base.page_config.dao.page_config import PageConfigDao
from biz_module.base.page_config.typedef import page_config as tf_conf
from biz.api_exception import ParameterException
from biz_module.base.page_config.constants import ModuleEnum, PageEnum, SetTypeEnum, ConfigType
from biz_module.common.feature_mapping import DimensionMappingReq, create_include_type_condition, FeatureMappingService


class PageConfigService(BaseService):
    MODULE_NAME = 'replenish_allot'
    PAGE_NAME = 'ra_decision'

    def __init__(self):
        super(PageConfigService, self).__init__()
        self.dao = PageConfigDao()

    def get_config(self, params: tf_conf.GetConfigByIDReq):
        """
        根据page_config_id 查询配置数据

        Args:
            params (pydandic obj):
                page_id (int):页面配置id
        """
        if not params.page_id or not isinstance(params.page_id, int):
            err_msg = 'page_id不可以为空'
            raise ParameterException(msg=err_msg)
        page_config = self.dao.get_page_conf_by_id(params)
        if not page_config:
            err_msg = '配置不存在'
            raise ParameterException(msg=err_msg)
        order_content = page_config.content
        excel_fields = page_config.content.get('excel_fields', [])
        fields = page_config.content.get('fields', [])
        filters = page_config.content.get('filters', [])
        if excel_fields and all(['serial_number' in i for i in excel_fields]):
            order_content['excel_fields'] = sorted(excel_fields, key=itemgetter('serial_number'))
        if fields and all(['serial_number' in i for i in fields]):
            order_content['fields'] = sorted(fields, key=itemgetter('serial_number'))
        if filters and all(['serial_number' in i for i in filters]):
            order_content['filters'] = sorted(filters, key=itemgetter('serial_number'))

        # 操作按钮是否禁用
        button_enable = self.decide_button_enable(page_config.config_type)

        pc_dict = {
            "id": page_config.page_id,
            "page_name": page_config.page_name,
            "module_name": page_config.module_name,
            "module_cn_name": ModuleEnum.get_cn_name(page_config.module_name),
            "page_cn_name": PageEnum.get_cn_name(page_config.page_name),
            "set_type": page_config.set_type,
            "set_type_name": SetTypeEnum.get_cn_name(page_config.set_type),
            "remark": page_config.remark,
            "content": order_content,
            "config_name": page_config.config_name,
            # 决定操作按钮是否禁用
            "enable_save": button_enable.enable_save,
            "enable_save_for": button_enable.enable_save_for,
            "enable_delete": button_enable.enable_delete
        }
        return pc_dict

    def decide_button_enable(self, config_type):
        user_type_code = current_user.type_code
        enable = namedtuple('enable', ['enable_save', 'enable_save_for', 'enable_delete'])
        if user_type_code == 1:
            if config_type == ConfigType.PRODUCT.value:
                return enable(enable_save=False, enable_save_for=True, enable_delete=False)
            else:
                return enable(enable_save=True, enable_save_for=True, enable_delete=True)
        else:
            if config_type in (ConfigType.PRODUCT.value, ConfigType.TENANT.value):
                return enable(enable_save=False, enable_save_for=True, enable_delete=False)
            else:
                return enable(enable_save=True, enable_save_for=True, enable_delete=True)

    def get_config_list(self, params: tf_conf.GetConfigListReq):
        """
        获取可选配置列表
        :params:
            module
            page
            set_type
        """
        if current_user.type_code == 1:
            # 能看到配置的级别
            params.is_product = True
            params.is_tenant = True
        else:
            # 能看到配置的级别
            params.is_common = True

        conf = self.dao.get_page_conf_list(params)

        conf_list = []
        for c in conf:
            conf_list.append({
                "page_id": c.page_id,
                "page_name": c.page_name,
                "page_cn_name": PageEnum.get_cn_name(c.page_name),
                "set_type": c.set_type,
                "set_type_name": SetTypeEnum.get_cn_name(c.set_type),
                "remark": c.remark,
                # "content": c.content,
                "config_name": c.config_name,
            })
        res = {
            "content": conf_list,
        }
        return res

    def get_dim_values(self, params: tf_conf.GetDimValueReq):
        """
        返回可配置筛选器默认值选项数据
            [{
                "page_name": "ra_decision",
                "brand_code": "004",
                "field_name": "scene_code",
                "option_name": "促销集货-调拨",
                "option_value": "13",
                "serial_number": "",
            }...]
        """
        data = self.dim_value_method_factory(params)()
        res = [dict(i) for i in data]
        return res

    def dim_value_method_factory(self, params: tf_conf.GetDimValueReq):
        """
        返回 对应页面的查询方法
        """
        method_dict = {
            "ra_decision": self.dao.get_ra_decision_dim_values,
        }

        res = method_dict.get(params.page_name)
        return res

    def save_for(self, params: tf_conf.SaveForReq):
        """
        另存为
        """
        params.set_type = 'custom'
        params.creator = current_user.user_id
        params.modifier = current_user.user_id
        params.type_code = current_user.type_code

        if current_user.type_code == 1:
            params.config_type = ConfigType.TENANT.value
        else:
            params.config_type = ConfigType.COMMON.value

        if self.dao.check_rep_name(params):
            raise ParameterException("配置名称重复！")

        page_id = self.dao.save_for_config(params)
        if params.page_name == 'ra_decision' and params.module_name == 'replenish_allot':
            from biz_module.replenish_allot.gto_instruct.service.order_detail_service import order_srv
            order_srv.calculate_feature_by_call_task()
        return {'page_id': page_id}

    def delete_conf(self, params: tf_conf.SaveReq):
        self.dao.delete_conf(params)
        return True

    def save(self, params: tf_conf.SaveReq) -> Dict:
        """
        修改 页面配置
        """
        conf_info = self.dao.get_page_conf_by_id(params)
        if not conf_info:
            raise ParameterException(msg="不存在此配置")

        page_id = self.dao.save_config(params)
        if conf_info.page_name == 'ra_decision' and conf_info.module_name == 'replenish_allot':
            from biz_module.replenish_allot.gto_instruct.service.order_detail_service import order_srv
            order_srv.calculate_feature_by_call_task()
        return {"page_id": page_id}

    @staticmethod
    def list_feature_by_dimension_codes(params: tf_conf.ListFeatureReq):
        req = DimensionMappingReq(dimension_codes=params.dimension_codes, is_cascaded=True)
        req.condition_items = [
            create_include_type_condition(
                "calculation_type", params.feature_calculation_type
            ),
            create_include_type_condition(
                "feature_type", params.feature_type
            )
        ]

        features = FeatureMappingService.from_dimension_codes(req)
        # 工厂模式根据功能模块进行特征过滤
        from .feature_register_filter import registered_filters
        if params.module_name in registered_filters:
            features = registered_filters[params.module_name](features)
        return [{'label': feature.feature_name, 'value': feature.feature_code} for feature in features]

    def feature_to_page_config(self, params: tf_conf.FeatureToConfigReq):
        feature_name = self.dao.get_feature_name_by_code(params.feature_code)

        feature_config_template = self.dao.get_feature_config_template(params)

        config = {}
        for k, v in feature_config_template.items():
            if isinstance(v, str):
                config[k] = v.format(feature_name=feature_name, feature_code=params.feature_code)
            else:
                config[k] = v
        return [{
            'config_type': 'fields',
            'config_content': config
        }]


page_config_service = PageConfigService()
