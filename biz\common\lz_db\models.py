# coding=utf-8
#
#  区域基础信息
# from datetime import datetime
from sqlalchemy import text

from biz.common.lz_db.base_db import db
from flask import json

from biz import config

CONSTANT = config['schema_constant']


class Base(db.Model):
    __abstract__ = True
    __table_args__ = {
        "extend_existing": True,
        'schema': CONSTANT["SYSTEM_SCHEMA_NAME"]
    }

    def keys(self):
        return self.fields

    def __getitem__(self, item):
        return getattr(self, item)

    def set_attrs(self, attrs_dict):
        for key, value in attrs_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def as_dict(self, include=None, exclude=()):
        """Convert all model's columns to dict."""
        data = {field: getattr(self, field)
                for field in self.__mapper__.columns.keys()
                if field not in exclude}
        if include:
            data.update(include)
        return data

    def to_json(self, include=None, exclude=None):
        return json.dumps(self.to_dict(include=include, exclude=exclude))

    @staticmethod
    def add(data):
        with db.auto_commit():
            db.session.add(data)

    def save(self, deferred_commit=False):
        with db.auto_commit():
            db.session.add(self)
            if not deferred_commit:
                db.session.commit()
            return self

    def delete(self):
        with db.auto_commit():
            db.session.delete(self)
            db.session.commit()

    @classmethod
    def all(cls):
        return db.session.query(cls).all()

    @classmethod
    def create(cls, **kwargs):
        return cls(**kwargs)

    @classmethod
    def get_or_create(cls, **kwargs):
        obj = cls.filter_by(**kwargs).first()
        if obj:
            return obj, False
        return cls.create(**kwargs), True

    @classmethod
    def get_objects_collection(cls, to_json=False):
        data = [obj.to_dict() for obj in db.session.query(cls).all()]
        if to_json:
            return json.dumps(data)
        return data


class BaseWithTime(Base):
    __abstract__ = True
    # 创建时间
    create_time = db.Column(db.DateTime, server_default=text('now()'), nullable=False)
    # 更新时间
    modify_time = db.Column(db.DateTime, server_default=text('now()'), nullable=False)


class BaseRemarkTime(BaseWithTime):
    __abstract__ = True
    # 是否有效
    is_active = db.Column(db.Boolean, server_default=text('true'), nullable=False)
    # 备注
    remark = db.Column(db.TEXT, nullable=True)
    # 扩展字段
    extra1 = db.Column(db.VARCHAR, nullable=True)
    extra2 = db.Column(db.VARCHAR, nullable=True)
