import threading
from functools import cached_property, partial
from typing import List, Dict, Any, <PERSON><PERSON>

import numpy as np
from flask import current_app
from linezone_commonserver.services.auth.org_service import CommonAuthOrgService
from linezone_commonserver.services.auth.typedef import UserAuthOrgReq
from linezone_commonserver.services.common.enums import OperatorTypeEnum, DataTypeEnum as CommonDataTypeEnum
from linezone_commonserver.services.feature.service import FeatureService
from linezone_commonserver.services.feature.typedef import FeatureEntity
from linezone_commonserver.services.meta_data.service import MetaDataService
from linezone_commonserver.utils.tools import make_tenant_table_schema, SqlSnippetHelper, SqlSnippetReq
from ourbatis import transaction_manager

from biz.base import BaseService
from fastboot.utils.nest import egg
import math
from biz.typedef import PageResultInfo
from biz_module.base.authorization.base.tools import page_permission_rw_checker
from biz_module.common.feature_workbench.dao import FeatureDao
from biz_module.common.feature_workbench import typedef as tf
from biz_module.common.feature_workbench.constants import (
    COLUMN_TYPE_DIM,
    COLUMN_TYPE_FEATURE,
    INSERT_CHUNK_SIZE,
    HUMAN_TAG_PAGE_ID,
    TEMP_DIM_COLUMN_CODE_SUFFIX,
)
from biz_module.common.feature_workbench.enums import DataTypeEnum
from ..tools import FeatureCategoryTree, pd_to_temp_table, try_drop_temp_table
from ..typedef import ListHumanTagReq, DynamicQueryDimDataReq, HumanTagTableTitleResponse, BatchInsertReq, MetaDataTableField
from flask_login import current_user
import pandas as pd
from ..utils.excel_tools.utils import ExcelColumn, HumanTagImportValidator
from biz.api_exception import ParameterException, ServerError

import json

import logging

from ...import_export.service.export import ExportService

logger = logging.getLogger(__name__)


def dict_json_sorted(value):
    temp = {}
    for key in sorted(value):
        temp[key] = value[key]
    return json.dumps(temp)


def concat_dimension_title(titles: List[str]):
    return "-".join(titles)


def make_on_expression(meta_fields: List[MetaDataTableField], other_field_name, meta_field_prop="name", temp_name="temp"):
    """
    构造关联语句
    Returns
    -------
    sql语句
    (feature_object->>'A')::integer=b.A and (feature_object->>'B')::integer=b.B

    Notes
    -------
    hotfix_5.8.1.5 修复维度数据类型默认为text类型

    """
    on_expressions = []
    for meta_field in meta_fields:
        attribute = getattr(meta_field, meta_field_prop)
        single_expression = f"({other_field_name}->>'{attribute}')::{meta_field.data_type}={temp_name}.{attribute}"
        on_expressions.append(single_expression)

    return " and ".join(on_expressions)


def transform_meta_data_table_field(table_field: tf.MetaDataTableField) -> tf.MetaDataTableField:
    """
    对元数据进行特殊转换
    目前包含对代理键的转换, is_dynamic字段不处理
    对所有代码统一小写(注意：目前只有动态组织可能由于历史原因存在大写字母问题)
    Parameters
    ----------
    table_field

    Returns
    -------

    """
    magic_str = "_sk"
    new_table_field = tf.MetaDataTableField(**table_field.dict())
    new_table_field.name = new_table_field.name.lower()
    is_contain_sk = magic_str in table_field.name
    if not is_contain_sk:
        new_table_field.customer_code = new_table_field.name
        return new_table_field

    # 处理包含sk的属性
    # 统一转为对应的code属性
    new_table_field.customer_code = table_field.name.replace(magic_str, "_code")
    new_table_field.title = table_field.title.replace("代理键", "")
    biz_title_suffix = "编码"
    if biz_title_suffix not in new_table_field.title:
        new_table_field.title += biz_title_suffix

    return new_table_field


@egg
class HumanFeatureService(BaseService):
    dao = FeatureDao()
    common_auth_org_service = CommonAuthOrgService()
    common_meta_data_service: MetaDataService
    common_feature_service: FeatureService
    required_columns = ["operating_unit_sk", "feature_code", "feature_value", "feature_code_object", "feature_object"]

    def query_views_by_human_tag(self):
        """
        视图列表

        1.查人工特征，关联的视图的所有粒度去重
        """
        queryset = self.dao.query_view_dimension_table_field_code()

        req = tf.QueryMetaDataTableFieldReq()

        meta_data_table_field_list = self.dao.query_meta_data_table_field(req=req)
        meta_data_table_field_map = {item.code: item for item in meta_data_table_field_list}
        result = []
        for i in queryset:
            values = i.table_field_code
            temp = []
            # 顺序是按照构造业务视图顺序展示的
            for j in values:
                map_item = transform_meta_data_table_field(meta_data_table_field_map[j])
                temp.append(map_item.title)

            result.append({"label": concat_dimension_title(temp), "values": values})

        return result

    def download_template_by_human_tag(self, req: tf.DownloadTemplateReq):
        """
        下载模板

        返回所有粒度+粒度对应的所有视图的特征
        """
        dim_objects = self._list_and_check_dim_object(req.code, is_transform=True)
        excel_headers = []
        dim_title = []
        for dim_object in dim_objects:
            excel_headers.append(dim_object.title)
            dim_title.append(dim_object.title)

        feature_objects = self._list_and_check_feature_object(req.feature_codes)
        for feature_object in feature_objects:
            excel_headers.append(feature_object.feature_name)

        excel_filename = f"{concat_dimension_title(dim_title)}特征模板"
        handler = ExportService(excel_name=excel_filename, ws_name=f"特征模板")
        table_header = [{"title": column, "key": column} for column in excel_headers]
        file = handler.generate_excel(table_header, [])
        return file, handler.file_name

    def dim_code_to_value_df(
        self, dim_object: tf.MetaDataTableField, dim_code_values: List[str], dim_column: ExcelColumn = None
    ) -> pd.DataFrame:
        """
        获取维度code到value的df
        value可能存在和code相同 比如大中小类
        value可能和code不一致   比如用户输入org_code value此时为 org_sk
        Parameters
        ----------
        dim_object:tf.MetaDataTableField 该参数后续优化
        dim_code_values:List[str] excel中导入的数据 统一为字符串数据类型
        dim_column:ExcelColumn 修复维度为int型bug引入
        Returns
        -------

        """
        table_name = self._get_fully_table_name_by_dim_object(dim_object)
        # code_field_name = "org_code" if dim_object.is_dynamic else dim_object.customer_code
        # def replace_special_char(str_value):
        #     # 对字符串中包含'进行转义为''
        #     return str_value if "'" not in str_value else str_value.replace("'", "''")

        # code_value_expression_generator = (
        #     f"{code_field_name}='{replace_special_char(dim_value)}'" for dim_value in dim_code_values
        # )
        code_field_name = dim_column.get_column_code()
        data_type_enum = CommonDataTypeEnum(dim_column.get_column_data_type().value)
        snippet_req = SqlSnippetReq(key=code_field_name, data_values=dim_code_values, data_type=data_type_enum)
        in_expression = SqlSnippetHelper.build_in_snippet(snippet_req)

        dynamic_req = DynamicQueryDimDataReq(
            table_name=table_name,
            code_field_name=code_field_name,
            other_required_field_name=dim_object.name,
            code_value_expression=in_expression,
        )

        code_value_df = self.dao.dynamic_query_dim_data(dynamic_req)
        code_value_df["code"] = code_value_df["code"].astype("str")
        return code_value_df

    def query_column_value_by_dim_object(self, dim_object: ExcelColumn, another_column_name, condition_value):
        """
        根据维度对象获取另外一个列的值
        Parameters
        ----------
        dim_object
        another_column_name
        condition_value

        Returns
        -------

        """

        table_name = self._get_fully_table_name_by_dim_object(dim_object)
        data_type_enum = CommonDataTypeEnum(dim_object.get_column_data_type().value)
        column_code = dim_object.get_column_code()
        req = SqlSnippetReq(
            key=column_code, data_values=condition_value, operator=OperatorTypeEnum.ANY, data_type=data_type_enum
        )
        any_array_sql_condition = SqlSnippetHelper.build_any_all_snippet(req)
        dynamic_req = DynamicQueryDimDataReq(
            table_name=table_name,
            code_field_name=column_code,
            other_required_field_name=another_column_name,
            code_value_expression=any_array_sql_condition,
        )

        code_value_df = self.dao.dynamic_query_dim_data(dynamic_req)
        code_value_df["code"] = code_value_df["code"].astype("str")
        # 当出现两个变量相同时候, 重命名一个
        if dim_object.column_code == another_column_name:
            return code_value_df.rename({"code": dim_object.column_code, "value": f"{dim_object.column_code}_1"}, axis=1)
        return code_value_df.rename({"code": dim_object.column_code, "value": another_column_name}, axis=1)

    def feature_code_to_value(self, feature_object, feature_values) -> List[str]:
        """"""
        feature = self.dao.query_feature_values_by_codes([feature_object.feature_code])
        if not feature:
            return []
        first = feature[0]
        return first.values

    @staticmethod
    def _get_fully_table_name_by_dim_object(dim_object):
        schema, table_name = dim_object.belong_table_name.split(".")
        valid_table_name = f"{make_tenant_table_schema(schema)}.{table_name}"
        return valid_table_name

    def _unify_excel_columns(self, dim_objects, feature_objects):
        """
        根据维度对象和特征对象构建统一表格列对象
        Parameters
        ----------
        dim_objects
        feature_objects

            data_type暂时均为字符串类型
        Returns
        -------
        List[ExcelColumn]

        """
        dim_columns = [
            ExcelColumn(
                column_title=dim_object.title,
                column_code=dim_object.customer_code,
                data_type=DataTypeEnum.TEXT,
                column_type=COLUMN_TYPE_DIM,
                is_required=True,
                is_value_required=True,
                original_obj=dim_object,
                get_code_value=partial(self.dim_code_to_value_df, dim_object),
            )
            for dim_object in dim_objects
        ]

        feature_columns = [
            ExcelColumn(
                column_title=feature_object.feature_name,
                column_code=feature_object.feature_code,
                data_type=feature_object.data_type_enum,
                column_type=COLUMN_TYPE_FEATURE,
                is_required=True,
                is_value_required=False,
                original_obj=feature_object,
                get_code_value=partial(self.feature_code_to_value, feature_object),
            )
            for feature_object in feature_objects
        ]

        return dim_columns + feature_columns

    @page_permission_rw_checker(HUMAN_TAG_PAGE_ID, "w")
    def import_data_by_human_tag(self, req: tf.ImportDataReq, file):
        """
        导入数据
        清空模式
              - 清空权限内所有
              - 插入有效特征列的数据(db中保存的是列转行的数据)
        非清空模式
              - 删除特征列中为null的数据
              - 插入有效特征列的数据(db中保存的是列转行的数据)
              需求文档描述：删除为空的特征 覆盖同维度 新增
        """
        dim_objects = self._list_and_check_dim_object(req.code, is_transform=True)
        feature_objects = self._list_and_check_feature_object(req.feature_codes)
        excel_columns = self._unify_excel_columns(dim_objects, feature_objects)

        auth_helper = HumanTagAuthHelper(req.operating_unit_sk, dim_objects)
        validator = HumanTagImportValidator(file, excel_columns, auth_helper, self)

        validator.check_dim_value_is_null()
        validator.check_dim_data_type()
        validator.check_column_value_is_valid()
        validator.check_dim_value_duplicated()
        if validator.has_errors:
            raise ParameterException(validator.err_msg)

        validator.check_dim_column_with_operating_unit(req.operating_unit_sk)
        validator.check_data_auth()
        # v580对导入指标数据类型进行校验
        if validator.has_errors:
            raise ParameterException(validator.err_msg)

        is_clear_all = req.deleteNull  # is_clear_all 更能表达清空含义
        temp_table_names = set()
        try:
            with transaction_manager():
                if is_clear_all:
                    self._clear_all_action(req, auth_helper)

                if not is_clear_all:
                    required_columns = [dim.name for dim in dim_objects]
                    required_columns.append("feature_code")
                    should_deleted_df = validator.get_melted_df_of_all_feature_value(required_columns)
                    clear_temp_table_name = self._not_clear_all_action(
                        req.operating_unit_sk, dim_objects, should_deleted_df, auth_helper.temp_table_name
                    )
                    if clear_temp_table_name:
                        temp_table_names.add(clear_temp_table_name)

                should_inserted_df = validator.get_should_insert_melted_data_df_with_needed_info()
                insert_temp_table_name = self.batch_insert_by_temp_table(req.operating_unit_sk, dim_objects, should_inserted_df)
                if insert_temp_table_name:
                    temp_table_names.add(insert_temp_table_name)

                mode = "清空模式" if is_clear_all else "非清空模式"
                logger.info(f"特征维护{mode}导入，{validator.dim_column_title_str}, 拆分后数据总条数:{len(should_inserted_df)}")
        finally:
            if temp_table_names:
                logger.info(temp_table_names)
                t = threading.Thread(
                    target=try_drop_temp_table, args=(list(temp_table_names), current_app._get_current_object())
                )
                t.start()

        ignore_index_msg = f"，忽略了{len(validator.null_row_index)}行空白记录" if validator.null_row_index else ""
        return f"成功导入{validator.import_data_number}条记录{ignore_index_msg}"

    def _clear_all_action(self, req, auth_helper):
        dynamic_condition = ListHumanTagReq(
            operating_unit_sk=req.operating_unit_sk,
            auth_orgs_sql_expression=auth_helper.auth_orgs_sql_expression,
            feature_codes=req.feature_codes,
            on_expression=auth_helper.on_with_extraction_expression,
            need_auth_flag=auth_helper.need_auth_flag,
        )
        # 清空权限内所有
        self.dao.batch_delete_authorized_human_tags(dynamic_condition)

    def _not_clear_all_action(self, operating_unit_sk, dim_objects, should_deleted_df, temp_alias):
        """
        非清空模式
        返回临时表名
        """
        should_deleted_temp_table_name = ""
        if not should_deleted_df.empty:
            dim_expression = make_on_expression(dim_objects, "feature_object")
            should_deleted_temp_table_name = pd_to_temp_table(should_deleted_df)
            self.dao.delete_human_tags_by_temp_table(
                should_deleted_temp_table_name, dim_expression, temp_alias=temp_alias, operating_unit_sk=operating_unit_sk
            )
        return should_deleted_temp_table_name

    def batch_insert_by_temp_table(self, operating_unit_sk, dim_objects, should_inserted_df):
        """
        通过临时表批量插入
        返回临时表名
        """
        title_name_mapper = {}
        feature_object_json_build_expressions = []
        feature_code_object_json_build_expressions = []
        should_inserted_temp_table_name = ""
        for dim in dim_objects:
            custom_column = dim.customer_code + TEMP_DIM_COLUMN_CODE_SUFFIX
            title_name_mapper[dim.title] = custom_column
            feature_object_pair = f"'{dim.name}', {dim.name}"
            feature_object_json_build_expressions.append(feature_object_pair)
            feature_code_object_pair = f"'{dim.customer_code}', {custom_column}"
            feature_code_object_json_build_expressions.append(feature_code_object_pair)

        if not should_inserted_df.empty:
            # 对有效数据把column从title->转为custom_code 并增加后缀，因为在动态组织属性code和name一样
            should_inserted_df.rename(title_name_mapper, inplace=True, axis=1)

            # 以临时表方式插入
            should_inserted_temp_table_name = pd_to_temp_table(should_inserted_df)
            insert_req = BatchInsertReq(
                operating_unit_sk=operating_unit_sk,
                feature_object_json_build_expression=",".join(feature_object_json_build_expressions),
                feature_code_object_json_build_expression=",".join(feature_code_object_json_build_expressions),
                temp_table_name=should_inserted_temp_table_name,
            )
            self.dao.batch_insert_by_temp_table(insert_req)

        return should_inserted_temp_table_name

    def batch_insert_human_tag_list(self, import_data):
        if len(import_data) > INSERT_CHUNK_SIZE:
            while import_data:
                sliced_import_data = import_data[:INSERT_CHUNK_SIZE]
                self._batch_insert_human_tag(sliced_import_data)
                import_data = import_data[INSERT_CHUNK_SIZE:]
        else:
            self._batch_insert_human_tag(import_data)

    def _batch_insert_human_tag(self, import_data):
        value_expressions = []
        for data in import_data:
            value_list = []
            for column in self.required_columns:
                _data = data[column]
                if isinstance(_data, int):
                    _data = str(_data)
                if isinstance(_data, str):
                    _data = f"'{_data}'"
                if isinstance(_data, dict):
                    _data = f"'{json.dumps(_data)}'"
                value_list.append(_data)
            value_str = ",".join(value_list)
            value_expressions.append(f"({value_str})")

        column_expression = ",".join(self.required_columns)
        value_expressions_str = ",".join(value_expressions)
        self.dao.batch_insert(column_expression, value_expressions_str)

    @staticmethod
    def _make_dim_feature_code_object_expression(dim_objects):
        """
        构造维度select
        Parameters
        ----------
        dim_objects

        Returns
        -------

        """
        custom_fields = []
        for dim_obj in dim_objects:
            custom_fields.append(f"(feature_code_object->>'{dim_obj.customer_code}')::text as {dim_obj.customer_code}")
        return ",".join(custom_fields)

    def export_data_by_human_tag(self, req: tf.QueryHumanTagReq):
        """
        导出数据
        """
        dim_objects = self._list_and_check_dim_object(req.dim_codes, is_transform=True)
        feature_code_object_expression = self._make_dim_feature_code_object_expression(dim_objects)
        feature_objects = self._list_and_check_feature_object(req.feature_codes)

        excel_columns = []
        code_title_map = {}
        ordered_dim_custom_code = []
        dim_title = []
        for dim_object in dim_objects:
            dim_title.append(dim_object.title)
            ordered_dim_custom_code.append(dim_object.customer_code)
            excel_columns.append(dim_object.title)
            code_title_map[dim_object.customer_code] = dim_object.title

        for feature_object in feature_objects:
            excel_columns.append(feature_object.feature_name)
            code_title_map[feature_object.feature_code] = feature_object.feature_name

        auth_helper = HumanTagAuthHelper(req.operating_unit_sk, dim_objects)
        dynamic_condition = ListHumanTagReq(
            operating_unit_sk=req.operating_unit_sk,
            auth_orgs_sql_expression=auth_helper.auth_orgs_sql_expression,
            on_expression=auth_helper.on_with_extraction_expression,
            extract_expression=feature_code_object_expression,
            need_auth_flag=auth_helper.need_auth_flag,
            feature_codes=req.feature_codes,
        )
        data_df = self.dao.list_human_tag_by_condition(dynamic_condition)
        pivot_df = data_df.pivot(index=ordered_dim_custom_code, columns="feature_code", values="feature_value")
        pivot_df.reset_index(inplace=True)
        pivot_df.rename(code_title_map, axis=1, inplace=True)
        pivot_df.replace(np.nan, "", inplace=True)
        additional_columns = set(excel_columns).difference(set(pivot_df.columns))
        if additional_columns:
            pivot_df[list(additional_columns)] = ""
        # pivot_df.columns = excel_columns
        handler = ExportService(excel_name=concat_dimension_title(dim_title), ws_name="sheet1")
        table_header = [{"title": column, "key": column} for column in excel_columns]
        file = handler.generate_excel(table_header, pivot_df[excel_columns].to_dict("records"))
        return file, handler.file_name

    @staticmethod
    def _generate_feature_filter_expression(feature_codes: List[str], feature_value_filters: Dict[str, List[str]]):
        """
        生成特征相关的表达式
        Parameters
        ----------
        feature_codes
        feature_value_filters
            eg. {"tag1":['1','2']}

        Returns
        -------
        str

        """
        value_expression = ""
        for picked_feature_code, value_filter in feature_value_filters.items():
            # 暂只支持一个筛选框
            value_filter = str(value_filter).replace("[", "(").replace("]", ")")
            # value_expression = f"(feature_code = '{picked_feature_code}' and feature_value in {value_filter})"
            value_expression = f"(feature_value_object->>'{picked_feature_code}')::text in {value_filter}"
            return value_expression

        if value_expression:

            def _inner_feature_filter(feature_code):
                return f"feature_code = '{feature_code}'" if feature_code != picked_feature_code else value_expression

            _expressions = [_inner_feature_filter(feature_code) for feature_code in feature_codes]
            return " or ".join(_expressions)
        else:
            # 只包含feature_code
            feature_code_or_expression = " or ".join([f"feature_code='{feature_code}'" for feature_code in feature_codes])
            feature_code_expression = f"({feature_code_or_expression})"
            return feature_code_expression

    def query_feature_config_by_human_tag(self, req: tf.QueryHumanTagReq):
        """
        查询特征
        """

        init_page = PageResultInfo(page_no=req.page_no, page_size=req.page_size, total_rows=0, items=[], total_pages=0)
        dim_qs = self._list_and_check_dim_object(req.dim_codes, is_transform=True)
        auth_helper = HumanTagAuthHelper(req.operating_unit_sk, dim_qs)

        # 需要权限但是在该动态维度无有效权限返回空
        if auth_helper.need_auth_flag and not auth_helper.dynamic_authorized_records:
            logger.info(f"特征维护，获取维度{req.dim_codes}的特征返回空，有效的权限组合为空，authorized_orgs:{auth_helper.authorized_orgs}")
            return init_page
        if auth_helper.need_auth_flag and not auth_helper.auth_orgs_sql_expression:
            logger.info(f"特征维护，构造动态组织权限:{dim_qs}失败了, auth_orgs_sql_expression为空, req:{req}")
            raise ServerError

        feature_sql_expression = self._generate_feature_filter_expression(req.feature_codes, req.feature_value_filters)
        offset = (req.page_no - 1) * req.page_size
        dynamic_condition = ListHumanTagReq(
            operating_unit_sk=req.operating_unit_sk,
            page_size=req.page_size,
            offset=offset,
            auth_orgs_sql_expression=auth_helper.auth_orgs_sql_expression,
            feature_related_expression=feature_sql_expression,
            need_auth_flag=auth_helper.need_auth_flag,
        )
        if req.feature_value_filters:
            dynamic_condition.feature_codes = req.feature_codes
            dynamic_condition.on_expression = auth_helper.on_with_extraction_expression
            result = self.dao.list_grouped_human_tag_by_condition(dynamic_condition) or []
            count = self.dao.count_grouped_human_tag_by_condition(dynamic_condition) or 0
        else:
            dynamic_condition.with_clause_name = auth_helper.with_clause_name
            dynamic_condition.on_expression = auth_helper.on_expression
            dynamic_condition.extract_expression = auth_helper.feature_object_extract_expression
            result = self.dao.list_human_tag_by_dynamic_condition(dynamic_condition) or []
            count = self.dao.count_human_tag_by_dynamic_condition(dynamic_condition) or 0

        total_pages = math.ceil(count / req.page_size)
        init_page.total_rows = count
        init_page.items = result
        init_page.total_pages = total_pages
        return init_page

    def query_code_value_options(self, req: tf.QueryHumanTagReq):
        result = {}
        dim_objects = self._list_and_check_dim_object(req.dim_codes, is_transform=True)
        dim_headers = [
            HumanTagTableTitleResponse(code=dim_object.customer_code, name=dim_object.title) for dim_object in dim_objects
        ]

        feature_entities = self._list_and_check_feature_object(req.feature_codes)
        feature_qs = []
        feature_headers = []
        for feature in feature_entities:
            feature_qs.append(tf.FeatureConfigInstance(**feature.dict()))
            feature_headers.append(
                HumanTagTableTitleResponse(
                    code=feature.feature_code, name=feature.feature_name, can_filter=True, column_type="tag"
                )
            )

        result["table_headers"] = dim_headers + feature_headers
        feature_value_df = self.dao.query_feature_human_value([item.feature_code for item in feature_qs])
        result["feature_value_options"] = {
            item.feature_code: feature_value_df[feature_value_df["feature_code"] == item.feature_code]["feature_value"]
            .unique()
            .tolist()
            for item in feature_qs
        }

        return result

    def get_human_tag_feature_tree_by_dim_group(self, dim_code_groups: List[str]):
        """
        根据维度组合获取特征树
        Parameters
        ----------
        dim_code_groups
            维度组合一个数组代表一个维度组合

        Returns
        -------

        """
        features = self._get_feature_by_dim_codes(dim_code_groups)
        feature_entities = [FeatureEntity(**feature.dict()) for feature in features]
        keep_columns = ["feature_code", "feature_name"]
        tree = FeatureCategoryTree(feature_entities, keep_root=False, keep_columns=keep_columns).to_tree()
        return tree

    def _get_feature_by_dim_codes(self, dim_codes):
        """
        通过dim_codes获取人工特征
        Parameters
        ----------
        dim_codes

        Returns
        -------

        """
        features = self.dao.query_feature_config_by_table_field_code(req=tf.DownloadTemplateReq(code=dim_codes))
        return features or []

    def _list_and_check_dim_object(self, dim_codes, is_transform=False):
        if not dim_codes:
            logger.warning(f"参数错误，特征对象codes为空")
            raise ParameterException("参数错误，特征对象为空")

        dim_qs = self.dao.query_view_dimension_table_field_code_by_code(req=tf.DownloadTemplateReq(code=dim_codes))
        if not dim_qs:
            logger.warning(f"参数错误，特征对象:{dim_codes}不存在")
            raise ParameterException("参数错误，特征对象不存在")

        if len(dim_codes) != len(dim_qs):
            logger.warning(f"参数错误，部分特征对象代码:{dim_codes}在元数据中获取不到,获取到的元数据:{dim_qs}")
            raise ParameterException("参数错误，部分特征对象不存在")

        # 保证顺序
        code_field_item_map = {dim.code: dim for dim in dim_qs}
        dim_qs = [code_field_item_map[dim_code] for dim_code in dim_codes]
        if is_transform:
            dim_qs = [transform_meta_data_table_field(item) for item in dim_qs]

        return dim_qs

    def _list_and_check_feature_object(self, feature_codes):
        if not feature_codes:
            logger.warning(f"参数错误，特征codes为空")
            raise ParameterException("参数错误，特征为空")

        feature_entities = self.dao.list_ordered_feature_by_feature_name(feature_codes)
        if not feature_entities:
            logger.warning(f"参数错误, 特征代码:{feature_codes}不存在")
            raise ParameterException("参数错误，找不到指定的特征")

        if len(feature_codes) != len(feature_entities):
            logger.warning(f"参数错误, 部分特征代码{feature_codes}在特征定义中不存在, 获取到的特征:{feature_entities}")
            raise ParameterException("参数错误，找不到指定的特征")

        return feature_entities


hfs = HumanFeatureService()


class HumanTagAuthHelper:
    common_auth_org_service = CommonAuthOrgService()
    feature_dao = FeatureDao()
    # jsonb中权限字段数据类型
    _feature_object_data_type = "integer"
    # _feature_code_object_data_type = "text"
    # 权限进行校验的字段名称
    _feature_object_field_name = "feature_object"

    # _feature_code_object_field_name = "feature_code_object"

    def __init__(self, operating_unit_sk: int, dim_list: List[tf.MetaDataTableField], temp_table_name: str = "temp"):
        # 业务实体
        self.operating_unit_sk = operating_unit_sk
        # 默认临时表名称
        self.temp_table_name = temp_table_name
        # with语句名称
        self.with_clause_name = "valid_human_tag"
        # 与组织相关的物理表字段name
        self.dim_list = dim_list
        self.dynamic_org_related_table_field_names, _ = self._parse_org_field()
        self.need_auth_flag = self._decide_need_auth_flag()

    @property
    def auth_orgs_sql_expression(self):
        """
        组织权限sql语句表达式
        Returns
        -------
        返回字符串，如下格式
            (values(org_sk1, 中区1, 大区1),(org_sk2, 中区2, 大区2)) temp(org_sk, 中区, 大区)
        """
        auth_records = self.dynamic_authorized_records
        if not auth_records:
            return ""

        # 根据有效权限组合生成sql片段
        record_expression_generator = (f"({','.join(record_tuple)})" for record_tuple in auth_records)
        # record_expressions: List[str] = []
        # for record_tuple in auth_records:
        #     auth_record_expression = "({value_expression})".format(value_expression=",".join(record_tuple))
        #     record_expressions.append(auth_record_expression)

        value_expression = ",".join(record_expression_generator)  # (1,2),(3,4)

        return "(values {values_expression}) {temp_table_name}({dynamic_field})".format(
            values_expression=value_expression,
            dynamic_field=self.get_dynamic_field_expression(),
            temp_table_name=self.temp_table_name,
        )

    @cached_property
    def dynamic_authorized_records(self):
        return self._make_authorized_records_by_dim_list()

    @cached_property
    def authorized_orgs(self):
        operating_unit_orgs = self.common_auth_org_service.list_user_authorized_operating_unit_orgs(
            req=UserAuthOrgReq(operating_unit_sk=self.operating_unit_sk, user_id=current_user.user_id)
        )
        return operating_unit_orgs

    @cached_property
    def authorized_org_sks_set(self):
        """
        拥有权限的org_sk集合
        Returns
        -------

        """
        auth_org_sks = {org.org_sk for org in self.authorized_orgs}
        return auth_org_sks

    @property
    def feature_object_extract_expression(self):
        """
        生成从feature_object动态提取组织权限相关字段
        eg. (feature_object ->> 'skc_sk')::integer as skc_sk
        """
        if not self.dynamic_org_related_table_field_names:
            return ""
        extract_expression_list = [
            f"({self._feature_object_field_name}->>'{field_name}')::{self._feature_object_data_type} as {field_name}"
            for field_name in self.dynamic_org_related_table_field_names
        ]
        return ",".join(extract_expression_list)

    @property
    def on_expression(self):
        """
        with语句和临时表生成关联条件
        Returns
        -------
        sql语句
        a.A=b.A and a.B=b.B

        """
        on_expressions = []
        for field_name in self.dynamic_org_related_table_field_names:
            single_expression = f"{self.with_clause_name}.{field_name}={self.temp_table_name}.{field_name}"
            on_expressions.append(single_expression)

        return " and ".join(on_expressions)

    @property
    def on_with_extraction_expression(self):
        """
        构造json方式的权限关联sql片段
        因为统一用org_sk校验，所以数据json中的数据类型统一为integer
        Returns
        -------
        sql语句
        (feature_object->>'A')::integer=b.A and (feature_object->>'B')::integer=b.B

        """
        on_expressions = []
        for field_name in self.dynamic_org_related_table_field_names:
            single_expression = (
                f"({self._feature_object_field_name}->>'{field_name}')::{self._feature_object_data_type}"
                f"={self.temp_table_name}.{field_name}"
            )
            on_expressions.append(single_expression)

        return " and ".join(on_expressions)

    def list_unauthorized_values(self, field_name, field_values: List[Any]) -> List[Any]:
        """
        获取未授权的组织代码
        Parameters
        ----------
        field_name
            根据元数据field_name获取权限数据，
            field_name需要进行转化
        field_values

        Returns
        -------
        未授权的org_code

        """
        transformed_name = "org_code" if field_name in self.dynamic_org_related_table_field_names else field_name
        authorized_values = set()
        for org in self.authorized_orgs:
            if not hasattr(org, transformed_name):
                return field_values
            authorized_values.add(getattr(org, transformed_name))

        return list(set(field_values).difference(authorized_values))

    def is_field_name_need_auth(self, field_name):
        """
        判断field name是否需要进行权限校验
        特殊处理款和业务实体的关系 todo
        """
        if not self.need_auth_flag:
            return False

        return field_name in self.dynamic_org_related_table_field_names

    def get_dynamic_field_expression(self, field_prefix=None):
        if not self.dynamic_org_related_table_field_names:
            return ""
        if field_prefix:
            return ",".join(list(map(lambda x: field_prefix + "." + x, self.dynamic_org_related_table_field_names)))
        return ",".join(self.dynamic_org_related_table_field_names)

    def _decide_need_auth_flag(self):
        """
        管理员默认最大权限， 无需进行权限相关校验
        维度信息需包含组织相关维度，反之无需进行权限相关校验
        Returns
        -------

        """
        if current_user.is_admin:
            return False
        if not self.dynamic_org_related_table_field_names:
            return False

        return True

    def _parse_org_field(self):
        """
        解析动态组织
        Returns
        -------

        """
        dynamic_org_field = []
        dynamic_org_dim_objects = []
        for dim in self.dim_list:
            if dim.name == "org_sk" or dim.is_dynamic:
                dynamic_org_dim_objects.append(dim)
                dynamic_org_field.append(dim.name)
        return dynamic_org_field, dynamic_org_dim_objects

    def _make_authorized_records_by_dim_list(self):
        """
        生成有权限的动态组织记录

        管理员默认最大权限， 无需进行权限相关校验
        维度信息需包含组织相关维度，反之无需进行权限相关校验
        组织说明:
            - 组织维度可能是多种组合，比如org_sk+中区+大区
            - dm层组织集成表动态层级字段已经存在表中, 最小粒度门店的动态层级字段都会有对应值
            - 组织枝干(org_flag=3)的动态层级字段值，如果存在动态层级则填充对应org_sk值，反之为null
            - 为枝干的org_sk本层级会被填充自己的org_sk
        权限说明:
             - 权限目前只覆盖门店以及动态组织层级, 其他组织属性比如省市则不进行权限校验
             - 权限数据本版本是取得拍平后的组织权限列表
        Returns
        -------
        返回字符串，如下格式
            (values(org_sk1, 中区1, 大区1),(org_sk2, 中区2, 大区2)) temp(org_sk, 中区, 大区)
        """
        approved_auth_record = set()
        if not self.need_auth_flag:
            return approved_auth_record

        dynamic_org_records = self._list_dynamic_org_records()
        if not dynamic_org_records:
            return approved_auth_record

        # 有效的权限组合
        for org_record in dynamic_org_records:
            record_tuple, is_ok = self._get_ordered_record_if_authorized(org_record)
            if not is_ok:
                continue

            # 组合完所有动态组织维度后,将有效记录添加到集合中
            if record_tuple and record_tuple not in approved_auth_record:
                approved_auth_record.add(record_tuple)

        return approved_auth_record

    def _list_dynamic_org_records(self) -> List[Dict]:
        """
        通过有权限的org_sk获取动态组织数据

        本版本简化处理
        根据有权限的org_sk获取所有动态组织信息记录, 这些记录可能包含门店和层级
        此时组织数据可能存在越权或重复数据，因为存在根据小粒度查询大粒度的情况
        Returns
        -------

        """
        if not self.authorized_orgs:
            return []
        auth_org_sks_expression = ",".join([f"({org.org_sk})" for org in self.authorized_orgs])
        if not auth_org_sks_expression:
            return []
        sk_expression = f"values {auth_org_sks_expression}"  # values (1), (2)

        # 根据有权限的org_sk获取所有动态组织维度数据
        # 注意：dynamic_org_records可能存在某个动态字段为null的情况
        dynamic_org_records = self.feature_dao.list_dynamic_authorized_orgs_by_org_sk_expression(
            sk_expression, self.get_dynamic_field_expression("dim_org_integration")
        )

        if not dynamic_org_records:
            return []

        return dynamic_org_records

    def _get_ordered_record_if_authorized(self, record: Dict) -> Tuple[tuple, bool]:
        """
        判断一条动态组织维度组合是否有权限
        Parameters
        ----------
        record

        Returns
        -------
        tuple
            根据动态组织顺序生成的权限组合 统一转为str类型
        bool
            是否有效

        """
        value_list = []
        for column_name in self.dynamic_org_related_table_field_names:
            column_value = record.get(column_name, None)
            # 为null或不在权限内时，那么权限数据无效
            if column_value is None or column_value not in self.authorized_org_sks_set:
                return tuple(), False
            value_list.append(str(column_value))

        return tuple(value_list), True
