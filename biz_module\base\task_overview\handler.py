#!/usr/bin/env python
# _*_ coding: utf-8 _*_

"""
@author: yang<PERSON><PERSON><PERSON>
@license: Copyright 2017-2020, LineZoneData.
@contact: yang<PERSON><PERSON><PERSON>@linezonedata.com
@software: pycharm
@time: 2020/4/1 11:32
@desc: 
"""
import json
import logging
import os
from urllib.parse import quote
from typing import List, Dict
from collections import defaultdict

import flask
from flask_login import current_user
from flask_babel import lazy_gettext as _
import pandas as pd

from biz.utils.tools import table_mapping
from biz.api_exception import NotFound, ParameterException, T2InternalError
from biz.extensions import cache
from biz.utils.auto_retry_requests import requests
from biz.biz_module.base.auth.models import User
from biz.common.lz_db import db
from .constants import (
    TaskType, ScheduleType, ScheduleTimeType, TaskStatus, EXPORT_CENTER_TASK_LIST_API, EXPORT_CENTER_TASK_DETAIL_API,
    EXPORT_CENTER_TASK_N_WAITING_API, EXPORT_CENTER_DOWNLOAD_TASK_FILE_API, PAGE_CONFIG_MAP,
    SceneEnum, SelectionType, ManageType)
from .tools import (
    get_export_center_response, format_datetime_str
)
from .models import get_brands, get_manager_orgs, get_skc_sk_name
from biz.biz_module.base.task_schedule.models import task_schedule_model
from biz.settings import config

logger = logging.getLogger(__name__)
TENANT_ID = config.APP_ID


def get_task_types_handler():
    """
    根据用户拥有的页面权限，获取任务类型列表
    :return:
    """
    # 根据用户拥有的页面权限，确定允许的操作类型
    allowed_page_ids = _get_page_permissions()
    task_type_codes = []
    for x in TaskType:
        if str(TaskType.task_filter(x.value)) in allowed_page_ids:
            task_type_codes.append(x.value)
    task_types = [
        {'label': TaskType.get_label(x), 'value': x}
        for x in task_type_codes
    ]
    # 排序
    sort_map = [7, 8, 12, 5, 6, 4, 9, 19, 14]
    task_types.sort(key=lambda x: sort_map.index(x['value']) if x['value'] in sort_map else 1000)

    return task_types


def _get_page_permissions() -> List[str]:
    """查询用户的页面权限"""
    from biz.biz_module.base.auth.constants import ADMIN_PAGE_TREE, USER_PAGE_TREE
    admin_sql = ADMIN_PAGE_TREE
    user_sql = USER_PAGE_TREE
    if not current_user.is_anonymous:
        if current_user.is_admin:
            params = {}
            result = pd.read_sql(sql=admin_sql, params=params, con=db.engine)
        else:
            user_id = current_user.user_id
            params = {"user_id": user_id}
            result = pd.read_sql(sql=user_sql, params=params, con=db.engine)
    else:
        # 测试
        user_id = 47
        params = {"user_id": user_id}
        result = pd.read_sql(sql=user_sql, params=params, con=db.engine)

    if result.empty:
        page_ids = []
    else:
        page_ids = result['page_id'].values
    return page_ids


def task_list_handler(params):
    """
    查询任务列表（接口最多返回最近30天的数据）
    :param params:
        task_type
        schedule_type
        task_status
        page
        per_page
    :return:
    """
    api_url = EXPORT_CENTER_TASK_LIST_API
    body = {
        'tenant_id': TENANT_ID,
        'task_type': params['task_type'],
        'schedule_type': params['schedule_type'],
        'task_status': params['task_status'],
        'page_no': params['page_no'],
        'page_size': params['page_size'],
    }
    query_params = {}
    if params['task_start_date']:
        query_params['task_start_date'] = params['task_start_date'].strftime('%Y-%m-%d')
    if not current_user.is_anonymous:
        if current_user.is_admin:
            # pass  # 如果根据权限不设置 task_creator_id 字段的话，不会筛选用户
            query_params['task_creator_id'] = current_user.user_id
        else:
            query_params['task_creator_id'] = current_user.user_id
    body['query_params'] = json.dumps(query_params, sort_keys=True)

    # 查询（接口最多返回最近30天的数据）
    r = requests.post(api_url, json=body)
    _success, content = get_export_center_response(r) or []

    # 生成响应结果
    title = table_mapping()
    # 处理查询结果，便于呈现给用户
    body = _process_task_list_result(content.get('data', []))
    result = {
        'title': title,
        'body': body,
        'page_no': content.get('page_no'),
        'page_size': content.get('page_size'),
        'total_rows': content.get('total_rows'),
        'total_pages': content.get('total_pages'),
    }
    return result


def task_param_map(filter_dict, brand_code=None):
    """
    生成任务列表参数项的映射，根据品牌过滤
    :param tasks:
    :return:
    """
    filter_dict = dict(filter_dict)
    map_result = {}
    for filter_name, filter_val in filter_dict.items():
        map_result[filter_name] = {v['option_value']: v['option_name'] \
                                   for v in filter_val
                                   if v['brand_code'] in brand_code or v['brand_code'] == None or not brand_code}

    # 更新品牌区域数据
    brand_code2name_map = get_brands()
    manager_org_code2name_map = get_manager_orgs()
    product_class_map = {
        '1': '畅销款',
        '2': '平销款',
        '3': '滞销款',
    }
    map_result.update({'brand_code': brand_code2name_map})
    map_result.update({'manager_org_code': manager_org_code2name_map})
    map_result.update({'product_class': product_class_map})

    map_result['config_name'] = {}
    for config in task_schedule_model.query_page_config_type():
        map_result['config_name'][config[0]] = config[0]

    for map_key, map_val in PAGE_CONFIG_MAP.items():
        if map_val not in map_result:
            continue
        map_result[map_key] = map_result[map_val]

    return map_result


def decision_pool_task_param_map():
    map_result = {
        "scene_code": SceneEnum.all_name_dict(),  # 场景
        "selection_type": SelectionType.all_name_dict(),  # 选款模式
        "manage_type": ManageType.all_name_dict(),  # 管理区域模式
    }
    return map_result


def _process_task_list_result(tasks: List[Dict]) -> List[Dict]:
    """
    处理从导入导出服务查询到的任务列表，返给用户前端展示需要的结果
    :param tasks:
    :return:

    """
    user_id2name_map = _get_user_id2name_map()
    brand_code2name_map = get_brands()
    manager_org_code2name_map = get_manager_orgs()
    skc_sk_2name_map = get_skc_sk_name()
    product_class_map = {
        '1': '畅销款',
        '2': '平销款',
        '3': '滞销款',
    }

    # 获取过滤器值列表
    filter_dict = task_schedule_model.query_filter_dict()

    result = []
    for t in tasks:
        task_creator_id = t['task_creator_id']
        task_creator_name = user_id2name_map.get(task_creator_id) or ''
        task_type_code = t['task_type']
        schedule_type_code = t['schedule_type']
        schedule_time_type_code = t['schedule_time_type']
        task_status_code = t['task_status']

        start_time = format_datetime_str(t['create_time'])  # 用任务创建时间而不是任务实际开始时间
        end_time = format_datetime_str(t['task_end_time'])

        if TaskType(task_type_code) in (TaskType.INTRA_RA_IMPORT, TaskType.EXTRA_ALLOT_IMPORT):
            file_name = t['input_file']
            is_downloadable = False
        else:
            file_name = t['output_file']
            is_downloadable = file_name and TaskStatus(task_status_code) == TaskStatus.SUCCESS
        # 任务参数
        task_params = t['task_params'] or {}

        other_param_map = {}
        if task_type_code == TaskType.RA_SCENE_EXPORT:
            other_param_map = task_param_map(filter_dict, task_params['brand_code'])
        if task_type_code in TaskType.decision_pool_task_codes():
            other_param_map = decision_pool_task_param_map()
        # 判断参数映射是否在接口层已经处理好处理好的直接拉取
        if task_params.get('params_map_str'):
            task_params_str = task_params.get('params_map_str')
        else:
            task_params_str = _get_task_params_string(task_params,
                                                      brand_code=brand_code2name_map,
                                                      manager_org_code=manager_org_code2name_map,
                                                      product_class=product_class_map,
                                                      other_param_map=other_param_map,
                                                      skc_sk=skc_sk_2name_map)
        # “查看日志”的日志
        result_info_dict = t['result_info'] or {}
        if task_status_code in (TaskStatus.PENDING.value, TaskStatus.STARTED.value):
            # 排队和执行中的任务不能查看日志
            log_msg = ''
            is_log_viewable = False
        else:
            log_msg = _get_log_msg(task_status_code, result_info_dict, task_type_code)
            is_log_viewable = True

        data = {
            'task_id': t['id'],
            'task_schedule_id': t['task_schedule_id'],
            'task_type_code': task_type_code,
            'task_type_name': TaskType.get_label(task_type_code),
            'schedule_type_code': schedule_type_code,
            'schedule_type_name': ScheduleType.get_label(schedule_type_code),
            'schedule_time_type_code': schedule_time_type_code,
            'schedule_time_type_name': ScheduleTimeType.get_label(schedule_time_type_code),
            'task_status_code': task_status_code,
            'task_status_name': TaskStatus.get_label(task_status_code),
            'task_creator_id': task_creator_id,
            'task_creator_name': task_creator_name,
            'start_time': start_time,
            'end_time': end_time,
            'file_name': file_name,
            'is_downloadable': is_downloadable,  # “下载文件”功能是否可用
            'task_params': task_params,
            'task_params_str': task_params_str,
            'log_msg': log_msg,  # “查看日志”
            'is_log_viewable': is_log_viewable,  # “查看日志”功能是否可用
            'day_date': t['day_date'],
            'task_token': t.get('task_token')
        }
        result.append(data)
    return result


def _get_user_id2name_map() -> Dict[int, str]:
    """获取用户id->用户名的映射"""
    id2name_map = {
        x.user_id: x.username
        for x in User.query.all()
    }
    return id2name_map


# 可能出现的任务参数，不在此范围的数据需要修改此元祖
TO_BE_PARSED_PARAMS = (
    'scene_code',
    'brand_code',
    'day_date',
    'product_year_quarter',
    'product_range',
    'band',
    'class_longcode',
    'in_sales_level_code',
    'out_sales_level_code',
    'skc_code',
    'model_allot_out_manager_org_code',
    'model_allot_in_manager_org_code',
    'human_allot_out_manager_org_code',
    'human_allot_in_manager_org_code',
    'model_allot_out_org_sk',
    'human_allot_out_org_sk',
    'model_allot_in_org_sk',
    'human_allot_in_org_sk',
    'model_allot_out_org_code',
    'human_allot_out_org_code',
    'model_allot_in_org_code',
    'human_allot_in_org_code',
    'ra_source',
    'commit_status',
    'modify_status',
    'org_type',
    'product_sales_class',
    'product_sales_type',
    'category',
    'manager_org_code',
    'product_class',
    'year_quarter',
    # 'node_id',
    # 'node_level',
    'low_level_node',
    'skc_sk',
    'store_level',
    'store_sk',
    'distribution_plan_name',
    'selection_type',
    'manage_type',
    'reserved7',
    'reserved8',
    'config_name'
)


def parse_list_func(to_parse, item, **kwargs):
    other_param_map = kwargs.get('other_param_map')

    if other_param_map and to_parse in other_param_map:
        mapping = other_param_map.get(to_parse)
        names = ','.join([str(mapping.get(str(c), str(c))) for c in item])
    elif to_parse in kwargs.keys():
        mapping = kwargs.get(to_parse)
        names = ','.join([str(mapping.get(c, c)) for c in item])
    else:
        names = ','.join([str(c) for c in item])
    return names


def parse_str_func(iter_item, item, **kwargs):
    name = str(item)

    other_param_map = kwargs.get('other_param_map')

    if other_param_map and iter_item in other_param_map:
        mapping = other_param_map.get(iter_item)
        name = mapping.get(str(name), str(name))
    elif iter_item in kwargs.keys():
        mapping = kwargs.get(iter_item)
        name = mapping.get(name, name)

    return name


def _parse_method_factory(item):
    """
    根据数据类型选择处理方法
    如果 字段类型在  list、 str 之外
    需要增加 func 并修改此方法
    """
    if isinstance(item, list):
        return parse_list_func
    return parse_str_func


def _get_task_params_string(params: Dict, **kwargs) -> str:
    """
    拼接表示请求参数的字符串
    kwargs 的参数名称 需要和参数字段一致 并不超出 TO_BE_PARSED_PARAMS
    """
    p = []
    for i in TO_BE_PARSED_PARAMS:
        if params.get(i, None):
            item = params.get(i)
            res = _parse_method_factory(item)(i, item, **kwargs)
            if res:
                p.append(res)
    return '; '.join(p)


def _get_log_msg(task_status_code: int, result_info_dict: Dict, task_type_code: int) -> str:
    """获取显示给客户的“查看日志”信息"""
    log_msg = ""
    if task_status_code == TaskStatus.SUCCESS.value:
        # 任务成功
        if task_type_code in (TaskType.INTRA_RA_IMPORT.value, TaskType.EXTRA_ALLOT_IMPORT.value,
                              TaskType.RA_SCENE_IMPORT.value):
            log_msg = "导入成功"
        elif task_type_code in (TaskType.INTRA_RA_EXPORT.value, TaskType.EXTRA_ALLOT_EXPORT.value,
                                TaskType.OVER_VIEW_EXPORT.value, TaskType.RA_SCENE_EXPORT.value,
                                TaskType.QUICK_RESPONSE_EXPORT.value, TaskType.HISTORY_DECISION_POOL_EXPORT.value,
                                TaskType.DECISION_POLL_PRODUCT_LIST_EXPORT.value,
                                TaskType.BizVerifyExportAll.value):
            log_msg = "导出成功"
        elif task_type_code in (
                TaskType.RA_COLLECTION_ACTIVITY.value, TaskType.RA_COLLECTION_STORE_PRODUCT_DETAIL_IMPORT.value,
                TaskType.QUICK_RESPONSE_DETAIL.value, TaskType.RA_COLLECTION_ACTIVITY_CHECK):
            log_msg = result_info_dict.get("msg")
    else:
        # 任务失败
        if task_type_code in (TaskType.INTRA_RA_IMPORT.value, TaskType.EXTRA_ALLOT_IMPORT.value,
                              TaskType.RA_SCENE_IMPORT.value, TaskType.RA_SCENE_EXPORT.value,
                              TaskType.DISTRIBUTE_PLAN_VERIFY.value, TaskType.DISTRIBUTE_PLAN_CALCULATE.value,
                              TaskType.OTB_SALE_PLAN_IMPORT.value, TaskType.GTO_FEATURE_CALCULATE.value,
                              TaskType.GTO_SIMULATION_CALCULATE.value, TaskType.GTO_REPLAY_CALCULATE.value,
                              TaskType.STORE_OVERVIEW_CALCULATE.value, TaskType.PLAN_DISTRIBUTION_CALCULATE.value):
            # 导入任务失败时，提供详情日志
            log_msg = result_info_dict.get("msg")
        elif task_type_code in (TaskType.INTRA_RA_EXPORT.value, TaskType.EXTRA_ALLOT_EXPORT.value,
                                TaskType.OVER_VIEW_EXPORT.value, TaskType.QUICK_RESPONSE_EXPORT.value,
                                TaskType.HISTORY_DECISION_POOL_EXPORT.value,
                                TaskType.DECISION_POLL_PRODUCT_LIST_EXPORT.value,
                                TaskType.BizVerifyExportAll.value):
            log_msg = "导出失败"
        elif task_type_code in (
                TaskType.RA_COLLECTION_ACTIVITY.value, TaskType.RA_COLLECTION_STORE_PRODUCT_DETAIL_IMPORT.value,
                TaskType.QUICK_RESPONSE_DETAIL.value, TaskType.RA_COLLECTION_ACTIVITY_CHECK):
            log_msg = result_info_dict.get("msg")

    return log_msg


def download_task_file_handler(params: Dict) -> flask.Response:
    """
    下载任务文件
    :param params:
        task_id
        file_name
        task_creator_id
    :return:
    """
    api_url = EXPORT_CENTER_DOWNLOAD_TASK_FILE_API
    filename = params['file_name']
    body = {
        'tenant_id': TENANT_ID,
        'task_id': params['task_id'],
        'file_name': filename,
    }
    r = requests.post(api_url, json=body)

    if r.status_code != 200:
        error_info = "请求发生错误, url: {}, params: {}, status_code: {}".format(r.url, body, r.status_code)
        try:
            error_info += " response: {}".format(r.text)
        except Exception:
            pass
        logger.error(error_info)
        raise T2InternalError(str(_("请求文件失败")))
    if 'json' in r.headers.get('Content-Type', ''):
        result = r.json()
        msg = result.get('msg') or None
        error_info = "请求失败, url: {}, params: {}, response".format(r.url, body, result)
        logger.info(error_info)
        raise ParameterException(msg)

    f = r.content
    # 生成响应
    response = flask.Response(f, status=200, mimetype='application/octet-stream')
    response.headers['Content-Type'] = 'application/vnd.ms-excel'
    response.headers['Content-Disposition'] = "attachment; filename*=utf-8''{}".format(quote(filename))
    response.headers.set(
        "Access-Control-Allow-Origin", "*"
    )
    response.headers.set(
        "Access-Control-Allow-Headers", "Access-Control-Allow-Origin, x-requested-with, content-type"
    )
    return response


@cache.memoize(5)
def get_task_n_waiting_handler(task_id: int, task_creator_id: int):
    """
    查询任务等待的数量
    :param task_id:
    :param task_creator_id:
    :return:
    """
    # 通过查询任务是否存在
    api_url = EXPORT_CENTER_TASK_N_WAITING_API
    body = {
        'tenant_id': TENANT_ID,
        'task_id': task_id,
    }
    r = requests.post(api_url, json=body)
    success, content = get_export_center_response(r, need_success=False) or {}
    n_waiting = None
    if success:
        if content:
            n_waiting = content.get("n_waiting")  # if task_creator_id == content.get("task_creator_id")

    content = {
        "n_waiting": n_waiting or 0,
    }
    return content
