import json
import logging

from fastboot.utils.nest import egg
from flask_login import current_user

from biz import BaseService
from biz.api_exception import ParameterException
from biz_module.common.feature_workbench.enums import (
    FeatureSubtypeEnum,
)
from biz_module.common.feature_workbench import typedef as tf
from biz_module.common.feature_workbench.service.feature_service import FeatureService
from biz_module.quick_response_module.base.dao import (
    AddOrderFeatureConfigDao,
    PageComponentConfigDao,
)


logger = logging.getLogger(__name__)

@egg
class FeatureServiceScript(FeatureService):
    def create_feature_script(self, feature_info: dict):
        """
        创建特征

        5.7.0之前特征提交分为左右两部分, 本版本改为整体提交
        Parameters
        ----------
        req

        Returns
        -------

        """
        user_name = feature_info.get("user_name")
        if not user_name:
            raise ParameterException("缺少用户信息")
        self.add_user_info(user_name)
        feature_info = feature_info.get("feature_info", [])
        data = {}
        for i in feature_info:
            feature_code = i.get("feature_code")
            feature_name = i.get("feature_name")
            feature_type = i.get("feature_type")
            is_custom = i.get("is_custom")
            biz_view_id = i.get("biz_view_id")
            biz_view_dimension_id = i.get("biz_view_dimension_id")
            calculation_type = i.get("calculation_type")
            data_type = i.get("data_type")
            is_value_multiple = i.get("is_value_multiple")
            fill_value = i.get("fill_value")
            feature_subtype = i.get("feature_subtype")
            history_data_keep_day = i.get("history_data_keep_day")
            feature_value = i.get("feature_value")
            sql_edit = i.get("sql_edit")
            condition_set = i.get("condition_set")
            template_id = i.get("template_id")
            template_params = i.get("template_params")
            value_build_type = i.get("value_build_type")

            config_body = tf.FeatureConfigEgg(
                feature_code=feature_code,
                feature_name=feature_name,
                feature_type=feature_type,
                is_custom=is_custom,
                biz_view_id=biz_view_id,
                biz_view_dimension_id=biz_view_dimension_id,
                calculation_type=calculation_type,
                data_type=data_type,
                is_value_multiple=is_value_multiple,
                fill_value=fill_value,
                feature_subtype=feature_subtype,
                history_data_keep_day=history_data_keep_day,
                value_build_type=value_build_type,
            )
            if feature_subtype == FeatureSubtypeEnum.SQL_APP.value:
                value_body = tf.FeatureValueReq(
                    feature_value=feature_value,
                    sql_edit=sql_edit,
                )
            elif feature_subtype == FeatureSubtypeEnum.RULE_AUTO.value:
                value_body = tf.FeatureValueReq(
                    feature_value=feature_value,
                    condition_set=json.loads(condition_set) if condition_set else None,
                )
            elif feature_subtype == FeatureSubtypeEnum.FEATURE_TEMPLATE.value:
                value_body = tf.FeatureValueReq(
                    feature_value=feature_value,
                    template_params=json.loads(template_params) if template_params else None,
                    template_id=template_id,
                )
            elif feature_subtype == FeatureSubtypeEnum.HUMAN_TAG.value:
                value_body = tf.FeatureValueReq(
                    feature_value=feature_value,
                    condition_set=sql_edit,
                )
            else:
                continue

            value = data.get(feature_code)
            if value:
                data[feature_code].value_body.append(value_body)
            else:
                data[feature_code] = tf.CreateFeatureReq(
                    config_body=config_body, value_body=[value_body]
                )
        error_msg = []
        success_msg = []
        for feature_code, req in data.items():
            print(f"开始复制特征{feature_code}!!!!")
            try:
                self.create_feature(req)
            except Exception as e:
                print(e)
                error_msg.append(f"{feature_code}:新增失败, 原因：{e}")
            else:
                success_msg.append(f"{feature_code}:新增成功")
        return {"success_msg": success_msg, "error_msg": error_msg}

    # def delete_feature_script(self, feature_ids: dict):
    #     user_name = feature_ids.get("user_name")
    #     if not user_name:
    #         raise ParameterException("缺少用户信息")
    #     self.add_user_info(user_name)
    #     feature_ids = set(feature_ids.get("feature_config_ids", []))
    #     nums = 0
    #     for i in feature_ids:
    #         res = tf.FeatureConfigDeleteReq(feature_config_ids=[i])
    #         try:
    #             self.delete_feature_config(res)
    #             nums += 1
    #         except Exception as e:
    #             print(e)
    #     print(f"本轮删除{nums}个特征")
    #     return {"msg": f"本轮删除{nums}个特征"}
    #
    # def delete_feature_config(self, query_info: tf.FeatureConfigDeleteReq):
    #     query_info.modifier = current_user.user_id
    #     mid_type_feature_hit_sql_dict = {}
    #     for feature_id in query_info.feature_config_ids:
    #         validator = FeatureValidator.create_from_feature_config_id(feature_id)
    #         # validator.validate_feature_is_used_by_third_party()
    #         if validator.calculation_type_enum is CalculationTypeEnum.MID:
    #             hit_sql_list = self.get_hit_sql(feature_id, validator)
    #             mid_type_feature_hit_sql_dict[
    #                 validator.feature_config.id
    #             ] = hit_sql_list[
    #                 0
    #             ]  # mid类型hit sql只能为一个
    #
    #     with transaction_manager():
    #         feature_config_ids = self.dao.delete_feature_config(query_info) or [-1]
    #         # 删除特征值和特征筛选条件
    #         feature_filter_ids = self.dao.clear_feature_value(
    #             tf.FeatureValueClearReq(feature_config_id=feature_config_ids)
    #         ) or [-1]
    #         feature_filter_ids = [
    #             filter_id for filter_id in feature_filter_ids if filter_id
    #         ] or [-1]
    #         self.dao.clear_feature_filter(
    #             tf.FeatureFilterClearReq(feature_filter_ids=feature_filter_ids)
    #         )
    #         # 调用数仓脚本
    #         for feature_config_id in query_info.feature_config_ids:
    #             self.call_wh_script(
    #                 feature_config_id,
    #                 self.ACTION_DELETE,
    #                 mid_type_feature_hit_sql_dict.get(feature_config_id, ""),
    #             )
    #     return True

    def update_feature_script(self, params: dict):
        """
        更新特征

        5.7.0之前特征提交分为左右两部分, 本版本改为整体提交
        Parameters
        ----------
        params

        Returns
        -------

        """
        if not params:
            return True
        user_name = params.get("user_name")
        if not user_name:
            raise ParameterException("缺少用户信息")
        self.add_user_info(user_name)
        params = params.get("update_info", [])
        feature_codes = [i.get("feature_code") for i in params if i.get("feature_code")]
        if not feature_codes:
            return True
        feature_info = AddOrderFeatureConfigDao().query_feature_info_by_feature_codes(
            feature_codes
        )
        feature_code_info = {i.feature_code: i.id for i in feature_info}
        feature_value_info = {
            i.feature_name_tag: i.feature_value_id for i in feature_info
        }
        data = {}
        for i in params:
            feature_code = i.get("feature_code")
            feature_id = feature_code_info.get(feature_code)
            feature_name = i.get("feature_name")
            feature_type = i.get("feature_type")
            is_custom = i.get("is_custom")
            biz_view_id = i.get("biz_view_id")
            biz_view_dimension_id = i.get("biz_view_dimension_id")
            calculation_type = i.get("calculation_type")
            data_type = i.get("data_type")
            is_value_multiple = i.get("is_value_multiple")
            fill_value = i.get("fill_value")
            feature_subtype = i.get("feature_subtype")
            history_data_keep_day = i.get("history_data_keep_day")
            feature_value = i.get("feature_value")
            sql_edit = i.get("sql_edit")
            condition_set = i.get("condition_set")
            template_id = i.get("template_id")
            template_params = i.get("template_params")
            feature_name_tag = i.get("feature_name_tag")
            feature_value_id = feature_value_info.get(feature_name_tag, -1)
            value_build_type = i.get('value_build_type')

            config_body = tf.FeatureConfigEgg(
                id=feature_id,
                feature_code=feature_code,
                feature_name=feature_name,
                feature_type=feature_type,
                is_custom=is_custom,
                biz_view_id=biz_view_id,
                biz_view_dimension_id=biz_view_dimension_id,
                calculation_type=calculation_type,
                data_type=data_type,
                is_value_multiple=is_value_multiple,
                fill_value=fill_value,
                feature_subtype=feature_subtype,
                history_data_keep_day=history_data_keep_day,
                value_build_type=value_build_type,
            )
            if feature_subtype == FeatureSubtypeEnum.SQL_APP.value:
                value_body = tf.FeatureValueReq(
                    feature_value=feature_value,
                    sql_edit=sql_edit,
                    feature_value_id=feature_value_id,
                )
            elif feature_subtype == FeatureSubtypeEnum.RULE_AUTO.value:
                value_body = tf.FeatureValueReq(
                    feature_value=feature_value,
                    condition_set=json.loads(condition_set) if condition_set else None,
                    feature_value_id=feature_value_id,
                )
            elif feature_subtype == FeatureSubtypeEnum.FEATURE_TEMPLATE.value:
                value_body = tf.FeatureValueReq(
                    feature_value=feature_value,
                    template_params=json.loads(template_params) if template_params else None,
                    template_id=template_id,
                    feature_value_id=feature_value_id,
                )
            elif feature_subtype == FeatureSubtypeEnum.HUMAN_TAG.value:
                value_body = tf.FeatureValueReq(
                    feature_value=feature_value,
                    condition_set=sql_edit,
                    feature_value_id=feature_value_id,
                )
            else:
                continue

            value = data.get(feature_code)
            if value:
                data[feature_code].value_body.append(value_body)
            else:
                data[feature_code] = tf.UpdateFeatureReq(
                    config_body=config_body, value_body=[value_body], need_check_ref=False
                )
        error_msg = []
        success_msg = []
        for feature_code, req in data.items():
            print(f"开始复制特征{feature_code}!!!!")
            try:
                self.update_feature(req)
            except Exception as e:
                logger.info(f"{feature_code}: {e}")
                error_msg.append(f"{feature_code}更新失败, 原因：{e}")
            else:
                print(f"特征{feature_code}更新成功!!!!")
                success_msg.append(f"{feature_code}更新成功")

        return {"error_msg": error_msg, "success_msg": success_msg}

    @classmethod
    def add_user_info(cls, user_name):
        from flask_login import login_user
        from biz_module.base.auth.models import User

        user = User.query.filter_by(username=user_name).first()
        login_user(user)
        from biz_module.base.authorization.auth_record.service import (
            auth_record_service,
        )

        auth_record_service.set_default_auth_to_cache_if_exist(current_user.user_id)

    # @staticmethod
    # def compare_different_environment_feature(params: dict):
    #
    #     prod = params.get('prod', {})
    #     uat2 = params.get('uat2', {})
    #     test = params.get('test', {})
    #     dev = params.get('dev', {})
    #
    #     prod_detail_dict = {}
    #     uat2_detail_dict = {}
    #     test_detail_dict = {}
    #     dev_detail_dict = {}
    #     for i in prod:
    #         feature_code = i.get('feature_code')
    #         feature_name = i.get('feature_name')
    #         print('prod: ' + feature_code)
    #         priority = i.get('priority')
    #         feature_value = i.get('feature_value', '') or 'None'
    #         sql_edit = i.get('sql_edit')
    #         prod_detail_dict[feature_code + ';;' + feature_name + ';;' + str(priority) + ';;' + feature_value] = sql_edit
    #     for i in uat2:
    #         feature_code = i.get('feature_code')
    #         feature_name = i.get('feature_name')
    #         print('uat2: ' + feature_code)
    #         priority = i.get('priority')
    #         feature_value = i.get('feature_value', '') or 'None'
    #         sql_edit = i.get('sql_edit')
    #         uat2_detail_dict[feature_code + ';;' + feature_name + ';;' + str(priority) + ';;' + feature_value] = sql_edit
    #     for i in test:
    #         feature_code = i.get('feature_code')
    #         feature_name = i.get('feature_name')
    #         print('test: ' + feature_code)
    #         priority = i.get('priority')
    #         feature_value = i.get('feature_value', '') or 'None'
    #         sql_edit = i.get('sql_edit')
    #         test_detail_dict[feature_code + ';;' + feature_name + ';;' + str(priority) + ';;' + feature_value] = sql_edit
    #     for i in dev:
    #         feature_code = i.get('feature_code')
    #         feature_name = i.get('feature_name')
    #         print('dev: ' + feature_code)
    #         priority = i.get('priority')
    #         feature_value = i.get('feature_value', '') or 'None'
    #         sql_edit = i.get('sql_edit')
    #         dev_detail_dict[feature_code + ';;' + feature_name + ';;' + str(priority) + ';;' + feature_value] = sql_edit
    #
    #     prod_uat2_diff = []
    #     prod_test_diff = []
    #     prod_dev_diff = []
    #
    #     for key, values in prod_detail_dict.items():
    #         feature_code, feature_name, priority, feature_value = key.split(';;')
    #         uat2_values = uat2_detail_dict.get(key)
    #         test_values = test_detail_dict.get(key)
    #         dev_values = dev_detail_dict.get(key)
    #         if values != uat2_values:
    #             prod_uat2_diff.append(f'特征编码{feature_code}, 特征名称{feature_name}, 优先级{priority}, 标签值{feature_value}, prod和uat2不一致')
    #         if values != test_values:
    #             prod_test_diff.append(f'特征编码{feature_code}, 特征名称{feature_name}, 标签值{feature_value}, prod和test不一致')
    #         if values != dev_values:
    #             prod_dev_diff.append(f'特征编码{feature_code}, 特征名称{feature_name}, 标签值{feature_value}, prod和dev不一致')
    #     prod_diff_msg = prod_uat2_diff + prod_test_diff + prod_dev_diff
    #
    #     dev_uat2_diff = []
    #     dev_test_diff = []
    #
    #     for key, values in dev_detail_dict.items():
    #         feature_code, feature_name, priority, feature_value = key.split(';;')
    #         uat2_values = uat2_detail_dict.get(key)
    #         test_values = test_detail_dict.get(key)
    #         if values != uat2_values:
    #             dev_uat2_diff.append(f'特征编码{feature_code}, 特征名称{feature_name}, 优先级{priority}, 标签值{feature_value}, dev和uat2不一致')
    #         if values != test_values:
    #             dev_test_diff.append(f'特征编码{feature_code}, 特征名称{feature_name}, 优先级{priority}, 标签值{feature_value}, dev和test不一致')
    #     dev_diff_msg = dev_uat2_diff + dev_test_diff
    #
    #     return {"msg": {"prod_diff_msg": prod_diff_msg, "dev_diff_msg": dev_diff_msg}}


class PageConfigServiceScript(BaseService):

    def page_config_config_format_update_script(self, update_info):
        res = update_info.get("update_info")
        format_info = update_info.get("format_info")
        result = []
        for i in res:
            page_id = i.get("page_id")
            content = i.get("content", "")
            content = json.loads(content) if content else {}
            kpi_type = content.get("type")
            fields = content.get("fields")
            if kpi_type == "list":
                for j in fields:
                    ori_name = j.get("ori_name")
                    format_ = format_info.get(ori_name)
                    if format_:
                        j["formatter"] = format_
            elif kpi_type == "tree":
                for j in fields:
                    children = j.get("children", [])
                    for x in children:
                        second_children = x.get("children", [])
                        for j in second_children:
                            ori_name = j.get("ori_name")
                            format_ = format_info.get(ori_name)
                            if format_:
                                j["formatter"] = format_
            else:
                continue
            from biz_module.quick_response_module.base.typedef import (
                ScriptPageConfigFormatUpdateDao,
            )

            result.append(
                ScriptPageConfigFormatUpdateDao(
                    page_id=page_id, content=json.dumps(content)
                )
            )
        for i in result:
            PageComponentConfigDao().update_page_config_format(i)

        return {"items": result}

    def add_config_config_script(self, update_info):
        module_name = update_info.get('module_name')
        page_name = update_info.get('page_name')
        frond_code = update_info.get('frond_code')
        new_feature = update_info.get('new_feature')

        data = PageComponentConfigDao().get_page_config_by_module_name_and_page_name(module_name, page_name)
        new_data = []
        for i in data:
            page_id = i.page_id
            content = i.content
            kpi_type = content.get("type")
            fields = content.get("fields")
            new_fields = []
            need_add = False
            if kpi_type == "list":
                for j in fields:
                    code = j.get('en_name')
                    if code == frond_code:
                        need_add = True
                        new_fields.append(j)
                        new_feature['serial_number'] = j.get("serial_number", 0) + 1
                        new_fields.append(new_feature)
                        continue
                    if need_add:
                        j["serial_number"] += 1
                    new_fields.append(j)
                if need_add is False:
                    new_feature['serial_number'] = 999
                    new_fields.append(new_feature)
                content["fields"] = new_fields
                from biz_module.quick_response_module.base.typedef import (
                    ScriptPageConfigFormatUpdateDao,
                )
                new_data.append(ScriptPageConfigFormatUpdateDao(
                    page_id=page_id, content=json.dumps(content)
                )
                )

        #     elif kpi_type == "tree":
        #         new_fields = []
        #         for j in fields:
        #             children = j.get("children", [])
        #             for index, x in enumerate(children):
        #                 second_children = x.get("children", [])
        #
        #                 for j in second_children:
        #                     code = j.get('en_name')
        #                     if code == frond_code:
        #                         need_add = True
        #
        #         content["fields"] = new_fields
        #     else:
        #         continue
        for i in new_data:
            PageComponentConfigDao().update_page_config_format(i)

        return {"msg": "新增成功"}