# *_*coding:utf-8 *_*
# @Time    : 2018/11/28 11:18
# <AUTHOR> hl_python
# @Email   : <EMAIL>
# @File    : models.py
# @Software: PyCharm
import datetime
import io
from collections import defaultdict

import pandas as pd
import sqlalchemy as sa
from sqlalchemy import desc
from flask import send_file, request
from flask_babel import lazy_gettext as _
from openpyxl import Workbook
from sqlalchemy import text, or_, and_
from sqlalchemy.orm import mapper
from wsme import Unset

from biz.api_exception import Fail, FileTypeError
from biz.common.lz_db import db
from biz.common.lz_db import models
from biz.biz_module.base.auth import models as auth_models

# 角色页面权限关系表
role_page_permission = db.Table('role_page_permission', models.BaseRemarkTime.metadata,
                                db.Column('id', db.Integer, primary_key=True),
                                db.Column('page_id', db.Integer, db.<PERSON>('page_permission.page_id')),
                                db.Column('role_id', db.Integer, db.<PERSON><PERSON>('role.role_id')),
                                # operation_type 0无权限、1有权限
                                db.Column('operation_type', db.Integer, server_default=sa.text('0')))

# 角色功能权限表
role_operation_permission = db.Table('role_operation_permission', models.BaseRemarkTime.metadata,
                                     db.Column('operation_id', db.Integer,
                                               db.ForeignKey('operation_permission.operation_id')),
                                     db.Column('role_id', db.Integer, db.ForeignKey('role.role_id')),
                                     db.Column('operation_type', db.Integer, server_default=sa.text('0')))

# 角色数据权限表
role_data_permission = db.Table('role_data_permission', models.BaseRemarkTime.metadata,
                                db.Column('data_id', db.Integer, db.ForeignKey('data_permission.data_id')),
                                db.Column('role_id', db.Integer, db.ForeignKey('role.role_id')),
                                # operation_type 0无权限、1可读、 2可写（品牌目前只有 0/1）
                                db.Column('operation_type', db.Integer, server_default=sa.text('0')))


class RolePage(object):
    pass


role_page_mapper = mapper(RolePage, role_page_permission, non_primary=False)


class Role(models.BaseRemarkTime):
    """角色表"""

    __tablename__ = 'role'
    # 角色id
    role_id = db.Column(db.Integer(), primary_key=True)
    # 角色名称
    role_name = db.Column(db.String(length=64), unique=True, nullable=False)
    # 角色类型(内部角色还是外部角色)
    role_type = db.Column(db.Integer(), server_default=text('0'), nullable=False)

    # 角色与页面权限表的对应关系
    page_permissions = db.relationship('PagePermission',
                                       secondary=role_page_permission,
                                       backref=db.backref('roles', lazy='dynamic'),
                                       lazy='dynamic')
    # 角色与功能权限表的对应关系
    operation_permissions = db.relationship('OperationPermission',
                                            secondary=role_operation_permission,
                                            backref=db.backref('roles', lazy='dynamic'),
                                            lazy='dynamic')
    # 角色与数据权限表的对应关系
    data_permissions = db.relationship('DataPermission',
                                       secondary=role_data_permission,
                                       backref=db.backref('roles', lazy='dynamic'),
                                       lazy='dynamic')

    @staticmethod
    def _have_resource_perm(role_id):
        resource_permissions = RoleResourcePermission.query. \
            filter_by(role_id=role_id).all()
        if resource_permissions:
            return True
        else:
            return False

    @staticmethod
    def query_role_name(form):
        """
        模糊查询角色名
        :param form: 前端传入参数
        :return:
        """
        query_level_value = form.query_level_value
        active = form.is_active
        # 是否只查询启用的角色(没有输入模糊查询的值， 就是查询所有)
        if active != Unset:
            if query_level_value:
                role_lists = Role.query.filter(and_(or_(Role.role_name.like("%" + query_level_value + "%"),
                                                        Role.remark.like("%" + query_level_value + "%")),
                                                    Role.is_active.is_(active))). \
                    order_by("role_id").all()
            else:
                role_lists = Role.query.filter_by(is_active=active).order_by(desc(Role.role_id)).all()
        else:
            if query_level_value:
                role_lists = Role.query.filter(or_(Role.role_name.like("%" + query_level_value + "%"),
                                                   Role.remark.like("%" + query_level_value + "%"))).order_by(
                    desc(Role.role_id)).all()
            else:
                role_lists = Role.query.order_by(desc(Role.role_id)).all()

        data = []
        for role_list in role_lists:
            used_by_user = False
            if role_list.users.all() or role_list.page_permissions.all():
                used_by_user = True
            elif Role._have_resource_perm(role_list.role_id):
                used_by_user = True

            item = {"role_id": role_list.role_id,
                    "role_name": role_list.role_name,
                    "remark": role_list.remark,
                    "is_active": role_list.is_active,
                    "used_by_user": used_by_user}
            data.append(item)

        result = data

        return result

    @staticmethod
    def query_role_info(form):
        """
        查询角色列表信息
        :param form:前端传入参数
        :return:
        """
        role_name_list = form.role_name_list

        page = form.page
        page_size = form.page_size

        # 角色列表信息分页
        if role_name_list:
            role_lists = Role.query.filter(Role.role_name.in_(role_name_list)).order_by("role_id").paginate(
                page=page,
                per_page=page_size,
                error_out=False)
        else:
            role_lists = Role.query.order_by("role_id").paginate(
                page=page,
                per_page=page_size,
                error_out=False)

        result = {"total_row_number": role_lists.total, "total_page_number": role_lists.pages}

        role_data = []
        for role_item in role_lists.items:
            item = {"role_id": role_item.role_id, "role_name": role_item.role_name, "remark": role_item.remark,
                    "create_time": role_item.create_time, "update_time": role_item.update_time,
                    "is_active": role_item.is_active, "count_users": role_item.users.count()}

            role_data.append(item)
        result["data"] = role_data
        return result

    @staticmethod
    def add_role(form):
        """
        新增角色
        :param form:
        :return:
        """
        role_name = form.role_name
        remark = form.remark
        role = Role()
        with db.auto_commit():
            role.role_name = role_name
            if remark:
                role.remark = remark
            db.session.add(role)
        return role

    @staticmethod
    def update_role(form):
        """
        编辑角色
        :param form:
        :return:
        """
        role_id = form.role_id
        role_name = form.role_name
        remark = form.remark
        is_active = form.is_active
        users = form.users

        role = Role.query.filter_by(role_id=role_id).first()

        with db.auto_commit():
            if is_active != Unset:
                role.is_active = is_active
            if remark != Unset:
                role.remark = remark
            if users != Unset:
                # import pdb;pdb.set_trace()
                role.users = auth_models.User.query.filter(auth_models.User.user_id.in_(users)).all()
            role.role_name = role_name
            role.update_time = datetime.datetime.now()

    @staticmethod
    def del_role(form):
        """
        删除用户
        :param form:前端传入参数
        :return:
        """
        role_id = form.role_id
        role = Role.query.filter_by(role_id=role_id).first()
        with db.auto_commit():
            db.session.delete(role)

    @staticmethod
    def forbidden_role(form):
        """
        禁用或启用角色
        :param form:
        :return:
        """
        role_id = form.role_id
        is_active = form.is_active
        role = Role.query.filter_by(role_id=role_id).first()
        with db.auto_commit():
            role.is_active = is_active
            role.update_time = datetime.datetime.now()

    @staticmethod
    def query_role_users(form):
        """
        查询角色对应的用户
        :param form:
        :return:
        """
        role_id = form.role_id

        role = Role.query.filter_by(role_id=role_id).first()
        # 查询只启用的用户
        users = role.users.filter_by(is_active=True).order_by("user_id").all()

        content = {"data": [{"username": user.username,
                             "name": user.name,
                             "user_id": user.user_id} for user in users]}
        return content

    @staticmethod
    def update_role_permission(form):
        """
        编辑角色权限
        :param form:
        :return:
        """
        role_id = form.role_id
        page_id_list = form.page_ids
        operation_id_list = form.operation_ids
        resource_ids = form.resource_ids
        role = Role.query.filter_by(role_id=role_id).first()
        with db.auto_commit():
            role.page_permissions = PagePermission.query.filter(PagePermission.page_id.in_(page_id_list)).all()
            role.operation_permissions = OperationPermission.query.filter(
                OperationPermission.operation_id.in_(operation_id_list)).all()
            data_permission = []
            # 添加数据权限
            for data in resource_ids:
                resource_id = data.get("resource_id")
                operation_type = 0 if data.get("is_read") else 1
                item = DataPermission.query.filter(
                    and_(DataPermission.resource_id == resource_id,
                         DataPermission.operation_type == operation_type)).first()
                if item:
                    data_permission.append(item)
            role.data_permissions = data_permission

    @staticmethod
    def upsert_page_permission(form):
        with db.auto_commit():
            for r_p in form.role_permissions:
                page_id = r_p.get("page_id")
                operation_type = r_p.get("operation_type")
                query = db.session.query(RolePage)
                page_permission = query.filter_by(role_id=form.role_id,
                                                  page_id=page_id).all()
                if page_permission:
                    if operation_type == 0:
                        db.session.delete(page_permission[0])
                        continue
                    page_permission[0].operation_type = operation_type

                else:
                    if operation_type == 0:
                        continue
                    page_permission = RolePage()
                    page_permission.page_id = page_id
                    page_permission.role_id = form.role_id
                    page_permission.operation_type = operation_type
                    db.session.add(page_permission)

    @staticmethod
    def export_role_info(form, file_name, header_name, header):
        """
        导出角色列表excel信息
        :param form: 前端参数
        :param file_name: 文件名
        :param header_name: 第一行标题
        :param header: 每列列名数据
        :return:
        """
        role_name_list = form.get("role_name_list")

        if role_name_list:
            role_list = Role.query.filter(Role.role_name.in_(role_name_list)).order_by("role_id").all()
        else:
            role_list = Role.query.order_by("role_id").all()
        wb = Workbook()
        ws = wb.active
        # 添加表头
        ws.append([header_name, ])
        ws.append(header)
        # 添加每行数据
        for role_item in role_list:
            item = [role_item.role_id, role_item.role_name, role_item.remark,
                    role_item.users.count(), role_item.create_time, role_item.update_time,
                    "启用" if role_item.is_active else "禁用"]

            ws.append(item)

        from biz.utils.tools import style_range
        # 设置excel样式
        style_range(ws, merge_header=True, auto_width=True)
        ws.freeze_panes = ws['A3']
        # 新建一个BytesIO流
        out = io.BytesIO()
        # excel写入到IO流中
        wb.save(out)
        out.seek(0)
        return send_file(out, mimetype='application/vnd.ms-excel', cache_timeout=0,
                         attachment_filename=file_name,
                         as_attachment=True)

    @staticmethod
    def export_role_template(file_name, header):
        """
        导出角色excel模板
        :param file_name: 文件名
        :param header: 每列列名数据
        :return:
        """
        wb = Workbook()
        ws = wb.active
        # 添加表头
        ws.append(header)
        role_list = [
            ['角色1名称', '角色拥有功能1'],
            ['角色2名称', '角色拥有功能2']
        ]
        # 添加每行数据
        for role_item in role_list:
            ws.append(role_item)

        from biz.utils.tools import style_range
        # 每一列添加批注数据
        comment_msg = {1: "角色名（必填）\n规范是：小于等于64个字符， 不能重名， 具体数据样式见以下模板数据",
                       2: "角色功能描述（非必填）\n规范是：具体数据样式见以下模板数据",
                       }
        # 设置excel样式
        style_range(ws, merge_header=False, auto_width=True, comment_msg=comment_msg)

        ws.freeze_panes = ws['A2']
        # 新建一个BytesIO流
        out = io.BytesIO()
        # excel写入到IO流中
        wb.save(out)
        out.seek(0)
        return send_file(out, mimetype='application/vnd.ms-excel', cache_timeout=0,
                         attachment_filename=file_name,
                         as_attachment=True)

    @staticmethod
    def import_role_info():
        """
        导入角色excel数据
        :return:
        """
        # 获取导入的excel文件数据
        file = request.files
        if file:
            file = file['file']
            try:
                # excel文件数据转化pandas DataFrame数据
                df = pd.read_excel(file, dtype=str, keep_default_na=False)
            except Exception:
                raise FileTypeError()
            # pandas  DataFrame数据转换成字典数据
            role_list = df.to_dict('records', )
            column_list = ["role_name", "remark"]
            # 获取excel列名
            df_column_list = df.columns.tolist()
            # 判断列名是否错误
            if len(column_list) != len(df_column_list) or False in map(lambda x: x in column_list, df_column_list):
                raise Fail(msg=str(_("column name error")))

            role_name_list = df["role_name"].tolist()
        else:
            raise Fail(msg=str(_("No imported excel file was selected")))
        from biz.utils.tools import check_role_data
        # 检查excel数据是否有输入错误
        result = check_role_data(role_name_list)

        # 没有错误就导入数据到数据库
        if result:
            result = result
        else:
            role_info_list = []
            for item in role_list:
                role = Role()
                role.role_name = item.get("role_name")
                role.remark = item.get("remark")
                role_info_list.append(role)

            with db.auto_commit():
                db.session.add_all(role_info_list)

            result = []

        return result


class PagePermission(models.BaseRemarkTime):
    """页面权限表"""
    __tablename__ = 'page_permission'
    # 页面id
    page_id = db.Column(db.Integer(), primary_key=True)
    # 页面名称
    page_name = db.Column(db.String(length=64), unique=True, nullable=False)
    # 页面路由
    page_route = db.Column(db.String(length=64), nullable=True)
    # 页面上一级页面id
    parent_id = db.Column(db.Integer(), nullable=True)
    # 页面的权重
    weight = db.Column(db.Integer(), nullable=True)
    # 图标
    icon = db.Column(db.String)
    # 页面权限与功能权限表的而对应关系
    operation_permissions = db.relationship('OperationPermission',
                                            backref=db.backref('page_permissions'))

    @staticmethod
    def query_page_operation_permission(form):
        """
        查询页面以及功能列表菜单栏数据
        :param form:
        :return:
        """
        is_operation = form.is_operation
        role_id = form.role_id
        # 获取所有页面数据
        page_data = [{"page_id": i.page_id, "page_name": i.page_name,
                      "parent_id": i.parent_id} for i in
                     PagePermission.query.order_by("page_id").all()]
        from biz.utils.hierarchical_data import get_tree_data

        # 如果是角色管理页面，就要查询当前角色具有哪些页面权限， 否则就是单纯查询所有页面层级菜单栏数据
        if role_id:
            role = Role.query.filter_by(role_id=role_id).first()
            # 获取角色具有哪些页面权限和功能权限
            role_page_ids = [i.page_id for i in role.page_permissions.all()]
            role_operation_ids = [i.operation_id * 10000 for i in role.operation_permissions.all()]
            # 是否展示功能权限数据
            if is_operation:
                operation_data = [
                    {"operation_id": i.operation_id * 10000, "page_id": i.page_id,
                     "page_name": i.api_name} for i
                    in OperationPermission.query.order_by("operation_id").all()]
                # 获取页面权限层级菜单栏数据
                page_operation_data = get_tree_data(page_data, "page_id", operation_data=operation_data,
                                                    role_page_ids=role_page_ids, role_operation_ids=role_operation_ids)
                result = {"data": page_operation_data, "checked_operation_data": role_operation_ids}
            else:
                page_operation_data = get_tree_data(page_data, "page_id")
                result = {"data": page_operation_data}

        else:
            page_operation_data = get_tree_data(page_data, "page_id")
            result = {"data": page_operation_data}

        return result

    @staticmethod
    def add_page(form):
        """
        增加页面权限
        :param form:
        :return:
        """
        page_name = form.page_name
        page_route = form.page_route
        weight = form.weight if form.weight else 100
        parent_id = form.parent_id

        with db.auto_commit():
            page_permission = PagePermission()
            page_permission.page_name = page_name
            page_permission.page_route = page_route
            page_permission.weight = weight
            page_permission.parent_id = parent_id
            db.session.add(page_permission)


class OperationPermission(models.BaseRemarkTime):
    """功能权限表(接口表)"""
    __tablename__ = 'operation_permission'
    # 功能权限id
    operation_id = db.Column(db.Integer(), primary_key=True)
    # 当前接口对应的页面id
    page_id = db.Column(db.Integer, db.ForeignKey('page_permission.page_id'))
    # 接口名
    api_name = db.Column(db.String(length=64), nullable=False)
    # 接口路由
    api_endpoint = db.Column(db.String(length=128), nullable=True)
    # 接口请求方法
    api_method = db.Column(db.String(length=64), nullable=True)

    @staticmethod
    def add_operation(form):
        """
        增加功能权限
        :param form:
        :return:
        """
        page_id = form.page_id
        api_name = form.api_name
        api_endpoint = form.api_endpoint if form.api_endpoint else ""
        api_method = form.api_method if form.api_method else ""

        with db.auto_commit():
            operation_permission = OperationPermission()
            operation_permission.page_id = page_id
            operation_permission.api_name = api_name
            operation_permission.api_endpoint = api_endpoint
            operation_permission.api_method = api_method
            db.session.add(operation_permission)


class DataPermission(models.BaseRemarkTime):
    """数据权限表"""
    __tablename__ = 'data_permission'
    # 数据权限id
    data_id = db.Column(db.Integer(), primary_key=True)
    # 当前用户可以看到的下级资源
    resource_id = db.Column(db.Integer(), nullable=True)
    resource_code = db.Column(db.String(length=64), nullable=True)
    resource_name = db.Column(db.String(length=64), nullable=True)
    # 当前用户可以看到的下级用户
    user_id = db.Column(db.Integer(), nullable=True)
    # 当前用户可以看到的下级资源类型
    resource_type_id = db.Column(db.Integer(), nullable=True)
    # 当前用户是可以查看还是修改下级资源
    operation_type = db.Column(db.Integer(), nullable=True)


class RoleResourcePermission(models.BaseRemarkTime):
    """资源权限表"""
    __tablename__ = 'role_resource_permission'

    id = db.Column(db.Integer(), primary_key=True)
    # 权限id
    role_id = db.Column(db.Integer, db.ForeignKey('role.role_id'))
    # 资源code
    resource_code = db.Column(db.String(length=64), nullable=True)
    # 资源
    resource_type_id = db.Column(db.Integer(), nullable=True)
    # 操作权限
    operation_type = db.Column(db.Integer(), nullable=True)
    # 品牌权限
    extra1 = db.Column(db.String(length=64), nullable=True)

    @staticmethod
    def upsert_brand_permission(form):
        """
        品牌、管理区域权限
        :param form:
        :return:
        """
        _d = defaultdict(set)

        if form.role_permissions:
            from .constants import BRAND_MANAGE_SQL
            b_m = pd.read_sql(BRAND_MANAGE_SQL, con=db.engine)
            [_d[i['brand_code']].add(i['org_sk']) for i in b_m.to_dict('records')]

        with db.auto_commit():
            for rp in form.role_permissions:
                resource_code = rp.get("resource_code")
                operation_type = rp.get("operation_type")
                resource_permission = RoleResourcePermission.query. \
                    filter_by(role_id=form.role_id,
                              resource_code=resource_code,
                              resource_type_id=form.resource_type_id).all()
                if resource_permission:
                    if operation_type == 0:
                        # 删品牌
                        db.session.delete(resource_permission[0])
                        # 删品牌下的管理区域
                        brand_ms = _d.get(resource_code, {})
                        if brand_ms:
                            # BUG #46032导致品牌数据全部删除做出如下更改
                            RoleResourcePermission.query.filter(and_(
                                RoleResourcePermission.role_id == form.role_id,
                                RoleResourcePermission.resource_type_id == 0,
                                RoleResourcePermission.resource_code.in_(brand_ms)
                            )).delete(synchronize_session=False)
                        continue

                    resource_permission[0].operation_type = operation_type
                else:
                    if operation_type == 0:
                        continue

                    resource_permission = RoleResourcePermission()
                    resource_permission.resource_code = resource_code
                    resource_permission.role_id = form.role_id
                    resource_permission.resource_type_id = form.resource_type_id
                    resource_permission.operation_type = operation_type
                    db.session.add(resource_permission)

    @staticmethod
    def upsert_manager_permission(form):
        """
        品牌、管理区域权限
        :param form:
        :return:
        """
        with db.auto_commit():
            for rp in form.role_permissions:
                resource_code = rp.get("resource_code")
                operation_type = rp.get("operation_type")
                resource_permission = RoleResourcePermission.query. \
                    filter_by(role_id=form.role_id,
                              resource_code=resource_code,
                              resource_type_id=form.resource_type_id).all()
                if resource_permission:
                    if operation_type == 0:
                        db.session.delete(resource_permission[0])
                        continue

                    resource_permission[0].operation_type = operation_type
                else:
                    if operation_type == 0:
                        continue

                    resource_permission = RoleResourcePermission()
                    resource_permission.resource_code = resource_code
                    resource_permission.role_id = form.role_id
                    resource_permission.resource_type_id = form.resource_type_id
                    resource_permission.operation_type = operation_type
                    db.session.add(resource_permission)

    @staticmethod
    def upsert_scene_permission(form):
        """
        场景权限
        :param form:
        :return:
        """
        if form.role_permissions:
            from .constants import DIM_SCENE_SQL
            scenes = pd.read_sql(
                DIM_SCENE_SQL, con=db.engine,
                params={'role_id': (form.role_id,), 'resource_type': form.resource_type_id}
            )
            scene_codes = set(scenes['scene_code'].tolist())
        else:
            scene_codes = set()

        with db.auto_commit():
            for rp in form.role_permissions:
                resource_code = rp.get("resource_code")
                operation_type = rp.get("operation_type")
                if resource_code not in scene_codes:
                    continue

                resource_permission = RoleResourcePermission.query. \
                    filter_by(role_id=form.role_id,
                              resource_code=resource_code,
                              resource_type_id=form.resource_type_id).all()
                if resource_permission:
                    if operation_type == 0:
                        db.session.delete(resource_permission[0])
                        continue

                    resource_permission[0].operation_type = operation_type
                else:
                    if operation_type == 0:
                        continue

                    resource_permission = RoleResourcePermission()
                    resource_permission.resource_code = resource_code
                    resource_permission.role_id = form.role_id
                    resource_permission.resource_type_id = form.resource_type_id
                    resource_permission.operation_type = operation_type
                    db.session.add(resource_permission)

    @staticmethod
    def upsert_store_permission(form):
        """
        门店权限
        :param form:
        :return:
        """
        with db.auto_commit():
            for rp in form.role_permissions:
                brand_code = rp.get("brand_code")
                resource_code = rp.get("resource_code")
                operation_type = rp.get("operation_type")

                resource_permission = RoleResourcePermission.query. \
                    filter_by(role_id=form.role_id,
                              extra1=brand_code,
                              resource_code=resource_code,
                              resource_type_id=form.resource_type_id).all()
                if resource_permission:
                    if operation_type == 0:
                        db.session.delete(resource_permission[0])
                        continue

                    resource_permission[0].operation_type = operation_type
                else:
                    if operation_type == 0:
                        continue

                    resource_permission = RoleResourcePermission()
                    resource_permission.extra1 = brand_code
                    resource_permission.resource_code = resource_code
                    resource_permission.role_id = form.role_id
                    resource_permission.resource_type_id = form.resource_type_id
                    resource_permission.operation_type = operation_type
                    db.session.add(resource_permission)
